# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

This is a **documentation-only repository** for a Glass Deep Processing Industry ERP System PRD (Product Requirements Document) project. The repository contains comprehensive product requirement documentation for a manufacturing ERP system, structured as a complete business analysis and design specification.

**Project Status**: Documentation Complete (V2.0)
**Industry Focus**: Glass deep processing manufacturing
**Document Language**: Chinese (Simplified)

## Repository Structure

### Documentation Architecture
```
docs/
├── prd_v1.0/                    # Original PRD version
├── prd_v2.0/                    # Current restructured version (V2.0)
│   ├── PRD_Readme_v2.0.md      # System integration overview
│   ├── Frontend_Design_Guidelines.md  # UI/UX design standards
│   ├── TODOLIST.md             # Project task tracking
│   ├── _Glossary.md            # Global terminology definitions
│   ├── _Business_Rules.md       # Core business rule library
│   └── [subsystem-folders]/     # Individual subsystem PRDs
└── user-requirement.md          # Original user requirements
```

### 11 Core Subsystems (All Complete)
- **basic-management/**: User auth, org structure, permissions (BMS-001 to BMS-006)
- **pdm-system/**: Product data management, BOM design (PDM-001 to PDM-007)  
- **sales-system/**: Order management, customer management (SMS-001 to SMS-008)
- **procurement-system/**: Purchasing, supplier management (PMS-001 to PMS-008)
- **production-system/**: Manufacturing execution system (MES-001 to MES-008)
- **warehouse-system/**: Inventory and warehouse management (WMS-001 to WMS-009)
- **finance-system/**: Financial accounting, cost management (FMS-001 to FMS-010)
- **project-system/**: Project management, delivery tracking (PMS-001 to PMS-008)
- **quality-system/**: Quality control and inspection (QMS-001 to QMS-008)
- **crm-system/**: Customer relationship management (CRM-001 to CRM-008)
- **hr-system/**: Human resources, payroll management (HR-001 to HR-010)
- **data-center/**: Business intelligence, reporting (DC-001 to DC-011)

## Key Business Concepts

### Manufacturing Domain
- **Glass Deep Processing**: Secondary processing of raw glass (cutting, edging, tempering, laminating)
- **Project-Based Manufacturing**: Custom orders requiring field measurement and bespoke production
- **Piece-Rate Compensation**: Payment system based on production quantity (industry standard)
- **Subcontracting (外协)**: Outsourced processing operations
- **Multi-Level BOM**: Sales BOM → Process BOM → Production BOM transformation

### System Integration Patterns
- **End-to-End Business Flow**: CRM opportunity → Sales order → PDM BOM → MES production → WMS shipping → Finance settlement
- **Data Integration Standards**: Unified coding schemes for orders, materials, batches, and processes
- **Real-Time Synchronization**: Critical business data synchronized across subsystems with <3s response time
- **Business-Finance Integration**: Automated financial recording triggered by business operations

## Development Context

### No Active Development
This repository contains **documentation only** - there is no source code, build scripts, or development environment. It represents completed business analysis and system design work.

### Document Standards Applied
- **Five Core Principles**: First Principles, DRY, KISS, SOLID, YAGNI
- **Unified Terminology**: All documents reference the global glossary (`_Glossary.md`)
- **Business Rules Centralization**: Common business logic in `_Business_Rules.md`
- **Design Standards**: Comprehensive UI/UX guidelines in `Frontend_Design_Guidelines.md`

### Quality Framework
- **Testable Acceptance Criteria**: Each module includes measurable acceptance standards
- **Risk Management**: Identified risks with mitigation strategies
- **Performance Targets**: Specific KPIs for system integration and business efficiency

## Working with This Repository

### Document Navigation
1. Start with `docs/prd_v2.0/PRD_Readme_v2.0.md` for system overview
2. Reference `_Glossary.md` for terminology understanding
3. Review subsystem-specific folders for detailed module specifications
4. Use `TODOLIST.md` to understand project completion status

### Content Modification Guidelines
- **Terminology Consistency**: Always use terms defined in `_Glossary.md`
- **Business Rule References**: Link to centralized rules in `_Business_Rules.md`
- **Version Control**: Update version control tables when making changes
- **Cross-Reference Integrity**: Maintain links between related documents

### Document Quality Standards
- **Structured Format**: All PRDs follow consistent template structure
- **Measurable Criteria**: Acceptance criteria must be testable and quantifiable
- **Integration Focus**: Emphasize cross-system data flow and business process continuity
- **Industry Context**: Maintain focus on glass deep processing manufacturing domain

## Common Tasks

### Document Analysis
```bash
# Search for specific business concepts across all documents
find docs/ -name "*.md" -exec grep -l "keyword" {} \;

# Validate cross-references between documents
grep -r "\[.*\](.*.md)" docs/prd_v2.0/
```

### Content Updates
- Always preserve existing document structure and formatting
- Update version control tables when making changes
- Maintain cross-document reference integrity
- Follow established terminology from glossary

### Quality Assurance
- Verify all acceptance criteria are measurable
- Ensure business flows align with system integration architecture
- Validate terminology consistency across documents
- Check that all risks have corresponding mitigation strategies

---

**Repository Type**: Documentation Repository  
**Primary Language**: Chinese (Simplified)  
**Business Domain**: Manufacturing ERP (Glass Deep Processing)  
**Documentation Standard**: V2.0 (Five Core Principles Applied)