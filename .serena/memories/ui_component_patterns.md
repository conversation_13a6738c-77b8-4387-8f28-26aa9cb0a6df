# UI Component Patterns - 基础管理模块

## 数据表格组件模式

### 1. 表格容器结构
```html
<!-- 标题区域 -->
<div class="bg-white border-b border-border-gray px-6 py-4">
    <div class="flex items-center justify-between">
        <div>
            <h2 class="text-lg font-medium text-title-gray">列表标题</h2>
            <p class="text-sm text-aux-gray mt-1">描述信息</p>
        </div>
        <div class="flex space-x-3">
            <!-- 操作按钮组 -->
        </div>
    </div>
</div>

<!-- 搜索筛选区域 -->
<div class="bg-white border-b border-border-gray px-6 py-4">
    <div class="flex items-center space-x-4">
        <!-- 搜索框 + 筛选器 -->
    </div>
</div>

<!-- 表格内容区域 -->
<div class="flex-1 overflow-auto bg-white">
    <table class="min-w-full">
        <!-- 表格内容 -->
    </table>
</div>
```

### 2. 搜索组件
```html
<div class="flex-1">
    <div class="relative">
        <input
            type="text"
            x-model="searchQuery"
            @input="handleSearch"
            placeholder="搜索提示文字..."
            class="w-full pl-10 pr-4 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
        >
        <svg class="absolute left-3 top-2.5 h-5 w-5 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
        </svg>
    </div>
</div>
```

## 模态框组件模式

### 1. 标准模态框结构
```html
<div x-show="showModal" 
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="transition ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0"
     class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
     @click="showModal = false">
    
    <div class="relative top-20 mx-auto p-5 border max-w-md shadow-lg rounded-md bg-white" @click.stop>
        <!-- 标题栏 -->
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-title-gray">模态框标题</h3>
            <button @click="showModal = false" class="text-aux-gray hover:text-title-gray">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
        </div>
        
        <!-- 内容区域 -->
        <div class="space-y-4">
            <!-- 表单或内容 -->
        </div>
        
        <!-- 底部按钮 -->
        <div class="flex justify-end space-x-3 pt-4">
            <button type="button" class="px-4 py-2 text-sm font-medium text-body-gray bg-white border border-border-gray rounded-md hover:bg-gray-50">
                取消
            </button>
            <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-blue-600">
                确定
            </button>
        </div>
    </div>
</div>
```

## 仪表板组件模式

### 1. KPI卡片
```html
<div class="bg-white p-6 rounded-lg shadow-sm border border-border-gray">
    <div class="flex items-center">
        <div class="w-12 h-12 rounded-lg flex items-center justify-center text-white mr-4"
             :class="iconBgColor">
            <!-- SVG图标 -->
        </div>
        <div>
            <div class="text-2xl font-bold text-title-gray" x-text="value"></div>
            <div class="text-sm text-aux-gray">指标名称</div>
            <div class="text-xs text-success mt-1">变化趋势</div>
        </div>
    </div>
</div>
```

### 2. 活动时间线
```html
<div class="space-y-3 max-h-80 overflow-y-auto">
    <template x-for="activity in activities" :key="activity.id">
        <div class="activity-item p-3 rounded-md">
            <div class="flex items-start space-x-3">
                <div class="w-8 h-8 rounded-full flex items-center justify-center text-white text-xs"
                     :class="getActivityColor(activity.type)">
                    <!-- 图标 -->
                </div>
                <div class="flex-1">
                    <div class="text-sm text-title-gray" x-text="activity.title"></div>
                    <div class="text-xs text-aux-gray mt-1" x-text="activity.description"></div>
                    <div class="flex items-center justify-between mt-2">
                        <span class="text-xs text-aux-gray" x-text="activity.user"></span>
                        <span class="text-xs text-aux-gray" x-text="activity.time"></span>
                    </div>
                </div>
            </div>
        </div>
    </template>
</div>
```

## 表单组件模式

### 1. 标准表单字段
```html
<div>
    <label for="fieldId" class="block text-sm font-medium text-title-gray mb-2">
        字段标签 <span class="text-error">*</span>
    </label>
    <input
        id="fieldId"
        type="text"
        x-model="form.field"
        :class="errors.field ? 'border-error focus:border-error focus:ring-error' : 'border-border-gray focus:border-primary focus:ring-primary'"
        class="w-full px-3 py-2 border rounded-md shadow-sm placeholder-aux-gray focus:outline-none focus:ring-1 transition-colors"
        placeholder="请输入..."
        :disabled="loading"
    >
    <p x-show="errors.field" x-text="errors.field" class="mt-1 text-sm text-error"></p>
</div>
```

### 2. 密码输入组件
```html
<div class="relative">
    <input
        :type="showPassword ? 'text' : 'password'"
        x-model="form.password"
        class="w-full px-3 py-2 pr-10 border rounded-md shadow-sm placeholder-aux-gray focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
        placeholder="请输入密码"
    >
    <button
        type="button"
        @click="showPassword = !showPassword"
        class="absolute inset-y-0 right-0 pr-3 flex items-center"
    >
        <svg x-show="!showPassword" class="h-5 w-5 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <!-- 眼睛图标 -->
        </svg>
        <svg x-show="showPassword" class="h-5 w-5 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <!-- 眼睛关闭图标 -->
        </svg>
    </button>
</div>
```

## 通知组件模式

### 1. 消息提示
```html
<!-- 错误提示 -->
<div x-show="generalError" class="p-4 bg-red-50 border border-red-200 rounded-md">
    <div class="flex">
        <svg class="h-5 w-5 text-error" fill="currentColor" viewBox="0 0 20 20">
            <!-- 错误图标 -->
        </svg>
        <div class="ml-3">
            <p class="text-sm text-error" x-text="generalError"></p>
        </div>
    </div>
</div>

<!-- 成功提示 -->
<div x-show="successMessage" class="p-4 bg-green-50 border border-green-200 rounded-md">
    <div class="flex">
        <svg class="h-5 w-5 text-success" fill="currentColor" viewBox="0 0 20 20">
            <!-- 成功图标 -->
        </svg>
        <div class="ml-3">
            <p class="text-sm text-success" x-text="successMessage"></p>
        </div>
    </div>
</div>
```

### 2. 通知下拉菜单
```html
<div class="relative" x-data="{ showNotifications: false }">
    <button @click="showNotifications = !showNotifications" 
            class="relative p-2 text-aux-gray hover:text-title-gray transition-colors">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <!-- 通知图标 -->
        </svg>
        <span x-show="unreadCount > 0" 
              class="notification-badge absolute -top-1 -right-1 w-5 h-5 bg-error text-white text-xs rounded-full flex items-center justify-center"
              x-text="unreadCount"></span>
    </button>
    
    <div x-show="showNotifications" @click.away="showNotifications = false"
         class="absolute right-0 mt-2 w-80 bg-white border border-border-gray rounded-md shadow-lg z-10">
        <!-- 通知列表内容 -->
    </div>
</div>
```

## 加载状态组件

### 1. 按钮加载状态
```html
<button
    type="submit"
    :disabled="loading"
    :class="loading ? 'bg-disabled-gray cursor-not-allowed' : 'bg-primary hover:bg-blue-600'"
    class="w-full py-3 px-4 text-sm font-medium rounded-md text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
>
    <span x-show="loading" class="loading-spinner mr-2"></span>
    <span x-text="loading ? '处理中...' : '确定'"></span>
</button>
```

### 2. 页面加载遮罩
```html
<div x-show="pageLoading" class="fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
    <div class="text-center">
        <div class="loading-spinner mx-auto mb-4"></div>
        <p class="text-aux-gray">正在加载...</p>
    </div>
</div>
```