# PRD 快速查找索引

## 使用指南

本索引提供玻璃深加工ERP系统PRD文档的快速查找入口，帮助快速定位功能需求、业务流程和技术规范。

## 系统概览快速导航

### 📋 总体架构
- **系统架构**: `read_memory('prd_system_architecture')` - 13个子系统架构总览
- **功能模块**: `read_memory('prd_modules_index')` - 129个功能模块索引
- **业务流程**: `read_memory('prd_business_workflows')` - 端到端业务流程
- **数据集成**: `read_memory('prd_data_integration_mapping')` - 跨系统数据关系
- **术语规则**: `read_memory('prd_terms_and_rules_index')` - 业务术语和规则

### 🔍 按业务域快速查找

#### 客户管理域
- **CRM系统**: 8个模块 (CRM-001 ~ CRM-008)
- **核心流程**: 销售机会 → 客户分配 → 跟进活动 → 服务管理
- **关键术语**: 销售漏斗、客户分级、服务工单
- **相关文档**: `docs/prd_v2.0/crm-system/CRM_System_PRD_v2.0.md`

#### 销售管理域
- **SMS系统**: 9个模块 (SMS-001 ~ SMS-009)
- **核心流程**: 客户关联 → 产品配置 → 报价管理 → 订单录入 → 价格审批
- **关键术语**: 产品配置器、价格策略、订单状态流转
- **相关文档**: `docs/prd_v2.0/sales-system/Sales_Management_System_PRD_v2.0.md`

#### 产品工艺域
- **PDM系统**: 8个模块 (PDM-001 ~ PDM-008)
- **核心流程**: 物料管理 → BOM设计 → 版本管理 → 工艺路线 → 文档管理
- **关键术语**: 销售BOM、工艺BOM、生产BOM、参数化设计
- **相关文档**: `docs/prd_v2.0/pdm-system/PDM_System_PRD_v2.0.md`

#### 采购管理域
- **PMS系统**: 12个模块 (PMS-001 ~ PMS-012)
- **核心流程**: 供应商管理 → MRP计算 → 采购建议 → 订单管理 → 外协管理
- **关键术语**: MRP、外协加工、供应商绩效、变体采购
- **相关文档**: `docs/prd_v2.0/procurement-system/Procurement_Management_System_PRD_v2.0.md`

#### 生产制造域
- **MES系统**: 13个模块 (MES-001 ~ MES-013)
- **核心流程**: 订单分解 → APS排程 → 计划下发 → 工位执行 → 设备管理
- **关键术语**: APS引擎、工序任务、扫码报工、设备数据采集
- **相关文档**: `docs/prd_v2.0/production-system/Production_Management_System_PRD_v2.0.md`

#### 仓储物流域
- **WMS系统**: 12个模块 (WMS-001 ~ WMS-012)
- **核心流程**: 仓储结构 → 收货入库 → 质量确认 → 智能拣货 → 出库配送
- **关键术语**: FIFO原则、批次追溯、智能拣货、变体库存
- **相关文档**: `docs/prd_v2.0/warehouse-system/Warehouse_Management_System_PRD_v2.0.md`

#### 质量管理域
- **QMS系统**: 8个模块 (QMS-001 ~ QMS-008)
- **核心流程**: 质量标准 → 检验任务 → 检验执行 → 不合格品处理 → 质量追溯
- **关键术语**: 三检制度 (IQC/IPQC/FQC)、质量预警、质量档案
- **相关文档**: `docs/prd_v2.0/quality-system/Quality_Management_System_PRD_v2.0.md`

#### 财务管理域
- **FMS系统**: 10个模块 (FMS-001 ~ FMS-010)
- **核心流程**: 会计科目 → 凭证管理 → 应收应付 → 收付款核销 → 成本核算
- **关键术语**: 业财一体化、AR/AP、成本要素归集
- **相关文档**: `docs/prd_v2.0/finance-system/Finance_Management_System_PRD_v2.0.md`

#### 项目管理域
- **PJS系统**: 9个模块 (PJS-001 ~ PJS-009)
- **核心流程**: 项目立项 → WBS分解 → 任务管理 → 进度跟踪 → 成本归集
- **关键术语**: WBS分解、项目现场管理、项目收款
- **相关文档**: `docs/prd_v2.0/project-system/Project_Management_System_PRD_v2.0.md`

#### 人力资源域
- **HR系统**: 10个模块 (HR-001 ~ HR-010)
- **核心流程**: 员工档案 → 考勤管理 → 薪酬计算 → 成本归集 → 自助服务
- **关键术语**: 计件单价、薪酬引擎、人工成本归集
- **相关文档**: `docs/prd_v2.0/hr-system/HR_Management_System_PRD_v2.0.md`

#### 数据分析域
- **DC系统**: 11个模块 (DC-001 ~ DC-011)
- **核心流程**: 数据源连接 → ETL处理 → 数据仓库 → 主题驾驶舱 → 报表分析
- **关键术语**: ETL、数据仓库、驾驶舱、交互式分析
- **相关文档**: `docs/prd_v2.0/data-center/Data_Center_System_PRD_v2.0.md`

#### 基础管理域
- **BMS系统**: 9个模块 (BMS-001 ~ BMS-009)
- **核心流程**: 用户认证 → 组织管理 → 权限控制 → 数据字典 → 合同管理
- **关键术语**: RBAC权限、SSO认证、数据权限、计量单位
- **相关文档**: `docs/prd_v2.0/basic-management/Basic_Management_System_PRD_v2.0.md`

## 🔄 按业务流程快速查找

### 端到端订单处理流程
1. **CRM-003** (销售机会) → **SMS-004** (订单录入) → **PDM-002** (BOM设计)
2. **MES-001** (任务分解) → **WMS-006** (批次出库) → **FMS-004** (应收管理)

### 采购到付款流程
1. **MES-003** (生产计划) → **PMS-003** (MRP计算) → **PMS-005** (采购订单)
2. **WMS-003** (收货入库) → **QMS-002** (检验任务) → **FMS-007** (应付管理)

### 生产执行流程
1. **PDM-005** (工艺路线) → **MES-002** (APS排程) → **MES-004** (工位执行)
2. **QMS-003** (过程检验) → **HR-006** (薪酬计算) → **FMS-009** (成本归集)

### 项目管理流程
1. **CRM-003** (项目机会) → **PJS-001** (项目立项) → **PJS-002** (WBS分解)
2. **MES-001** (生产任务) → **PJS-004** (进度跟踪) → **PJS-008** (项目收款)

## 📊 按数据实体快速查找

### 主数据实体
- **客户数据**: CRM-001 → SMS-001 → FMS-006 → PJS-001
- **物料数据**: PDM-001 → WMS-001 → PMS-001 → MES-007 → FMS-009
- **供应商数据**: PMS-001 → FMS-007 → QMS-001 → WMS-003
- **员工数据**: HR-002 → MES-005 → FMS-009 → BMS-003

### 业务单据实体
- **销售订单**: SMS-004 → PDM-002 → MES-001 → WMS-006 → FMS-004
- **采购订单**: PMS-005 → WMS-003 → QMS-002 → FMS-007
- **生产订单**: MES-001 → MES-004 → QMS-003 → WMS-003 → HR-006
- **项目订单**: PJS-001 → SMS-004 → MES-001 → PJS-005

## 🏭 按玻璃深加工行业特色查找

### 玻璃产品特性管理
- **玻璃原片管理**: PDM-001 + WMS-012 (变体库存管理)
- **钢化玻璃工艺**: PDM-005 (工艺路线) + MES-004 (工位执行)
- **夹层玻璃追溯**: QMS-005 (质量追溯) + MES-007 (产品追溯)
- **中空玻璃管理**: PDM-002 (BOM设计) + QMS-008 (质量档案)

### 项目化生产管理
- **项目BOM管理**: PJS-002 (项目分解) + PDM-002 (BOM设计)
- **项目成本核算**: PJS-005 (成本归集) + FMS-010 (成本核算)
- **项目现场管理**: PJS-009 + MES-004 (工位执行)
- **项目收款管理**: PJS-008 + FMS-005 (收款核销)

### 外协加工管理
- **外协工序定义**: PDM-006 (外协工序) + MES-004 (工位任务)
- **外协订单管理**: PMS-007 + PMS-008 (物料追踪)
- **外协质量管理**: PMS-009 + QMS-003 (检验执行)
- **外协成本管理**: PMS-010 + FMS-009 (成本要素)

### 计件工资管理
- **计件单价设定**: HR-005 + PDM-006 (工序管理)
- **报工数据采集**: MES-005 (扫码报工) + HR-003 (考勤数据)
- **工资计算引擎**: HR-006 + HR-008 (成本归集)
- **财务凭证生成**: HR-009 + FMS-002 (凭证管理)

## 🚨 常见问题快速定位

### 权限管理问题
- **用户无法登录**: BMS-001 (用户认证) + BMS-003 (用户管理)
- **功能权限不足**: BMS-004 (角色权限) + BMS-005 (数据权限)
- **跨部门数据访问**: 参考业务规则 R1.2

### 订单流程问题
- **订单状态异常**: 参考业务规则 R2.1 (订单状态流转规则)
- **BOM无法固化**: 参考业务规则 R2.2 (BOM固化规则)
- **价格审批卡住**: SMS-008 (价格审批) + 价格审批流程

### 库存管理问题
- **库存数据不准**: WMS-007 (库存盘点) + 参考业务规则 R3.1
- **出库批次错误**: WMS-006 (批次出库) + 参考业务规则 R3.2
- **安全库存预警**: PMS-004 (采购建议) + 参考业务规则 R3.1

### 生产计划问题
- **排程冲突**: MES-002 (APS排程) + 参考业务规则 R2.3
- **设备维护影响生产**: MES-010 (设备维护) + MES-011 (性能分析)
- **工序任务无法下达**: MES-003 (计划下发) + PDM-005 (工艺路线)

### 质量问题追溯
- **来料质量问题**: QMS-002 (检验任务) + PMS-009 (外协质量)
- **生产过程质量**: QMS-003 (检验执行) + MES-007 (产品追溯)
- **客户投诉处理**: CRM-007 (服务工单) + QMS-005 (质量追溯)

### 成本核算问题
- **成本数据异常**: FMS-010 (成本核算) + FMS-009 (要素归集)
- **人工成本计算**: HR-006 (薪酬引擎) + HR-008 (成本归集)
- **外协成本分摊**: PMS-010 (外协成本) + FMS-009 (要素归集)

## 📈 性能优化查找

### 系统集成优化
- **数据同步延迟**: 参考数据集成关系映射中的实时同步接口
- **跨系统调用超时**: 参考业务规则 R7.2 (接口调用规则)
- **主数据一致性**: 参考业务规则 R7.1 (数据一致性规则)

### 业务流程优化
- **订单处理周期**: 端到端订单流程优化，目标从15天缩短至8天
- **采购响应时间**: MRP计算 + 采购建议自动化
- **生产效率提升**: APS智能排程 + 扫码报工自动化

## 🔧 技术实现参考

### UI/UX设计
- **设计系统**: `read_memory('basic_management_ui_design_system')`
- **组件模式**: `read_memory('ui_component_patterns')`
- **数据管理**: `read_memory('alpinejs_data_patterns')`

### 开发规范
- **编码规范**: 功能模块编码 (BMS/PDM/SMS等) + 业务单据编码
- **接口标准**: API设计规范 + 数据交换格式
- **权限控制**: RBAC权限模型 + 数据访问控制