# 常用工作流

## 文档分析任务
1. **系统概览**: 阅读`PRD_Readme_v2.0.md`了解整体架构
2. **术语理解**: 查阅`_Glossary.md`确保术语准确性
3. **业务规则**: 参考`_Business_Rules.md`理解跨系统规则
4. **子系统深入**: 选择具体子系统PRD进行详细分析

## 文档修改最佳实践
- **术语一致性**: 始终使用`_Glossary.md`中定义的术语
- **业务规则引用**: 链接集中化规则，避免重复定义
- **版本控制**: 更新版本控制表记录变更
- **交叉引用完整性**: 维护文档间链接关系

## 质量保证流程
- 验收标准可测试性
- 业务流程与系统集成架构一致
- 术语定义统一性
- 风险及缓解策略完整性

## 搜索模式
- 特定业务概念: `find docs/ -name "*.md" -exec grep -l "关键词" {} \;`
- 交叉引用验证: `grep -r "\[.*\](.*.md)" docs/prd_v2.0/`