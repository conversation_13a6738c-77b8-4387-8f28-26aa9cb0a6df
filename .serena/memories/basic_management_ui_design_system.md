# Basic Management UI Design System

## 核心设计原则

### 1. 技术栈统一
- **前端框架**: Vanilla HTML + TailwindCSS + Alpine.js
- **CDN依赖**: 
  - TailwindCSS: `https://cdn.tailwindcss.com`
  - Alpine.js: `https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js`
  - Chart.js (仪表板): `https://cdn.jsdelivr.net/npm/chart.js`

### 2. 颜色系统 (Tailwind配置)
```javascript
colors: {
    primary: '#1890FF',      // 主色调-蓝色
    success: '#52C41A',      // 成功-绿色  
    warning: '#FAAD14',      // 警告-橙色
    error: '#F5222D',        // 错误-红色
    'title-gray': '#262626', // 标题文字
    'body-gray': '#595959',  // 正文文字
    'aux-gray': '#8C8C8C',   // 辅助文字
    'disabled-gray': '#BFBFBF', // 禁用状态
    'border-gray': '#D9D9D9',   // 边框颜色
    'bg-gray': '#FAFAFA'        // 背景色
}
```

### 3. 中文字体系统
```css
fontFamily: {
    'chinese': ['PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', 'sans-serif']
}
```

## 页面布局模式

### 1. 管理页面标准布局
```html
<body class="bg-bg-gray font-chinese">
    <!-- 顶部导航栏：固定高度 h-16 -->
    <header class="bg-white shadow-sm border-b border-border-gray h-16">
        <div class="flex items-center justify-between px-6 h-full">
            <!-- 左侧：页面标题 + 面包屑 -->
            <div class="flex items-center space-x-4">
                <h1 class="text-xl font-medium text-title-gray">页面标题</h1>
                <span class="text-aux-gray text-sm">/ 面包屑</span>
            </div>
            <!-- 右侧：操作按钮/用户信息 -->
        </div>
    </header>
    
    <!-- 主内容区域：calc(100vh-64px) -->
    <main class="flex-1 overflow-hidden">
        <!-- 内容具体布局 -->
    </main>
</body>
```

### 2. 登录页面专用布局
```html
<body class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 font-chinese">
    <div class="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <!-- 居中卡片布局 -->
    </div>
</body>
```

## 组件设计模式

### 1. 卡片组件
```html
<div class="bg-white rounded-lg shadow-sm border border-border-gray p-6">
    <!-- 卡片内容 -->
</div>
```

### 2. 按钮系统
- **主要按钮**: `bg-primary text-white hover:bg-blue-600`
- **次要按钮**: `bg-white border border-border-gray text-body-gray hover:bg-gray-50`
- **危险按钮**: `bg-error text-white hover:bg-red-600`
- **禁用状态**: `bg-disabled-gray cursor-not-allowed`

### 3. 表单控件
- **输入框**: `w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary`
- **选择框**: 同输入框样式
- **错误状态**: `border-error focus:border-error focus:ring-error`

### 4. 状态指示器
- **成功**: `bg-green-100 text-green-800`
- **警告**: `bg-yellow-100 text-yellow-800`  
- **错误**: `bg-red-100 text-red-800`
- **信息**: `bg-blue-100 text-blue-800`

## 交互模式

### 1. Alpine.js 状态管理模式
```javascript
function pageApp() {
    return {
        // 数据状态
        loading: false,
        showModal: false,
        
        // 表单数据
        form: {
            // 表单字段
        },
        
        // 错误处理
        errors: {},
        generalError: '',
        successMessage: '',
        
        // 方法
        init() {
            // 初始化逻辑
        },
        
        validateForm() {
            // 表单验证
        },
        
        async submitForm() {
            // 表单提交
        }
    }
}
```

### 2. 模态框标准结构
```html
<div x-show="showModal" 
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
     @click="showModal = false">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white" @click.stop>
        <!-- 模态框内容 -->
    </div>
</div>
```

## 视觉特效

### 1. 悬停效果
```css
.module-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}
```

### 2. 加载动画
```css
.loading-spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #1890FF;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    animation: spin 1s linear infinite;
}
```

### 3. 渐变背景
```css
.kpi-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
```

## 响应式设计

### 1. 网格系统
- 仪表板: `grid-cols-1 md:grid-cols-2 lg:grid-cols-4`
- 模块卡片: `grid-cols-1 md:grid-cols-2 lg:grid-cols-3`
- 内容布局: `grid-cols-1 lg:grid-cols-3`

### 2. 间距系统
- 容器边距: `px-6 py-4`
- 元素间距: `space-x-4`, `space-y-6`
- 内容填充: `p-6`, `p-4`

## 图标使用规范

### 1. SVG图标系统
- 统一使用 Heroicons 风格
- 标准尺寸: `w-4 h-4`, `w-5 h-5`, `w-6 h-6`
- 颜色: `text-aux-gray`, `text-primary`, `text-white`

### 2. 常用图标语义
- 搜索: `M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z`
- 添加: `M12 6v6m0 0v6m0-6h6m-6 0H6`
- 编辑: `M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z`

## 业务特色

### 1. 中文优先设计
- 页面标题: 中文主标题 + 英文副标题
- 系统名称: "玻璃深加工ERP系统"
- 版权信息: 中文版权声明

### 2. 行业特色元素
- Logo设计: 圆形蓝色背景
- 品牌色调: 专业蓝色系 (#1890FF)
- 业务术语: 工号、部门、权限等企业管理概念