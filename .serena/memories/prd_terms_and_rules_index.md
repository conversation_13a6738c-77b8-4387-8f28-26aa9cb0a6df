# PRD 关键术语和业务规则索引

## 核心业务术语

### 系统架构术语
- **ERP**: 企业资源计划系统 - 整个企业管理系统总称
- **MES**: 制造执行系统 - 生产管理子系统，负责生产计划执行和现场管理
- **WMS**: 仓储管理系统 - 仓储管理子系统，负责库存和出入库管理
- **CRM**: 客户关系管理系统 - 负责客户档案和销售机会管理
- **PDM**: 产品数据管理系统/工艺管理子系统 - 负责产品结构和工艺路线管理
- **BI**: 商业智能/数据中心 - 负责数据分析和决策支持

### 核心业务流程术语
- **BOM**: 物料清单 - 分为销售BOM、工艺BOM、生产BOM三种类型
- **销售BOM**: 面向客户的产品配置清单，在报价和订单阶段使用
- **工艺BOM**: 工艺设计阶段的物料清单，由工艺工程师设计
- **生产BOM**: 经过审核固化的最终生产指导文件
- **MRP**: 物料需求计划 - 基于生产计划和BOM计算物料需求的算法
- **APS**: 高级计划排程引擎 - 智能生产排程算法
- **工序任务**: 生产工序的具体执行任务，MES系统中的基本执行单元

### 质量管理术语
- **IQC**: 来料检验 - 对采购入库物料的质量检验
- **IPQC**: 过程检验 - 生产过程中的质量检验
- **FQC**: 完工检验/最终检验 - 产品完工后的最终质量检验
- **OQC**: 出货检验 - 产品发货前的最终质量检验
- **三检制度**: IQC + IPQC + FQC的完整质量检验体系

### 财务管理术语
- **AR**: 应收账款 - 客户欠款管理
- **AP**: 应付账款 - 供应商付款管理
- **业财一体化**: 业务与财务系统的深度集成，业务操作自动触发财务记录

### 用户角色定义
- **系统管理员**: 拥有最高系统权限，负责系统配置、用户管理、权限分配
- **销售代表**: 负责客户开发和订单管理，主要使用销售管理和CRM子系统
- **工艺工程师**: 负责产品工艺设计，主要使用PDM子系统进行BOM和工艺路线设计
- **生产计划员**: 负责生产计划制定和排程，主要使用MES子系统
- **仓库管理员**: 负责仓储作业，主要使用WMS子系统进行出入库操作
- **质检员**: 负责质量检验，主要使用质量管理子系统进行各类检验

## 核心业务规则

### 1. 用户权限与安全规则

#### R1.1 基于角色的权限控制 (RBAC)
- 所有系统功能必须基于用户角色进行权限控制
- 用户只能访问其角色权限范围内的功能和数据
- 权限变更必须经过系统管理员审批
- 敏感操作必须记录操作日志

#### R1.2 数据访问权限
- 用户只能查看和操作其职责范围内的数据
- 跨部门数据访问需要特殊授权
- 财务数据访问需要额外的安全验证
- 客户敏感信息访问需要记录访问日志

### 2. 订单与生产流程规则

#### R2.1 订单状态流转规则
```
草稿 → 待审核 → 已确认 → 生产中 → 已完工 → 已发货 → 已收款
```
- 订单状态只能按照上述顺序正向流转
- 特殊情况下可以回退到上一状态（需要审批）
- 已发货订单不允许修改订单内容
- 订单取消需要特殊权限和审批流程

#### R2.2 BOM固化规则
- 销售BOM确认后自动生成工艺BOM
- 工艺BOM经工艺工程师审核后固化为生产BOM
- 生产BOM一旦下达生产订单后不允许修改
- BOM变更需要走正式的工程变更流程

#### R2.3 生产优先级规则
- 紧急订单优先级最高
- 交期相同的订单按下单时间排序
- 同一客户的订单可以合并排产
- 设备维护时间不能安排生产任务

### 3. 库存与物料管理规则

#### R3.1 库存安全规则
- 所有物料必须设置安全库存下限
- 库存低于安全库存时自动触发采购建议
- 危险品和贵重物料需要特殊存储管理
- 库存盘点必须定期进行（至少月度一次）

#### R3.2 出入库规则
- 所有出入库操作必须有对应的业务单据
- 出库必须遵循先进先出(FIFO)原则
- 特殊物料（如玻璃原片）需要按批次管理
- 出入库操作必须经过质量检验确认

#### R3.3 物料编码规则
```
物料编码格式：[类别码(2位)][材质码(2位)][规格码(4位)][流水号(4位)]
示例：GL01-1200-0001 (玻璃-普通-1200mm-流水号0001)
```

### 4. 质量管理规则

#### R4.1 三检制度
- IQC：所有采购物料必须经过来料检验
- IPQC：关键工序必须进行过程检验
- FQC：所有完工产品必须经过最终检验
- 不合格品必须隔离存放并标识

#### R4.2 质量追溯规则
- 所有产品必须建立完整的质量档案
- 质量问题必须能够追溯到具体批次和责任人
- 客户投诉必须在24小时内响应
- 质量改进措施必须形成闭环管理

### 5. 财务与成本规则

#### R5.1 业财一体化规则
- 所有业务操作必须实时触发财务记录
- 成本核算必须精确到产品级别
- 应收应付必须与业务单据保持一致
- 财务关账前必须完成所有业务数据核对

#### R5.2 成本核算规则
- 直接材料成本按实际消耗计算
- 人工成本按工时和计件相结合方式计算
- 制造费用按标准成本法分摊
- 外协成本按实际发生额核算

### 6. 玻璃深加工行业特殊规则

#### R6.1 玻璃产品特性规则
- 玻璃原片必须按供应商批次管理
- 钢化玻璃不能进行二次加工
- 夹层玻璃必须记录胶片批次信息
- 中空玻璃必须记录密封胶使用情况

#### R6.2 项目化生产规则
- 定制产品必须建立项目档案
- 项目变更必须客户书面确认
- 项目物料不能与库存物料混用
- 项目成本必须独立核算

#### R6.3 外协管理规则
- 外协订单必须明确技术要求
- 外协物料运输风险由外协方承担
- 外协产品必须经过质量检验
- 外协成本必须与产品成本关联

### 7. 系统集成规则

#### R7.1 数据一致性规则
- 主数据变更必须在5分钟内同步到所有子系统
- 跨系统业务操作必须保证事务一致性
- 数据冲突必须通过工作流程解决
- 系统故障时必须有数据恢复机制

#### R7.2 接口调用规则
- 所有系统间接口必须有超时控制
- 接口调用失败必须有重试机制
- 关键接口调用必须记录日志
- 接口性能必须满足业务要求

## 编码规范

### 功能模块编码
- **基础管理**: BMS-001 ~ BMS-009
- **工艺管理**: PDM-001 ~ PDM-008
- **销售管理**: SMS-001 ~ SMS-009
- **采购管理**: PMS-001 ~ PMS-012
- **生产管理**: MES-001 ~ MES-013
- **仓储管理**: WMS-001 ~ WMS-012
- **财务管理**: FMS-001 ~ FMS-010
- **质量管理**: QMS-001 ~ QMS-008
- **客户关系**: CRM-001 ~ CRM-008
- **人事管理**: HR-001 ~ HR-010
- **项目管理**: PJS-001 ~ PJS-009
- **数据中心**: DC-001 ~ DC-011

### 业务单据编码
- **订单编码**: SO + YYYYMM + 流水号6位
- **采购编码**: PO + YYYYMM + 流水号6位
- **生产编码**: MO + YYYYMM + 流水号6位
- **出入库编码**: WO + YYYYMM + 流水号6位
- **项目编码**: PJ + YYYY + 流水号4位