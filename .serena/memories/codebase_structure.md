# 代码库结构

## 目录组织
```
docs/
├── prd_v2.0/                    # 当前V2.0版本（主要工作目录）
│   ├── PRD_Readme_v2.0.md      # 系统集成总览
│   ├── _Glossary.md            # 全局术语表（所有文档必须引用）
│   ├── _Business_Rules.md       # 核心业务规则库（避免重复定义）
│   ├── Frontend_Design_Guidelines.md  # 前端设计规范
│   ├── TODOLIST.md             # 项目任务清单（已完成状态）
│   └── [12个子系统文件夹]/       # 各业务模块PRD文档
├── prd_v1.0/                   # 历史版本（参考用）
└── user-requirement.md         # 原始用户需求
```

## 12个核心子系统
**基础支撑层**: basic-management, pdm-system
**核心业务层**: sales-system, procurement-system, production-system, warehouse-system
**支持协同层**: finance-system, project-system, quality-system, crm-system, hr-system
**决策支持层**: data-center

## 关键文件说明
- `_Glossary.md`: 统一术语定义，所有文档必须严格遵循
- `_Business_Rules.md`: 跨系统业务规则，避免重复定义
- `PRD_Readme_v2.0.md`: 系统集成架构和端到端业务流程说明