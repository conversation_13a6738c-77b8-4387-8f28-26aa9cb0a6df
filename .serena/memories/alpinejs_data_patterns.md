# Alpine.js 数据管理模式 - 基础管理模块

## 核心数据结构模式

### 1. 应用状态管理结构
```javascript
function pageApp() {
    return {
        // ===== UI状态管理 =====
        loading: false,              // 全局加载状态
        pageLoading: false,          // 页面加载状态  
        showModal: false,            // 模态框显示状态
        showConfirmDialog: false,    // 确认对话框状态
        
        // ===== 表单数据 =====
        form: {
            // 表单字段数据
            username: '',
            password: '',
            email: '',
            // ...其他字段
        },
        
        // ===== 错误处理 =====
        errors: {},                  // 字段级错误信息
        generalError: '',            // 全局错误信息
        successMessage: '',          // 成功信息
        
        // ===== 列表数据管理 =====
        items: [],                   // 主要数据列表
        filteredItems: [],           // 过滤后的数据
        selectedItems: [],           // 选中的项目
        
        // ===== 搜索和筛选 =====
        searchQuery: '',             // 搜索关键词
        filterDepartment: '',        // 部门筛选
        filterStatus: '',            // 状态筛选
        
        // ===== 分页管理 =====
        currentPage: 1,              // 当前页码
        pageSize: 10,                // 每页数量
        totalItems: 0,               // 总数据量
        
        // ===== 编辑状态 =====
        editingItem: null,           // 当前编辑的项目
        isEditing: false,            // 是否在编辑模式
        
        // ===== 生命周期方法 =====
        init() {
            this.loadData();
            this.setupEventListeners();
        },
        
        // ===== 数据操作方法 =====
        async loadData() {
            this.loading = true;
            try {
                // 模拟API调用
                const response = await this.fetchData();
                this.items = response.data;
                this.totalItems = response.total;
            } catch (error) {
                this.generalError = '数据加载失败';
            } finally {
                this.loading = false;
            }
        },
        
        // ===== 表单验证方法 =====
        validateForm() {
            this.errors = {};
            let isValid = true;
            
            // 验证逻辑
            if (!this.form.username.trim()) {
                this.errors.username = '用户名不能为空';
                isValid = false;
            }
            
            return isValid;
        },
        
        // ===== 搜索和筛选方法 =====
        handleSearch() {
            this.applyFilters();
        },
        
        applyFilters() {
            let filtered = this.items;
            
            if (this.searchQuery) {
                filtered = filtered.filter(item => 
                    item.name.includes(this.searchQuery) ||
                    item.email.includes(this.searchQuery)
                );
            }
            
            if (this.filterDepartment) {
                filtered = filtered.filter(item => 
                    item.department === this.filterDepartment
                );
            }
            
            if (this.filterStatus) {
                filtered = filtered.filter(item => 
                    item.status === this.filterStatus
                );
            }
            
            this.filteredItems = filtered;
        }
    }
}
```

## 数据操作模式

### 1. CRUD操作模式
```javascript
// 创建操作
async createItem() {
    if (!this.validateForm()) return;
    
    this.loading = true;
    try {
        const response = await this.apiCreate(this.form);
        this.items.push(response.data);
        this.showModal = false;
        this.successMessage = '创建成功';
        this.resetForm();
    } catch (error) {
        this.generalError = error.message;
    } finally {
        this.loading = false;
    }
},

// 更新操作
async updateItem() {
    if (!this.validateForm()) return;
    
    this.loading = true;
    try {
        const response = await this.apiUpdate(this.editingItem.id, this.form);
        const index = this.items.findIndex(item => item.id === this.editingItem.id);
        this.items[index] = response.data;
        this.showModal = false;
        this.successMessage = '更新成功';
        this.resetForm();
    } catch (error) {
        this.generalError = error.message;
    } finally {
        this.loading = false;
    }
},

// 删除操作
async deleteItem(item) {
    if (!confirm('确定要删除该项目吗？')) return;
    
    this.loading = true;
    try {
        await this.apiDelete(item.id);
        const index = this.items.findIndex(i => i.id === item.id);
        this.items.splice(index, 1);
        this.successMessage = '删除成功';
    } catch (error) {
        this.generalError = error.message;
    } finally {
        this.loading = false;
    }
}
```

### 2. 表单管理模式
```javascript
// 重置表单
resetForm() {
    this.form = {
        username: '',
        password: '',
        email: '',
        department: '',
        status: 'active'
    };
    this.errors = {};
    this.generalError = '';
    this.successMessage = '';
},

// 填充表单（编辑时）
fillForm(item) {
    this.form = {
        username: item.username,
        email: item.email,
        department: item.department,
        status: item.status
        // 不填充密码字段
    };
    this.editingItem = item;
    this.isEditing = true;
},

// 表单提交处理
async submitForm() {
    this.generalError = '';
    this.successMessage = '';
    
    if (!this.validateForm()) {
        return;
    }
    
    if (this.isEditing) {
        await this.updateItem();
    } else {
        await this.createItem();
    }
}
```

### 3. 批量操作模式
```javascript
// 全选/取消全选
toggleSelectAll() {
    if (this.selectedItems.length === this.filteredItems.length) {
        this.selectedItems = [];
    } else {
        this.selectedItems = [...this.filteredItems];
    }
},

// 批量删除
async batchDelete() {
    if (this.selectedItems.length === 0) {
        alert('请选择要删除的项目');
        return;
    }
    
    if (!confirm(`确定要删除选中的 ${this.selectedItems.length} 个项目吗？`)) {
        return;
    }
    
    this.loading = true;
    try {
        const deletePromises = this.selectedItems.map(item => 
            this.apiDelete(item.id)
        );
        await Promise.all(deletePromises);
        
        // 从列表中移除已删除的项目
        this.selectedItems.forEach(selectedItem => {
            const index = this.items.findIndex(item => item.id === selectedItem.id);
            if (index > -1) {
                this.items.splice(index, 1);
            }
        });
        
        this.selectedItems = [];
        this.successMessage = '批量删除成功';
    } catch (error) {
        this.generalError = '批量删除失败';
    } finally {
        this.loading = false;
    }
}
```

## 计算属性模式

### 1. 数据统计计算
```javascript
// 获取过滤后的数据
get filteredItems() {
    let filtered = this.items;
    
    if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase();
        filtered = filtered.filter(item => 
            item.name.toLowerCase().includes(query) ||
            item.email.toLowerCase().includes(query) ||
            item.username.toLowerCase().includes(query)
        );
    }
    
    if (this.filterDepartment) {
        filtered = filtered.filter(item => 
            item.department === this.filterDepartment
        );
    }
    
    if (this.filterStatus) {
        filtered = filtered.filter(item => 
            item.status === this.filterStatus
        );
    }
    
    return filtered;
},

// 分页数据
get paginatedItems() {
    const start = (this.currentPage - 1) * this.pageSize;
    const end = start + this.pageSize;
    return this.filteredItems.slice(start, end);
},

// 总页数
get totalPages() {
    return Math.ceil(this.filteredItems.length / this.pageSize);
},

// 是否全选
get isAllSelected() {
    return this.selectedItems.length === this.filteredItems.length && 
           this.filteredItems.length > 0;
}
```

### 2. 状态判断计算
```javascript
// 获取状态文本
getStatusText(status) {
    const statusMap = {
        'active': '启用',
        'inactive': '禁用',
        'pending': '待激活'
    };
    return statusMap[status] || '未知';
},

// 获取状态样式
getStatusClass(status) {
    const classMap = {
        'active': 'bg-green-100 text-green-800',
        'inactive': 'bg-red-100 text-red-800',
        'pending': 'bg-yellow-100 text-yellow-800'
    };
    return classMap[status] || 'bg-gray-100 text-gray-800';
},

// 检查权限
hasPermission(action) {
    // 权限检查逻辑
    return this.userPermissions.includes(action);
}
```

## 事件处理模式

### 1. 防抖搜索
```javascript
searchTimeout: null,

handleSearch() {
    clearTimeout(this.searchTimeout);
    this.searchTimeout = setTimeout(() => {
        this.applyFilters();
        this.currentPage = 1; // 重置到第一页
    }, 300);
}
```

### 2. 键盘事件处理
```javascript
handleKeydown(event) {
    // ESC键关闭模态框
    if (event.key === 'Escape' && this.showModal) {
        this.showModal = false;
    }
    
    // Enter键提交表单
    if (event.key === 'Enter' && event.target.tagName !== 'TEXTAREA') {
        event.preventDefault();
        this.submitForm();
    }
}
```

### 3. 数据同步处理
```javascript
// 监听数据变化
$watch('searchQuery', () => {
    this.handleSearch();
}),

$watch('filterDepartment', () => {
    this.applyFilters();
    this.currentPage = 1;
}),

$watch('filterStatus', () => {
    this.applyFilters();
    this.currentPage = 1;
})
```