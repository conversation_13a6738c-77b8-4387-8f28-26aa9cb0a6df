# 技术架构

## 四层架构设计
```
决策支持层: 数据中心(BI)
    ↑
业务应用层: 核心业务系统 + 支持协同系统
    ↑
基础支撑层: PDM(工艺管理) + 基础管理(权限/组织)
    ↑
数据存储层: 统一数据库
```

## 系统集成模式
- **实时数据同步**: 关键业务数据<3秒响应时间
- **统一编码标准**: 订单、物料、批次、工序统一编码
- **业务-财务集成**: 业务操作自动触发财务记录
- **端到端流程**: 跨系统无缝业务流转

## 前端设计标准
- **24列栅格系统**: 响应式布局基础
- **专业制造业UI**: 任务导向，高效录入，防错设计
- **统一交互模式**: 一致的用户体验，降低学习成本

## 技术栈配置
- **文档语言**: Markdown
- **MCP服务器**: Context7 (文档模式), Playwright (测试), Sequential Thinking (分析)