# 功能模块规格说明书：报表设计器模块

- **模块ID**: DC-009
- **所属子系统**: 数据中心子系统(Data Center)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 数据分析师, **I want to** 使用可视化报表设计器, **so that** 快速创建各种业务报表和图表。
- **As a** 业务用户, **I want to** 自助设计报表, **so that** 满足个性化的数据分析需求。
- **As a** 报表管理员, **I want to** 管理报表模板, **so that** 提供标准化的报表设计和复用。
- **As a** 决策者, **I want to** 定制专属报表, **so that** 获得精准的业务洞察和决策支持。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 数据源已配置并可访问
- 用户具有报表设计权限
- 报表模板库已建立
- 图表组件库已准备

### 核心流程

#### 2.1 报表设计流程
1. 选择数据源和数据集
2. 设计报表布局和结构
3. 添加图表和数据组件
4. 配置数据绑定和计算
5. 设置样式和格式
6. 预览和测试报表
7. 保存和发布报表

#### 2.2 数据绑定流程
1. 连接数据源
2. 选择数据表和字段
3. 配置数据过滤条件
4. 设置数据聚合和计算
5. 建立参数和变量
6. 验证数据绑定

#### 2.3 报表发布流程
1. 验证报表完整性
2. 设置访问权限
3. 配置刷新策略
4. 发布到报表库
5. 通知相关用户
6. 监控报表使用

### 后置条件
- 报表已创建并保存
- 数据绑定已验证
- 权限已配置
- 报表已发布可用

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：报表设计器页面
### 页面目标：提供可视化的报表设计和创建功能

### 信息架构：
- **顶部工具栏**：包含 保存, 预览, 发布, 撤销重做, 帮助
- **左侧面板**：包含 组件库, 数据源, 图层管理
- **中央画布**：包含 设计区域, 网格对齐, 标尺
- **右侧面板**：包含 属性配置, 样式设置, 数据绑定

### 交互逻辑与状态：

#### **设计器工具栏**
- **文件操作：**
  - **新建报表：**
    - **空白报表：** 按钮，创建空白报表
    - **模板报表：** 下拉菜单，选择报表模板
    - **导入报表：** 按钮，导入外部报表文件
  - **保存操作：**
    - **保存：** 按钮，保存当前报表
    - **另存为：** 按钮，另存为新报表
    - **自动保存：** 开关，启用自动保存功能
- **编辑操作：**
  - **撤销重做：**
    - **撤销：** 按钮，撤销上一步操作
    - **重做：** 按钮，重做已撤销的操作
    - **历史记录：** 下拉菜单，查看操作历史
  - **复制粘贴：**
    - **复制：** 按钮，复制选中的组件
    - **粘贴：** 按钮，粘贴复制的组件
    - **剪切：** 按钮，剪切选中的组件
- **视图操作：**
  - **缩放控制：**
    - **放大：** 按钮，放大设计画布
    - **缩小：** 按钮，缩小设计画布
    - **适应窗口：** 按钮，自适应窗口大小
    - **实际大小：** 按钮，显示实际大小
  - **预览模式：**
    - **设计预览：** 按钮，预览报表设计效果
    - **数据预览：** 按钮，预览报表数据效果
    - **移动预览：** 按钮，预览移动端效果

#### **组件库面板**
- **基础组件：**
  - **文本组件：**
    - **标题：** 拖拽添加标题文本
    - **正文：** 拖拽添加正文文本
    - **标签：** 拖拽添加标签文本
    - **超链接：** 拖拽添加超链接
  - **图片组件：**
    - **静态图片：** 拖拽添加静态图片
    - **动态图片：** 拖拽添加动态图片
    - **图标：** 拖拽添加图标
- **数据组件：**
  - **表格组件：**
    - **数据表格：** 拖拽添加数据表格
    - **交叉表：** 拖拽添加交叉表
    - **分组表：** 拖拽添加分组表
  - **图表组件：**
    - **柱状图：** 拖拽添加柱状图
    - **折线图：** 拖拽添加折线图
    - **饼图：** 拖拽添加饼图
    - **散点图：** 拖拽添加散点图
    - **仪表盘：** 拖拽添加仪表盘
    - **地图：** 拖拽添加地图组件
- **容器组件：**
  - **面板：** 拖拽添加面板容器
  - **选项卡：** 拖拽添加选项卡容器
  - **分组框：** 拖拽添加分组框
- **交互组件：**
  - **参数控件：**
    - **下拉框：** 拖拽添加下拉选择框
    - **日期选择：** 拖拽添加日期选择器
    - **文本输入：** 拖拽添加文本输入框
    - **复选框：** 拖拽添加复选框
    - **单选框：** 拖拽添加单选框

#### **设计画布区域**
- **画布操作：**
  - **网格系统：**
    - **网格显示：** 开关，显示/隐藏网格线
    - **网格对齐：** 开关，启用网格对齐
    - **网格大小：** 滑块，调整网格大小
  - **标尺系统：**
    - **标尺显示：** 开关，显示/隐藏标尺
    - **参考线：** 拖拽创建参考线
    - **单位设置：** 下拉选择，像素/厘米/英寸
- **组件操作：**
  - **选择操作：**
    - **单选：** 点击选择单个组件
    - **多选：** Ctrl+点击选择多个组件
    - **框选：** 拖拽框选多个组件
    - **全选：** Ctrl+A全选所有组件
  - **移动操作：**
    - **拖拽移动：** 拖拽移动组件位置
    - **键盘移动：** 方向键微调位置
    - **对齐操作：** 多组件对齐功能
  - **缩放操作：**
    - **拖拽缩放：** 拖拽控制点缩放组件
    - **等比缩放：** Shift+拖拽等比缩放
    - **数值缩放：** 输入精确尺寸数值
- **层级管理：**
  - **图层操作：**
    - **置顶：** 将组件置于最顶层
    - **置底：** 将组件置于最底层
    - **上移一层：** 向上移动一个层级
    - **下移一层：** 向下移动一个层级
  - **分组操作：**
    - **组合：** 将多个组件组合
    - **取消组合：** 取消组件组合
    - **进入组合：** 进入组合编辑模式

#### **数据源面板**
- **数据源管理：**
  - **数据源列表：**
    - **数据库连接：** 显示已配置的数据库连接
    - **文件数据源：** 显示Excel、CSV等文件数据源
    - **API数据源：** 显示REST API数据源
    - **连接状态：** 显示数据源连接状态
  - **数据源操作：**
    - **添加数据源：** 按钮，添加新的数据源
    - **编辑数据源：** 按钮，编辑数据源配置
    - **测试连接：** 按钮，测试数据源连接
    - **刷新数据：** 按钮，刷新数据源数据
- **数据集管理：**
  - **数据集列表：**
    - **表名：** 显示数据表名称
    - **字段列表：** 展开显示表字段
    - **字段类型：** 显示字段数据类型
    - **字段描述：** 显示字段业务描述
  - **数据集操作：**
    - **预览数据：** 按钮，预览数据集内容
    - **字段拖拽：** 拖拽字段到组件绑定数据
    - **SQL编辑：** 按钮，编辑自定义SQL查询
    - **数据过滤：** 按钮，设置数据过滤条件

#### **属性配置面板**
- **组件属性：**
  - **基本属性：**
    - **组件名称：** 输入框，设置组件名称
    - **组件ID：** 显示组件唯一标识
    - **组件类型：** 显示组件类型
    - **可见性：** 复选框，设置组件可见性
  - **位置尺寸：**
    - **X坐标：** 数字输入框，设置X坐标
    - **Y坐标：** 数字输入框，设置Y坐标
    - **宽度：** 数字输入框，设置组件宽度
    - **高度：** 数字输入框，设置组件高度
    - **锁定比例：** 复选框，锁定宽高比例
- **样式属性：**
  - **字体样式：**
    - **字体族：** 下拉选择，选择字体
    - **字体大小：** 数字输入框，设置字体大小
    - **字体颜色：** 颜色选择器，设置字体颜色
    - **字体样式：** 复选框，粗体/斜体/下划线
  - **背景样式：**
    - **背景颜色：** 颜色选择器，设置背景颜色
    - **背景图片：** 文件选择器，设置背景图片
    - **背景模式：** 下拉选择，平铺/拉伸/居中
  - **边框样式：**
    - **边框宽度：** 数字输入框，设置边框宽度
    - **边框颜色：** 颜色选择器，设置边框颜色
    - **边框样式：** 下拉选择，实线/虚线/点线
    - **圆角半径：** 数字输入框，设置圆角半径

#### **数据绑定配置**
- **数据绑定：**
  - **数据源绑定：**
    - **选择数据集：** 下拉选择，选择数据集
    - **字段映射：** 表格，配置字段映射关系
    - **数据过滤：** 按钮，设置数据过滤条件
    - **数据排序：** 按钮，设置数据排序规则
  - **参数绑定：**
    - **参数列表：** 显示可用参数列表
    - **参数类型：** 下拉选择，字符串/数字/日期/布尔
    - **默认值：** 输入框，设置参数默认值
    - **参数验证：** 输入框，设置参数验证规则
- **计算字段：**
  - **表达式编辑：**
    - **表达式输入：** 文本域，输入计算表达式
    - **函数库：** 列表，显示可用函数
    - **字段引用：** 列表，显示可引用字段
    - **语法检查：** 按钮，检查表达式语法
  - **聚合计算：**
    - **聚合函数：** 下拉选择，SUM/AVG/COUNT/MAX/MIN
    - **分组字段：** 多选框，选择分组字段
    - **过滤条件：** 输入框，设置聚合过滤条件

#### **图表配置专区**
- **图表数据：**
  - **数据系列：**
    - **系列名称：** 输入框，设置系列名称
    - **数据字段：** 下拉选择，选择数据字段
    - **聚合方式：** 下拉选择，选择聚合方式
    - **系列颜色：** 颜色选择器，设置系列颜色
  - **坐标轴：**
    - **X轴字段：** 下拉选择，选择X轴字段
    - **Y轴字段：** 下拉选择，选择Y轴字段
    - **轴标题：** 输入框，设置轴标题
    - **轴范围：** 输入框，设置轴数值范围
- **图表样式：**
  - **图表标题：**
    - **标题文本：** 输入框，设置图表标题
    - **标题位置：** 下拉选择，顶部/底部/左侧/右侧
    - **标题样式：** 字体、颜色、大小设置
  - **图例设置：**
    - **显示图例：** 复选框，是否显示图例
    - **图例位置：** 下拉选择，图例位置
    - **图例样式：** 字体、颜色设置
  - **网格线：**
    - **显示网格：** 复选框，是否显示网格线
    - **网格样式：** 线条样式、颜色设置

### 数据校验规则：

#### **组件名称**
- **校验规则：** 必填，2-50个字符，不能重复
- **错误提示文案：** "组件名称为必填项，长度2-50个字符，不能重复"

#### **数据表达式**
- **校验规则：** 必须是有效的表达式语法
- **错误提示文案：** "表达式语法错误，请检查表达式"

#### **参数设置**
- **校验规则：** 参数名称不能重复，默认值类型必须匹配
- **错误提示文案：** "参数配置错误，请检查参数设置"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **报表定义**:
  - **报表名称 (report_name)**: String, 必填, 2-100字符
  - **报表描述 (description)**: String, 可选, 最大500字符
  - **报表类型 (report_type)**: String, 必填, table/chart/dashboard
  - **数据源ID (datasource_id)**: String, 必填, 有效的数据源标识
- **组件配置**:
  - **组件列表 (components)**: Array, 必填, 组件配置列表
  - **布局信息 (layout)**: Object, 必填, 页面布局信息
  - **样式配置 (styles)**: Object, 可选, 样式配置信息

### 展示数据
- **报表列表**: 已创建的报表列表和基本信息
- **组件库**: 可用的报表组件和模板
- **数据源**: 可用的数据源和数据集信息
- **预览数据**: 报表预览和数据展示

### 空状态/零数据
- **无报表**: 显示"暂无报表，请点击新建报表开始"
- **无数据源**: 显示"暂无可用数据源，请先配置数据源"
- **无数据**: 显示"暂无数据，请检查数据源连接"

### API接口
- **报表管理**: GET/POST/PUT/DELETE /api/datacenter/reports
- **组件库**: GET /api/datacenter/report-components
- **数据源**: GET /api/datacenter/datasources
- **报表预览**: POST /api/datacenter/reports/preview
- **报表发布**: POST /api/datacenter/reports/publish

## 5. 异常与边界处理 (Error & Edge Cases)

### **数据源连接失败**
- **提示信息**: "数据源连接失败，请检查连接配置"
- **用户操作**: 提供重新连接和数据源配置选项

### **组件渲染失败**
- **提示信息**: "组件渲染失败，请检查组件配置"
- **用户操作**: 提供组件重置和配置检查选项

### **表达式计算错误**
- **提示信息**: "表达式计算错误，请检查表达式语法"
- **用户操作**: 提供表达式验证和语法帮助

### **报表保存失败**
- **提示信息**: "报表保存失败，请稍后重试"
- **用户操作**: 提供重新保存和本地备份选项

### **大数据量处理**
- **提示信息**: "数据量较大，加载可能需要更长时间"
- **用户操作**: 提供数据分页和筛选建议

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持拖拽式可视化报表设计
- [ ] 提供丰富的组件库和图表类型
- [ ] 支持多种数据源的连接和绑定
- [ ] 完善的样式配置和主题设置
- [ ] 支持复杂的数据计算和表达式
- [ ] 实时预览和多端适配功能
- [ ] 报表模板管理和复用机制
- [ ] 设计器响应时间<2秒，操作流畅
- [ ] 支持大型报表的设计和渲染
- [ ] 所有页面元素符合全局设计规范
