# 功能模块规格说明书：数据源连接管理模块

- **模块ID**: DC-001
- **所属子系统**: 数据中心子系统(Data Center)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 数据工程师, **I want to** 配置各业务系统的数据源连接, **so that** 统一采集所有业务数据进行分析。
- **As a** 系统管理员, **I want to** 监控数据源连接状态, **so that** 及时发现和处理连接异常问题。
- **As a** 数据工程师, **I want to** 管理数据源访问权限, **so that** 确保数据安全和合规访问。
- **As a** 运维人员, **I want to** 查看连接日志和性能指标, **so that** 优化数据采集性能和稳定性。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有数据源管理权限
- 目标业务系统已部署并可访问
- 数据库连接信息已获取
- 网络连通性已确认

### 核心流程

#### 2.1 数据源添加流程
1. 用户选择数据源类型（MySQL、PostgreSQL、Oracle等）
2. 填写连接配置信息（主机、端口、数据库名、用户名、密码）
3. 系统验证连接配置的有效性
4. 测试数据源连接是否成功
5. 配置数据源权限和访问控制
6. 保存数据源配置并生成唯一标识
7. 记录操作日志和审计信息

#### 2.2 连接状态监控流程
1. 系统定时检测所有数据源连接状态
2. 记录连接响应时间和可用性指标
3. 检测到连接异常时触发告警
4. 自动重试连接并记录重试结果
5. 更新连接状态和健康度评分
6. 生成连接状态报告和趋势分析

#### 2.3 数据源权限管理流程
1. 管理员配置数据源访问权限
2. 设置用户或角色的数据源访问范围
3. 配置数据表和字段级别的访问控制
4. 审核和批准权限申请
5. 定期审查和更新权限配置
6. 记录权限变更日志

### 后置条件
- 数据源连接配置已保存
- 连接状态监控已启动
- 权限配置已生效
- 操作日志已记录

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：数据源连接管理页面
### 页面目标：提供数据源的添加、配置、监控和管理功能

### 信息架构：
- **顶部区域**：包含 页面标题, 添加数据源, 批量操作, 刷新状态
- **左侧区域**：包含 数据源分类, 状态筛选, 快速搜索
- **中间区域**：包含 数据源列表, 连接详情, 配置表单
- **右侧区域**：包含 连接监控, 性能指标, 操作日志

### 交互逻辑与状态：

#### **数据源列表管理**
- **数据源列表：**
  - **列表项信息：**
    - **数据源名称：** 显示数据源的名称和描述
    - **数据源类型：** 显示数据库类型图标和名称
    - **连接状态：** 状态指示器，正常/异常/未知
    - **最后更新：** 显示最后连接测试时间
    - **操作按钮：** 编辑、测试连接、删除、查看详情
  - **状态指示器：**
    - **正常状态：** 绿色圆点，连接正常
    - **异常状态：** 红色圆点，连接失败
    - **测试中状态：** 黄色圆点，正在测试连接
    - **未知状态：** 灰色圆点，未进行连接测试
- **批量操作：**
  - **批量测试：** 按钮，批量测试选中数据源的连接
  - **批量删除：** 按钮，批量删除选中的数据源
  - **导出配置：** 按钮，导出数据源配置信息
  - **导入配置：** 按钮，批量导入数据源配置

#### **数据源配置表单**
- **基本信息配置：**
  - **数据源名称：**
    - **默认状态：** 文本输入框，白色背景
    - **校验规则：** 必填，2-50个字符，不能重复
    - **错误状态：** 红色边框，显示错误提示
  - **数据源类型：**
    - **默认状态：** 下拉选择框，显示支持的数据库类型
    - **选项：** MySQL、PostgreSQL、Oracle、SQL Server、MongoDB
    - **交互行为：** 选择后显示对应的连接参数模板
- **连接参数配置：**
  - **主机地址：**
    - **默认状态：** 文本输入框，支持IP地址或域名
    - **校验规则：** 必填，有效的IP地址或域名格式
    - **提示信息：** 占位符显示"请输入主机IP或域名"
  - **端口号：**
    - **默认状态：** 数字输入框，根据数据库类型自动填充默认端口
    - **校验规则：** 必填，1-65535范围内的数字
    - **自动填充：** MySQL默认3306，PostgreSQL默认5432
  - **数据库名：**
    - **默认状态：** 文本输入框
    - **校验规则：** 必填，符合数据库命名规范
  - **用户名：**
    - **默认状态：** 文本输入框
    - **校验规则：** 必填，不能包含特殊字符
  - **密码：**
    - **默认状态：** 密码输入框，支持显示/隐藏切换
    - **校验规则：** 必填，支持特殊字符
    - **安全处理：** 加密存储，页面不显示明文

#### **连接测试功能**
- **测试连接按钮：**
  - **默认状态：** 蓝色背景，白色文字，"测试连接"
  - **交互行为：** 点击后发起连接测试请求
  - **测试中状态：** 灰色背景，显示加载动画，"测试中..."
  - **成功状态：** 绿色背景，显示成功图标，"连接成功"
  - **失败状态：** 红色背景，显示错误图标，"连接失败"
- **测试结果显示：**
  - **成功结果：**
    - **状态信息：** 绿色提示框，"连接测试成功"
    - **详细信息：** 显示连接响应时间、数据库版本等
  - **失败结果：**
    - **状态信息：** 红色提示框，"连接测试失败"
    - **错误详情：** 显示具体的错误信息和解决建议

#### **连接状态监控**
- **实时状态监控：**
  - **状态概览：**
    - **总数统计：** 显示数据源总数
    - **正常数量：** 显示连接正常的数据源数量
    - **异常数量：** 显示连接异常的数据源数量
    - **成功率：** 显示整体连接成功率
  - **状态图表：**
    - **状态分布：** 饼图显示各状态数据源的分布
    - **趋势图表：** 折线图显示连接成功率的时间趋势
- **性能监控：**
  - **响应时间：**
    - **平均响应时间：** 显示所有数据源的平均响应时间
    - **响应时间分布：** 柱状图显示响应时间分布
  - **连接池状态：**
    - **活跃连接数：** 显示当前活跃的连接数
    - **空闲连接数：** 显示空闲的连接数
    - **连接池使用率：** 显示连接池的使用率

#### **权限管理功能**
- **权限配置：**
  - **访问权限：**
    - **用户权限：** 多选框，选择可访问该数据源的用户
    - **角色权限：** 多选框，选择可访问该数据源的角色
    - **权限级别：** 单选按钮，只读/读写/管理员
  - **数据权限：**
    - **表级权限：** 树形选择器，选择可访问的数据表
    - **字段级权限：** 复选框列表，选择可访问的字段
    - **行级权限：** 文本输入框，配置行级过滤条件
- **权限审核：**
  - **权限申请：**
    - **申请列表：** 显示待审核的权限申请
    - **申请详情：** 显示申请的具体权限内容
    - **审核操作：** 批准/拒绝按钮，审核意见输入框

#### **操作日志查看**
- **日志列表：**
  - **日志信息：**
    - **操作时间：** 显示操作的具体时间
    - **操作用户：** 显示执行操作的用户
    - **操作类型：** 显示操作类型（添加/修改/删除/测试）
    - **操作结果：** 显示操作的结果状态
    - **详细信息：** 链接，查看操作的详细信息
  - **日志筛选：**
    - **时间范围：** 日期范围选择器
    - **操作类型：** 下拉多选框
    - **操作用户：** 用户选择器
    - **操作结果：** 单选按钮，成功/失败/全部

### 数据校验规则：

#### **数据源名称**
- **校验规则：** 必填，2-50个字符，不能重复，不能包含特殊字符
- **错误提示文案：** "数据源名称为必填项，长度2-50个字符，不能重复"

#### **主机地址**
- **校验规则：** 必填，有效的IP地址或域名格式
- **错误提示文案：** "请输入有效的IP地址或域名"

#### **端口号**
- **校验规则：** 必填，1-65535范围内的整数
- **错误提示文案：** "端口号必须是1-65535范围内的数字"

#### **用户名**
- **校验规则：** 必填，不能包含特殊字符，长度1-50个字符
- **错误提示文案：** "用户名为必填项，不能包含特殊字符"

#### **密码**
- **校验规则：** 必填，长度6-100个字符
- **错误提示文案：** "密码为必填项，长度6-100个字符"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **数据源配置信息**:
  - **数据源名称 (datasource_name)**: String, 必填, 2-50字符
  - **数据源类型 (datasource_type)**: String, 必填, MySQL/PostgreSQL/Oracle等
  - **主机地址 (host)**: String, 必填, IP地址或域名
  - **端口号 (port)**: Integer, 必填, 1-65535
  - **数据库名 (database_name)**: String, 必填, 符合数据库命名规范
  - **用户名 (username)**: String, 必填, 1-50字符
  - **密码 (password)**: String, 必填, 6-100字符，加密存储
- **权限配置信息**:
  - **访问用户 (access_users)**: Array, 可选, 用户ID列表
  - **访问角色 (access_roles)**: Array, 可选, 角色ID列表
  - **权限级别 (permission_level)**: String, 必填, readonly/readwrite/admin

### 展示数据
- **数据源列表**: 数据源基本信息和连接状态
- **连接状态**: 实时连接状态和性能指标
- **操作日志**: 数据源操作的历史记录
- **权限信息**: 数据源的权限配置详情

### 空状态/零数据
- **无数据源**: 显示"暂无数据源，请点击添加数据源按钮创建"
- **无日志记录**: 显示"暂无操作日志记录"
- **连接测试失败**: 显示"连接测试失败，请检查配置信息"

### API接口
- **数据源管理**: GET/POST/PUT/DELETE /api/datacenter/datasources
- **连接测试**: POST /api/datacenter/datasources/test
- **状态监控**: GET /api/datacenter/datasources/status
- **权限管理**: GET/POST/PUT /api/datacenter/datasources/permissions
- **操作日志**: GET /api/datacenter/datasources/logs

## 5. 异常与边界处理 (Error & Edge Cases)

### **连接测试失败**
- **提示信息**: "数据源连接测试失败，请检查网络连接和配置信息"
- **用户操作**: 显示具体错误信息，提供重新测试和配置修改选项

### **数据源名称重复**
- **提示信息**: "数据源名称已存在，请使用其他名称"
- **用户操作**: 高亮显示重复的名称，提供名称建议

### **网络连接超时**
- **提示信息**: "网络连接超时，请检查网络状况或稍后重试"
- **用户操作**: 提供重试按钮和网络诊断建议

### **权限不足**
- **提示信息**: "您没有权限执行此操作，请联系管理员"
- **用户操作**: 显示权限要求和申请流程

### **数据源正在使用**
- **提示信息**: "该数据源正在被ETL任务使用，无法删除"
- **用户操作**: 显示使用该数据源的任务列表，提供停止任务选项

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持MySQL、PostgreSQL、Oracle、SQL Server等主流数据库连接
- [ ] 数据源连接测试成功率≥99%，响应时间<3秒
- [ ] 支持数据源的增删改查和批量操作
- [ ] 连接状态实时监控，异常自动告警
- [ ] 完善的权限管理，支持用户和角色级别的访问控制
- [ ] 详细的操作日志记录，支持审计追溯
- [ ] 连接信息加密存储，确保数据安全
- [ ] 支持连接池管理，优化连接性能
- [ ] 所有页面元素符合全局设计规范
- [ ] 响应式设计，支持PC和移动端访问
