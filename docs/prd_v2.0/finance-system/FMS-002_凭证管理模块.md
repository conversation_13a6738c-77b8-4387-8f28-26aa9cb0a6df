# 功能模块规格说明书：凭证管理模块

- **模块ID**: FMS-002
- **所属子系统**: 财务管理子系统(FMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 财务人员, **I want to** 快速录入记账凭证, **so that** 及时准确地记录企业的经济业务。
- **As a** 财务主管, **I want to** 审核财务凭证, **so that** 确保凭证的准确性和合规性。
- **As a** 系统用户, **I want to** 业务单据自动生成凭证, **so that** 实现业财一体化和减少手工录入。
- **As a** 财务人员, **I want to** 使用凭证模板, **so that** 提高重复业务的录入效率。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 会计科目已设置完成
- 用户具有凭证操作权限
- 会计期间已开启
- 相关业务单据已审核

### 核心流程

#### 2.1 手工凭证录入流程
1. 选择凭证类型和日期
2. 录入凭证摘要
3. 选择会计科目和输入金额
4. 系统自动校验借贷平衡
5. 保存凭证草稿或直接提交审核
6. 凭证审核通过后过账生效

#### 2.2 自动凭证生成流程
1. 业务系统产生业务单据（销售出库、采购入库等）
2. 系统根据预设的凭证模板匹配业务类型
3. 自动提取业务单据关键信息
4. 按模板规则生成凭证分录
5. 凭证进入待审核状态
6. 财务人员审核确认后过账

#### 2.3 凭证审核流程
1. 查询待审核凭证列表
2. 逐张检查凭证内容和附件
3. 验证科目使用和金额准确性
4. 审核通过或退回修改
5. 审核通过的凭证自动过账
6. 更新科目余额和相关报表

#### 2.4 凭证修改流程
1. 查询需要修改的凭证
2. 检查凭证状态（只能修改未审核凭证）
3. 进入编辑模式修改凭证内容
4. 重新校验借贷平衡
5. 保存修改并重新提交审核

#### 2.5 凭证反过账流程
1. 选择需要反过账的凭证
2. 检查凭证是否允许反过账
3. 确认反过账的影响范围
4. 执行反过账操作
5. 恢复科目余额到反过账前状态
6. 凭证状态变更为已审核未过账

### 后置条件
- 凭证数据准确保存
- 科目余额正确更新
- 凭证状态准确变更
- 审计日志完整记录

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：凭证管理页面
### 页面目标：提供完整的凭证录入、审核、查询功能

### 信息架构：
- **顶部区域**：包含 凭证查询, 新增凭证, 批量操作
- **中间区域**：包含 凭证列表, 凭证详情, 分录明细
- **底部区域**：包含 操作按钮, 审核意见, 附件管理

### 交互逻辑与状态：

#### **凭证查询区域**
- **基础查询：**
  - **凭证号：** 输入框，支持模糊搜索
  - **凭证日期：** 日期范围选择器
  - **凭证状态：** 下拉多选，草稿/待审核/已审核/已过账
  - **制单人：** 下拉选择，显示所有制单人
- **高级查询：**
  - **凭证类型：** 下拉选择，收款/付款/转账/自动生成
  - **科目范围：** 科目选择器，查询包含指定科目的凭证
  - **金额范围：** 数字输入框，设置金额区间
  - **摘要关键词：** 输入框，摘要内容模糊搜索

#### **凭证列表区域**
- **列表表头：**
  - **凭证号：** 可排序，点击查看凭证详情
  - **凭证日期：** 可排序，显示YYYY-MM-DD格式
  - **凭证类型：** 显示图标和文字
  - **摘要：** 显示主要摘要，过长时省略
  - **借方金额：** 右对齐，千分位格式
  - **贷方金额：** 右对齐，千分位格式
  - **制单人：** 显示制单人姓名
  - **状态：** 状态标签，不同颜色区分
  - **操作：** 查看、编辑、审核等操作
- **状态标识：**
  - **草稿：** 灰色标签，"草稿"
  - **待审核：** 橙色标签，"待审核"
  - **已审核：** 蓝色标签，"已审核"
  - **已过账：** 绿色标签，"已过账"
  - **已冲销：** 红色标签，"已冲销"

#### **凭证录入界面**
- **凭证头信息：**
  - **凭证类型：** 下拉选择，收款凭证/付款凭证/转账凭证
  - **凭证日期：** 日期选择器，默认当前日期
  - **凭证号：** 自动生成，可手动修改
  - **附件张数：** 数字输入框，记录附件数量
- **分录录入区域：**
  - **序号：** 自动编号，1、2、3...
  - **摘要：** 输入框，描述经济业务内容
  - **科目：** 科目选择器，支持搜索和树形选择
  - **借方金额：** 数字输入框，自动千分位格式化
  - **贷方金额：** 数字输入框，自动千分位格式化
  - **操作：** 插入行、删除行、复制行
- **合计信息：**
  - **借方合计：** 自动计算，红色字体显示
  - **贷方合计：** 自动计算，红色字体显示
  - **差额：** 自动计算，不平衡时红色警告

#### **凭证模板选择**
- **模板列表：**
  - **模板名称：** 显示模板名称和描述
  - **适用业务：** 显示适用的业务类型
  - **使用频次：** 显示模板使用次数
  - **最后使用：** 显示最后使用时间
- **模板应用：**
  - **选择模板：** 单选按钮选择模板
  - **预览模板：** 显示模板的科目和分录结构
  - **应用模板：** 将模板内容填入凭证录入界面
  - **修改模板：** 基于当前凭证保存为新模板

#### **操作按钮组**
- **保存草稿按钮：**
  - **默认状态：** 灰色边框，"保存草稿"
  - **点击效果：** 保存凭证为草稿状态，可后续修改
  - **禁用条件：** 必填字段未填写时禁用
- **提交审核按钮：**
  - **默认状态：** 蓝色背景(#1890FF)，"提交审核"
  - **点击效果：** 校验通过后提交审核，状态变为待审核
  - **禁用条件：** 借贷不平衡或必填字段未填写时禁用
- **审核通过按钮：**
  - **默认状态：** 绿色背景(#52C41A)，"审核通过"
  - **点击效果：** 凭证审核通过并自动过账
  - **显示条件：** 只有审核人员且凭证状态为待审核时显示
- **退回修改按钮：**
  - **默认状态：** 橙色边框，"退回修改"
  - **点击效果：** 退回凭证给制单人修改，需填写退回原因
  - **显示条件：** 只有审核人员且凭证状态为待审核时显示

#### **自动生成凭证设置**
- **业务类型配置：**
  - **销售出库：** 配置销售收入和应收账款科目
  - **采购入库：** 配置采购成本和应付账款科目
  - **生产领料：** 配置生产成本和原材料科目
  - **工资计提：** 配置工资费用和应付工资科目
- **科目映射：**
  - **借方科目：** 设置默认借方科目
  - **贷方科目：** 设置默认贷方科目
  - **辅助核算：** 设置客户、供应商等辅助核算
  - **摘要模板：** 设置自动生成的摘要格式

#### **凭证审核界面**
- **审核信息：**
  - **制单人：** 显示凭证制单人信息
  - **制单时间：** 显示凭证制单时间
  - **业务来源：** 显示凭证来源（手工录入/自动生成）
  - **关联单据：** 显示关联的业务单据链接
- **审核操作：**
  - **审核意见：** 文本框，填写审核意见
  - **审核结果：** 单选按钮，通过/退回
  - **退回原因：** 下拉选择常见退回原因
  - **审核签名：** 电子签名或密码确认

#### **凭证查看界面**
- **凭证预览：**
  - **标准格式：** 按会计凭证标准格式显示
  - **分录明细：** 表格形式显示所有分录
  - **合计信息：** 显示借贷方合计和平衡状态
  - **审核信息：** 显示制单、审核、过账等操作记录
- **操作历史：**
  - **操作时间：** 显示各操作的时间
  - **操作人员：** 显示操作人员姓名
  - **操作类型：** 显示操作类型（制单/审核/过账等）
  - **操作说明：** 显示操作的详细说明

### 数据校验规则：

#### **借贷平衡**
- **校验规则：** 借方金额合计必须等于贷方金额合计
- **错误提示文案：** "借贷不平衡，借方合计￥X，贷方合计￥Y"

#### **科目有效性**
- **校验规则：** 选择的科目必须是启用状态的末级科目
- **错误提示文案：** "所选科目已停用或不是末级科目"

#### **金额有效性**
- **校验规则：** 金额必须大于0，小数位不超过2位
- **错误提示文案：** "金额必须大于0且小数位不超过2位"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **凭证头信息**:
  - **凭证号 (voucher_no)**: String, 必填, 唯一标识
  - **凭证日期 (voucher_date)**: Date, 必填, 会计日期
  - **凭证类型 (voucher_type)**: Enum, 必填, 收款/付款/转账
  - **摘要 (summary)**: String, 可选, 业务描述
- **凭证分录**:
  - **科目编码 (account_code)**: String, 必填, 引用科目
  - **摘要 (summary)**: String, 必填, 分录说明
  - **借方金额 (debit_amount)**: Decimal, 可选, 借方金额
  - **贷方金额 (credit_amount)**: Decimal, 可选, 贷方金额

### 展示数据
- **凭证列表**: 凭证号、日期、类型、摘要、金额、状态
- **凭证详情**: 完整的凭证头和分录信息
- **审核记录**: 制单、审核、过账等操作历史
- **关联单据**: 相关业务单据的链接信息

### 空状态/零数据
- **无凭证数据**: 显示"暂无凭证数据，请新增凭证"
- **无搜索结果**: 显示"未找到符合条件的凭证"
- **无分录**: 显示"请添加凭证分录"

### API接口
- **凭证查询**: GET /api/vouchers
- **凭证新增**: POST /api/vouchers
- **凭证修改**: PUT /api/vouchers/{id}
- **凭证审核**: POST /api/vouchers/{id}/audit
- **凭证过账**: POST /api/vouchers/{id}/post

## 5. 异常与边界处理 (Error & Edge Cases)

### **借贷不平衡**
- **提示信息**: "凭证借贷不平衡，请检查分录金额"
- **用户操作**: 高亮显示不平衡金额，提供自动平衡建议

### **科目已停用**
- **提示信息**: "所选科目已停用，请重新选择"
- **用户操作**: 自动清除已停用科目，提供替代科目建议

### **会计期间已关闭**
- **提示信息**: "该会计期间已关闭，无法录入凭证"
- **用户操作**: 显示可用期间，提供期间调整选项

### **权限不足**
- **提示信息**: "您没有权限执行此操作"
- **用户操作**: 显示所需权限和申请流程

### **数据保存失败**
- **提示信息**: "凭证保存失败，请检查网络连接"
- **用户操作**: 提供重试按钮和本地缓存恢复

## 6. 验收标准 (Acceptance Criteria)

- [ ] 凭证录入界面友好，支持快速录入
- [ ] 借贷平衡校验，不平衡不允许保存
- [ ] 审核过账流程清晰，权限控制严格
- [ ] 自动生成凭证准确率100%
- [ ] 支持凭证模板功能，提高录入效率
- [ ] 凭证查询功能完善，支持多维度筛选
- [ ] 凭证修改和反过账功能安全可控
- [ ] 所有操作记录完整，支持审计追踪
- [ ] 界面响应速度快，操作体验流畅
- [ ] 所有页面元素符合全局设计规范
- [ ] 凭证处理效率提升≥80%
- [ ] 数据准确率≥99.9%
