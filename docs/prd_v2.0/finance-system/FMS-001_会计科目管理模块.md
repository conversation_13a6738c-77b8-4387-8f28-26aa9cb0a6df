# 功能模块规格说明书：会计科目管理模块

- **模块ID**: FMS-001
- **所属子系统**: 财务管理子系统(FMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 财务主管, **I want to** 建立和维护标准的会计科目体系, **so that** 规范企业的财务核算和报表编制。
- **As a** 财务人员, **I want to** 配置科目属性和权限, **so that** 确保科目使用的准确性和安全性。
- **As a** 系统管理员, **I want to** 管理科目的启用停用状态, **so that** 灵活控制科目的使用范围。
- **As a** 财务主管, **I want to** 查询科目余额和发生额, **so that** 实时了解各科目的财务状况。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有科目管理权限
- 系统已完成基础配置
- 会计期间已设置

### 核心流程

#### 2.1 科目新增流程
1. 选择上级科目（如果是下级科目）
2. 输入科目编码和名称
3. 设置科目属性（借贷方向、科目性质等）
4. 配置科目权限和使用范围
5. 保存并验证科目信息
6. 科目生效，可用于凭证录入

#### 2.2 科目修改流程
1. 查询需要修改的科目
2. 检查科目是否已被使用
3. 修改允许的科目属性
4. 保存修改并记录变更日志
5. 通知相关用户科目变更

#### 2.3 科目停用流程
1. 检查科目是否有余额
2. 检查科目是否有未过账凭证
3. 确认停用后的影响范围
4. 执行停用操作
5. 记录停用原因和时间

### 后置条件
- 科目信息准确保存
- 科目状态正确更新
- 相关权限生效
- 变更记录完整

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：会计科目管理页面
### 页面目标：提供完整的会计科目体系管理功能

### 信息架构：
- **顶部区域**：包含 科目搜索, 新增科目, 批量操作
- **左侧区域**：包含 科目树形结构, 科目分类筛选
- **中间区域**：包含 科目列表, 科目详情, 操作按钮
- **右侧区域**：包含 科目属性, 权限设置, 使用统计

### 交互逻辑与状态：

#### **科目搜索区域**
- **搜索输入框：**
  - **默认状态：** 白色背景，灰色边框，占位符"请输入科目编码或名称"
  - **聚焦状态：** 蓝色边框(#1890FF)，清除占位符
  - **搜索行为：** 支持科目编码、名称的模糊搜索，实时过滤结果
  - **清空按钮：** 输入内容后显示，点击清空搜索条件
- **高级搜索：**
  - **展开按钮：** "高级搜索"链接，点击展开更多筛选条件
  - **科目级别：** 下拉选择1-6级科目
  - **科目性质：** 多选资产、负债、权益、收入、费用
  - **使用状态：** 单选启用、停用、全部

#### **科目树形结构**
- **树形展示：**
  - **根节点：** 按科目性质分组（资产、负债、权益、收入、费用）
  - **展开图标：** 有下级科目的显示展开/收起图标
  - **科目节点：** 显示科目编码和名称，支持点击选中
  - **层级缩进：** 每级缩进20px，最多支持6级
- **节点状态：**
  - **正常状态：** 黑色文字，白色背景
  - **选中状态：** 蓝色背景(#E6F7FF)，蓝色文字(#1890FF)
  - **停用状态：** 灰色文字，删除线样式
  - **有余额：** 科目名称后显示绿色圆点

#### **科目列表区域**
- **列表表头：**
  - **科目编码：** 可排序，支持点击排序
  - **科目名称：** 可排序，显示完整名称
  - **科目级别：** 显示1-6级
  - **借贷方向：** 显示借方/贷方
  - **科目性质：** 显示资产/负债等
  - **期末余额：** 显示当前余额，金额右对齐
  - **状态：** 显示启用/停用状态
  - **操作：** 编辑、停用、查看明细等操作
- **列表行：**
  - **奇偶行：** 交替显示白色和浅灰色背景
  - **悬停效果：** 鼠标悬停显示浅蓝色背景
  - **选中效果：** 点击行选中，显示蓝色边框

#### **科目详情面板**
- **基本信息：**
  - **科目编码：** 只读显示，不可修改
  - **科目名称：** 可编辑输入框
  - **上级科目：** 下拉选择，显示完整路径
  - **科目级别：** 自动计算显示
- **科目属性：**
  - **借贷方向：** 单选按钮，借方/贷方
  - **科目性质：** 下拉选择，资产/负债/权益/收入/费用
  - **数量核算：** 开关控件，是否启用数量核算
  - **外币核算：** 开关控件，是否启用外币核算
- **使用控制：**
  - **状态：** 开关控件，启用/停用
  - **使用部门：** 多选下拉，限制使用部门
  - **使用人员：** 多选下拉，限制使用人员

#### **操作按钮组**
- **新增科目按钮：**
  - **默认状态：** 蓝色背景(#1890FF)，白色文字，"新增科目"
  - **点击效果：** 打开新增科目对话框
  - **权限控制：** 无权限时禁用并显示提示
- **编辑按钮：**
  - **默认状态：** 绿色边框，绿色文字，"编辑"
  - **点击效果：** 进入编辑模式或打开编辑对话框
  - **禁用条件：** 科目已被使用且不允许修改时禁用
- **停用按钮：**
  - **默认状态：** 橙色边框，橙色文字，"停用"
  - **点击效果：** 弹出确认对话框，确认后执行停用
  - **禁用条件：** 科目有余额或未过账凭证时禁用
- **删除按钮：**
  - **默认状态：** 红色边框，红色文字，"删除"
  - **点击效果：** 弹出确认对话框，确认后删除
  - **禁用条件：** 科目已被使用时禁用

#### **新增/编辑科目对话框**
- **对话框标题：** "新增科目" 或 "编辑科目"
- **表单字段：**
  - **上级科目：** 树形选择器，选择上级科目
  - **科目编码：** 输入框，自动生成或手动输入
  - **科目名称：** 输入框，必填，最大50字符
  - **借贷方向：** 单选按钮组
  - **科目性质：** 下拉选择
- **操作按钮：**
  - **保存按钮：** 蓝色背景，"保存"
  - **取消按钮：** 灰色边框，"取消"
  - **重置按钮：** 橙色边框，"重置"

#### **科目余额查询**
- **查询条件：**
  - **会计期间：** 日期选择器，选择查询期间
  - **科目范围：** 科目选择器，支持单个或范围选择
  - **包含下级：** 复选框，是否包含下级科目
- **余额展示：**
  - **期初余额：** 显示期初借方、贷方余额
  - **本期发生：** 显示本期借方、贷方发生额
  - **期末余额：** 显示期末借方、贷方余额
  - **累计发生：** 显示累计借方、贷方发生额

### 数据校验规则：

#### **科目编码**
- **校验规则：** 必须唯一，符合编码规则，长度4-20位
- **错误提示文案：** "科目编码已存在，请重新输入"

#### **科目名称**
- **校验规则：** 必填，长度2-50字符，同级科目名称不能重复
- **错误提示文案：** "科目名称不能为空且不能重复"

#### **科目层级**
- **校验规则：** 最多支持6级，下级科目性质必须与上级一致
- **错误提示文案：** "科目层级不能超过6级"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **科目信息**:
  - **科目编码 (account_code)**: String, 必填, 4-20位, 唯一
  - **科目名称 (account_name)**: String, 必填, 2-50字符
  - **上级科目 (parent_code)**: String, 可选, 引用其他科目编码
  - **借贷方向 (dc_flag)**: Enum, 必填, 借方/贷方
  - **科目性质 (account_type)**: Enum, 必填, 资产/负债/权益/收入/费用

### 展示数据
- **科目列表**: 科目编码、名称、级别、性质、余额、状态
- **科目树**: 层级结构、展开状态、选中状态
- **科目余额**: 期初、本期、期末余额和发生额
- **使用统计**: 凭证数量、最后使用时间

### 空状态/零数据
- **无科目数据**: 显示"暂无科目数据，请先新增科目"
- **无搜索结果**: 显示"未找到符合条件的科目"
- **无余额数据**: 显示"该科目暂无余额数据"

### API接口
- **科目查询**: GET /api/accounts
- **科目新增**: POST /api/accounts
- **科目修改**: PUT /api/accounts/{id}
- **科目删除**: DELETE /api/accounts/{id}
- **余额查询**: GET /api/accounts/balance

## 5. 异常与边界处理 (Error & Edge Cases)

### **科目编码重复**
- **提示信息**: "科目编码已存在，请重新输入"
- **用户操作**: 提供编码规则说明和自动生成选项

### **科目已被使用无法删除**
- **提示信息**: "该科目已被使用，无法删除，可以选择停用"
- **用户操作**: 提供停用选项和使用明细查看

### **科目有余额无法停用**
- **提示信息**: "该科目有余额，无法停用，请先处理余额"
- **用户操作**: 显示余额详情和处理建议

### **权限不足**
- **提示信息**: "您没有权限执行此操作"
- **用户操作**: 显示所需权限和申请流程

### **数据加载失败**
- **提示信息**: "数据加载失败，请刷新页面重试"
- **用户操作**: 提供刷新按钮和联系管理员选项

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持多级科目结构，最多6级
- [ ] 科目编码规则可配置，保证唯一性
- [ ] 科目属性设置完整，包含所有必要属性
- [ ] 已使用科目限制删除，提供停用功能
- [ ] 科目权限控制严格，按角色分配
- [ ] 科目余额查询准确，支持多维度统计
- [ ] 界面操作便捷，支持批量操作
- [ ] 数据校验严格，防止错误数据
- [ ] 所有页面元素符合全局设计规范
- [ ] 科目查询响应时间<2秒
- [ ] 支持科目导入导出功能
- [ ] 变更记录完整，支持审计追踪
