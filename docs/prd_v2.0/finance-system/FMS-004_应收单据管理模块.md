# 功能模块规格说明书：应收单据管理模块

- **模块ID**: FMS-004
- **所属子系统**: 财务管理子系统(FMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 应收会计, **I want to** 系统自动生成应收单据, **so that** 及时准确追踪客户欠款情况。
- **As a** 财务主管, **I want to** 监控客户信用额度, **so that** 控制应收账款风险。
- **As a** 应收会计, **I want to** 分析客户账龄结构, **so that** 制定有效的催收策略。
- **As a** 销售人员, **I want to** 查看客户应收余额, **so that** 了解客户的付款情况。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 客户档案已建立完成
- 销售订单已审核确认
- 用户具有应收管理权限
- 信用政策已设置

### 核心流程

#### 2.1 应收单据自动生成流程
1. 销售出库单审核通过
2. 系统根据出库单信息自动创建应收单
3. 提取客户、产品、金额等关键信息
4. 根据销售合同确定付款条件
5. 计算应收金额和到期日期
6. 应收单据进入待确认状态
7. 财务人员确认后正式生效

#### 2.2 信用额度控制流程
1. 新增应收单据时检查客户信用额度
2. 计算客户当前应收余额
3. 判断新增应收是否超过信用额度
4. 超限时触发预警机制
5. 根据超限政策决定是否允许继续
6. 记录超限情况和处理结果
7. 通知相关人员关注风险

#### 2.3 账龄分析流程
1. 设置账龄分析期间和客户范围
2. 系统按应收单据到期日计算账龄
3. 按30天、60天、90天等区间分组
4. 统计各账龄区间的应收金额
5. 计算账龄占比和风险等级
6. 生成账龄分析报表
7. 提供催收建议和风险提示

#### 2.4 应收单据维护流程
1. 查询需要维护的应收单据
2. 检查单据状态和权限
3. 修改允许变更的字段信息
4. 重新计算相关金额和日期
5. 保存修改并记录变更日志
6. 更新客户应收余额
7. 触发相关业务流程

### 后置条件
- 应收单据数据准确保存
- 客户应收余额正确更新
- 信用额度状态实时更新
- 账龄分析数据及时刷新

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：应收单据管理页面
### 页面目标：提供完整的应收单据管理和分析功能

### 信息架构：
- **顶部区域**：包含 应收查询, 新增应收, 批量操作, 导出功能
- **左侧区域**：包含 客户筛选, 状态筛选, 账龄筛选
- **中间区域**：包含 应收单据列表, 单据详情
- **右侧区域**：包含 客户信息, 信用状况, 账龄分析

### 交互逻辑与状态：

#### **应收查询区域**
- **基础查询：**
  - **客户名称：** 输入框，支持模糊搜索和下拉选择
  - **单据日期：** 日期范围选择器，默认当月
  - **单据状态：** 下拉多选，未收款/部分收款/已收款
  - **业务员：** 下拉选择，显示所有销售人员
- **高级查询：**
  - **应收金额：** 数字范围输入，设置金额区间
  - **到期日期：** 日期范围选择器，查询到期应收
  - **账龄范围：** 下拉选择，30天内/30-60天/60-90天/90天以上
  - **产品类别：** 下拉选择，按产品分类查询

#### **应收单据列表**
- **列表表头：**
  - **单据编号：** 可排序，点击查看单据详情
  - **客户名称：** 显示客户全称，可点击查看客户信息
  - **单据日期：** 可排序，显示YYYY-MM-DD格式
  - **应收金额：** 右对齐，千分位格式，红色字体
  - **已收金额：** 右对齐，千分位格式，绿色字体
  - **未收金额：** 右对齐，千分位格式，橙色字体
  - **到期日期：** 可排序，逾期显示红色
  - **账龄天数：** 显示距到期日天数，逾期为负数
  - **状态：** 状态标签，不同颜色区分
  - **操作：** 查看、编辑、收款等操作
- **状态标识：**
  - **未收款：** 红色标签，"未收款"
  - **部分收款：** 橙色标签，"部分收款"
  - **已收款：** 绿色标签，"已收款"
  - **已逾期：** 深红色标签，"已逾期"

#### **客户信息面板**
- **基本信息：**
  - **客户名称：** 显示客户全称
  - **客户编码：** 显示客户编码
  - **联系人：** 显示主要联系人
  - **联系电话：** 显示联系电话
- **信用信息：**
  - **信用额度：** 显示客户信用额度
  - **已用额度：** 显示已使用的信用额度
  - **可用额度：** 显示剩余可用额度
  - **信用等级：** 显示客户信用等级
- **应收统计：**
  - **应收总额：** 显示客户应收总金额
  - **逾期金额：** 显示逾期应收金额
  - **最大账龄：** 显示最长账龄天数
  - **平均账龄：** 显示平均账龄天数

#### **账龄分析图表**
- **账龄分布：**
  - **30天内：** 绿色柱状图，显示金额和占比
  - **31-60天：** 黄色柱状图，显示金额和占比
  - **61-90天：** 橙色柱状图，显示金额和占比
  - **90天以上：** 红色柱状图，显示金额和占比
- **趋势分析：**
  - **月度趋势：** 折线图显示近12个月账龄变化
  - **客户对比：** 横向对比主要客户账龄情况
  - **风险预警：** 高亮显示高风险客户和金额

#### **新增应收单据**
- **单据信息：**
  - **客户选择：** 下拉搜索选择器，显示客户名称和编码
  - **单据日期：** 日期选择器，默认当前日期
  - **业务类型：** 下拉选择，销售收入/其他应收
  - **币种：** 下拉选择，人民币/美元/欧元
- **金额信息：**
  - **应收金额：** 数字输入框，必填，大于0
  - **税率：** 下拉选择，0%/6%/13%等
  - **税额：** 自动计算，只读显示
  - **价税合计：** 自动计算，只读显示
- **付款条件：**
  - **付款方式：** 下拉选择，现金/银行转账/承兑汇票
  - **付款期限：** 数字输入框，天数
  - **到期日期：** 自动计算，可手动调整
  - **备注说明：** 文本框，可选填写

#### **信用额度监控**
- **额度预警：**
  - **预警阈值：** 设置预警比例，如80%、90%
  - **预警方式：** 弹窗提示、邮件通知、短信提醒
  - **预警级别：** 黄色预警、橙色预警、红色预警
- **超限处理：**
  - **超限策略：** 允许超限/禁止超限/需要审批
  - **审批流程：** 设置超限审批人员和流程
  - **风险评估：** 显示超限风险和影响分析

#### **批量操作功能**
- **批量选择：**
  - **全选复选框：** 选择当前页所有记录
  - **行复选框：** 选择单个记录
  - **选择统计：** 显示"已选择X条记录"
- **批量操作：**
  - **批量收款：** 对选中记录进行批量收款处理
  - **批量导出：** 导出选中记录的详细信息
  - **批量打印：** 打印选中记录的应收单据
  - **批量催收：** 发送催收通知给相关客户

#### **应收单据详情**
- **单据头信息：**
  - **单据编号：** 只读显示
  - **客户信息：** 显示客户详细信息
  - **单据日期：** 显示单据创建日期
  - **业务员：** 显示负责业务员
- **金额明细：**
  - **应收金额：** 显示原始应收金额
  - **已收金额：** 显示累计收款金额
  - **未收金额：** 显示剩余未收金额
  - **收款记录：** 显示所有收款记录
- **关联信息：**
  - **销售订单：** 显示关联的销售订单
  - **出库单：** 显示关联的出库单据
  - **发票信息：** 显示相关发票信息
  - **收款记录：** 显示收款历史记录

### 数据校验规则：

#### **客户选择**
- **校验规则：** 必须选择有效的客户，客户状态必须为启用
- **错误提示文案：** "请选择有效的客户"

#### **应收金额**
- **校验规则：** 必须大于0，小数位不超过2位
- **错误提示文案：** "应收金额必须大于0"

#### **信用额度**
- **校验规则：** 新增应收后总额不能超过客户信用额度
- **错误提示文案：** "超出客户信用额度，当前可用额度￥X"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **应收单据信息**:
  - **客户编码 (customer_code)**: String, 必填, 引用客户档案
  - **单据日期 (bill_date)**: Date, 必填, 应收发生日期
  - **应收金额 (receivable_amount)**: Decimal, 必填, 应收金额
  - **到期日期 (due_date)**: Date, 必填, 应收到期日期
  - **币种 (currency)**: String, 必填, 币种代码

### 展示数据
- **应收列表**: 单据编号、客户、金额、状态、账龄等
- **客户信息**: 基本信息、信用状况、应收统计
- **账龄分析**: 各账龄区间金额分布和占比
- **收款记录**: 历史收款记录和核销情况

### 空状态/零数据
- **无应收数据**: 显示"暂无应收数据"
- **无搜索结果**: 显示"未找到符合条件的应收单据"
- **客户无应收**: 显示"该客户暂无应收款项"

### API接口
- **应收查询**: GET /api/receivables
- **应收新增**: POST /api/receivables
- **应收修改**: PUT /api/receivables/{id}
- **账龄分析**: GET /api/receivables/aging
- **信用查询**: GET /api/customers/{id}/credit

## 5. 异常与边界处理 (Error & Edge Cases)

### **信用额度超限**
- **提示信息**: "客户信用额度不足，当前可用额度￥X"
- **用户操作**: 提供信用额度调整申请和风险评估

### **客户状态异常**
- **提示信息**: "客户状态异常，无法新增应收"
- **用户操作**: 显示客户状态和联系管理员处理

### **数据同步延迟**
- **提示信息**: "数据正在同步中，请稍后刷新"
- **用户操作**: 提供手动刷新按钮和自动刷新机制

### **账龄计算异常**
- **提示信息**: "账龄计算异常，请检查日期设置"
- **用户操作**: 提供日期校正和重新计算选项

### **导出数据过多**
- **提示信息**: "导出数据量过大，建议分批导出"
- **用户操作**: 提供分页导出和条件筛选建议

## 6. 验收标准 (Acceptance Criteria)

- [ ] 销售出库后5分钟内自动生成应收单
- [ ] 应收单据信息完整准确
- [ ] 信用额度超限自动预警
- [ ] 账龄分析准确，支持多维度统计
- [ ] 应收数据与销售数据100%一致
- [ ] 客户信用状况实时更新
- [ ] 支持批量操作，提高工作效率
- [ ] 账龄计算准确，支持自定义账龄区间
- [ ] 应收查询响应时间<2秒
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持多币种应收管理
- [ ] 风险预警机制完善，及时提醒
