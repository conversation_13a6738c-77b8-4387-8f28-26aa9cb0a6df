# 功能模块规格说明书：付款核销模块

- **模块ID**: FMS-008
- **所属子系统**: 财务管理子系统(FMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 应付会计, **I want to** 录入供应商付款信息, **so that** 及时处理供应商付款。
- **As a** 应付会计, **I want to** 核销付款与应付单据, **so that** 准确管理供应商账务。
- **As a** 财务主管, **I want to** 审批付款申请, **so that** 控制资金支出和现金流。
- **As a** 财务人员, **I want to** 自动生成付款凭证, **so that** 确保财务记录的完整性。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 供应商应付单据已存在
- 用户具有付款权限
- 银行账户已设置
- 付款审批流程已配置

### 核心流程

#### 2.1 付款申请流程
1. 选择需要付款的应付单据
2. 填写付款申请信息
3. 选择付款方式和银行账户
4. 计算付款金额和手续费
5. 提交付款申请
6. 进入付款审批流程

#### 2.2 付款审批流程
1. 审批人员查看付款申请
2. 核实应付单据和付款信息
3. 检查资金预算和现金流
4. 审批通过或退回修改
5. 审批通过后生成付款单
6. 付款单进入待执行状态

#### 2.3 付款执行流程
1. 财务人员执行付款操作
2. 选择付款银行账户
3. 生成银行付款指令
4. 确认付款信息无误
5. 执行付款并记录流水
6. 付款状态更新为已付款

#### 2.4 付款核销流程
1. 付款执行完成后进行核销
2. 自动匹配付款与应付单据
3. 确认核销关系和金额
4. 处理部分付款和多次付款
5. 更新应付余额和付款状态
6. 生成付款凭证

#### 2.5 付款撤销流程
1. 查询需要撤销的付款记录
2. 检查是否允许撤销
3. 确认撤销的影响范围
4. 执行撤销操作
5. 恢复应付和付款的原始状态
6. 记录撤销原因和日志

### 后置条件
- 付款数据准确保存
- 应付余额正确更新
- 核销关系清晰记录
- 付款凭证自动生成

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：付款核销管理页面
### 页面目标：提供完整的付款申请、审批、执行和核销功能

### 信息架构：
- **顶部区域**：包含 付款查询, 新增付款, 批量付款, 审批管理
- **左侧区域**：包含 供应商筛选, 状态筛选, 审批筛选
- **中间区域**：包含 付款列表, 付款详情, 核销界面
- **右侧区域**：包含 应付明细, 资金状况, 审批流程

### 交互逻辑与状态：

#### **付款查询区域**
- **基础查询：**
  - **供应商名称：** 输入框，支持模糊搜索
  - **付款日期：** 日期范围选择器
  - **付款状态：** 下拉选择，申请中/已审批/已付款/已核销
  - **付款方式：** 下拉选择，银行转账/现金/承兑汇票
- **高级查询：**
  - **付款金额：** 数字范围输入
  - **银行账户：** 下拉选择付款账户
  - **付款编号：** 输入框，精确查询
  - **申请人：** 下拉选择付款申请人

#### **付款列表区域**
- **列表表头：**
  - **付款编号：** 可排序，点击查看详情
  - **供应商名称：** 显示供应商全称
  - **申请日期：** 可排序，YYYY-MM-DD格式
  - **付款金额：** 右对齐，千分位格式，红色字体
  - **已核销金额：** 右对齐，绿色字体
  - **未核销金额：** 右对齐，橙色字体
  - **付款方式：** 显示付款方式图标
  - **审批状态：** 状态标签
  - **付款状态：** 状态标签
  - **操作：** 审批、付款、核销、查看等操作
- **状态标识：**
  - **申请中：** 橙色标签，"申请中"
  - **已审批：** 蓝色标签，"已审批"
  - **已付款：** 绿色标签，"已付款"
  - **已核销：** 深绿色标签，"已核销"
  - **已退回：** 红色标签，"已退回"

#### **付款申请界面**
- **申请信息：**
  - **供应商选择：** 下拉搜索选择器，必填
  - **申请日期：** 日期选择器，默认当前日期
  - **付款金额：** 数字输入框，必填
  - **付款方式：** 下拉选择，银行转账/现金/承兑汇票
- **银行信息：**
  - **付款账户：** 下拉选择公司银行账户
  - **收款账户：** 显示供应商银行账户信息
  - **手续费：** 数字输入框，银行手续费
  - **实付金额：** 自动计算，付款金额+手续费
- **应付选择：**
  - **应付列表：** 显示供应商所有未付应付单据
  - **选择框：** 复选框，选择要付款的应付单
  - **应付金额：** 显示应付单据金额
  - **未付金额：** 显示剩余未付金额
  - **本次付款：** 输入框，设置本次付款金额
- **申请说明：**
  - **付款用途：** 下拉选择，货款/费用/其他
  - **付款备注：** 文本框，付款说明
  - **紧急程度：** 下拉选择，普通/紧急/特急
  - **申请人：** 显示当前用户，不可修改

#### **付款审批界面**
- **申请信息展示：**
  - **申请人：** 显示付款申请人
  - **申请时间：** 显示申请时间
  - **供应商信息：** 显示供应商详细信息
  - **付款金额：** 突出显示付款金额
- **应付明细：**
  - **应付单号：** 显示相关应付单据
  - **应付金额：** 显示应付金额
  - **已付金额：** 显示已付金额
  - **本次付款：** 显示本次付款金额
- **审批操作：**
  - **审批意见：** 文本框，填写审批意见
  - **审批结果：** 单选按钮，通过/退回
  - **退回原因：** 下拉选择常见退回原因
  - **审批签名：** 电子签名或密码确认
- **资金检查：**
  - **账户余额：** 显示付款账户余额
  - **资金预算：** 显示相关资金预算
  - **现金流影响：** 显示对现金流的影响
  - **风险提示：** 显示资金风险提示

#### **付款执行界面**
- **付款确认：**
  - **付款金额：** 确认付款金额
  - **付款账户：** 确认付款银行账户
  - **收款账户：** 确认供应商收款账户
  - **付款用途：** 确认付款用途
- **银行操作：**
  - **网银转账：** 生成网银转账指令
  - **支票付款：** 生成支票付款信息
  - **现金付款：** 记录现金付款信息
  - **其他方式：** 其他付款方式处理
- **付款记录：**
  - **银行流水号：** 输入银行交易流水号
  - **实际付款时间：** 记录实际付款时间
  - **手续费：** 记录实际手续费
  - **付款凭证：** 上传付款凭证附件

#### **付款核销界面**
- **核销匹配：**
  - **自动匹配：** 按钮，"智能匹配"
  - **匹配规则：** 显示匹配规则说明
  - **匹配结果：** 显示匹配的应付单据
- **应付单据列表：**
  - **选择框：** 复选框，选择要核销的应付单
  - **单据编号：** 显示应付单据编号
  - **应付金额：** 显示应付金额
  - **未付金额：** 显示剩余未付金额
  - **核销金额：** 输入框，设置核销金额
- **核销汇总：**
  - **付款金额：** 显示付款总金额
  - **已分配金额：** 显示已分配核销金额
  - **未分配金额：** 显示剩余未分配金额
  - **核销差额：** 显示核销差额

#### **批量付款功能**
- **批量选择：**
  - **供应商分组：** 按供应商分组显示应付
  - **到期日筛选：** 按到期日筛选应付
  - **金额筛选：** 按金额范围筛选应付
  - **批量选择：** 复选框批量选择
- **批量操作：**
  - **批量申请：** 批量生成付款申请
  - **批量审批：** 批量审批付款申请
  - **批量付款：** 批量执行付款操作
  - **批量核销：** 批量进行付款核销

#### **付款凭证预览**
- **凭证信息：**
  - **凭证类型：** 显示付款凭证类型
  - **凭证日期：** 显示凭证日期
  - **凭证摘要：** 显示凭证摘要
  - **凭证金额：** 显示凭证金额
- **分录明细：**
  - **借方科目：** 显示借方科目（应付账款等）
  - **贷方科目：** 显示贷方科目（银行存款等）
  - **辅助核算：** 显示供应商等辅助核算项
  - **分录金额：** 显示分录金额

### 数据校验规则：

#### **付款金额**
- **校验规则：** 必须大于0，不能超过应付余额
- **错误提示文案：** "付款金额必须大于0且不能超过应付余额"

#### **银行账户**
- **校验规则：** 必须选择有效的银行账户，账户状态为启用
- **错误提示文案：** "请选择有效的银行账户"

#### **审批权限**
- **校验规则：** 审批人必须具有相应的审批权限和金额权限
- **错误提示文案：** "您没有权限审批该金额的付款申请"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **付款申请**:
  - **供应商编码 (supplier_code)**: String, 必填, 引用供应商档案
  - **付款金额 (payment_amount)**: Decimal, 必填, 付款金额
  - **付款方式 (payment_method)**: Enum, 必填, 付款方式
  - **付款账户 (payment_account)**: String, 必填, 付款银行账户
- **核销信息**:
  - **应付单号 (payable_no)**: String, 必填, 被核销应付单
  - **核销金额 (write_off_amount)**: Decimal, 必填, 核销金额

### 展示数据
- **付款列表**: 付款编号、供应商、金额、状态、审批情况
- **应付明细**: 供应商所有未付应付单据
- **审批记录**: 审批历史和当前审批状态
- **凭证信息**: 自动生成的付款凭证

### 空状态/零数据
- **无付款数据**: 显示"暂无付款记录"
- **无应付可付**: 显示"该供应商暂无可付应付"
- **无审批记录**: 显示"暂无审批记录"

### API接口
- **付款查询**: GET /api/payments
- **付款申请**: POST /api/payments/apply
- **付款审批**: POST /api/payments/approve
- **付款执行**: POST /api/payments/execute
- **付款核销**: POST /api/payments/writeoff

## 5. 异常与边界处理 (Error & Edge Cases)

### **资金不足**
- **提示信息**: "付款账户余额不足，无法执行付款"
- **用户操作**: 显示账户余额和资金调配建议

### **审批超时**
- **提示信息**: "付款申请审批超时，请联系审批人"
- **用户操作**: 提供审批提醒和催办功能

### **付款失败**
- **提示信息**: "付款执行失败，请检查银行账户信息"
- **用户操作**: 提供重试选项和技术支持

### **核销金额不匹配**
- **提示信息**: "核销金额与付款金额不匹配"
- **用户操作**: 提供金额调整和差额处理选项

### **权限不足**
- **提示信息**: "您没有权限执行此付款操作"
- **用户操作**: 显示所需权限和申请流程

## 6. 验收标准 (Acceptance Criteria)

- [ ] 付款申请流程清晰简便
- [ ] 审批流程可配置，权限控制严格
- [ ] 付款执行安全可靠
- [ ] 核销关系准确无误
- [ ] 付款凭证自动生成准确率100%
- [ ] 支持批量付款操作
- [ ] 付款状态实时更新
- [ ] 资金预算控制有效
- [ ] 付款查询响应时间<2秒
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持多种付款方式
- [ ] 审批记录完整，支持审计追踪
