# 功能模块规格说明书：收款核销模块

- **模块ID**: FMS-005
- **所属子系统**: 财务管理子系统(FMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 应收会计, **I want to** 快速录入客户收款信息, **so that** 及时处理客户付款。
- **As a** 应收会计, **I want to** 智能匹配收款与应收单据, **so that** 提高核销效率和准确性。
- **As a** 财务人员, **I want to** 处理多对多核销关系, **so that** 灵活处理复杂的收款情况。
- **As a** 财务主管, **I want to** 自动生成收款凭证, **so that** 确保财务记录的完整性。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 客户应收单据已存在
- 用户具有收款核销权限
- 银行账户已设置
- 收款方式已配置

### 核心流程

#### 2.1 收款单录入流程
1. 选择收款客户和收款日期
2. 录入收款金额和收款方式
3. 选择收款银行账户
4. 填写收款备注和附件信息
5. 保存收款单并生成收款编号
6. 收款单进入待核销状态

#### 2.2 智能匹配核销流程
1. 系统自动列出客户所有未核销应收
2. 按金额、日期等条件智能匹配
3. 提供匹配建议和相似度评分
4. 用户确认或调整匹配关系
5. 执行核销操作并更新余额
6. 生成核销记录和收款凭证

#### 2.3 手工核销流程
1. 查询需要核销的收款单
2. 手动选择要核销的应收单据
3. 设置核销金额和核销方式
4. 处理部分核销和多次核销
5. 确认核销关系并保存
6. 更新应收余额和收款状态

#### 2.4 核销撤销流程
1. 查询已核销的收款记录
2. 检查是否允许撤销核销
3. 确认撤销的影响范围
4. 执行撤销操作
5. 恢复应收和收款的原始状态
6. 记录撤销原因和操作日志

#### 2.5 收款凭证生成流程
1. 核销完成后自动触发凭证生成
2. 根据收款方式选择凭证模板
3. 自动填充科目、金额等信息
4. 生成收款凭证草稿
5. 财务人员审核确认
6. 凭证过账更新总账数据

### 后置条件
- 收款数据准确保存
- 应收余额正确更新
- 核销关系清晰记录
- 收款凭证自动生成

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：收款核销管理页面
### 页面目标：提供高效的收款录入和核销处理功能

### 信息架构：
- **顶部区域**：包含 收款查询, 新增收款, 批量核销
- **左侧区域**：包含 客户选择, 收款筛选, 核销状态
- **中间区域**：包含 收款列表, 核销界面
- **右侧区域**：包含 应收明细, 核销记录

### 交互逻辑与状态：

#### **收款查询区域**
- **基础查询：**
  - **客户名称：** 输入框，支持模糊搜索
  - **收款日期：** 日期范围选择器
  - **收款状态：** 下拉选择，待核销/部分核销/已核销
  - **收款方式：** 下拉选择，现金/银行转账/承兑汇票
- **高级查询：**
  - **收款金额：** 数字范围输入
  - **银行账户：** 下拉选择收款账户
  - **收款编号：** 输入框，精确查询
  - **经办人：** 下拉选择收款经办人

#### **收款列表区域**
- **列表表头：**
  - **收款编号：** 可排序，点击查看详情
  - **客户名称：** 显示客户全称
  - **收款日期：** 可排序，YYYY-MM-DD格式
  - **收款金额：** 右对齐，千分位格式，蓝色字体
  - **已核销金额：** 右对齐，绿色字体
  - **未核销金额：** 右对齐，橙色字体
  - **收款方式：** 显示收款方式图标和文字
  - **核销状态：** 状态标签
  - **操作：** 核销、查看、撤销等操作
- **状态标识：**
  - **待核销：** 橙色标签，"待核销"
  - **部分核销：** 蓝色标签，"部分核销"
  - **已核销：** 绿色标签，"已核销"
  - **已撤销：** 灰色标签，"已撤销"

#### **新增收款界面**
- **收款信息：**
  - **客户选择：** 下拉搜索选择器，必填
  - **收款日期：** 日期选择器，默认当前日期
  - **收款金额：** 数字输入框，必填，大于0
  - **收款方式：** 下拉选择，现金/银行转账/承兑汇票/其他
- **银行信息：**
  - **收款账户：** 下拉选择公司银行账户
  - **银行流水号：** 输入框，银行交易流水号
  - **手续费：** 数字输入框，银行手续费
  - **实收金额：** 自动计算，收款金额-手续费
- **附加信息：**
  - **收款备注：** 文本框，收款说明
  - **附件上传：** 文件上传，支持图片和PDF
  - **经办人：** 下拉选择，默认当前用户
  - **联系电话：** 输入框，客户联系电话

#### **智能核销界面**
- **匹配建议：**
  - **自动匹配：** 按钮，"智能匹配"，点击自动匹配
  - **匹配规则：** 显示匹配规则说明
  - **相似度：** 显示匹配相似度百分比
  - **匹配结果：** 列表显示匹配的应收单据
- **应收单据列表：**
  - **选择框：** 复选框，选择要核销的应收单
  - **单据编号：** 显示应收单据编号
  - **单据日期：** 显示应收单据日期
  - **应收金额：** 显示原始应收金额
  - **未收金额：** 显示剩余未收金额
  - **账龄：** 显示账龄天数，逾期红色显示
  - **核销金额：** 输入框，可编辑核销金额
- **核销汇总：**
  - **收款金额：** 显示当前收款总金额
  - **已分配金额：** 显示已分配的核销金额
  - **未分配金额：** 显示剩余未分配金额
  - **核销差额：** 显示核销差额，不为0时红色警告

#### **核销操作区域**
- **核销方式：**
  - **完全核销：** 单选按钮，收款金额完全核销应收
  - **部分核销：** 单选按钮，收款金额部分核销应收
  - **多单核销：** 单选按钮，一笔收款核销多张应收单
  - **预收款：** 单选按钮，收款金额大于应收时处理为预收
- **核销明细：**
  - **应收单号：** 显示被核销的应收单据编号
  - **核销金额：** 显示本次核销金额
  - **核销日期：** 显示核销日期
  - **核销说明：** 输入框，核销说明
- **操作按钮：**
  - **确认核销：** 蓝色背景，"确认核销"
  - **保存草稿：** 灰色边框，"保存草稿"
  - **重置：** 橙色边框，"重置"
  - **取消：** 灰色边框，"取消"

#### **核销记录查看**
- **核销历史：**
  - **核销日期：** 显示核销操作日期
  - **核销金额：** 显示核销金额
  - **应收单号：** 显示被核销的应收单据
  - **操作人：** 显示核销操作人
  - **核销状态：** 显示核销状态
- **撤销操作：**
  - **撤销按钮：** 红色边框，"撤销核销"
  - **撤销原因：** 下拉选择撤销原因
  - **撤销说明：** 文本框，详细说明
  - **确认撤销：** 需要二次确认

#### **批量核销功能**
- **批量选择：**
  - **全选：** 复选框，选择当前页所有收款
  - **条件选择：** 按客户、日期等条件批量选择
  - **选择统计：** 显示已选择的收款数量和金额
- **批量操作：**
  - **批量核销：** 对选中收款进行批量核销
  - **批量生成凭证：** 批量生成收款凭证
  - **批量导出：** 导出选中收款的详细信息
  - **批量打印：** 打印收款单据

#### **收款凭证预览**
- **凭证信息：**
  - **凭证类型：** 显示收款凭证类型
  - **凭证日期：** 显示凭证日期
  - **凭证摘要：** 显示凭证摘要
  - **凭证金额：** 显示凭证金额
- **分录明细：**
  - **借方科目：** 显示借方科目（银行存款等）
  - **贷方科目：** 显示贷方科目（应收账款等）
  - **辅助核算：** 显示客户等辅助核算项
  - **分录金额：** 显示分录金额

### 数据校验规则：

#### **收款金额**
- **校验规则：** 必须大于0，小数位不超过2位
- **错误提示文案：** "收款金额必须大于0"

#### **核销金额**
- **校验规则：** 核销金额不能超过收款金额和应收余额
- **错误提示文案：** "核销金额不能超过收款金额或应收余额"

#### **客户一致性**
- **校验规则：** 收款客户必须与应收单据客户一致
- **错误提示文案：** "收款客户与应收单据客户不一致"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **收款信息**:
  - **客户编码 (customer_code)**: String, 必填, 引用客户档案
  - **收款日期 (receipt_date)**: Date, 必填, 收款日期
  - **收款金额 (receipt_amount)**: Decimal, 必填, 收款金额
  - **收款方式 (receipt_method)**: Enum, 必填, 收款方式
- **核销信息**:
  - **应收单号 (receivable_no)**: String, 必填, 被核销应收单
  - **核销金额 (write_off_amount)**: Decimal, 必填, 核销金额
  - **核销日期 (write_off_date)**: Date, 必填, 核销日期

### 展示数据
- **收款列表**: 收款编号、客户、金额、状态、核销情况
- **应收明细**: 客户所有未核销应收单据
- **核销记录**: 历史核销记录和撤销记录
- **凭证信息**: 自动生成的收款凭证

### 空状态/零数据
- **无收款数据**: 显示"暂无收款记录"
- **无应收可核销**: 显示"该客户暂无可核销应收"
- **无核销记录**: 显示"暂无核销记录"

### API接口
- **收款查询**: GET /api/receipts
- **收款新增**: POST /api/receipts
- **智能匹配**: GET /api/receipts/match
- **执行核销**: POST /api/receipts/writeoff
- **撤销核销**: POST /api/receipts/cancel

## 5. 异常与边界处理 (Error & Edge Cases)

### **核销金额超限**
- **提示信息**: "核销金额超过可核销金额，请重新输入"
- **用户操作**: 自动调整为最大可核销金额

### **客户不匹配**
- **提示信息**: "收款客户与应收单据客户不一致"
- **用户操作**: 提供客户信息对比和修正建议

### **重复核销**
- **提示信息**: "该应收单据已被核销，请勿重复操作"
- **用户操作**: 显示已核销记录和处理建议

### **核销撤销限制**
- **提示信息**: "该核销记录已生成凭证，无法撤销"
- **用户操作**: 提供凭证冲销流程指导

### **数据保存失败**
- **提示信息**: "核销数据保存失败，请重试"
- **用户操作**: 提供重试按钮和数据恢复选项

## 6. 验收标准 (Acceptance Criteria)

- [ ] 收款单录入简单快捷
- [ ] 智能匹配准确率≥95%
- [ ] 支持部分核销和多次核销
- [ ] 收款凭证自动生成准确率100%
- [ ] 核销操作响应时间<2秒
- [ ] 支持批量核销操作
- [ ] 核销撤销功能安全可控
- [ ] 核销记录完整，支持审计追踪
- [ ] 应收余额实时更新
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持多种收款方式处理
- [ ] 核销差额处理机制完善
