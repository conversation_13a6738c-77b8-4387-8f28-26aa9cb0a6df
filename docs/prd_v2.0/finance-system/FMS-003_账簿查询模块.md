# 功能模块规格说明书：账簿查询模块

- **模块ID**: FMS-003
- **所属子系统**: 财务管理子系统(FMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 财务人员, **I want to** 查询总分类账和明细分类账, **so that** 了解各科目的详细发生情况。
- **As a** 财务主管, **I want to** 查看科目余额表和试算平衡表, **so that** 进行财务分析和账务核对。
- **As a** 会计人员, **I want to** 导出和打印各种账簿, **so that** 满足内外部审计和报告需求。
- **As a** 管理人员, **I want to** 查询多栏账和数量金额账, **so that** 获得更详细的财务分析信息。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有账簿查询权限
- 凭证已审核过账
- 会计期间数据完整
- 科目体系已建立

### 核心流程

#### 2.1 总分类账查询流程
1. 选择查询期间和科目范围
2. 设置查询条件（包含下级、零余额等）
3. 系统汇总科目发生额和余额
4. 按期间和科目展示总账数据
5. 支持钻取查看明细分类账
6. 提供导出和打印功能

#### 2.2 明细分类账查询流程
1. 选择具体科目和查询期间
2. 设置明细查询条件
3. 系统按时间顺序列示凭证明细
4. 显示每笔业务的借贷发生和余额
5. 支持按凭证号钻取查看凭证
6. 提供汇总统计和导出功能

#### 2.3 科目余额表查询流程
1. 设置查询期间和科目级别
2. 选择余额表格式（期初期末/累计发生）
3. 系统计算各科目余额数据
4. 按科目层级展示余额信息
5. 提供余额试算平衡校验
6. 支持多种格式导出

#### 2.4 多栏账查询流程
1. 选择主科目和分析维度
2. 设置多栏显示的子科目
3. 系统按维度分列显示数据
4. 提供横向和纵向汇总
5. 支持图表可视化展示
6. 提供详细数据导出

### 后置条件
- 查询结果准确完整
- 数据格式符合会计规范
- 导出文件格式正确
- 查询记录完整保存

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：账簿查询页面
### 页面目标：提供完整的财务账簿查询和分析功能

### 信息架构：
- **左侧区域**：包含 账簿类型选择, 查询条件设置
- **顶部区域**：包含 快速查询, 导出打印, 刷新按钮
- **中间区域**：包含 账簿数据展示, 分页控制
- **右侧区域**：包含 汇总信息, 图表分析

### 交互逻辑与状态：

#### **账簿类型选择**
- **账簿菜单：**
  - **总分类账：** 图标+文字，点击切换到总账查询
  - **明细分类账：** 图标+文字，点击切换到明细账查询
  - **科目余额表：** 图标+文字，点击切换到余额表查询
  - **试算平衡表：** 图标+文字，点击切换到试算表查询
  - **多栏账：** 图标+文字，点击切换到多栏账查询
  - **数量金额账：** 图标+文字，点击切换到数量账查询
- **选中状态：**
  - **当前选中：** 蓝色背景(#E6F7FF)，蓝色文字(#1890FF)
  - **未选中：** 白色背景，黑色文字
  - **悬停效果：** 浅灰色背景(#F5F5F5)

#### **查询条件设置**
- **基础条件：**
  - **查询期间：** 日期范围选择器，支持快速选择（本月/本季/本年）
  - **科目范围：** 科目选择器，支持单个科目或科目范围
  - **包含下级：** 复选框，是否包含下级科目数据
  - **显示零余额：** 复选框，是否显示余额为零的科目
- **高级条件：**
  - **币种：** 下拉选择，人民币/美元/欧元等
  - **数据来源：** 下拉选择，已过账/包含未过账
  - **科目级别：** 下拉选择，显示到第几级科目
  - **排序方式：** 下拉选择，按科目编码/按余额大小

#### **总分类账展示**
- **账簿表头：**
  - **科目编码：** 可排序，点击排序
  - **科目名称：** 显示完整科目名称
  - **期初余额：** 分借方/贷方显示，右对齐
  - **本期借方：** 本期借方发生额，红色字体
  - **本期贷方：** 本期贷方发生额，蓝色字体
  - **期末余额：** 分借方/贷方显示，加粗显示
  - **操作：** 查看明细、导出等操作
- **数据行：**
  - **科目层级：** 通过缩进显示科目层级关系
  - **金额格式：** 千分位分隔，保留2位小数
  - **余额方向：** 借方余额显示在借方列，贷方余额显示在贷方列
  - **小计行：** 各级科目小计，背景色区分

#### **明细分类账展示**
- **账簿表头：**
  - **凭证日期：** 按时间顺序排列
  - **凭证号：** 可点击查看凭证详情
  - **摘要：** 显示业务摘要
  - **对方科目：** 显示对应的借方或贷方科目
  - **借方金额：** 借方发生额
  - **贷方金额：** 贷方发生额
  - **余额：** 累计余额，显示借贷方向
- **余额计算：**
  - **期初余额：** 第一行显示期初余额
  - **逐笔累计：** 每行显示累计余额
  - **期末余额：** 最后一行显示期末余额
  - **借贷标识：** 借方余额显示"借"，贷方余额显示"贷"

#### **科目余额表展示**
- **表格结构：**
  - **科目编码：** 按编码顺序排列
  - **科目名称：** 显示科目全称
  - **期初借方：** 期初借方余额
  - **期初贷方：** 期初贷方余额
  - **本期借方：** 本期借方发生额
  - **本期贷方：** 本期贷方发生额
  - **期末借方：** 期末借方余额
  - **期末贷方：** 期末贷方余额
- **试算平衡：**
  - **借方合计：** 各列借方金额合计
  - **贷方合计：** 各列贷方金额合计
  - **平衡校验：** 借贷合计相等性校验
  - **不平衡提示：** 不平衡时红色警告显示

#### **多栏账展示**
- **动态列设置：**
  - **主科目：** 固定显示的主科目列
  - **分析维度：** 根据选择的子科目动态生成列
  - **合计列：** 横向合计各分析维度
  - **列宽调整：** 支持拖拽调整列宽
- **数据展示：**
  - **按期间：** 按月份或日期分行显示
  - **分列显示：** 各子科目分列显示发生额
  - **小计汇总：** 提供期间小计和总计
  - **图表切换：** 支持切换到图表视图

#### **查询操作区域**
- **查询按钮：**
  - **默认状态：** 蓝色背景(#1890FF)，白色文字，"查询"
  - **点击效果：** 根据条件查询数据，显示加载状态
  - **加载状态：** 按钮禁用，显示"查询中..."
- **重置按钮：**
  - **默认状态：** 灰色边框，"重置"
  - **点击效果：** 清空所有查询条件，恢复默认值
- **导出按钮：**
  - **默认状态：** 绿色边框，"导出"
  - **下拉菜单：** Excel/PDF/CSV格式选择
  - **点击效果：** 根据选择格式导出当前查询结果
- **打印按钮：**
  - **默认状态：** 橙色边框，"打印"
  - **点击效果：** 打开打印预览，支持打印设置

#### **数据分页控制**
- **分页信息：**
  - **总记录数：** 显示"共X条记录"
  - **当前页码：** 显示"第X页/共Y页"
  - **每页条数：** 下拉选择20/50/100条
- **分页按钮：**
  - **首页：** 跳转到第一页
  - **上一页：** 跳转到上一页，第一页时禁用
  - **页码：** 显示当前页前后各2页
  - **下一页：** 跳转到下一页，最后一页时禁用
  - **末页：** 跳转到最后一页

#### **汇总信息面板**
- **金额汇总：**
  - **借方合计：** 显示当前查询结果借方合计
  - **贷方合计：** 显示当前查询结果贷方合计
  - **净额：** 显示借贷方差额
  - **记录数：** 显示查询到的记录条数
- **图表分析：**
  - **饼图：** 按科目性质分析金额占比
  - **柱状图：** 按期间分析发生额趋势
  - **折线图：** 显示余额变化趋势
  - **切换按钮：** 在不同图表类型间切换

### 数据校验规则：

#### **查询期间**
- **校验规则：** 开始日期不能大于结束日期，期间不能超过3年
- **错误提示文案：** "查询期间设置有误，请重新选择"

#### **科目选择**
- **校验规则：** 必须选择有效的科目，科目必须在查询期间内有数据
- **错误提示文案：** "请选择有效的科目进行查询"

#### **导出数据量**
- **校验规则：** 导出记录数不能超过10万条
- **错误提示文案：** "导出数据量过大，请缩小查询范围"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **查询条件**:
  - **期间范围 (date_range)**: DateRange, 必填, 查询的时间范围
  - **科目范围 (account_range)**: String[], 可选, 查询的科目范围
  - **包含下级 (include_sub)**: Boolean, 可选, 是否包含下级科目
  - **显示零余额 (show_zero)**: Boolean, 可选, 是否显示零余额

### 展示数据
- **总分类账**: 科目编码、名称、期初余额、本期发生、期末余额
- **明细分类账**: 凭证信息、摘要、对方科目、发生额、余额
- **科目余额表**: 各科目的期初、本期、期末数据
- **试算平衡表**: 借贷方合计和平衡校验结果

### 空状态/零数据
- **无查询结果**: 显示"该期间内无相关数据"
- **科目无发生**: 显示"该科目在查询期间内无发生额"
- **余额为零**: 根据设置显示或隐藏零余额科目

### API接口
- **总账查询**: GET /api/ledger/general
- **明细账查询**: GET /api/ledger/detail
- **余额表查询**: GET /api/ledger/balance
- **数据导出**: POST /api/ledger/export

## 5. 异常与边界处理 (Error & Edge Cases)

### **查询超时**
- **提示信息**: "查询时间过长，请缩小查询范围或稍后重试"
- **用户操作**: 提供查询优化建议和重试选项

### **数据量过大**
- **提示信息**: "查询结果过多，建议缩小查询范围"
- **用户操作**: 提供分页查询和条件优化建议

### **期间数据不完整**
- **提示信息**: "该期间数据可能不完整，请确认凭证是否全部过账"
- **用户操作**: 显示未过账凭证数量和处理建议

### **导出失败**
- **提示信息**: "文件导出失败，请检查网络连接或稍后重试"
- **用户操作**: 提供重试按钮和技术支持联系方式

### **打印异常**
- **提示信息**: "打印设置异常，请检查打印机连接"
- **用户操作**: 提供打印设置指导和PDF导出替代方案

## 6. 验收标准 (Acceptance Criteria)

- [ ] 账簿查询响应时间<3秒
- [ ] 支持多维度筛选和排序
- [ ] 账簿格式标准，符合会计规范
- [ ] 支持Excel导出和PDF打印
- [ ] 数据准确性100%，与凭证数据一致
- [ ] 试算平衡表借贷必须平衡
- [ ] 支持大数据量查询（100万条记录）
- [ ] 查询条件保存和快速调用
- [ ] 图表分析功能完善，支持多种图表类型
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持移动端查看（响应式设计）
- [ ] 操作日志完整，支持审计追踪
