# 功能模块规格说明书：成本核算模块

- **模块ID**: FMS-010
- **所属子系统**: 财务管理子系统(FMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 成本会计, **I want to** 执行月末成本核算, **so that** 准确计算产品成本和库存成本。
- **As a** 财务主管, **I want to** 分析成本变动趋势, **so that** 制定成本控制策略。
- **As a** 生产经理, **I want to** 查看产品盈利分析, **so that** 优化产品结构和生产计划。
- **As a** 总经理, **I want to** 获得成本分析报告, **so that** 进行经营决策。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 成本要素已归集完成
- 生产数据已确认
- 库存数据已盘点
- 成本核算规则已设置

### 核心流程

#### 2.1 月末成本核算流程
1. 检查成本归集数据完整性
2. 确认生产完工和在产品数量
3. 计算完工产品单位成本
4. 分配成本到完工产品和在产品
5. 结转完工产品成本到库存
6. 生成成本核算报告

#### 2.2 标准成本维护流程
1. 制定产品标准成本
2. 设置材料、人工、费用标准
3. 定期评估标准成本合理性
4. 调整标准成本参数
5. 发布新的标准成本版本
6. 分析标准成本执行情况

#### 2.3 实际成本计算流程
1. 汇总实际发生的各项成本
2. 按成本对象分配实际成本
3. 计算实际单位成本
4. 对比实际成本与标准成本
5. 分析成本差异原因
6. 提出成本改进建议

#### 2.4 成本分析流程
1. 收集成本核算结果数据
2. 进行成本结构分析
3. 计算成本变动趋势
4. 分析成本影响因素
5. 评估成本控制效果
6. 生成成本分析报告

#### 2.5 库存成本调整流程
1. 获取库存盘点结果
2. 计算库存成本差异
3. 分析差异产生原因
4. 制定成本调整方案
5. 执行库存成本调整
6. 更新库存成本数据

### 后置条件
- 产品成本准确计算
- 库存成本及时更新
- 成本分析报告生成
- 成本数据传递到总账

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：成本核算管理页面
### 页面目标：提供完整的成本核算和分析功能

### 信息架构：
- **顶部区域**：包含 核算查询, 执行核算, 标准维护, 成本分析
- **左侧区域**：包含 期间选择, 产品筛选, 核算状态筛选
- **中间区域**：包含 核算任务列表, 核算详情, 分析图表
- **右侧区域**：包含 核算进度, 成本统计, 差异分析

### 交互逻辑与状态：

#### **成本核算设置区域**
- **核算参数：**
  - **核算期间：** 月份选择器，选择核算月份
  - **核算范围：** 多选下拉，选择核算产品范围
  - **核算方法：** 单选按钮，品种法/分批法/分步法
  - **计价方法：** 下拉选择，先进先出/加权平均/标准成本
- **执行设置：**
  - **自动核算：** 复选框，是否自动执行核算
  - **核算时间：** 时间选择器，设置自动核算时间
  - **通知设置：** 复选框，核算完成后是否通知
  - **备份设置：** 复选框，核算前是否备份数据

#### **核算任务列表**
- **任务信息：**
  - **任务编号：** 自动生成的核算任务编号
  - **核算期间：** 显示核算的年月
  - **产品范围：** 显示核算的产品范围
  - **开始时间：** 显示核算开始时间
  - **结束时间：** 显示核算结束时间
  - **执行状态：** 状态标签
  - **操作：** 查看、重新核算、导出等操作
- **状态标识：**
  - **等待中：** 灰色标签，"等待执行"
  - **执行中：** 蓝色标签，"正在核算"
  - **已完成：** 绿色标签，"核算完成"
  - **有错误：** 红色标签，"核算错误"

#### **成本核算执行界面**
- **核算进度：**
  - **总体进度：** 进度条显示整体核算进度
  - **当前步骤：** 显示当前执行的核算步骤
  - **已完成：** 显示已完成的核算步骤
  - **预计时间：** 显示预计完成时间
- **核算步骤：**
  - **数据检查：** 检查基础数据完整性
  - **成本归集：** 归集各项成本要素
  - **成本分配：** 分配成本到产品
  - **单位成本计算：** 计算产品单位成本
  - **库存成本更新：** 更新库存成本
  - **报告生成：** 生成核算报告
- **执行日志：**
  - **执行时间：** 显示步骤执行时间
  - **执行内容：** 显示具体执行内容
  - **执行结果：** 显示执行成功或失败
  - **错误信息：** 显示错误详细信息

#### **产品成本明细**
- **成本构成：**
  - **产品编码：** 显示产品编码
  - **产品名称：** 显示产品名称
  - **生产数量：** 显示生产完工数量
  - **直接材料：** 显示直接材料成本
  - **直接人工：** 显示直接人工成本
  - **制造费用：** 显示制造费用
  - **总成本：** 显示产品总成本
  - **单位成本：** 显示产品单位成本
- **成本对比：**
  - **标准成本：** 显示产品标准成本
  - **实际成本：** 显示产品实际成本
  - **成本差异：** 显示成本差异金额
  - **差异率：** 显示成本差异百分比
- **成本趋势：**
  - **本期成本：** 显示本期产品成本
  - **上期成本：** 显示上期产品成本
  - **环比变化：** 显示环比变化金额和比例
  - **趋势分析：** 显示成本变化趋势

#### **标准成本维护**
- **标准设置：**
  - **产品选择：** 下拉选择要设置标准成本的产品
  - **版本管理：** 显示标准成本版本信息
  - **生效日期：** 设置标准成本生效日期
  - **失效日期：** 设置标准成本失效日期
- **成本要素：**
  - **材料标准：** 设置标准材料用量和价格
  - **人工标准：** 设置标准工时和工资率
  - **费用标准：** 设置标准制造费用率
  - **总标准：** 计算总标准成本
- **标准维护：**
  - **新增标准：** 新增产品标准成本
  - **修改标准：** 修改现有标准成本
  - **复制标准：** 复制其他产品标准
  - **删除标准：** 删除无效标准成本

#### **成本分析图表**
- **成本构成分析：**
  - **饼图：** 显示成本要素构成比例
  - **柱状图：** 显示各要素成本金额
  - **趋势图：** 显示成本变化趋势
  - **对比图：** 显示产品成本对比
- **差异分析：**
  - **材料差异：** 材料价格和用量差异
  - **人工差异：** 人工效率和工资差异
  - **费用差异：** 制造费用预算和效率差异
  - **总差异：** 总成本差异分析
- **盈利分析：**
  - **产品毛利：** 显示产品毛利金额和率
  - **盈利排行：** 显示产品盈利排行
  - **盈利趋势：** 显示盈利变化趋势
  - **盈利预测：** 显示盈利预测分析

#### **成本报表展示**
- **核算汇总表：**
  - **期间汇总：** 显示期间成本汇总
  - **产品汇总：** 显示各产品成本汇总
  - **要素汇总：** 显示成本要素汇总
  - **车间汇总：** 显示各车间成本汇总
- **成本明细表：**
  - **产品明细：** 显示产品成本明细
  - **订单明细：** 显示订单成本明细
  - **工序明细：** 显示工序成本明细
  - **材料明细：** 显示材料成本明细
- **差异分析表：**
  - **标准实际对比：** 标准成本与实际成本对比
  - **差异明细：** 详细差异分析
  - **差异原因：** 差异产生原因分析
  - **改进建议：** 成本改进建议

#### **库存成本调整**
- **调整原因：**
  - **盘点差异：** 库存盘点产生的差异
  - **计价调整：** 成本计价方法调整
  - **错误更正：** 成本计算错误更正
  - **政策调整：** 会计政策变更调整
- **调整明细：**
  - **物料编码：** 需要调整的物料
  - **调整前成本：** 调整前的库存成本
  - **调整金额：** 成本调整金额
  - **调整后成本：** 调整后的库存成本
- **调整审批：**
  - **调整申请：** 提交成本调整申请
  - **审批流程：** 成本调整审批流程
  - **审批结果：** 显示审批结果
  - **执行调整：** 执行成本调整

### 数据校验规则：

#### **核算期间**
- **校验规则：** 核算期间不能为空，不能重复核算同一期间
- **错误提示文案：** "该期间已完成核算，不能重复执行"

#### **标准成本**
- **校验规则：** 标准成本必须大于0，生效日期不能早于当前日期
- **错误提示文案：** "标准成本必须大于0，生效日期不能早于当前日期"

#### **成本调整**
- **校验规则：** 调整金额不能为空，必须有调整原因
- **错误提示文案：** "请填写调整金额和调整原因"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **核算参数**:
  - **核算期间 (period)**: String, 必填, 核算年月
  - **产品范围 (product_range)**: String[], 必填, 核算产品范围
  - **核算方法 (costing_method)**: Enum, 必填, 成本核算方法
  - **计价方法 (pricing_method)**: Enum, 必填, 库存计价方法
- **标准成本**:
  - **产品编码 (product_code)**: String, 必填, 产品编码
  - **标准成本 (standard_cost)**: Decimal, 必填, 标准成本金额
  - **生效日期 (effective_date)**: Date, 必填, 生效日期

### 展示数据
- **核算结果**: 产品成本、单位成本、成本构成
- **差异分析**: 标准成本差异、成本变动分析
- **成本报表**: 各类成本核算报表
- **库存成本**: 库存商品成本信息

### 空状态/零数据
- **无核算数据**: 显示"该期间无核算数据"
- **无生产完工**: 显示"该期间无生产完工"
- **无标准成本**: 显示"该产品未设置标准成本"

### API接口
- **核算执行**: POST /api/costing/execute
- **核算查询**: GET /api/costing/results
- **标准维护**: POST /api/costing/standards
- **成本分析**: GET /api/costing/analysis
- **报表生成**: GET /api/costing/reports

## 5. 异常与边界处理 (Error & Edge Cases)

### **数据不完整**
- **提示信息**: "基础数据不完整，无法执行成本核算"
- **用户操作**: 显示缺失数据清单和补充指导

### **核算失败**
- **提示信息**: "成本核算执行失败，请检查数据"
- **用户操作**: 显示错误日志和重新执行选项

### **标准成本过期**
- **提示信息**: "产品标准成本已过期，请更新"
- **用户操作**: 提供标准成本更新界面

### **成本异常**
- **提示信息**: "检测到异常成本数据，请核实"
- **用户操作**: 显示异常数据和处理建议

### **权限不足**
- **提示信息**: "您没有权限执行成本核算"
- **用户操作**: 显示权限要求和申请流程

## 6. 验收标准 (Acceptance Criteria)

- [ ] 月末成本核算自动准确执行
- [ ] 产品单位成本计算准确
- [ ] 标准成本维护功能完善
- [ ] 成本差异分析清晰明确
- [ ] 库存成本及时准确更新
- [ ] 支持多种成本核算方法
- [ ] 成本核算响应时间<30秒
- [ ] 核算结果与业务数据100%一致
- [ ] 成本报表数据准确完整
- [ ] 所有页面元素符合全局设计规范
- [ ] 异常情况处理机制完善
- [ ] 核算过程可追溯，支持审计
