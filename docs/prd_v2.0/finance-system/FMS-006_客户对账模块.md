# 功能模块规格说明书：客户对账模块

- **模块ID**: FMS-006
- **所属子系统**: 财务管理子系统(FMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 应收会计, **I want to** 自动生成客户对账单, **so that** 与客户进行准确的账务核对。
- **As a** 财务主管, **I want to** 分析对账差异, **so that** 及时发现和解决账务问题。
- **As a** 客户经理, **I want to** 查看客户账务明细, **so that** 了解客户的交易和付款情况。
- **As a** 应收会计, **I want to** 批量生成和发送对账单, **so that** 提高对账工作效率。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 客户档案信息完整
- 应收和收款数据准确
- 用户具有对账权限
- 对账单模板已配置

### 核心流程

#### 2.1 对账单生成流程
1. 选择对账期间和客户范围
2. 系统汇总客户期间内所有交易
3. 计算期初余额、本期发生、期末余额
4. 按对账单模板格式化数据
5. 生成对账单并保存
6. 提供预览和调整功能

#### 2.2 对账差异分析流程
1. 收集客户反馈的对账信息
2. 对比我方记录与客户记录
3. 识别差异项目和差异原因
4. 分类处理不同类型的差异
5. 生成差异分析报告
6. 制定差异处理方案

#### 2.3 对账单发送流程
1. 确认对账单内容准确无误
2. 选择发送方式（邮件/传真/快递）
3. 设置发送时间和提醒机制
4. 批量发送对账单给客户
5. 跟踪发送状态和客户确认
6. 记录对账沟通过程

#### 2.4 对账确认流程
1. 客户收到对账单并核对
2. 客户反馈对账结果和差异
3. 双方协商解决差异问题
4. 确认最终对账结果
5. 更新客户账务状态
6. 归档对账单和确认文件

### 后置条件
- 对账单数据准确生成
- 差异问题得到解决
- 客户账务状态更新
- 对账记录完整保存

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：客户对账管理页面
### 页面目标：提供完整的客户对账单生成和管理功能

### 信息架构：
- **顶部区域**：包含 对账查询, 生成对账单, 批量操作
- **左侧区域**：包含 客户筛选, 期间选择, 状态筛选
- **中间区域**：包含 对账单列表, 对账单详情
- **右侧区域**：包含 差异分析, 发送记录

### 交互逻辑与状态：

#### **对账单生成设置**
- **基础设置：**
  - **对账期间：** 日期范围选择器，支持月度/季度/年度
  - **客户选择：** 多选下拉，支持全选和按条件筛选
  - **对账模板：** 下拉选择，标准模板/自定义模板
  - **币种：** 下拉选择，人民币/美元/欧元等
- **高级设置：**
  - **包含零余额：** 复选框，是否包含余额为零的客户
  - **明细级别：** 下拉选择，汇总/明细/详细明细
  - **排序方式：** 下拉选择，按客户编码/按余额大小
  - **输出格式：** 下拉选择，PDF/Excel/Word

#### **对账单列表区域**
- **列表表头：**
  - **对账单号：** 可排序，点击查看详情
  - **客户名称：** 显示客户全称
  - **对账期间：** 显示对账的起止日期
  - **期初余额：** 右对齐，千分位格式
  - **本期发生：** 右对齐，分借贷显示
  - **期末余额：** 右对齐，加粗显示
  - **生成日期：** 显示对账单生成日期
  - **发送状态：** 状态标签
  - **确认状态：** 状态标签
  - **操作：** 查看、发送、重新生成等操作
- **状态标识：**
  - **未发送：** 灰色标签，"未发送"
  - **已发送：** 蓝色标签，"已发送"
  - **已确认：** 绿色标签，"已确认"
  - **有差异：** 橙色标签，"有差异"
  - **已解决：** 深绿色标签，"已解决"

#### **对账单详情界面**
- **对账单头信息：**
  - **对账单号：** 自动生成的唯一编号
  - **客户信息：** 客户名称、编码、联系方式
  - **对账期间：** 对账的起止日期
  - **生成日期：** 对账单生成日期
  - **制表人：** 对账单制作人员
- **余额信息：**
  - **期初余额：** 期初应收余额
  - **本期销售：** 本期销售发生额
  - **本期收款：** 本期收款发生额
  - **期末余额：** 期末应收余额
- **明细交易：**
  - **交易日期：** 业务发生日期
  - **单据编号：** 相关单据编号
  - **业务类型：** 销售/收款/调整等
  - **摘要说明：** 业务摘要描述
  - **发生金额：** 交易金额，分借贷
  - **累计余额：** 累计应收余额

#### **差异分析界面**
- **差异类型：**
  - **金额差异：** 我方与客户记录金额不一致
  - **时间差异：** 业务确认时间不一致
  - **单据差异：** 单据记录不一致
  - **其他差异：** 其他类型差异
- **差异明细：**
  - **差异项目：** 具体差异的业务项目
  - **我方记录：** 我方系统记录的金额和信息
  - **客户记录：** 客户反馈的金额和信息
  - **差异金额：** 差异的具体金额
  - **差异原因：** 差异产生的原因分析
  - **处理方案：** 差异的处理建议
- **差异处理：**
  - **差异确认：** 确认差异的真实性
  - **调整方案：** 制定差异调整方案
  - **责任认定：** 确定差异责任方
  - **处理结果：** 记录差异处理结果

#### **对账单发送功能**
- **发送方式：**
  - **邮件发送：** 输入客户邮箱地址
  - **传真发送：** 输入客户传真号码
  - **快递发送：** 输入客户邮寄地址
  - **系统消息：** 通过系统内部消息发送
- **发送设置：**
  - **发送时间：** 立即发送/定时发送
  - **抄送人员：** 选择抄送的内部人员
  - **发送备注：** 发送时的备注说明
  - **回执要求：** 是否要求客户回执确认
- **批量发送：**
  - **批量选择：** 选择多个客户的对账单
  - **统一发送：** 批量发送对账单
  - **发送进度：** 显示批量发送进度
  - **发送结果：** 显示发送成功和失败情况

#### **对账单模板设置**
- **模板选择：**
  - **标准模板：** 系统预设的标准对账单格式
  - **行业模板：** 针对特定行业的对账单格式
  - **自定义模板：** 用户自定义的对账单格式
- **模板配置：**
  - **表头信息：** 设置对账单表头显示内容
  - **明细字段：** 选择明细部分显示的字段
  - **汇总信息：** 设置汇总部分显示内容
  - **格式样式：** 设置字体、颜色、边框等样式
- **模板预览：**
  - **实时预览：** 实时显示模板效果
  - **样本数据：** 使用样本数据预览效果
  - **打印预览：** 预览打印效果
  - **保存模板：** 保存自定义模板

#### **对账确认管理**
- **确认状态：**
  - **待确认：** 客户尚未确认对账单
  - **已确认：** 客户已确认对账单无误
  - **有异议：** 客户对对账单有异议
  - **协商中：** 双方正在协商差异
- **确认记录：**
  - **确认时间：** 客户确认的时间
  - **确认方式：** 邮件/电话/传真等确认方式
  - **确认人员：** 客户方确认人员
  - **确认内容：** 确认的具体内容
- **异议处理：**
  - **异议内容：** 客户提出的异议内容
  - **异议分析：** 对异议的分析和判断
  - **处理方案：** 异议的处理方案
  - **处理结果：** 异议处理的最终结果

### 数据校验规则：

#### **对账期间**
- **校验规则：** 开始日期不能大于结束日期，期间不能超过1年
- **错误提示文案：** "对账期间设置有误，请重新选择"

#### **客户选择**
- **校验规则：** 必须选择至少一个有效客户
- **错误提示文案：** "请至少选择一个客户进行对账"

#### **发送信息**
- **校验规则：** 邮箱格式正确，传真号码格式正确
- **错误提示文案：** "请输入正确的邮箱地址或传真号码"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **对账设置**:
  - **对账期间 (period_range)**: DateRange, 必填, 对账时间范围
  - **客户列表 (customer_list)**: String[], 必填, 对账客户列表
  - **模板类型 (template_type)**: String, 必填, 对账单模板
  - **明细级别 (detail_level)**: Enum, 必填, 明细程度

### 展示数据
- **对账单列表**: 对账单号、客户、期间、余额、状态
- **对账明细**: 期初余额、本期发生、期末余额、交易明细
- **差异分析**: 差异类型、差异金额、处理状态
- **发送记录**: 发送时间、发送方式、发送状态

### 空状态/零数据
- **无对账数据**: 显示"该期间内无对账数据"
- **无交易记录**: 显示"该客户在对账期间内无交易记录"
- **无差异**: 显示"对账无差异"

### API接口
- **对账单生成**: POST /api/statements/generate
- **对账单查询**: GET /api/statements
- **差异分析**: GET /api/statements/differences
- **对账单发送**: POST /api/statements/send
- **确认状态**: PUT /api/statements/confirm

## 5. 异常与边界处理 (Error & Edge Cases)

### **生成失败**
- **提示信息**: "对账单生成失败，请检查数据完整性"
- **用户操作**: 提供数据检查和重新生成选项

### **发送失败**
- **提示信息**: "对账单发送失败，请检查联系方式"
- **用户操作**: 提供联系方式确认和重新发送选项

### **差异过大**
- **提示信息**: "检测到较大差异，建议人工核实"
- **用户操作**: 提供差异明细和核实建议

### **模板错误**
- **提示信息**: "对账单模板配置错误，请联系管理员"
- **用户操作**: 提供默认模板和技术支持联系方式

### **权限不足**
- **提示信息**: "您没有权限查看该客户的对账信息"
- **用户操作**: 显示所需权限和申请流程

## 6. 验收标准 (Acceptance Criteria)

- [ ] 对账单生成准确完整
- [ ] 支持多种对账单格式
- [ ] 差异分析清晰明确
- [ ] 支持批量生成和发送
- [ ] 对账单响应时间<5秒
- [ ] 发送成功率≥95%
- [ ] 支持多种发送方式
- [ ] 差异处理流程完善
- [ ] 对账确认机制健全
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持自定义对账单模板
- [ ] 对账记录完整，支持审计追踪
