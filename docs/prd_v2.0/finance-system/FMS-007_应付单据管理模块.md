# 功能模块规格说明书：应付单据管理模块

- **模块ID**: FMS-007
- **所属子系统**: 财务管理子系统(FMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 应付会计, **I want to** 系统自动生成暂估应付, **so that** 及时准确记录供应商欠款。
- **As a** 应付会计, **I want to** 进行三单匹配核对, **so that** 确保采购、入库、发票信息一致。
- **As a** 财务主管, **I want to** 分析供应商账龄结构, **so that** 优化付款计划和现金流管理。
- **As a** 采购人员, **I want to** 查看供应商应付余额, **so that** 了解供应商的付款情况。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 供应商档案已建立
- 采购订单已审核
- 用户具有应付管理权限
- 付款条件已设置

### 核心流程

#### 2.1 暂估应付生成流程
1. 采购入库单审核通过
2. 系统自动创建暂估应付单据
3. 提取供应商、物料、金额等信息
4. 根据采购合同确定付款条件
5. 计算应付金额和到期日期
6. 暂估应付进入待确认状态
7. 财务人员确认后正式生效

#### 2.2 发票三单匹配流程
1. 收到供应商发票并录入系统
2. 系统自动匹配采购订单
3. 匹配对应的入库单据
4. 核对数量、单价、金额一致性
5. 处理差异并确认匹配结果
6. 生成正式应付单据
7. 冲销原暂估应付

#### 2.3 应付账龄分析流程
1. 设置分析期间和供应商范围
2. 系统按应付单据到期日计算账龄
3. 按30天、60天、90天等区间分组
4. 统计各账龄区间的应付金额
5. 分析付款优先级和现金需求
6. 生成账龄分析报表
7. 提供付款建议和资金计划

#### 2.4 应付单据维护流程
1. 查询需要维护的应付单据
2. 检查单据状态和修改权限
3. 修改允许变更的字段信息
4. 重新计算相关金额和日期
5. 保存修改并记录变更日志
6. 更新供应商应付余额
7. 触发相关业务流程

### 后置条件
- 应付单据数据准确保存
- 供应商应付余额正确更新
- 三单匹配关系清晰
- 账龄分析数据及时更新

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：应付单据管理页面
### 页面目标：提供完整的应付单据管理和三单匹配功能

### 信息架构：
- **顶部区域**：包含 应付查询, 新增应付, 发票录入, 三单匹配
- **左侧区域**：包含 供应商筛选, 状态筛选, 账龄筛选
- **中间区域**：包含 应付单据列表, 单据详情
- **右侧区域**：包含 供应商信息, 匹配状态, 账龄分析

### 交互逻辑与状态：

#### **应付查询区域**
- **基础查询：**
  - **供应商名称：** 输入框，支持模糊搜索
  - **单据日期：** 日期范围选择器
  - **单据状态：** 下拉多选，暂估/已确认/部分付款/已付款
  - **采购员：** 下拉选择，显示所有采购人员
- **高级查询：**
  - **应付金额：** 数字范围输入
  - **到期日期：** 日期范围选择器
  - **账龄范围：** 下拉选择账龄区间
  - **物料类别：** 下拉选择物料分类

#### **应付单据列表**
- **列表表头：**
  - **单据编号：** 可排序，点击查看详情
  - **供应商名称：** 显示供应商全称
  - **单据日期：** 可排序，YYYY-MM-DD格式
  - **应付金额：** 右对齐，千分位格式，红色字体
  - **已付金额：** 右对齐，绿色字体
  - **未付金额：** 右对齐，橙色字体
  - **到期日期：** 可排序，逾期红色显示
  - **账龄天数：** 显示距到期日天数
  - **匹配状态：** 状态标签
  - **单据状态：** 状态标签
  - **操作：** 查看、编辑、付款、匹配等操作
- **状态标识：**
  - **暂估应付：** 橙色标签，"暂估"
  - **已确认：** 蓝色标签，"已确认"
  - **部分付款：** 黄色标签，"部分付款"
  - **已付款：** 绿色标签，"已付款"

#### **发票录入界面**
- **发票信息：**
  - **供应商选择：** 下拉搜索选择器
  - **发票号码：** 输入框，必填
  - **发票日期：** 日期选择器
  - **发票类型：** 下拉选择，增值税专票/普票
- **发票明细：**
  - **物料编码：** 输入框或选择器
  - **物料名称：** 自动带出或手动输入
  - **规格型号：** 输入框
  - **单位：** 下拉选择
  - **数量：** 数字输入框
  - **单价：** 数字输入框
  - **金额：** 自动计算
  - **税率：** 下拉选择
  - **税额：** 自动计算
- **发票汇总：**
  - **不含税金额：** 自动汇总
  - **税额合计：** 自动汇总
  - **价税合计：** 自动汇总
  - **大写金额：** 自动转换

#### **三单匹配界面**
- **匹配规则：**
  - **自动匹配：** 按钮，"智能匹配"
  - **匹配条件：** 供应商+物料+数量+单价
  - **容差设置：** 设置匹配容差范围
  - **匹配结果：** 显示匹配成功和失败情况
- **采购订单信息：**
  - **订单编号：** 显示采购订单编号
  - **订单日期：** 显示采购订单日期
  - **物料信息：** 显示物料编码、名称、规格
  - **订单数量：** 显示采购订单数量
  - **订单单价：** 显示采购订单单价
  - **订单金额：** 显示采购订单金额
- **入库单信息：**
  - **入库单号：** 显示入库单编号
  - **入库日期：** 显示入库日期
  - **入库数量：** 显示实际入库数量
  - **入库单价：** 显示入库单价
  - **入库金额：** 显示入库金额
- **发票信息：**
  - **发票号码：** 显示发票号码
  - **发票日期：** 显示发票日期
  - **发票数量：** 显示发票数量
  - **发票单价：** 显示发票单价
  - **发票金额：** 显示发票金额

#### **差异处理界面**
- **差异类型：**
  - **数量差异：** 订单、入库、发票数量不一致
  - **单价差异：** 订单、入库、发票单价不一致
  - **金额差异：** 计算金额与发票金额不一致
  - **税率差异：** 税率设置与发票税率不一致
- **差异明细：**
  - **差异项目：** 具体差异的字段
  - **订单数据：** 采购订单中的数据
  - **入库数据：** 入库单中的数据
  - **发票数据：** 发票中的数据
  - **差异金额：** 差异的具体金额
- **差异处理：**
  - **差异原因：** 下拉选择差异原因
  - **处理方式：** 下拉选择处理方式
  - **调整金额：** 输入调整金额
  - **处理说明：** 文本框，详细说明

#### **供应商信息面板**
- **基本信息：**
  - **供应商名称：** 显示供应商全称
  - **供应商编码：** 显示供应商编码
  - **联系人：** 显示主要联系人
  - **联系电话：** 显示联系电话
- **付款信息：**
  - **付款条件：** 显示付款条件
  - **付款方式：** 显示付款方式
  - **银行账户：** 显示供应商银行账户
  - **开户行：** 显示开户银行
- **应付统计：**
  - **应付总额：** 显示供应商应付总金额
  - **逾期金额：** 显示逾期应付金额
  - **最大账龄：** 显示最长账龄天数
  - **平均账龄：** 显示平均账龄天数

#### **账龄分析图表**
- **账龄分布：**
  - **30天内：** 绿色柱状图
  - **31-60天：** 黄色柱状图
  - **61-90天：** 橙色柱状图
  - **90天以上：** 红色柱状图
- **付款计划：**
  - **本周到期：** 显示本周到期应付金额
  - **本月到期：** 显示本月到期应付金额
  - **下月到期：** 显示下月到期应付金额
  - **资金需求：** 显示未来资金需求

#### **批量操作功能**
- **批量选择：**
  - **全选：** 复选框，选择当前页所有应付
  - **条件选择：** 按供应商、到期日等条件选择
  - **选择统计：** 显示已选择的应付数量和金额
- **批量操作：**
  - **批量付款：** 对选中应付进行批量付款
  - **批量匹配：** 批量进行三单匹配
  - **批量导出：** 导出选中应付的详细信息
  - **批量确认：** 批量确认暂估应付

### 数据校验规则：

#### **供应商选择**
- **校验规则：** 必须选择有效的供应商，供应商状态必须为启用
- **错误提示文案：** "请选择有效的供应商"

#### **应付金额**
- **校验规则：** 必须大于0，小数位不超过2位
- **错误提示文案：** "应付金额必须大于0"

#### **三单匹配**
- **校验规则：** 供应商、物料必须一致，数量和金额在容差范围内
- **错误提示文案：** "三单匹配失败，请检查供应商、物料、数量、金额是否一致"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **应付单据信息**:
  - **供应商编码 (supplier_code)**: String, 必填, 引用供应商档案
  - **单据日期 (bill_date)**: Date, 必填, 应付发生日期
  - **应付金额 (payable_amount)**: Decimal, 必填, 应付金额
  - **到期日期 (due_date)**: Date, 必填, 应付到期日期
- **发票信息**:
  - **发票号码 (invoice_no)**: String, 必填, 发票号码
  - **发票日期 (invoice_date)**: Date, 必填, 发票日期
  - **发票金额 (invoice_amount)**: Decimal, 必填, 发票金额

### 展示数据
- **应付列表**: 单据编号、供应商、金额、状态、账龄等
- **供应商信息**: 基本信息、付款条件、应付统计
- **匹配状态**: 三单匹配结果和差异信息
- **账龄分析**: 各账龄区间金额分布

### 空状态/零数据
- **无应付数据**: 显示"暂无应付数据"
- **无匹配结果**: 显示"未找到匹配的单据"
- **供应商无应付**: 显示"该供应商暂无应付款项"

### API接口
- **应付查询**: GET /api/payables
- **应付新增**: POST /api/payables
- **发票录入**: POST /api/invoices
- **三单匹配**: POST /api/matching
- **账龄分析**: GET /api/payables/aging

## 5. 异常与边界处理 (Error & Edge Cases)

### **三单匹配失败**
- **提示信息**: "三单匹配失败，存在差异需要处理"
- **用户操作**: 显示差异明细和处理建议

### **发票重复录入**
- **提示信息**: "该发票号码已存在，请勿重复录入"
- **用户操作**: 显示已存在的发票信息

### **供应商状态异常**
- **提示信息**: "供应商状态异常，无法新增应付"
- **用户操作**: 显示供应商状态和处理建议

### **数据同步延迟**
- **提示信息**: "数据正在同步中，请稍后刷新"
- **用户操作**: 提供手动刷新和自动刷新机制

### **匹配容差超限**
- **提示信息**: "差异超出容差范围，需要人工处理"
- **用户操作**: 提供差异处理界面和审批流程

## 6. 验收标准 (Acceptance Criteria)

- [ ] 采购入库后自动生成暂估应付
- [ ] 发票与入库单三单匹配准确
- [ ] 应付单据信息准确完整
- [ ] 供应商账龄分析及时准确
- [ ] 三单匹配成功率≥95%
- [ ] 差异处理流程完善
- [ ] 应付数据与采购数据100%一致
- [ ] 支持批量操作，提高工作效率
- [ ] 应付查询响应时间<2秒
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持多种发票类型处理
- [ ] 账龄计算准确，支持付款计划
