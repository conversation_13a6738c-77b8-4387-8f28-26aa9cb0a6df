# 功能模块规格说明书：客户档案管理模块

- **模块ID**: CRM-001
- **所属子系统**: 客户关系管理子系统(CRM)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 销售人员, **I want to** 维护完整的客户档案, **so that** 建立统一的客户信息库。
- **As a** 销售人员, **I want to** 管理客户的多个联系人, **so that** 与不同部门的人员建立联系。
- **As a** 销售经理, **I want to** 查看客户的完整信息, **so that** 了解客户背景和业务潜力。
- **As a** 客服人员, **I want to** 快速查找客户信息, **so that** 为客户提供准确的服务支持。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户已登录系统
- 用户具有客户管理权限
- 客户信息模板已配置
- 数据字典已建立

### 核心流程

#### 2.1 客户档案创建流程
1. 销售人员点击"新建客户"按钮
2. 填写客户基本信息（公司名称、行业、规模等）
3. 系统自动检查客户重复性
4. 如无重复，保存客户基本档案
5. 添加主要联系人信息
6. 设置客户分类和标签
7. 完成客户档案创建

#### 2.2 联系人管理流程
1. 在客户详情页点击"添加联系人"
2. 填写联系人基本信息（姓名、职位、联系方式）
3. 设置联系人角色和重要程度
4. 关联联系人到具体部门
5. 保存联系人信息
6. 更新客户档案的联系人列表

#### 2.3 客户信息维护流程
1. 搜索并打开目标客户档案
2. 编辑需要更新的信息字段
3. 系统记录修改历史和操作人
4. 验证信息的完整性和准确性
5. 保存更新后的客户信息
6. 通知相关人员信息变更

### 后置条件
- 客户档案信息完整准确
- 联系人信息关联正确
- 操作历史完整记录
- 相关人员收到变更通知

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：客户档案管理页面
### 页面目标：提供客户信息的创建、查看、编辑和管理功能

### 信息架构：
- **顶部区域**：包含 搜索框, 筛选器, 新建客户按钮, 批量操作
- **左侧区域**：包含 客户分类树, 标签筛选, 我的客户, 公海客户
- **中间区域**：包含 客户列表, 客户详情, 联系人列表
- **右侧区域**：包含 快速操作, 相关信息, 操作历史

### 交互逻辑与状态：

#### **客户列表区域**
- **客户信息卡片：**
  - **客户名称：** 显示公司全称，点击进入详情页
  - **行业标签：** 彩色标签显示所属行业
  - **客户等级：** 星级显示客户重要程度
  - **联系人数量：** 显示关联的联系人数量
  - **最后跟进：** 显示最近一次跟进时间
  - **归属销售：** 显示负责的销售人员
- **列表操作：**
  - **排序功能：** 支持按创建时间、跟进时间、客户等级排序
  - **筛选功能：** 支持按行业、地区、客户等级、归属人筛选
  - **搜索功能：** 支持客户名称、联系人姓名的模糊搜索
  - **批量操作：** 支持批量分配、批量标签、批量导出

#### **客户详情界面**
- **基本信息区域：**
  - **公司信息：**
    - **公司名称：** 输入框，必填，最大100字符
    - **公司简称：** 输入框，可选，最大50字符
    - **统一社会信用代码：** 输入框，18位字符，格式验证
    - **所属行业：** 下拉选择，引用行业字典
    - **公司规模：** 下拉选择，微型/小型/中型/大型
    - **成立时间：** 日期选择器，不能晚于当前日期
  - **联系信息：**
    - **注册地址：** 文本域，详细地址信息
    - **办公地址：** 文本域，可与注册地址相同
    - **公司电话：** 输入框，电话格式验证
    - **公司传真：** 输入框，可选
    - **公司网站：** 输入框，URL格式验证
    - **公司邮箱：** 输入框，邮箱格式验证
- **业务信息区域：**
  - **客户来源：** 下拉选择，网络推广/电话营销/展会/转介绍等
  - **客户等级：** 单选按钮，A级(重要)/B级(一般)/C级(潜在)
  - **信用等级：** 下拉选择，优秀/良好/一般/较差
  - **年营业额：** 数字输入框，单位万元
  - **员工人数：** 数字输入框，正整数
  - **主营业务：** 文本域，描述客户主要业务
- **标签和分类：**
  - **客户标签：** 标签选择器，支持多选和自定义标签
  - **客户分类：** 下拉选择，战略客户/重点客户/普通客户
  - **所属区域：** 下拉选择，按地理区域分类
  - **负责销售：** 下拉选择，选择归属的销售人员

#### **联系人管理界面**
- **联系人列表：**
  - **联系人信息：**
    - **姓名：** 显示联系人姓名
    - **职位：** 显示在公司的职位
    - **部门：** 显示所属部门
    - **手机号：** 显示手机联系方式
    - **邮箱：** 显示邮箱地址
    - **重要程度：** 星级显示重要程度
  - **联系人操作：**
    - **编辑：** 修改联系人信息
    - **删除：** 删除联系人记录
    - **设为主联系人：** 设置为主要联系人
    - **发送邮件：** 直接发送邮件
    - **拨打电话：** 调用电话功能
- **新增联系人：**
  - **基本信息：**
    - **姓名：** 输入框，必填，最大50字符
    - **性别：** 单选按钮，男/女
    - **职位：** 输入框，必填，最大50字符
    - **部门：** 输入框，可选，最大50字符
  - **联系方式：**
    - **手机号：** 输入框，必填，11位数字验证
    - **座机号：** 输入框，可选，电话格式验证
    - **邮箱：** 输入框，必填，邮箱格式验证
    - **微信号：** 输入框，可选
    - **QQ号：** 输入框，可选，数字验证
  - **其他信息：**
    - **生日：** 日期选择器，可选
    - **爱好：** 文本域，可选
    - **备注：** 文本域，可选，最大500字符
    - **重要程度：** 星级选择，1-5星

#### **客户搜索功能**
- **快速搜索：**
  - **搜索框：** 支持客户名称、联系人姓名的模糊搜索
  - **搜索建议：** 输入时显示搜索建议
  - **搜索历史：** 保存最近的搜索记录
  - **清空搜索：** 一键清空搜索条件
- **高级搜索：**
  - **多条件组合：** 支持多个条件的AND/OR组合
  - **精确匹配：** 支持精确匹配和模糊匹配
  - **范围搜索：** 支持数值和日期范围搜索
  - **保存搜索：** 保存常用的搜索条件

#### **客户重复检查**
- **自动检查：**
  - **公司名称检查：** 检查公司名称的相似度
  - **联系方式检查：** 检查电话、邮箱的重复
  - **地址检查：** 检查注册地址的相似性
  - **智能提醒：** 发现疑似重复时智能提醒
- **重复处理：**
  - **重复提醒：** 显示疑似重复的客户列表
  - **信息对比：** 并列显示重复客户的信息
  - **合并客户：** 提供客户信息合并功能
  - **忽略重复：** 确认不是重复客户

#### **客户分类管理**
- **分类树结构：**
  - **行业分类：** 按行业类型分类客户
  - **地区分类：** 按地理区域分类客户
  - **等级分类：** 按客户重要程度分类
  - **自定义分类：** 支持用户自定义分类
- **分类操作：**
  - **创建分类：** 创建新的客户分类
  - **编辑分类：** 修改分类名称和描述
  - **删除分类：** 删除空的分类节点
  - **移动客户：** 在分类间移动客户

#### **客户标签系统**
- **标签管理：**
  - **预设标签：** 系统预设的常用标签
  - **自定义标签：** 用户自定义的个性化标签
  - **标签颜色：** 为不同标签设置不同颜色
  - **标签统计：** 统计各标签的使用情况
- **标签操作：**
  - **添加标签：** 为客户添加相关标签
  - **移除标签：** 移除不相关的标签
  - **批量标签：** 批量为客户添加或移除标签
  - **标签筛选：** 按标签筛选客户列表

### 数据校验规则：

#### **公司名称**
- **校验规则：** 必填，2-100字符，不能包含特殊符号
- **错误提示文案：** "请输入2-100字符的公司名称，不能包含特殊符号"

#### **统一社会信用代码**
- **校验规则：** 18位字符，符合国家标准格式
- **错误提示文案：** "请输入正确的18位统一社会信用代码"

#### **联系人手机号**
- **校验规则：** 11位数字，以1开头
- **错误提示文案：** "请输入正确的11位手机号码"

#### **邮箱地址**
- **校验规则：** 符合邮箱格式，包含@和域名
- **错误提示文案：** "请输入正确的邮箱地址"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **客户基本信息**:
  - **公司名称 (company_name)**: String, 必填, 最大100字符
  - **统一社会信用代码 (credit_code)**: String, 可选, 18字符
  - **所属行业 (industry)**: String, 必填, 引用行业字典
  - **公司规模 (company_size)**: Enum, 必填, 微型/小型/中型/大型
- **联系人信息**:
  - **姓名 (contact_name)**: String, 必填, 最大50字符
  - **手机号 (mobile_phone)**: String, 必填, 11位数字
  - **邮箱 (email)**: String, 必填, 邮箱格式
  - **职位 (position)**: String, 必填, 最大50字符

### 展示数据
- **客户列表**: 客户基本信息和统计数据
- **客户详情**: 完整的客户档案信息
- **联系人列表**: 客户关联的所有联系人
- **操作历史**: 客户信息的修改记录

### 空状态/零数据
- **无客户**: 显示"暂无客户数据，点击新建客户开始"
- **无联系人**: 显示"暂无联系人，点击添加联系人"
- **无搜索结果**: 显示"未找到匹配的客户，请调整搜索条件"

### API接口
- **客户查询**: GET /api/crm/customers
- **客户创建**: POST /api/crm/customers
- **客户更新**: PUT /api/crm/customers/{id}
- **联系人管理**: POST /api/crm/customers/{id}/contacts
- **重复检查**: POST /api/crm/customers/duplicate-check

## 5. 异常与边界处理 (Error & Edge Cases)

### **客户重复创建**
- **提示信息**: "检测到疑似重复客户，请确认是否继续创建"
- **用户操作**: 显示疑似重复的客户信息，提供合并或继续创建选项

### **联系人信息冲突**
- **提示信息**: "该手机号已被其他联系人使用，请确认信息准确性"
- **用户操作**: 显示冲突的联系人信息，提供修改或确认选项

### **权限不足**
- **提示信息**: "您没有权限编辑此客户信息"
- **用户操作**: 显示只读模式，提供权限申请入口

### **数据保存失败**
- **提示信息**: "数据保存失败，请检查网络连接或稍后重试"
- **用户操作**: 提供重试按钮和数据恢复选项

### **必填字段缺失**
- **提示信息**: "请完善必填信息后再保存"
- **用户操作**: 高亮显示缺失的必填字段

## 6. 验收标准 (Acceptance Criteria)

- [ ] 用户可以成功创建客户档案，包含完整的基本信息
- [ ] 系统能够自动检测客户重复，准确率≥95%
- [ ] 联系人管理功能完整，支持多联系人关联
- [ ] 客户搜索响应时间<2秒，搜索准确率≥98%
- [ ] 客户分类和标签功能正常，支持灵活分类
- [ ] 数据校验规则严格，错误提示友好明确
- [ ] 支持批量操作，提高工作效率
- [ ] 所有页面元素符合全局设计规范
- [ ] 移动端适配良好，核心功能可用
- [ ] 数据安全可靠，操作历史完整记录
