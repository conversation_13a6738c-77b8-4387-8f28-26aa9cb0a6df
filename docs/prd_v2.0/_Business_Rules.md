# 核心业务规则库 (Core Business Rules)

> **版本**: 1.0  
> **最后更新**: 2025-07-30  
> **适用范围**: 玻璃深加工行业ERP系统所有子系统  
> **维护责任**: 产品团队

---

## 使用说明

本文档定义了跨系统的核心业务规则，所有PRD文档应引用这些规则而非重复定义。

---

## 1. 用户权限与安全规则

### R1.1 基于角色的权限控制 (RBAC)
- 所有系统功能必须基于用户角色进行权限控制
- 用户只能访问其角色权限范围内的功能和数据
- 权限变更必须经过系统管理员审批
- 敏感操作必须记录操作日志

### R1.2 数据访问权限
- 用户只能查看和操作其职责范围内的数据
- 跨部门数据访问需要特殊授权
- 财务数据访问需要额外的安全验证
- 客户敏感信息访问需要记录访问日志

---

## 2. 订单与生产流程规则

### R2.1 订单状态流转规则
```
草稿 → 待审核 → 已确认 → 生产中 → 已完工 → 已发货 → 已收款
```
- 订单状态只能按照上述顺序正向流转
- 特殊情况下可以回退到上一状态（需要审批）
- 已发货订单不允许修改订单内容
- 订单取消需要特殊权限和审批流程

### R2.2 BOM固化规则
- 销售BOM确认后自动生成工艺BOM
- 工艺BOM经工艺工程师审核后固化为生产BOM
- 生产BOM一旦下达生产订单后不允许修改
- BOM变更需要走正式的工程变更流程

### R2.3 生产优先级规则
- 紧急订单优先级最高
- 交期相同的订单按下单时间排序
- 同一客户的订单可以合并排产
- 设备维护时间不能安排生产任务

---

## 3. 库存与物料管理规则

### R3.1 库存安全规则
- 所有物料必须设置安全库存下限
- 库存低于安全库存时自动触发采购建议
- 危险品和贵重物料需要特殊存储管理
- 库存盘点必须定期进行（至少月度一次）

### R3.2 出入库规则
- 所有出入库操作必须有对应的业务单据
- 出库必须遵循先进先出(FIFO)原则
- 特殊物料（如玻璃原片）需要按批次管理
- 出入库操作必须经过质量检验确认

### R3.3 物料编码规则
```
物料编码格式：[类别码(2位)][材质码(2位)][规格码(4位)][流水号(4位)]
示例：GL01-1200-0001 (玻璃-普通-1200mm-流水号0001)
```

---

## 4. 质量管理规则

### R4.1 三检制度
- IQC：所有采购物料必须经过来料检验
- IPQC：关键工序必须进行过程检验
- FQC：所有完工产品必须经过最终检验
- 不合格品必须隔离存放并标识

### R4.2 质量追溯规则
- 所有产品必须建立完整的质量档案
- 质量问题必须能够追溯到具体批次和责任人
- 客户投诉必须在24小时内响应
- 质量改进措施必须形成闭环管理

---

## 5. 财务与成本规则

### R5.1 业财一体化规则
- 所有业务操作必须实时触发财务记录
- 成本核算必须精确到产品级别
- 应收应付必须与业务单据保持一致
- 财务数据必须支持多维度分析

### R5.2 成本计算规则
```
产品成本 = 直接材料成本 + 直接人工成本 + 制造费用
- 直接材料成本：按实际消耗量×标准单价计算
- 直接人工成本：按工时或计件数量计算
- 制造费用：按预设分摊比例计算
```

### R5.3 价格管理规则
- 客户价格必须基于成本加成定价
- 特殊价格需要销售经理以上权限审批
- 价格变更必须提前通知客户
- 历史价格必须保留完整记录

---

## 6. 项目管理规则

### R6.1 项目层级结构
```
项目 → 子项目 → 工作包 → 任务
- 项目：整体工程项目（如某酒店隔断项目）
- 子项目：按楼栋或区域划分
- 工作包：按产品类型划分
- 任务：具体的生产或安装任务
```

### R6.2 项目交付规则
- 项目必须按阶段进行交付验收
- 每个阶段完成后才能进入下一阶段
- 项目变更必须经过客户确认
- 项目完工必须经过客户最终验收

---

## 7. 数据集成规则

### R7.1 主数据管理规则
- 客户、供应商、物料等主数据必须统一管理
- 主数据变更必须经过审批流程
- 主数据必须保持各系统间的一致性
- 废弃的主数据不能删除，只能标记为无效

### R7.2 数据同步规则
- 关键业务数据必须实时同步
- 数据同步失败必须有告警机制
- 数据冲突必须有解决机制
- 数据备份必须定期进行

---

## 8. 系统集成规则

### R8.1 接口调用规则
- 所有系统间调用必须通过标准API
- API调用必须有身份验证和权限控制
- API调用必须有日志记录
- API异常必须有重试机制

### R8.2 数据流转规则
- 数据流转必须遵循既定的业务流程
- 关键节点必须有人工确认
- 异常情况必须有处理机制
- 数据流转必须有完整的审计轨迹

---

## 版本控制

| 版本 | 日期 | 变更说明 | 责任人 |
|------|------|----------|--------|
| 1.0 | 2025-07-30 | 初始版本，建立核心业务规则体系 | 产品团队 |

---

## 维护说明

1. **规则新增**: 必须经过业务部门和技术团队联合评审
2. **规则修改**: 需要评估对现有系统的影响
3. **规则废弃**: 必须提供替代方案和迁移计划
4. **定期审查**: 每半年进行一次业务规则审查和更新
