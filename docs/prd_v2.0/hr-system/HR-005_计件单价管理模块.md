# 功能模块规格说明书：计件单价管理模块

- **模块ID**: HR-005
- **所属子系统**: 人事管理子系统(HR)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** HR专员, **I want to** 管理计件工序单价, **so that** 为计件薪酬计算提供准确的价格基础。
- **As a** 工艺工程师, **I want to** 设定工序标准工时和单价, **so that** 确保计件价格的合理性和公平性。
- **As a** 成本会计, **I want to** 审核计件单价变更, **so that** 控制人工成本和价格风险。
- **As a** 车间主管, **I want to** 查看工序单价信息, **so that** 了解生产成本和员工收入构成。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 产品工艺已定义
- 工序信息已建立
- 成本核算规则已配置
- 审批流程已设定

### 核心流程

#### 2.1 计件单价设定流程
1. 工艺工程师分析产品工序要求
2. 测算工序标准工时和难度系数
3. 计算工序基础单价
4. 考虑市场因素和成本控制要求
5. 设定工序计件单价
6. 提交单价审批申请
7. 成本会计审核单价合理性
8. HR专员确认单价可执行性
9. 审批通过后单价生效

#### 2.2 单价调整流程
1. 发现单价需要调整的情况
2. 分析调整原因和影响范围
3. 计算新的单价标准
4. 提交单价调整申请
5. 相关部门审核调整合理性
6. 评估调整对成本的影响
7. 审批通过后执行调整
8. 通知相关人员单价变更
9. 更新系统单价数据

#### 2.3 单价生效管理流程
1. 确定单价生效时间
2. 系统自动切换单价版本
3. 通知相关部门单价变更
4. 更新MES系统单价数据
5. 验证单价数据同步正确性
6. 监控单价执行效果
7. 收集单价执行反馈
8. 必要时进行单价微调

### 后置条件
- 计件单价数据准确
- 单价变更记录完整
- 相关系统数据同步
- 成本控制目标达成

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：计件单价管理页面
### 页面目标：提供计件工序单价的设定、调整、审批和管理功能

### 信息架构：
- **顶部区域**：包含 产品筛选, 工序筛选, 单价版本, 批量操作
- **左侧区域**：包含 产品分类, 工序分类, 单价状态, 快速筛选
- **中间区域**：包含 单价列表, 单价详情, 调整记录
- **右侧区域**：包含 成本分析, 单价统计, 操作面板

### 交互逻辑与状态：

#### **计件单价列表界面**
- **单价信息展示：**
  - **单价卡片：**
    - **工序信息：** 显示工序名称、编码、产品类型
    - **当前单价：** 显示现行的计件单价
    - **标准工时：** 显示工序的标准工时
    - **难度系数：** 显示工序的难度系数
    - **生效时间：** 显示单价的生效时间
    - **状态标识：** 生效/待生效/已失效
  - **单价对比：**
    - **历史单价：** 显示历史单价变化趋势
    - **同类对比：** 对比同类工序的单价水平
    - **成本占比：** 显示单价在总成本中的占比
- **筛选和搜索：**
  - **产品筛选：** 按产品类型、规格筛选工序
  - **工序筛选：** 按工序类型、难度筛选
  - **状态筛选：** 按单价状态筛选
  - **关键词搜索：** 支持工序名称、编码搜索

#### **单价设定界面**
- **工序基本信息：**
  - **工序选择：**
    - **产品类型：** 下拉选择，选择产品类型
    - **工序名称：** 下拉选择，选择具体工序
    - **工序编码：** 显示框，自动显示工序编码
    - **工序描述：** 文本域，工序的详细描述
  - **工艺参数：**
    - **标准工时：** 数字输入框，工序标准工时（分钟）
    - **难度系数：** 数字输入框，工序难度系数（0.5-2.0）
    - **质量要求：** 下拉选择，A级/B级/C级质量要求
    - **技能要求：** 下拉选择，初级/中级/高级技能要求
- **单价计算：**
  - **基础单价：**
    - **基准工资：** 数字输入框，基准小时工资
    - **工时单价：** 显示框，根据标准工时计算
    - **难度调整：** 显示框，根据难度系数调整
    - **基础单价：** 显示框，计算得出的基础单价
  - **调整因子：**
    - **市场因子：** 数字输入框，市场供需调整系数
    - **成本因子：** 数字输入框，成本控制调整系数
    - **质量因子：** 数字输入框，质量要求调整系数
    - **技能因子：** 数字输入框，技能要求调整系数
  - **最终单价：**
    - **调整后单价：** 显示框，应用调整因子后的单价
    - **单价区间：** 显示框，建议的单价区间
    - **最终确认单价：** 数字输入框，最终确认的单价
- **生效设置：**
  - **生效日期：** 日期选择器，单价开始生效的日期
  - **失效日期：** 日期选择器，单价失效的日期（可选）
  - **适用范围：** 多选框，适用的车间、班组范围
  - **特殊说明：** 文本域，单价的特殊说明

#### **单价调整界面**
- **调整申请：**
  - **调整原因：**
    - **调整类型：** 单选按钮，定期调整/临时调整/紧急调整
    - **调整原因：** 下拉选择，成本变化/市场变化/工艺改进等
    - **详细说明：** 文本域，调整的详细原因说明
  - **调整方案：**
    - **当前单价：** 显示框，显示当前的单价
    - **调整单价：** 数字输入框，调整后的新单价
    - **调整幅度：** 显示框，自动计算调整幅度
    - **调整依据：** 文本域，调整的具体依据
- **影响分析：**
  - **成本影响：**
    - **月度影响：** 显示框，对月度成本的影响
    - **年度影响：** 显示框，对年度成本的影响
    - **影响人数：** 显示框，受影响的员工人数
  - **收入影响：**
    - **员工收入变化：** 显示框，对员工收入的影响
    - **部门收入变化：** 显示框，对部门收入的影响
    - **激励效果分析：** 文本域，对员工激励的影响分析

#### **单价审批界面**
- **审批信息查看：**
  - **申请详情：** 完整显示单价设定或调整的申请信息
  - **计算过程：** 显示单价计算的详细过程
  - **影响分析：** 显示单价变更的影响分析
  - **历史对比：** 对比历史单价和变化趋势
- **审批操作：**
  - **审批意见：**
    - **审批结果：** 单选按钮，同意/拒绝/退回修改
    - **审批意见：** 文本域，详细的审批意见
    - **调整建议：** 文本域，对单价的调整建议
  - **审批条件：**
    - **生效条件：** 文本域，单价生效的附加条件
    - **监控要求：** 文本域，单价执行的监控要求
    - **调整机制：** 文本域，后续调整的机制安排

#### **单价版本管理**
- **版本控制：**
  - **版本列表：**
    - **版本号：** 显示单价版本的编号
    - **版本名称：** 显示版本的名称和描述
    - **生效时间：** 显示版本的生效时间
    - **失效时间：** 显示版本的失效时间
    - **版本状态：** 当前版本/历史版本/预发布版本
  - **版本操作：**
    - **查看版本：** 查看版本的详细信息
    - **对比版本：** 对比不同版本的差异
    - **复制版本：** 复制版本创建新版本
    - **回滚版本：** 回滚到历史版本
- **版本发布：**
  - **发布准备：**
    - **版本检查：** 检查版本数据的完整性
    - **影响评估：** 评估版本发布的影响
    - **发布计划：** 制定版本发布的计划
  - **发布执行：**
    - **发布确认：** 确认版本发布操作
    - **数据同步：** 同步版本数据到相关系统
    - **发布验证：** 验证版本发布的正确性
    - **发布通知：** 通知相关人员版本发布

#### **成本分析功能**
- **单价成本分析：**
  - **成本构成：**
    - **人工成本：** 显示人工成本的构成
    - **材料成本：** 显示相关材料成本
    - **设备成本：** 显示设备折旧成本
    - **管理成本：** 显示管理费用分摊
  - **成本对比：**
    - **历史对比：** 对比历史成本变化
    - **同行对比：** 对比同行业成本水平
    - **目标对比：** 对比成本控制目标
- **盈利分析：**
  - **毛利分析：** 分析工序的毛利水平
  - **贡献分析：** 分析工序对总利润的贡献
  - **敏感性分析：** 分析单价变化对利润的影响

#### **统计报表功能**
- **单价统计：**
  - **单价分布：** 统计不同单价区间的工序分布
  - **调整频率：** 统计单价调整的频率和幅度
  - **执行效果：** 统计单价执行的效果
- **图表展示：**
  - **趋势图：** 显示单价变化的时间趋势
  - **分布图：** 显示单价的分布情况
  - **对比图：** 对比不同工序的单价水平
  - **热力图：** 显示单价调整的热力分布

#### **系统集成功能**
- **MES系统集成：**
  - **数据同步：** 与MES系统同步单价数据
  - **实时更新：** 实时更新MES系统的单价信息
  - **数据校验：** 校验同步数据的准确性
- **财务系统集成：**
  - **成本核算：** 为财务系统提供成本核算数据
  - **预算管理：** 支持财务系统的预算管理
  - **报表生成：** 生成财务相关的单价报表

### 数据校验规则：

#### **标准工时**
- **校验规则：** 必填，大于0，不超过480分钟（8小时）
- **错误提示文案：** "标准工时必须大于0且不超过480分钟"

#### **计件单价**
- **校验规则：** 必填，大于0，符合单价区间要求
- **错误提示文案：** "计件单价必须大于0且在合理区间内"

#### **难度系数**
- **校验规则：** 必填，0.5-2.0之间的数值
- **错误提示文案：** "难度系数必须在0.5-2.0之间"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **工序单价信息**:
  - **工序ID (process_id)**: String, 必填, 引用工序主数据
  - **标准工时 (standard_hours)**: Decimal, 必填, 单位分钟
  - **计件单价 (piece_rate)**: Decimal, 必填, 单位元
  - **难度系数 (difficulty_factor)**: Decimal, 必填, 0.5-2.0
  - **生效日期 (effective_date)**: Date, 必填
- **调整记录**:
  - **调整原因 (adjustment_reason)**: String, 必填, 最大200字符
  - **调整前单价 (old_price)**: Decimal, 必填
  - **调整后单价 (new_price)**: Decimal, 必填

### 展示数据
- **单价列表**: 工序的单价信息和状态
- **单价详情**: 完整的单价信息和计算过程
- **调整记录**: 单价的调整历史和原因
- **统计分析**: 单价的统计分析数据

### 空状态/零数据
- **无单价数据**: 显示"暂无单价数据，请先设定工序单价"
- **无调整记录**: 显示"该工序暂无单价调整记录"
- **无统计数据**: 显示"数据不足，无法生成统计分析"

### API接口
- **单价查询**: GET /api/hr/piece-rates
- **单价设定**: POST /api/hr/piece-rates
- **单价调整**: PUT /api/hr/piece-rates/{id}
- **版本管理**: GET/POST /api/hr/piece-rate-versions
- **统计分析**: GET /api/hr/piece-rate-statistics

## 5. 异常与边界处理 (Error & Edge Cases)

### **单价区间超限**
- **提示信息**: "设定的单价超出合理区间，请重新设定"
- **用户操作**: 显示建议的单价区间和调整建议

### **工序信息缺失**
- **提示信息**: "工序信息不完整，无法设定单价"
- **用户操作**: 提供工序信息补充入口和要求

### **版本冲突**
- **提示信息**: "检测到单价版本冲突，请解决冲突后重试"
- **用户操作**: 显示冲突的详细信息和解决方案

### **审批超时**
- **提示信息**: "单价审批超时，请联系审批人或系统管理员"
- **用户操作**: 提供催办功能和联系方式

### **数据同步失败**
- **提示信息**: "单价数据同步失败，请检查网络连接或联系技术支持"
- **用户操作**: 提供重试选项和技术支持联系方式

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持按工序设定计件单价，包含标准工时和难度系数
- [ ] 单价调整有完整的审批流程和影响分析
- [ ] 支持单价版本管理和历史追溯
- [ ] 与MES系统集成，实时同步单价数据
- [ ] 提供成本分析和盈利分析功能
- [ ] 支持单价统计分析和报表导出
- [ ] 单价数据校验准确，防止异常数据
- [ ] 权限控制严格，保护敏感价格信息
- [ ] 所有页面元素符合全局设计规范
- [ ] 系统性能稳定，支持大量单价数据管理
