# 功能模块规格说明书：员工岗位职级管理模块

- **模块ID**: HR-001
- **所属子系统**: 人事管理子系统(HR)
- **最后更新**: 2025-07-31

## 1. 用户故事 (User Stories)

- **As a** HR专员, **I want to** 管理员工岗位分配, **so that** 明确员工的工作职责和汇报关系。
- **As a** HR专员, **I want to** 维护职级体系, **so that** 建立清晰的职业发展路径和薪酬等级。
- **As a** 部门经理, **I want to** 查看下属员工岗位信息, **so that** 了解团队结构和人员配置。
- **As a** 员工, **I want to** 查看自己的岗位职级信息, **so that** 了解职业发展方向和晋升路径。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 组织架构已在基础管理系统(BMS-002)中建立
- 员工基础信息已录入系统
- 岗位和职级体系已定义
- 用户具有人事管理权限

### 核心流程

#### 2.1 员工岗位分配流程
1. 选择需要分配岗位的员工
2. 根据组织架构选择所属部门
3. 从岗位清单中选择具体岗位
4. 设置岗位生效时间和汇报关系
5. 配置岗位权限和职责范围
6. 提交岗位分配申请
7. 相关领导审批确认
8. 岗位分配生效，发送通知

#### 2.2 职级调整流程
1. 发起员工职级调整申请
2. 填写调整原因和依据
3. 选择目标职级和生效时间
4. 系统校验职级调整规则
5. 提交多级审批流程
6. 审批通过后更新员工职级
7. 同步更新薪酬和权限
8. 记录职级变更历史

#### 2.3 岗位职级体系维护流程
1. 定义岗位分类和层级结构
2. 设置职级等级和晋升条件
3. 配置岗位职责和任职要求
4. 建立职级与薪酬的对应关系
5. 设置岗位权限和系统访问范围
6. 定期评估和调整岗位职级体系

### 后置条件
- 员工岗位信息更新完成
- 汇报关系和权限生效
- 相关系统数据同步
- 变更记录完整保存

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：员工岗位职级管理页面
### 页面目标：提供员工岗位分配、职级管理和体系维护功能

### 信息架构：
- **顶部区域**：包含 员工搜索, 岗位筛选, 批量操作, 导入导出
- **左侧区域**：包含 部门树, 岗位分类, 职级筛选
- **中间区域**：包含 员工列表, 岗位详情, 职级信息
- **右侧区域**：包含 操作面板, 审批流程, 变更历史

### 交互逻辑与状态：

#### **员工岗位查询区域**
- **基础查询：**
  - **员工姓名：** 输入框，支持模糊搜索
  - **员工工号：** 输入框，精确查询
  - **所属部门：** 树形选择器，支持多选
  - **当前岗位：** 下拉选择，支持搜索
- **高级筛选：**
  - **职级范围：** 多选下拉，按职级筛选
  - **入职时间：** 日期范围选择器
  - **岗位状态：** 单选，在职/离职/调岗
  - **汇报关系：** 下拉选择直接上级

#### **员工岗位列表**
- **列表表头：**
  - **员工工号：** 可排序，点击查看详情
  - **员工姓名：** 显示员工姓名和头像
  - **所属部门：** 显示完整部门路径
  - **当前岗位：** 显示岗位名称和编码
  - **职级等级：** 显示职级标签
  - **直接上级：** 显示汇报对象
  - **岗位状态：** 状态标签
  - **操作：** 编辑、调岗、晋升等操作
- **状态标识：**
  - **在职：** 绿色标签，"在职"
  - **试用期：** 蓝色标签，"试用期"
  - **调岗中：** 橙色标签，"调岗中"
  - **离职：** 灰色标签，"离职"

#### **岗位分配界面**
- **员工信息：**
  - **员工选择：** 搜索选择器，支持工号和姓名搜索
  - **当前岗位：** 只读显示，当前岗位信息
  - **当前部门：** 只读显示，当前所属部门
  - **入职时间：** 只读显示，员工入职时间
- **岗位配置：**
  - **目标部门：** 树形选择器，选择目标部门
  - **目标岗位：** 下拉选择，根据部门过滤岗位
  - **汇报对象：** 搜索选择器，选择直接上级
  - **生效时间：** 日期选择器，岗位生效时间
- **权限设置：**
  - **系统权限：** 复选框组，选择系统访问权限
  - **数据权限：** 下拉选择，数据访问范围
  - **审批权限：** 复选框组，选择审批权限
  - **特殊权限：** 文本域，特殊权限说明

#### **职级管理界面**
- **当前职级：**
  - **职级名称：** 只读显示，当前职级
  - **职级等级：** 只读显示，职级数字等级
  - **获得时间：** 只读显示，职级获得时间
  - **薪酬等级：** 只读显示，对应薪酬等级
- **职级调整：**
  - **目标职级：** 下拉选择，可选择的目标职级
  - **调整类型：** 单选按钮，晋升/平调/降级
  - **调整原因：** 下拉选择，预设调整原因
  - **调整说明：** 文本域，详细调整说明
  - **生效时间：** 日期选择器，职级生效时间
- **晋升条件：**
  - **任职时间：** 显示当前职级任职时间
  - **绩效要求：** 显示晋升绩效要求
  - **培训要求：** 显示必需的培训课程
  - **技能要求：** 显示技能认证要求

#### **岗位职级体系管理**
- **岗位管理：**
  - **岗位分类：** 树形结构，管理岗位分类
  - **岗位列表：** 表格显示，岗位基本信息
  - **岗位详情：** 表单编辑，岗位详细信息
  - **任职要求：** 文本编辑，岗位任职要求
- **职级体系：**
  - **职级等级：** 数字输入，职级数字等级
  - **职级名称：** 输入框，职级名称
  - **薪酬范围：** 数字范围，薪酬区间
  - **晋升条件：** 文本域，晋升条件说明
- **权限配置：**
  - **系统模块：** 复选框树，系统功能权限
  - **数据范围：** 下拉选择，数据访问范围
  - **操作权限：** 复选框组，增删改查权限

#### **审批流程管理**
- **流程配置：**
  - **审批类型：** 下拉选择，岗位调整/职级晋升
  - **审批层级：** 数字输入，审批层级数量
  - **审批人员：** 搜索选择，各级审批人员
  - **审批条件：** 文本域，审批触发条件
- **流程监控：**
  - **待审批：** 列表显示，待处理的审批事项
  - **审批历史：** 表格显示，历史审批记录
  - **流程状态：** 状态图，审批流程进度
- **审批操作：**
  - **审批意见：** 文本域，审批意见填写
  - **审批结果：** 单选按钮，通过/拒绝/退回
  - **下一步：** 显示下一审批环节

### 数据校验规则：

#### **岗位分配**
- **校验规则：** 员工不能同时担任多个主要岗位，生效时间不能早于当前时间
- **错误提示文案：** "员工已有在职岗位，请先办理调岗手续"

#### **职级调整**
- **校验规则：** 职级调整必须符合晋升规则，不能跨级晋升
- **错误提示文案：** "职级调整不符合晋升规则，请检查晋升条件"

#### **汇报关系**
- **校验规则：** 不能形成循环汇报关系，上级职级必须高于下级
- **错误提示文案：** "汇报关系设置错误，不能形成循环汇报"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **岗位分配**:
  - **员工ID (employee_id)**: String, 必填, 引用员工主数据
  - **部门ID (department_id)**: String, 必填, 引用组织架构
  - **岗位ID (position_id)**: String, 必填, 引用岗位主数据
  - **汇报对象ID (supervisor_id)**: String, 可选, 引用员工主数据
- **职级信息**:
  - **职级ID (grade_id)**: String, 必填, 引用职级主数据
  - **生效时间 (effective_date)**: Date, 必填, 职级生效时间
  - **调整原因 (reason)**: String, 必填, 职级调整原因

### 展示数据
- **员工岗位列表**: 员工基本信息、岗位、职级、汇报关系
- **岗位职级体系**: 岗位分类、职级等级、权限配置
- **审批流程**: 审批状态、审批历史、流程进度
- **统计报表**: 岗位分布、职级统计、人员变动

### 空状态/零数据
- **无岗位分配**: 显示"该员工暂未分配岗位"
- **无职级信息**: 显示"该员工暂无职级信息"
- **无审批记录**: 显示"暂无审批记录"

### API接口
- **岗位查询**: GET /api/hr/positions
- **岗位分配**: POST /api/hr/position-assignments
- **职级管理**: GET/POST/PUT /api/hr/grades
- **审批流程**: POST /api/hr/approvals
- **权限同步**: POST /api/hr/permissions/sync

## 5. 异常与边界处理 (Error & Edge Cases)

### **岗位冲突**
- **提示信息**: "该岗位已有其他员工担任，请确认是否继续分配"
- **用户操作**: 提供岗位共享或重新选择岗位选项

### **职级调整限制**
- **提示信息**: "当前职级任职时间不足，不满足晋升条件"
- **用户操作**: 显示具体的晋升条件和时间要求

### **权限同步失败**
- **提示信息**: "权限同步失败，请联系系统管理员"
- **用户操作**: 提供手动同步和技术支持联系方式

### **审批流程异常**
- **提示信息**: "审批流程配置错误，无法提交审批"
- **用户操作**: 提供流程检查和管理员联系方式

### **数据一致性问题**
- **提示信息**: "检测到数据不一致，正在自动修复"
- **用户操作**: 显示修复进度和结果报告

## 6. 验收标准 (Acceptance Criteria)

- [ ] 员工岗位分配功能完整，支持批量操作
- [ ] 职级管理体系完善，晋升规则清晰
- [ ] 汇报关系设置正确，避免循环汇报
- [ ] 权限配置灵活，支持细粒度控制
- [ ] 审批流程可配置，支持多级审批
- [ ] 数据校验规则完善，错误提示友好
- [ ] 与组织架构系统(BMS-002)集成正常
- [ ] 支持岗位职级的历史追溯
- [ ] 所有页面元素符合全局设计规范
- [ ] 响应时间<3秒，支持并发操作
- [ ] 操作日志完整，支持审计追踪
- [ ] 数据权限控制严格，保护敏感信息
