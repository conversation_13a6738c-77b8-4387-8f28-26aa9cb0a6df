# 功能模块规格说明书：薪酬计算引擎模块

- **模块ID**: HR-006
- **所属子系统**: 人事管理子系统(HR)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** HR专员, **I want to** 自动计算员工薪酬, **so that** 提高薪酬处理效率并减少计算错误。
- **As a** 成本会计, **I want to** 获取准确的人工成本数据, **so that** 进行精确的成本核算和分析。
- **As a** 员工, **I want to** 了解薪酬构成明细, **so that** 清楚自己的收入来源和计算依据。
- **As a** 财务专员, **I want to** 获取薪酬汇总数据, **so that** 进行财务核算和报表编制。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 员工档案数据完整
- 考勤数据已采集
- 计件单价已设定
- 薪酬规则已配置

### 核心流程

#### 2.1 薪酬计算流程
1. 系统定时触发薪酬计算任务
2. 获取员工考勤和计件数据
3. 读取薪酬计算规则和参数
4. 计算基本工资和岗位工资
5. 计算计件工资和加班工资
6. 计算各类津贴和补贴
7. 计算应扣项目（社保、公积金、个税等）
8. 生成薪酬明细和汇总数据
9. 数据校验和异常处理
10. 生成薪酬计算报告

#### 2.2 计件工资计算流程
1. 从MES系统获取员工产量数据
2. 匹配工序和计件单价
3. 计算各工序的计件工资
4. 应用质量系数和效率系数
5. 处理返工和废品扣减
6. 计算计件工资小计
7. 应用计件工资上下限
8. 生成计件工资明细

#### 2.3 薪酬数据传递流程
1. 薪酬计算完成后数据校验
2. 生成薪酬传递文件
3. 传递数据到财务系统
4. 传递数据到成本核算系统
5. 更新员工薪酬档案
6. 生成薪酬发放清单
7. 记录数据传递日志

### 后置条件
- 薪酬数据计算准确
- 相关系统数据同步
- 薪酬明细可查询
- 成本数据可用于核算

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：薪酬计算引擎页面
### 页面目标：提供薪酬计算的配置、执行、监控和结果查看功能

### 信息架构：
- **顶部区域**：包含 计算周期, 计算状态, 批量操作, 导出功能
- **左侧区域**：包含 计算任务, 规则配置, 异常处理, 历史记录
- **中间区域**：包含 计算结果, 薪酬明细, 统计分析
- **右侧区域**：包含 计算监控, 数据校验, 快速操作

### 交互逻辑与状态：

#### **薪酬计算控制台**
- **计算任务管理：**
  - **任务创建：**
    - **计算周期：** 日期选择器，选择薪酬计算的周期
    - **计算范围：** 多选框，选择计算的部门或员工范围
    - **计算类型：** 单选按钮，月薪计算/临时计算/补算
    - **特殊参数：** 输入框，特殊计算参数设置
  - **任务执行：**
    - **开始计算：** 按钮，启动薪酬计算任务
    - **暂停计算：** 按钮，暂停正在执行的计算
    - **重新计算：** 按钮，重新执行计算任务
    - **取消计算：** 按钮，取消计算任务
  - **任务监控：**
    - **执行进度：** 进度条，显示计算任务的执行进度
    - **处理状态：** 状态标识，等待/执行中/完成/失败
    - **处理速度：** 显示框，显示计算的处理速度
    - **预计完成时间：** 显示框，预计任务完成时间

#### **薪酬计算规则配置**
- **基本工资规则：**
  - **工资结构：**
    - **基本工资：** 数字输入框，员工的基本工资标准
    - **岗位工资：** 数字输入框，岗位工资标准
    - **技能工资：** 数字输入框，技能等级工资
    - **工龄工资：** 数字输入框，工龄工资标准
  - **计算规则：**
    - **出勤系数：** 数字输入框，出勤率对工资的影响系数
    - **请假扣减：** 下拉选择，请假工资扣减规则
    - **迟到扣减：** 数字输入框，迟到扣减标准
    - **早退扣减：** 数字输入框，早退扣减标准
- **计件工资规则：**
  - **计算方式：**
    - **计件基础：** 单选按钮，按产量/按工时/按标准工时
    - **质量系数：** 数字输入框，质量对计件工资的影响
    - **效率系数：** 数字输入框，效率对计件工资的影响
    - **团队系数：** 数字输入框，团队绩效系数
  - **限制条件：**
    - **最低保障：** 数字输入框，计件工资最低保障
    - **最高限制：** 数字输入框，计件工资最高限制
    - **异常处理：** 下拉选择，异常数据的处理方式

#### **薪酬计算结果查看**
- **薪酬汇总信息：**
  - **汇总统计：**
    - **总人数：** 显示框，参与计算的总人数
    - **总工资：** 显示框，工资总额
    - **平均工资：** 显示框，平均工资水平
    - **工资分布：** 图表，工资分布情况
  - **部门汇总：**
    - **部门列表：** 表格，各部门的薪酬汇总
    - **部门对比：** 图表，部门间薪酬对比
    - **成本分析：** 图表，人工成本分析
- **员工薪酬明细：**
  - **明细列表：**
    - **员工信息：** 显示员工姓名、工号、部门
    - **基本工资：** 显示基本工资和岗位工资
    - **计件工资：** 显示计件工资明细
    - **津贴补贴：** 显示各类津贴补贴
    - **扣减项目：** 显示各类扣减项目
    - **实发工资：** 显示最终实发工资

### 数据校验规则：

#### **薪酬金额**
- **校验规则：** 必须大于等于0，不能超过合理上限
- **错误提示文案：** "薪酬金额必须大于等于0且在合理范围内"

#### **计算周期**
- **校验规则：** 必须是有效的日期范围，不能重复计算
- **错误提示文案：** "计算周期设置有误或该周期已计算"

#### **计算参数**
- **校验规则：** 系数必须在合理范围内，不能为负数
- **错误提示文案：** "计算参数设置有误，请检查系数设置"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **薪酬计算参数**:
  - **计算周期 (calculation_period)**: String, 必填, YYYY-MM格式
  - **员工范围 (employee_scope)**: Array, 必填, 员工ID列表
  - **基本工资 (base_salary)**: Decimal, 必填, 单位元
  - **计件单价 (piece_rate)**: Decimal, 必填, 引用单价数据
- **考勤数据**:
  - **出勤天数 (attendance_days)**: Integer, 必填
  - **加班小时 (overtime_hours)**: Decimal, 必填
  - **请假天数 (leave_days)**: Decimal, 必填
- **产量数据**:
  - **工序产量 (process_output)**: Integer, 必填
  - **质量等级 (quality_grade)**: String, 必填, A/B/C

### 展示数据
- **薪酬汇总**: 部门和个人的薪酬汇总数据
- **薪酬明细**: 详细的薪酬构成和计算过程
- **计算日志**: 薪酬计算的执行日志
- **异常报告**: 计算过程中的异常情况

### 空状态/零数据
- **无计算任务**: 显示"暂无薪酬计算任务，请创建计算任务"
- **无薪酬数据**: 显示"该周期暂无薪酬数据"
- **计算失败**: 显示"薪酬计算失败，请检查数据和规则配置"

### API接口
- **薪酬计算**: POST /api/hr/salary-calculation
- **计算结果**: GET /api/hr/salary-results
- **规则配置**: GET/POST/PUT /api/hr/salary-rules
- **数据校验**: POST /api/hr/salary-validation
- **异常处理**: GET/PUT /api/hr/salary-exceptions

## 5. 异常与边界处理 (Error & Edge Cases)

### **数据缺失**
- **提示信息**: "关键数据缺失，无法完成薪酬计算"
- **用户操作**: 显示缺失的具体数据和补充方法

### **计算规则冲突**
- **提示信息**: "检测到薪酬计算规则冲突，请检查规则配置"
- **用户操作**: 显示冲突的规则和解决建议

### **计算结果异常**
- **提示信息**: "薪酬计算结果异常，请检查数据和规则"
- **用户操作**: 提供异常数据的详细信息和处理选项

### **系统性能问题**
- **提示信息**: "计算任务执行缓慢，请稍后重试或联系管理员"
- **用户操作**: 提供任务优化建议和技术支持联系方式

### **数据同步失败**
- **提示信息**: "薪酬数据同步失败，请检查网络连接"
- **用户操作**: 提供重试选项和手动同步功能

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持基本工资、计件工资、津贴补贴的自动计算
- [ ] 与MES系统集成，自动获取产量数据进行计件计算
- [ ] 薪酬计算准确率≥99.9%，500人计算时间<4小时
- [ ] 支持多种薪酬计算规则和参数配置
- [ ] 提供完整的薪酬明细和计算过程追溯
- [ ] 异常数据自动识别和处理机制完善
- [ ] 计算结果数据校验和质量控制严格
- [ ] 与财务系统无缝集成，数据传递准确
- [ ] 所有页面元素符合全局设计规范
- [ ] 系统性能稳定，支持大规模薪酬计算
