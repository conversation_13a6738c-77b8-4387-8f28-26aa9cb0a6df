# 功能模块规格说明书：员工档案管理模块

- **模块ID**: HR-002
- **所属子系统**: 人事管理子系统(HR)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** HR专员, **I want to** 电子化管理员工档案, **so that** 高效处理人事事务和信息查询。
- **As a** HR专员, **I want to** 管理员工合同和岗位信息, **so that** 确保员工信息的完整性和准确性。
- **As a** HR专员, **I want to** 处理员工入离职流程, **so that** 规范化管理员工生命周期。
- **As a** 部门主管, **I want to** 查看下属员工档案, **so that** 了解团队成员的基本情况。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 组织架构已建立
- 岗位信息已配置
- 用户具有员工档案管理权限
- 相关字典数据已维护

### 核心流程

#### 2.1 员工入职流程
1. HR专员创建新员工档案
2. 填写员工基本信息和身份信息
3. 上传员工照片和相关证件
4. 设置员工岗位和组织归属
5. 配置员工合同信息和薪酬等级
6. 设置系统账号和权限
7. 生成员工工号并激活档案
8. 发送入职通知和系统账号信息

#### 2.2 员工信息变更流程
1. 提交员工信息变更申请
2. 填写变更内容和变更原因
3. 上传相关证明材料
4. 部门主管审核确认
5. HR专员审核变更合规性
6. 系统更新员工档案信息
7. 记录变更历史和生效时间
8. 通知相关系统同步更新

#### 2.3 员工离职流程
1. 员工或部门提交离职申请
2. 填写离职原因和交接安排
3. 部门主管确认离职申请
4. HR专员审核离职手续
5. 办理工作交接和资产归还
6. 结算最后工资和相关费用
7. 注销系统账号和权限
8. 归档员工档案并标记离职状态

### 后置条件
- 员工档案信息完整准确
- 员工状态与实际情况一致
- 相关系统数据同步更新
- 变更历史完整记录

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：员工档案管理页面
### 页面目标：提供员工档案的创建、维护、查询和生命周期管理功能

### 信息架构：
- **顶部区域**：包含 搜索筛选, 批量操作, 导入导出, 新增员工
- **左侧区域**：包含 组织架构树, 员工分类, 快速筛选
- **中间区域**：包含 员工列表, 员工详情, 操作记录
- **右侧区域**：包含 统计信息, 快速操作, 相关信息

### 交互逻辑与状态：

#### **员工列表界面**
- **员工信息展示：**
  - **员工卡片：**
    - **员工照片：** 显示员工头像，默认使用系统头像
    - **基本信息：** 显示姓名、工号、部门、岗位
    - **状态标识：** 在职/试用/离职等状态标识
    - **联系方式：** 显示手机号和邮箱
    - **入职时间：** 显示员工入职日期
  - **列表操作：**
    - **查看详情：** 点击员工卡片查看详细信息
    - **编辑信息：** 快速编辑员工基本信息
    - **状态变更：** 变更员工在职状态
    - **打印档案：** 打印员工档案信息
- **筛选和搜索：**
  - **组织筛选：** 按部门、班组筛选员工
  - **状态筛选：** 按在职状态筛选员工
  - **岗位筛选：** 按岗位类型筛选员工
  - **关键词搜索：** 支持姓名、工号、手机号搜索

#### **员工详情界面**
- **基本信息标签页：**
  - **个人信息：**
    - **姓名：** 输入框，必填，最大20字符
    - **性别：** 单选按钮，男/女
    - **出生日期：** 日期选择器，必填
    - **身份证号：** 输入框，必填，自动校验格式
    - **民族：** 下拉选择，引用民族字典
    - **婚姻状况：** 下拉选择，未婚/已婚/离异/丧偶
    - **政治面貌：** 下拉选择，群众/党员/团员等
    - **户籍地址：** 输入框，可选，最大200字符
  - **联系信息：**
    - **手机号码：** 输入框，必填，自动校验格式
    - **邮箱地址：** 输入框，可选，自动校验格式
    - **现住地址：** 输入框，可选，最大200字符
    - **紧急联系人：** 输入框，可选，最大20字符
    - **紧急联系电话：** 输入框，可选，自动校验格式
  - **教育背景：**
    - **最高学历：** 下拉选择，小学/初中/高中/大专/本科/硕士/博士
    - **毕业院校：** 输入框，可选，最大50字符
    - **专业：** 输入框，可选，最大30字符
    - **毕业时间：** 日期选择器，可选
- **工作信息标签页：**
  - **岗位信息：**
    - **员工工号：** 显示框，系统自动生成
    - **所属部门：** 下拉选择，引用组织架构
    - **岗位名称：** 下拉选择，引用岗位信息
    - **岗位等级：** 显示框，根据岗位自动带出
    - **直接上级：** 下拉选择，选择直接汇报上级
    - **入职日期：** 日期选择器，必填
    - **试用期：** 数字输入框，试用期月数
    - **转正日期：** 日期选择器，自动计算或手动设置
  - **合同信息：**
    - **合同类型：** 下拉选择，劳动合同/劳务合同/实习协议
    - **合同开始日期：** 日期选择器，必填
    - **合同结束日期：** 日期选择器，必填
    - **合同期限：** 显示框，自动计算合同期限
    - **续签次数：** 数字输入框，合同续签次数
    - **签约主体：** 下拉选择，签约的法人主体
  - **薪酬信息：**
    - **薪酬等级：** 下拉选择，引用薪酬等级体系
    - **基本工资：** 数字输入框，员工基本工资
    - **岗位工资：** 数字输入框，岗位工资标准
    - **绩效系数：** 数字输入框，绩效工资系数
    - **津贴补贴：** 数字输入框，各类津贴补贴

#### **员工入职界面**
- **入职信息收集：**
  - **基本信息录入：** 按照员工详情界面的基本信息标签页录入
  - **证件照片上传：**
    - **员工照片：** 文件上传，支持jpg/png格式，最大2MB
    - **身份证正面：** 文件上传，身份证正面照片
    - **身份证反面：** 文件上传，身份证反面照片
    - **学历证书：** 文件上传，最高学历证书照片
  - **工作安排：**
    - **入职部门：** 下拉选择，员工入职的部门
    - **入职岗位：** 下拉选择，员工入职的岗位
    - **汇报上级：** 下拉选择，员工的直接上级
    - **工作地点：** 下拉选择，员工的工作地点
- **系统账号创建：**
  - **用户名：** 输入框，系统登录用户名
  - **初始密码：** 输入框，系统自动生成或手动设置
  - **权限角色：** 多选框，分配员工的系统权限角色
  - **邮箱账号：** 输入框，企业邮箱账号（如有）

#### **员工离职界面**
- **离职申请：**
  - **离职类型：** 单选按钮，主动离职/被动离职/合同到期
  - **离职原因：** 下拉选择，个人发展/薪酬待遇/工作环境等
  - **离职日期：** 日期选择器，预计离职日期
  - **离职说明：** 文本域，详细离职原因说明
- **工作交接：**
  - **交接人员：** 下拉选择，工作交接的接收人
  - **交接内容：** 文本域，详细的工作交接内容
  - **交接完成时间：** 日期选择器，交接完成的时间
  - **交接确认：** 复选框，交接人确认交接完成
- **资产归还：**
  - **办公用品：** 复选框列表，需要归还的办公用品
  - **电子设备：** 复选框列表，需要归还的电子设备
  - **证件资料：** 复选框列表，需要归还的证件资料
  - **归还确认：** 复选框，资产管理员确认归还完成

#### **档案变更管理**
- **变更申请：**
  - **变更类型：** 下拉选择，个人信息/岗位信息/合同信息
  - **变更字段：** 多选框，选择需要变更的具体字段
  - **变更原因：** 文本域，说明变更的原因
  - **生效日期：** 日期选择器，变更生效的日期
- **变更审批：**
  - **审批流程：** 显示变更的审批流程和当前状态
  - **审批意见：** 文本域，审批人填写审批意见
  - **审批操作：** 按钮组，同意/拒绝/退回修改
  - **审批历史：** 列表显示完整的审批历史记录
- **变更记录：**
  - **变更历史：** 时间线显示员工档案的所有变更记录
  - **变更对比：** 对比显示变更前后的信息差异
  - **变更影响：** 显示变更对其他系统的影响
  - **回滚操作：** 支持错误变更的回滚操作

#### **统计分析功能**
- **员工统计：**
  - **人员结构：** 按部门、岗位、年龄、学历等维度统计
  - **入离职统计：** 统计入职、离职的人数和趋势
  - **合同统计：** 统计合同到期、续签等情况
  - **试用期统计：** 统计试用期员工和转正情况
- **图表展示：**
  - **饼图：** 显示人员结构的比例分布
  - **柱状图：** 显示各部门的人员数量对比
  - **折线图：** 显示入离职的时间趋势
  - **仪表盘：** 显示关键人事指标的仪表盘

### 数据校验规则：

#### **身份证号码**
- **校验规则：** 必填，18位数字，符合身份证号码规则
- **错误提示文案：** "请输入正确的18位身份证号码"

#### **手机号码**
- **校验规则：** 必填，11位数字，以1开头
- **错误提示文案：** "请输入正确的11位手机号码"

#### **邮箱地址**
- **校验规则：** 可选，符合邮箱格式规范
- **错误提示文案：** "请输入正确的邮箱地址格式"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **员工基本信息**:
  - **姓名 (employee_name)**: String, 必填, 最大20字符
  - **身份证号 (id_number)**: String, 必填, 18位数字
  - **手机号码 (mobile_phone)**: String, 必填, 11位数字
  - **入职日期 (hire_date)**: Date, 必填
- **工作信息**:
  - **部门ID (department_id)**: String, 必填, 引用组织架构
  - **岗位ID (position_id)**: String, 必填, 引用岗位信息
  - **薪酬等级 (salary_grade)**: String, 必填, 引用薪酬等级

### 展示数据
- **员工列表**: 员工的基本信息和状态
- **员工详情**: 完整的员工档案信息
- **统计数据**: 人员结构和变动统计
- **变更记录**: 员工档案的变更历史

### 空状态/零数据
- **无员工数据**: 显示"暂无员工档案，点击新增员工"
- **无变更记录**: 显示"该员工暂无档案变更记录"
- **无统计数据**: 显示"数据不足，无法生成统计分析"

### API接口
- **员工查询**: GET /api/hr/employees
- **员工创建**: POST /api/hr/employees
- **员工更新**: PUT /api/hr/employees/{id}
- **员工离职**: POST /api/hr/employees/{id}/resignation
- **变更记录**: GET /api/hr/employee-changes

## 5. 异常与边界处理 (Error & Edge Cases)

### **身份证号重复**
- **提示信息**: "该身份证号已存在，请检查是否重复录入"
- **用户操作**: 提供查询现有员工和信息核实功能

### **合同日期冲突**
- **提示信息**: "合同日期与现有合同存在冲突，请调整日期"
- **用户操作**: 显示现有合同信息和日期调整建议

### **离职员工操作限制**
- **提示信息**: "该员工已离职，无法进行此操作"
- **用户操作**: 提供离职员工的专门操作入口

### **权限不足**
- **提示信息**: "您没有权限查看或编辑该员工档案"
- **用户操作**: 显示权限要求和申请流程

### **文件上传失败**
- **提示信息**: "文件上传失败，请检查文件格式和大小"
- **用户操作**: 提供重试选项和格式要求说明

## 6. 验收标准 (Acceptance Criteria)

- [ ] 员工档案信息包含基本信息、合同信息、岗位信息等完整内容
- [ ] 支持员工入职、调岗、离职等完整生命周期管理
- [ ] 员工档案变更有审批流程和完整的历史记录
- [ ] 支持员工信息的多维度查询和统计分析
- [ ] 员工照片和证件文件上传功能正常
- [ ] 身份证号、手机号等关键信息校验准确
- [ ] 支持批量导入导出员工档案数据
- [ ] 与组织架构和岗位信息正确关联
- [ ] 所有页面元素符合全局设计规范
- [ ] 系统性能稳定，支持大量员工档案管理
