# PRD-11: 人事管理子系统（HR）产品需求文档

> **版本**: 2.0  
> **状态**: 重构版  
> **撰写人**: 产品团队  
> **日期**: 2025-07-30  
> **术语表**: 参考 [全局术语表](../_Glossary.md)  
> **业务规则**: 参考 [核心业务规则库](../_Business_Rules.md)

---

## 1. 核心问题与价值主张

### 1.1 核心问题
**玻璃制造企业大量一线操作人员采用计件薪酬，传统人事管理无法精确核算人工成本，无法实现与生产系统和财务系统的业财一体化。**

### 1.2 价值主张
构建与MES和财务系统深度集成的人事管理平台，实现计件薪酬的自动准确计算，精确核算人工成本，支撑业财一体化管理。

### 1.3 商业价值量化
- **薪酬计算效率**: 自动化计件薪酬计算使HR工作效率提升85%，月末算薪时间从3天缩短至4小时
- **成本核算精度**: 人工成本核算准确率从80%提升至99%，为产品定价提供准确依据
- **业财一体化**: 人工成本自动归集到生产订单，财务凭证自动生成，数据一致性100%
- **员工满意度**: 透明的计件薪酬和实时查询使员工满意度提升60%

---

## 2. 目标用户与使用场景

### 2.1 目标用户
参考 [全局术语表](../_Glossary.md) 中的用户角色定义：

| 用户角色 | 职责描述 | 核心需求 |
|----------|----------|----------|
| **生产操作工** | 负责生产线操作、工序完成、产量汇报 | 需要查看计件产量和薪酬明细 |
| **车间主管** | 负责班组管理、工时审核、生产效率监控 | 需要审核工时数据和异常处理工具 |
| **HR专员** | 负责员工档案、薪酬管理、考勤管理 | 需要自动化薪酬计算和管理工具 |
| **成本会计** | 负责人工成本核算、成本归集、财务凭证 | 需要准确的人工成本数据和自动化归集 |

### 2.2 核心使用场景

#### 场景一：计件薪酬自动计算
**用户故事**: 作为一个HR专员，我想要系统自动计算员工计件薪酬，以便高效准确地完成月末算薪工作。

**操作流程**:
1. 月初为PDM中的工序设置计件单价
2. 生产工人在MES中汇报工序完成数量
3. 车间主管审核确认工序汇报数据
4. 系统自动从MES获取已确认的计件数据
5. 月末运行薪酬计算引擎
6. 系统自动计算基本工资、计件工资、津贴等
7. 生成详细工资条草稿供HR审核

**成功标准**: 计件薪酬计算准确率100%，算薪时间缩短85%

#### 场景二：人工成本自动归集
**用户故事**: 作为一个成本会计，我想要获取准确的人工成本数据，以便精确核算产品成本。

**操作流程**:
1. HR系统完成薪酬计算并审核确认
2. 系统将每个工人的计件工资按生产订单归集
3. 直接人工成本自动分配到对应生产订单
4. 间接人工成本按设定规则分摊到制造费用
5. 系统自动在财务系统中生成记账凭证
6. 成本会计查看人工成本归集结果

**成功标准**: 人工成本归集准确率99%，财务凭证自动生成

#### 场景三：员工自助查询
**用户故事**: 作为一个生产操作工，我想要查看自己的计件产量和薪酬明细，以便了解工作成果和收入情况。

**操作流程**:
1. 员工通过手机或PC登录自助门户
2. 查看当月计件产量统计和明细
3. 查看工资条详细构成（基本工资、计件工资、津贴等）
4. 查看考勤记录和异常情况
5. 对薪酬有疑问时可追溯到具体工序和产量
6. 提交请假、加班等申请

**成功标准**: 信息查询实时准确，员工满意度提升60%

---

## 3. 功能需求（用户故事格式）

### 3.1 组织与员工管理

#### 需求 3.1.1: 组织架构管理
**用户故事**: 作为一个HR专员，我想要维护组织架构，以便建立清晰的管理层级和归属关系。

**功能描述**:
- 多层级组织架构维护
- 部门和岗位管理
- 组织变更历史记录
- 权限与组织关联

**验收标准**:
- [ ] 支持公司、部门、班组等多层级组织架构
- [ ] 组织架构变更有审批流程和历史记录
- [ ] 岗位与薪酬等级关联
- [ ] 组织架构与权限体系集成

#### 需求 3.1.2: 员工档案管理
**用户故事**: 作为一个HR专员，我想要电子化管理员工档案，以便高效处理人事事务。

**功能描述**:
- 员工基本信息管理
- 合同和岗位信息维护
- 入离职流程管理
- 员工档案查询和统计

**验收标准**:
- [ ] 员工信息包含基本信息、合同信息、岗位信息等
- [ ] 支持员工入职、调岗、离职等流程
- [ ] 员工档案变更有审批和记录
- [ ] 支持员工信息的查询和统计分析

### 3.2 考勤管理

#### 需求 3.2.1: 考勤数据采集
**用户故事**: 作为一个HR专员，我想要自动采集考勤数据，以便准确记录员工出勤情况。

**功能描述**:
- 多种考勤方式支持
- 考勤数据自动采集
- 异常考勤识别
- 考勤规则配置

**验收标准**:
- [ ] 对接考勤机，自动获取打卡记录
- [ ] 支持移动端打卡和定位验证
- [ ] 自动识别迟到、早退、缺勤等异常
- [ ] 支持固定班次、倒班等多种排班规则

#### 需求 3.2.2: 考勤异常处理
**用户故事**: 作为一个员工，我想要在线申请请假和加班，以便规范处理考勤异常。

**功能描述**:
- 在线请假申请
- 加班申请和审批
- 出差和外勤管理
- 考勤异常审批流程

**验收标准**:
- [ ] 员工可在线提交请假、加班、出差申请
- [ ] 支持多级审批流程和权限控制
- [ ] 考勤异常自动影响薪酬计算
- [ ] 提供考勤报表和统计分析

### 3.3 薪酬管理（核心功能）

#### 需求 3.3.1: 计件单价管理
**用户故事**: 作为一个HR专员，我想要维护工序计件单价，以便支撑计件薪酬计算。

**功能描述**:
- 工序计件单价设置
- 单价版本和历史管理
- 单价审批流程
- 与PDM工序关联

**验收标准**:
- [ ] 为PDM中定义的每道工序设定计件单价
- [ ] 支持按产品、工序难度设置不同单价
- [ ] 单价变更需要审批流程和版本记录
- [ ] 单价生效时间和适用范围控制

#### 需求 3.3.2: 薪酬计算引擎
**用户故事**: 作为一个HR专员，我想要自动计算员工薪酬，以便高效准确地完成算薪工作。

**功能描述**:
- 多元化薪酬结构支持
- 计件工资自动计算
- 薪酬计算规则配置
- 工资条生成和审核

**验收标准**:
- [ ] 支持基本工资、岗位工资、绩效、津贴、计件工资等
- [ ] 自动从MES获取计件数据并计算计件工资
- [ ] 计件工资 = ∑(工序完成合格数 × 对应工序单价)
- [ ] 生成详细工资条，包含各项明细

#### 需求 3.3.3: 薪酬审核发放
**用户故事**: 作为一个HR专员，我想要审核和发放薪酬，以便确保薪酬数据准确无误。

**功能描述**:
- 薪酬数据审核
- 薪酬发放管理
- 薪酬调整处理
- 薪酬统计分析

**验收标准**:
- [ ] HR可审核薪酬计算结果并进行调整
- [ ] 薪酬数据提交财务审批后标记发放状态
- [ ] 支持薪酬重算和补发处理
- [ ] 提供薪酬统计和成本分析报表

### 3.4 成本归集与财务集成

#### 需求 3.4.1: 人工成本归集
**用户故事**: 作为一个成本会计，我想要自动归集人工成本，以便精确核算产品成本。

**功能描述**:
- 直接人工成本计算
- 间接人工成本分摊
- 成本归集规则配置
- 成本数据验证

**验收标准**:
- [ ] 将计件工资按生产订单自动归集为直接人工成本
- [ ] 非计件岗位工资按规则分摊计入制造费用
- [ ] 成本归集规则可配置（按工时、按产线等）
- [ ] 成本数据与生产数据一致性验证

#### 需求 3.4.2: 财务凭证生成
**用户故事**: 作为一个成本会计，我想要自动生成财务凭证，以便实现业财一体化。

**功能描述**:
- 工资凭证自动生成
- 成本归集凭证生成
- 凭证数据验证
- 与财务系统集成

**验收标准**:
- [ ] 经确认的工资数据自动生成财务凭证
- [ ] 成本归集数据自动生成成本凭证
- [ ] 凭证数据与HR数据完全一致
- [ ] 与财务系统实时同步，无延迟

---

## 4. 验收标准（可测试列表）

### 4.1 功能验收标准
- [ ] 计件薪酬计算准确率 100%
- [ ] 人工成本归集准确率 ≥ 99%
- [ ] 考勤数据采集准确率 ≥ 99.5%
- [ ] 财务凭证生成准确率 100%
- [ ] 员工自助查询准确率 100%

### 4.2 性能验收标准
- [ ] 薪酬计算时间 < 4小时（500名员工）
- [ ] 考勤数据查询响应时间 < 2秒
- [ ] 员工自助门户响应时间 < 3秒
- [ ] 成本归集计算时间 < 30分钟
- [ ] 系统并发处理能力 ≥ 100用户

### 4.3 业务效果验收标准
- [ ] HR工作效率提升 ≥ 85%
- [ ] 人工成本核算准确率提升至 ≥ 99%
- [ ] 算薪时间缩短至 ≤ 4小时
- [ ] 员工满意度提升 ≥ 60%

---

## 5. 交互设计要求

### 5.1 界面设计原则
- **移动友好**: 员工自助功能适配移动端，便于随时查询
- **数据透明**: 薪酬构成清晰展示，计件明细可追溯
- **操作高效**: HR操作界面简洁，减少手动录入
- **权限严格**: 敏感薪酬数据严格权限控制

### 5.2 关键界面要求
- **员工自助门户**: 薪酬查询、考勤查询、申请提交
- **薪酬管理**: 计件单价设置、薪酬计算、审核发放
- **考勤管理**: 考勤数据、异常处理、报表统计
- **成本分析**: 人工成本归集、成本分析、财务集成

---

## 6. 数据埋点需求

### 6.1 HR操作埋点
- 薪酬计算和审核行为
- 考勤管理和异常处理
- 员工档案维护操作
- 计件单价设置和调整

### 6.2 员工使用埋点
- 员工自助查询行为
- 移动端使用情况
- 申请提交和审批
- 薪酬查询频率

### 6.3 系统性能埋点
- 薪酬计算耗时
- 考勤数据处理性能
- 成本归集计算时间
- 财务集成同步延迟

---

## 7. 未来展望/V-Next

### 7.1 暂不开发功能
- **AI薪酬分析**: 智能薪酬建议和市场对标
- **员工发展**: 培训管理和职业发展规划
- **绩效管理**: 全面的绩效考核和评估体系
- **招聘管理**: 在线招聘和人才库管理

### 7.2 技术演进方向
- **智能排班**: AI驱动的智能排班优化
- **预测分析**: 人工成本预测和人员需求分析
- **移动办公**: 全功能移动HR应用

---

## 版本控制

| 版本 | 日期 | 变更说明 | 责任人 |
|------|------|----------|--------|
| 1.0 | 2025-07-29 | 初始版本 | Roo |
| 2.0 | 2025-07-30 | 重构版本，应用五大核心原则 | 产品团队 |

---

**文档状态**: 已重构 ✅  
**应用原则**: 第一性原理 ✅ DRY ✅ KISS ✅ SOLID ✅ YAGNI ✅
