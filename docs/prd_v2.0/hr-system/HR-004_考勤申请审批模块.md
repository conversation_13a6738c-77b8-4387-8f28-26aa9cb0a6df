# 功能模块规格说明书：考勤申请审批模块

- **模块ID**: HR-004
- **所属子系统**: 人事管理子系统(HR)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 员工, **I want to** 在线申请请假和调休, **so that** 方便地处理个人事务并规范请假流程。
- **As a** 车间主管, **I want to** 审批下属的考勤申请, **so that** 合理安排工作并控制人员出勤。
- **As a** HR专员, **I want to** 管理考勤申请流程, **so that** 确保申请的合规性和数据的准确性。
- **As a** 员工, **I want to** 申请补卡和调班, **so that** 处理考勤异常和工作安排变更。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 员工档案已建立
- 组织架构已配置
- 审批流程已设定
- 假期规则已配置

### 核心流程

#### 2.1 请假申请流程
1. 员工登录系统提交请假申请
2. 选择请假类型和时间范围
3. 填写请假原因和相关说明
4. 上传请假证明材料（如需要）
5. 系统自动路由到审批人
6. 直接上级审批请假申请
7. HR专员审核请假合规性
8. 审批完成后更新考勤数据
9. 发送审批结果通知

#### 2.2 调班申请流程
1. 员工提交调班申请
2. 选择原班次和目标班次
3. 填写调班原因和时间
4. 系统检查班次冲突和人员安排
5. 相关同事确认调班安排
6. 部门主管审批调班申请
7. HR专员确认调班可行性
8. 审批通过后更新排班计划
9. 通知相关人员调班结果

#### 2.3 补卡申请流程
1. 员工发现考勤异常提交补卡申请
2. 选择补卡日期和时间
3. 说明未打卡的原因
4. 提供相关证明材料
5. 直接上级确认员工出勤情况
6. HR专员审核补卡申请
7. 审批通过后补充考勤记录
8. 更新考勤统计数据

### 后置条件
- 申请状态明确（通过/拒绝）
- 考勤数据准确更新
- 相关人员及时通知
- 申请记录完整保存

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：考勤申请审批页面
### 页面目标：提供考勤相关申请的提交、审批和管理功能

### 信息架构：
- **顶部区域**：包含 申请类型, 状态筛选, 时间筛选, 快速操作
- **左侧区域**：包含 申请分类, 我的申请, 待审批, 已处理
- **中间区域**：包含 申请列表, 申请详情, 审批操作
- **右侧区域**：包含 申请统计, 快速申请, 相关信息

### 交互逻辑与状态：

#### **申请列表界面**
- **申请记录展示：**
  - **申请卡片：**
    - **申请类型：** 显示请假/调班/补卡等申请类型
    - **申请人信息：** 显示申请人姓名、部门、岗位
    - **申请时间：** 显示申请的起止时间
    - **申请状态：** 待审批/审批中/已通过/已拒绝
    - **审批进度：** 显示当前审批节点和进度
  - **状态标识：**
    - **待审批：** 橙色标识，等待审批处理
    - **审批中：** 蓝色标识，正在审批流程中
    - **已通过：** 绿色标识，审批通过
    - **已拒绝：** 红色标识，审批被拒绝
    - **已撤销：** 灰色标识，申请被撤销
- **筛选和搜索：**
  - **申请类型筛选：** 按请假、调班、补卡等类型筛选
  - **状态筛选：** 按审批状态筛选申请
  - **时间筛选：** 按申请时间或生效时间筛选
  - **申请人搜索：** 支持姓名、工号搜索

#### **请假申请界面**
- **请假信息填写：**
  - **请假类型：**
    - **年假：** 单选按钮，使用年假额度
    - **事假：** 单选按钮，个人事务请假
    - **病假：** 单选按钮，因病请假
    - **婚假：** 单选按钮，结婚请假
    - **产假：** 单选按钮，生育请假
    - **丧假：** 单选按钮，丧事请假
    - **其他：** 单选按钮，其他类型请假
  - **请假时间：**
    - **开始时间：** 日期时间选择器，请假开始时间
    - **结束时间：** 日期时间选择器，请假结束时间
    - **请假天数：** 显示框，自动计算请假天数
    - **请假时长：** 显示框，自动计算请假小时数
  - **请假原因：**
    - **请假事由：** 文本域，详细说明请假原因
    - **紧急程度：** 下拉选择，一般/紧急/特急
    - **联系方式：** 输入框，请假期间的联系方式
    - **工作安排：** 文本域，请假期间的工作交接安排
- **证明材料：**
  - **病假证明：** 文件上传，病假需要医院证明
  - **婚假证明：** 文件上传，婚假需要结婚证明
  - **其他证明：** 文件上传，其他相关证明材料
  - **材料说明：** 文本域，证明材料的说明

#### **调班申请界面**
- **调班信息设置：**
  - **原班次信息：**
    - **原班次日期：** 日期选择器，需要调整的班次日期
    - **原班次类型：** 显示框，显示原来的班次类型
    - **原班次时间：** 显示框，显示原班次的上下班时间
  - **目标班次信息：**
    - **目标日期：** 日期选择器，调班后的工作日期
    - **目标班次：** 下拉选择，选择调班后的班次
    - **目标时间：** 显示框，显示目标班次的时间
  - **调班原因：**
    - **调班事由：** 文本域，详细说明调班原因
    - **调班类型：** 单选按钮，临时调班/长期调班
    - **影响分析：** 文本域，分析调班对工作的影响
- **相关人员确认：**
  - **交接人员：** 下拉选择，工作交接的相关人员
  - **确认状态：** 显示框，相关人员的确认状态
  - **确认意见：** 文本域，相关人员的确认意见

#### **补卡申请界面**
- **补卡信息填写：**
  - **补卡日期：** 日期选择器，需要补卡的日期
  - **补卡时间：**
    - **上班时间：** 时间选择器，实际上班时间
    - **下班时间：** 时间选择器，实际下班时间
    - **补卡类型：** 单选按钮，上班补卡/下班补卡/全天补卡
  - **未打卡原因：**
    - **原因类型：** 下拉选择，忘记打卡/设备故障/外出工作等
    - **详细说明：** 文本域，详细说明未打卡的具体原因
    - **证明人员：** 下拉选择，可以证明出勤的同事
- **证明材料：**
  - **工作记录：** 文件上传，当天的工作记录或产出
  - **外出证明：** 文件上传，外出工作的相关证明
  - **其他证明：** 文件上传，其他相关证明材料

#### **审批处理界面**
- **申请详情查看：**
  - **申请信息：** 完整显示申请的所有信息
  - **申请人信息：** 显示申请人的基本信息和历史申请
  - **证明材料：** 查看申请人上传的证明材料
  - **影响分析：** 显示申请对工作安排的影响
- **审批操作：**
  - **审批意见：**
    - **审批结果：** 单选按钮，同意/拒绝/退回修改
    - **审批意见：** 文本域，填写详细的审批意见
    - **条件说明：** 文本域，附加条件或要求说明
  - **审批操作：**
    - **提交审批：** 按钮，提交审批结果
    - **转交他人：** 按钮，转交给其他人审批
    - **暂存草稿：** 按钮，暂存审批意见
    - **查看历史：** 链接，查看历史审批记录

#### **审批流程管理**
- **流程配置：**
  - **审批节点：**
    - **节点名称：** 输入框，审批节点的名称
    - **审批人员：** 下拉选择，节点的审批人员
    - **审批条件：** 文本域，触发审批的条件
    - **超时处理：** 下拉选择，超时的处理方式
  - **流程规则：**
    - **申请类型：** 多选框，适用的申请类型
    - **申请金额：** 数字输入框，触发流程的金额阈值
    - **申请天数：** 数字输入框，触发流程的天数阈值
    - **特殊条件：** 文本域，其他特殊触发条件
- **流程监控：**
  - **流程实例：** 显示当前运行的流程实例
  - **节点状态：** 显示各个审批节点的状态
  - **处理时长：** 显示各节点的处理时长
  - **异常处理：** 处理流程中的异常情况

#### **申请统计分析**
- **申请统计：**
  - **申请数量：** 统计各类申请的数量
  - **审批效率：** 统计审批的平均处理时间
  - **通过率：** 统计各类申请的通过率
  - **异常分析：** 分析申请中的异常情况
- **图表展示：**
  - **趋势图：** 显示申请数量的时间趋势
  - **分布图：** 显示申请类型的分布情况
  - **对比图：** 对比不同部门的申请情况
  - **效率图：** 显示审批效率的变化趋势

#### **移动端申请功能**
- **快速申请：**
  - **常用申请：** 快速入口，常用的申请类型
  - **一键申请：** 简化流程，快速提交申请
  - **模板申请：** 使用预设模板快速申请
  - **拍照上传：** 快速拍照上传证明材料
- **申请查询：**
  - **我的申请：** 查看个人的申请记录和状态
  - **待审批：** 查看需要自己审批的申请
  - **审批历史：** 查看历史审批记录
  - **消息通知：** 接收申请相关的消息通知

#### **权限控制**
- **申请权限：**
  - **申请类型权限：** 控制用户可以申请的类型
  - **申请额度权限：** 控制用户的申请额度限制
  - **申请时间权限：** 控制用户的申请时间限制
- **审批权限：**
  - **审批范围权限：** 控制用户的审批范围
  - **审批金额权限：** 控制用户的审批金额限制
  - **审批类型权限：** 控制用户可审批的申请类型

### 数据校验规则：

#### **请假时间**
- **校验规则：** 结束时间必须晚于开始时间，不能超过假期余额
- **错误提示文案：** "请假时间设置有误或超出假期余额"

#### **调班时间**
- **校验规则：** 调班日期不能冲突，必须在合理时间范围内
- **错误提示文案：** "调班时间冲突或超出允许范围"

#### **补卡时间**
- **校验规则：** 补卡时间必须在合理范围内，不能重复补卡
- **错误提示文案：** "补卡时间不合理或该时间已有打卡记录"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **申请基本信息**:
  - **申请类型 (application_type)**: String, 必填, 请假/调班/补卡
  - **申请人ID (applicant_id)**: String, 必填, 引用员工主数据
  - **开始时间 (start_time)**: DateTime, 必填
  - **结束时间 (end_time)**: DateTime, 必填
  - **申请原因 (reason)**: String, 必填, 最大500字符
- **审批信息**:
  - **审批人ID (approver_id)**: String, 必填, 引用员工主数据
  - **审批结果 (approval_result)**: String, 必填, 同意/拒绝/退回
  - **审批意见 (approval_comment)**: String, 可选, 最大200字符

### 展示数据
- **申请列表**: 申请的基本信息和状态
- **申请详情**: 完整的申请信息和审批记录
- **审批流程**: 申请的审批流程和进度
- **统计数据**: 申请的统计分析数据

### 空状态/零数据
- **无申请记录**: 显示"暂无申请记录，点击新增申请"
- **无待审批**: 显示"暂无待审批申请"
- **无统计数据**: 显示"数据不足，无法生成统计分析"

### API接口
- **申请查询**: GET /api/hr/applications
- **申请提交**: POST /api/hr/applications
- **申请审批**: PUT /api/hr/applications/{id}/approve
- **流程管理**: GET/POST/PUT /api/hr/approval-flows
- **统计分析**: GET /api/hr/application-statistics

## 5. 异常与边界处理 (Error & Edge Cases)

### **假期余额不足**
- **提示信息**: "您的假期余额不足，无法申请该时长的假期"
- **用户操作**: 显示当前假期余额和建议申请时长

### **审批人不在岗**
- **提示信息**: "当前审批人不在岗，系统将自动转交给代理人"
- **用户操作**: 显示代理审批人信息和预计处理时间

### **申请时间冲突**
- **提示信息**: "申请时间与现有申请或排班冲突"
- **用户操作**: 显示冲突的具体信息和调整建议

### **审批超时**
- **提示信息**: "审批超时，系统将自动处理或转交上级"
- **用户操作**: 提供催办功能和联系方式

### **申请撤销限制**
- **提示信息**: "申请已进入审批流程，无法撤销"
- **用户操作**: 提供联系审批人的方式和说明

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持请假、调班、补卡等多种考勤申请类型
- [ ] 申请审批流程可配置，支持多级审批
- [ ] 申请状态实时更新，审批进度清晰可见
- [ ] 移动端支持快速申请和审批操作
- [ ] 申请数据与考勤数据自动同步更新
- [ ] 支持申请的统计分析和报表导出
- [ ] 审批超时自动提醒和处理机制
- [ ] 申请权限和审批权限严格控制
- [ ] 所有页面元素符合全局设计规范
- [ ] 系统性能稳定，支持大量申请处理
