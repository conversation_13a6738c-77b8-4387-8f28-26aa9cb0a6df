# 功能模块规格说明书：员工自助门户模块

- **模块ID**: HR-010
- **所属子系统**: 人事管理子系统(HR)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 员工, **I want to** 查看个人薪酬信息, **so that** 了解自己的收入构成和发放情况。
- **As a** 员工, **I want to** 在线申请请假, **so that** 方便快捷地处理考勤事务。
- **As a** 员工, **I want to** 查看个人考勤记录, **so that** 了解自己的出勤情况。
- **As a** 员工, **I want to** 更新个人信息, **so that** 保持档案信息的准确性。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 员工账户已激活
- 权限配置已完成
- 个人信息已录入
- 移动端应用已部署

### 核心流程

#### 2.1 员工登录流程
1. 员工输入工号和密码
2. 系统验证身份信息
3. 检查账户状态和权限
4. 生成登录会话
5. 跳转到个人门户首页
6. 记录登录日志

#### 2.2 薪酬查询流程
1. 员工选择查询周期
2. 系统验证查询权限
3. 获取员工薪酬数据
4. 展示薪酬明细信息
5. 支持薪酬条下载
6. 记录查询日志

#### 2.3 考勤申请流程
1. 员工选择申请类型
2. 填写申请表单
3. 上传相关附件
4. 提交申请审批
5. 系统发送审批通知
6. 跟踪审批进度
7. 接收审批结果

#### 2.4 个人信息维护流程
1. 员工查看当前信息
2. 选择需要修改的项目
3. 填写修改内容
4. 上传证明材料
5. 提交修改申请
6. HR审核确认
7. 更新个人档案

### 后置条件
- 员工操作记录完整
- 数据变更有审核
- 系统日志详细
- 移动端同步更新

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：员工自助门户页面
### 页面目标：为员工提供便捷的自助服务功能

### 信息架构：
- **顶部区域**：包含 用户信息, 消息通知, 快捷操作, 退出登录
- **左侧区域**：包含 功能菜单, 常用功能, 个人中心
- **中间区域**：包含 功能内容, 数据展示, 操作表单
- **右侧区域**：包含 待办事项, 通知公告, 快速链接

### 交互逻辑与状态：

#### **门户首页**
- **个人概览：**
  - **基本信息：**
    - **员工照片：** 显示员工头像照片
    - **姓名工号：** 显示员工姓名和工号
    - **部门岗位：** 显示所属部门和岗位
    - **入职时间：** 显示入职时间和工龄
  - **快捷统计：**
    - **本月薪酬：** 显示本月薪酬金额
    - **本月考勤：** 显示本月出勤天数
    - **待办事项：** 显示待处理事项数量
    - **消息通知：** 显示未读消息数量
- **快捷功能：**
  - **常用操作：**
    - **薪酬查询：** 按钮，快速查询薪酬信息
    - **考勤申请：** 按钮，快速申请考勤事务
    - **个人信息：** 按钮，查看个人档案信息
    - **密码修改：** 按钮，修改登录密码
  - **移动端功能：**
    - **移动打卡：** 按钮，移动端考勤打卡
    - **移动申请：** 按钮，移动端申请功能
    - **消息推送：** 开关，消息推送设置
    - **指纹登录：** 开关，指纹登录设置

#### **薪酬信息查询**
- **薪酬查询：**
  - **查询条件：**
    - **查询年份：** 下拉选择，选择查询的年份
    - **查询月份：** 下拉选择，选择查询的月份
    - **查询类型：** 单选按钮，月薪/年终奖/补发
  - **查询结果：**
    - **薪酬汇总：** 显示薪酬的汇总信息
    - **薪酬明细：** 表格显示薪酬的详细构成
    - **扣减明细：** 表格显示各类扣减项目
    - **实发金额：** 突出显示实际发放金额
- **薪酬条管理：**
  - **薪酬条查看：**
    - **在线查看：** 链接，在线查看薪酬条
    - **下载薪酬条：** 按钮，下载PDF格式薪酬条
    - **打印薪酬条：** 按钮，打印薪酬条
    - **邮件发送：** 按钮，邮件发送薪酬条
  - **历史记录：**
    - **查询历史：** 显示薪酬查询的历史记录
    - **下载历史：** 显示薪酬条下载的历史记录
    - **操作日志：** 显示相关操作的日志记录

#### **考勤信息查询**
- **考勤记录查询：**
  - **查询条件：**
    - **查询日期：** 日期范围选择器，选择查询的日期范围
    - **记录类型：** 多选框，上班/下班/加班/请假
  - **查询结果：**
    - **考勤汇总：** 显示考勤的汇总统计
    - **考勤明细：** 表格显示每日考勤记录
    - **异常记录：** 突出显示异常的考勤记录
    - **统计分析：** 图表显示考勤趋势分析
- **考勤统计：**
  - **月度统计：**
    - **出勤天数：** 显示本月实际出勤天数
    - **请假天数：** 显示本月请假天数
    - **加班时间：** 显示本月加班时间
    - **迟到次数：** 显示本月迟到次数
  - **年度统计：**
    - **年度出勤：** 显示年度出勤情况
    - **年假余额：** 显示年假剩余天数
    - **调休余额：** 显示调休剩余时间
    - **考勤评分：** 显示考勤综合评分

#### **考勤申请功能**
- **请假申请：**
  - **申请表单：**
    - **请假类型：** 下拉选择，年假/事假/病假/婚假等
    - **请假时间：** 日期时间选择器，开始和结束时间
    - **请假天数：** 自动计算，显示请假天数
    - **请假原因：** 文本域，请假原因说明
    - **联系方式：** 输入框，请假期间联系方式
    - **附件上传：** 文件上传，相关证明材料
  - **申请操作：**
    - **保存草稿：** 按钮，保存申请草稿
    - **提交申请：** 按钮，提交请假申请
    - **取消申请：** 按钮，取消申请操作
- **其他申请：**
  - **加班申请：**
    - **加班日期：** 日期选择器，加班日期
    - **加班时间：** 时间选择器，加班开始和结束时间
    - **加班原因：** 文本域，加班原因说明
    - **加班类型：** 单选按钮，工作日加班/休息日加班
  - **调休申请：**
    - **调休日期：** 日期选择器，调休日期
    - **调休原因：** 文本域，调休原因说明
    - **补班安排：** 日期选择器，补班日期安排
  - **补卡申请：**
    - **补卡日期：** 日期选择器，需要补卡的日期
    - **补卡时间：** 时间选择器，补卡的具体时间
    - **补卡原因：** 文本域，补卡原因说明
    - **证明材料：** 文件上传，相关证明材料

#### **申请审批跟踪**
- **申请列表：**
  - **申请记录：**
    - **申请类型：** 显示申请的类型
    - **申请时间：** 显示申请的提交时间
    - **申请内容：** 显示申请的主要内容
    - **审批状态：** 显示当前审批状态
    - **审批人：** 显示当前审批人
  - **状态跟踪：**
    - **待审批：** 橙色标识，等待审批
    - **审批中：** 蓝色标识，正在审批
    - **已通过：** 绿色标识，审批通过
    - **已拒绝：** 红色标识，审批拒绝
- **审批详情：**
  - **审批流程：**
    - **审批节点：** 显示审批的各个节点
    - **审批人员：** 显示各节点的审批人员
    - **审批时间：** 显示各节点的审批时间
    - **审批意见：** 显示审批人的审批意见
  - **操作功能：**
    - **撤回申请：** 按钮，撤回未审批的申请
    - **催办申请：** 按钮，催办审批进度
    - **查看详情：** 链接，查看申请详细信息

#### **个人信息管理**
- **基本信息查看：**
  - **个人资料：**
    - **基本信息：** 显示姓名、性别、出生日期等
    - **联系信息：** 显示电话、邮箱、地址等
    - **身份信息：** 显示身份证号、民族、婚姻状况等
    - **教育背景：** 显示学历、专业、毕业院校等
  - **工作信息：**
    - **岗位信息：** 显示部门、岗位、职级等
    - **合同信息：** 显示合同类型、期限、签订日期等
    - **薪酬信息：** 显示薪酬等级、银行账户等
- **信息修改申请：**
  - **可修改项目：**
    - **联系方式：** 电话、邮箱、紧急联系人
    - **家庭地址：** 现住址、户籍地址
    - **银行账户：** 工资卡账户信息
    - **紧急联系人：** 紧急联系人信息
  - **修改流程：**
    - **选择修改项：** 选择需要修改的信息项
    - **填写新信息：** 输入修改后的信息
    - **上传证明：** 上传相关证明材料
    - **提交审核：** 提交修改申请审核
    - **跟踪进度：** 跟踪审核进度和结果

#### **移动端功能**
- **移动考勤：**
  - **位置打卡：**
    - **GPS定位：** 自动获取当前位置
    - **打卡按钮：** 大按钮，上班/下班打卡
    - **打卡记录：** 显示今日打卡记录
    - **异常提醒：** 提醒异常打卡情况
  - **拍照打卡：**
    - **拍照功能：** 打卡时拍照记录
    - **照片上传：** 自动上传打卡照片
    - **照片查看：** 查看历史打卡照片
- **移动申请：**
  - **快速申请：**
    - **请假申请：** 移动端请假申请
    - **加班申请：** 移动端加班申请
    - **补卡申请：** 移动端补卡申请
  - **语音输入：**
    - **语音识别：** 支持语音输入申请原因
    - **语音转文字：** 自动转换为文字
    - **语音校对：** 支持语音内容校对

#### **消息通知功能**
- **系统通知：**
  - **审批通知：**
    - **申请提醒：** 申请提交成功通知
    - **审批结果：** 审批通过或拒绝通知
    - **催办提醒：** 审批超时催办通知
  - **薪酬通知：**
    - **发放通知：** 薪酬发放完成通知
    - **薪酬条通知：** 薪酬条生成通知
    - **异常通知：** 薪酬异常情况通知
- **消息管理：**
  - **消息列表：**
    - **未读消息：** 突出显示未读消息
    - **已读消息：** 显示已读消息
    - **消息分类：** 按类型分类显示消息
  - **消息操作：**
    - **标记已读：** 标记消息为已读
    - **删除消息：** 删除不需要的消息
    - **消息设置：** 设置消息接收偏好

### 数据校验规则：

#### **请假天数**
- **校验规则：** 不能超过年假余额，不能为负数
- **错误提示文案：** "请假天数超过可用余额或输入有误"

#### **联系方式**
- **校验规则：** 手机号必须11位，邮箱格式正确
- **错误提示文案：** "请输入正确的手机号或邮箱格式"

#### **银行账户**
- **校验规则：** 必须是有效的银行账户格式
- **错误提示文案：** "请输入正确的银行账户信息"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **申请信息**:
  - **申请类型 (application_type)**: String, 必填, 请假/加班/补卡
  - **申请时间 (application_time)**: DateTime, 必填
  - **申请原因 (reason)**: String, 必填, 最大500字符
- **个人信息**:
  - **联系电话 (phone)**: String, 必填, 11位手机号
  - **邮箱地址 (email)**: String, 必填, 邮箱格式
  - **家庭地址 (address)**: String, 可选, 最大200字符
- **考勤数据**:
  - **打卡时间 (clock_time)**: DateTime, 必填
  - **打卡位置 (location)**: String, 必填, GPS坐标
  - **打卡照片 (photo)**: File, 可选, 图片文件

### 展示数据
- **个人信息**: 员工的基本信息和工作信息
- **薪酬数据**: 员工的薪酬明细和历史记录
- **考勤记录**: 员工的考勤记录和统计分析
- **申请记录**: 员工的申请历史和审批状态

### 空状态/零数据
- **无薪酬数据**: 显示"暂无薪酬数据，请联系HR"
- **无考勤记录**: 显示"暂无考勤记录"
- **无申请记录**: 显示"暂无申请记录"

### API接口
- **员工登录**: POST /api/hr/employee-login
- **薪酬查询**: GET /api/hr/employee-salary
- **考勤查询**: GET /api/hr/employee-attendance
- **申请提交**: POST /api/hr/employee-application
- **信息更新**: PUT /api/hr/employee-info

## 5. 异常与边界处理 (Error & Edge Cases)

### **登录失败**
- **提示信息**: "用户名或密码错误，请重新输入"
- **用户操作**: 提供密码重置和账户解锁功能

### **权限不足**
- **提示信息**: "您没有权限查看此信息"
- **用户操作**: 显示权限说明和申请流程

### **网络连接失败**
- **提示信息**: "网络连接失败，请检查网络设置"
- **用户操作**: 提供重试选项和离线功能

### **数据加载失败**
- **提示信息**: "数据加载失败，请稍后重试"
- **用户操作**: 提供刷新按钮和技术支持联系方式

### **文件上传失败**
- **提示信息**: "文件上传失败，请检查文件格式和大小"
- **用户操作**: 显示文件要求和重新上传选项

## 6. 验收标准 (Acceptance Criteria)

- [ ] 员工可以安全登录和使用自助门户
- [ ] 支持薪酬信息查询和薪酬条下载
- [ ] 支持考勤记录查询和统计分析
- [ ] 支持各类考勤申请的在线提交和跟踪
- [ ] 支持个人信息的查看和修改申请
- [ ] 移动端功能完整，支持移动打卡和申请
- [ ] 消息通知及时准确，支持多种通知方式
- [ ] 页面响应速度<3秒，移动端适配良好
- [ ] 数据安全可靠，权限控制严格
- [ ] 所有页面元素符合全局设计规范
