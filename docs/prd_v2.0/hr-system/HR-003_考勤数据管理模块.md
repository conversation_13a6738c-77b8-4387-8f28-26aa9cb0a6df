# 功能模块规格说明书：考勤数据管理模块

- **模块ID**: HR-003
- **所属子系统**: 人事管理子系统(HR)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** HR专员, **I want to** 自动采集考勤数据, **so that** 准确记录员工出勤情况并减少手工统计。
- **As a** 员工, **I want to** 通过多种方式打卡, **so that** 方便地记录我的上下班时间。
- **As a** 车间主管, **I want to** 查看班组考勤情况, **so that** 监控团队的出勤状态和工作安排。
- **As a** HR专员, **I want to** 配置考勤规则, **so that** 适应不同岗位的排班需求。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 员工档案已建立
- 考勤设备已配置
- 排班规则已设定
- 考勤规则已配置

### 核心流程

#### 2.1 考勤数据采集流程
1. 员工通过考勤机或移动端打卡
2. 系统自动记录打卡时间和地点
3. 验证员工身份和打卡有效性
4. 根据排班规则判断考勤状态
5. 自动识别迟到、早退、缺勤等异常
6. 生成考勤原始记录
7. 推送异常考勤通知

#### 2.2 排班管理流程
1. HR专员创建排班模板
2. 设置班次时间和考勤规则
3. 为员工分配排班计划
4. 支持临时调班和换班
5. 系统自动生成排班表
6. 员工查看个人排班安排
7. 异常排班及时调整

#### 2.3 考勤统计流程
1. 系统定时汇总考勤数据
2. 按员工、部门、时间维度统计
3. 计算出勤天数、工时、异常次数
4. 生成考勤报表和分析
5. 异常考勤数据标记和处理
6. 考勤数据传递给薪酬系统
7. 归档考勤历史数据

### 后置条件
- 考勤数据准确完整
- 异常考勤及时处理
- 考勤统计数据可用
- 薪酬计算数据准备就绪

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：考勤数据管理页面
### 页面目标：提供考勤数据的采集、统计、分析和管理功能

### 信息架构：
- **顶部区域**：包含 时间筛选, 部门筛选, 导出报表, 考勤设置
- **左侧区域**：包含 组织架构, 考勤状态, 异常分类, 快速筛选
- **中间区域**：包含 考勤列表, 考勤详情, 统计图表
- **右侧区域**：包含 考勤统计, 异常提醒, 快速操作

### 交互逻辑与状态：

#### **考勤数据列表界面**
- **考勤记录展示：**
  - **考勤卡片：**
    - **员工信息：** 显示员工姓名、工号、部门
    - **打卡时间：** 显示上班和下班打卡时间
    - **考勤状态：** 正常/迟到/早退/缺勤/加班
    - **工时统计：** 显示实际工时和标准工时
    - **异常标识：** 红色标识异常考勤记录
  - **状态标识：**
    - **正常：** 绿色标识，按时上下班
    - **迟到：** 橙色标识，上班时间晚于规定时间
    - **早退：** 黄色标识，下班时间早于规定时间
    - **缺勤：** 红色标识，未打卡或请假
    - **加班：** 蓝色标识，超出标准工时
    - **调休：** 紫色标识，调休或补休
- **筛选和搜索：**
  - **时间筛选：** 按日期范围筛选考勤记录
  - **部门筛选：** 按组织架构筛选考勤数据
  - **状态筛选：** 按考勤状态筛选记录
  - **员工搜索：** 支持姓名、工号搜索

#### **考勤打卡界面**
- **打卡功能：**
  - **打卡按钮：**
    - **上班打卡：** 大按钮，显示当前时间和打卡状态
    - **下班打卡：** 大按钮，显示下班时间和工时统计
    - **外勤打卡：** 特殊按钮，支持外勤人员打卡
    - **补卡申请：** 链接按钮，申请补打卡
  - **打卡信息显示：**
    - **当前时间：** 实时显示当前日期和时间
    - **打卡地点：** 显示打卡的地理位置（移动端）
    - **班次信息：** 显示当前班次的上下班时间
    - **工时统计：** 显示已工作时间和剩余时间
- **打卡验证：**
  - **身份验证：** 人脸识别、指纹识别或刷卡
  - **地点验证：** GPS定位验证打卡地点
  - **时间验证：** 验证打卡时间的有效性
  - **设备验证：** 验证打卡设备的合法性

#### **排班管理界面**
- **排班模板：**
  - **班次设置：**
    - **班次名称：** 输入框，如早班、中班、晚班
    - **上班时间：** 时间选择器，班次开始时间
    - **下班时间：** 时间选择器，班次结束时间
    - **休息时间：** 时间选择器，中间休息时间
    - **标准工时：** 数字输入框，班次标准工时
  - **考勤规则：**
    - **迟到容忍：** 数字输入框，迟到容忍分钟数
    - **早退容忍：** 数字输入框，早退容忍分钟数
    - **加班起算：** 数字输入框，加班起算分钟数
    - **打卡限制：** 时间范围，允许打卡的时间窗口
- **排班计划：**
  - **排班日历：**
    - **月视图：** 显示整月的排班安排
    - **周视图：** 显示一周的详细排班
    - **日视图：** 显示单日的班次安排
    - **拖拽排班：** 支持拖拽方式安排班次
  - **批量排班：**
    - **模板应用：** 将排班模板应用到指定时间段
    - **循环排班：** 设置循环排班规则
    - **批量调整：** 批量调整多人的排班安排
    - **导入排班：** 支持Excel导入排班计划

#### **考勤统计分析**
- **统计报表：**
  - **个人考勤统计：**
    - **出勤天数：** 统计员工的实际出勤天数
    - **迟到次数：** 统计迟到的次数和时长
    - **早退次数：** 统计早退的次数和时长
    - **加班时长：** 统计加班的总时长
    - **缺勤天数：** 统计缺勤和请假天数
  - **部门考勤统计：**
    - **出勤率：** 计算部门的整体出勤率
    - **异常率：** 计算考勤异常的比例
    - **加班统计：** 统计部门的加班情况
    - **人员对比：** 对比部门内员工的考勤情况
- **图表分析：**
  - **趋势图：** 显示考勤数据的时间趋势
  - **对比图：** 对比不同部门或员工的考勤情况
  - **分布图：** 显示考勤异常的分布情况
  - **热力图：** 显示考勤打卡的时间热力分布

#### **异常考勤处理**
- **异常识别：**
  - **自动识别：**
    - **迟到识别：** 自动识别超过容忍时间的迟到
    - **早退识别：** 自动识别提前下班的早退
    - **缺卡识别：** 自动识别未打卡的情况
    - **异常打卡：** 识别非正常时间或地点的打卡
  - **异常标记：**
    - **异常类型：** 标记异常的具体类型
    - **异常程度：** 标记异常的严重程度
    - **处理状态：** 标记异常的处理状态
    - **处理建议：** 提供异常处理的建议
- **异常处理：**
  - **异常审核：**
    - **审核列表：** 显示待审核的异常考勤
    - **审核操作：** 确认、忽略、转申请等操作
    - **审核意见：** 填写审核的具体意见
    - **批量审核：** 支持批量处理相似异常
  - **异常调整：**
    - **时间调整：** 调整错误的打卡时间
    - **状态调整：** 调整错误的考勤状态
    - **补卡处理：** 为缺卡员工补充打卡记录
    - **特殊处理：** 处理特殊情况的考勤异常

#### **移动端考勤功能**
- **移动打卡：**
  - **快速打卡：** 一键完成上下班打卡
  - **GPS定位：** 自动获取打卡地理位置
  - **拍照打卡：** 支持拍照记录打卡现场
  - **离线打卡：** 支持网络不佳时的离线打卡
- **考勤查询：**
  - **个人考勤：** 查看个人的考勤记录和统计
  - **排班查询：** 查看个人的排班安排
  - **异常查询：** 查看个人的考勤异常记录
  - **申请入口：** 快速进入各类考勤申请

#### **考勤设备管理**
- **设备配置：**
  - **设备信息：**
    - **设备名称：** 输入框，考勤设备的名称
    - **设备编号：** 输入框，设备的唯一编号
    - **设备类型：** 下拉选择，指纹机/人脸机/刷卡机
    - **安装位置：** 输入框，设备的安装位置
  - **设备参数：**
    - **IP地址：** 输入框，设备的网络IP地址
    - **端口号：** 输入框，设备的通信端口
    - **通信协议：** 下拉选择，设备的通信协议
    - **数据同步：** 设置数据同步的频率和方式
- **设备监控：**
  - **设备状态：** 实时监控设备的在线状态
  - **数据同步：** 监控设备数据的同步情况
  - **异常报警：** 设备异常时的报警机制
  - **维护记录：** 记录设备的维护和故障情况

### 数据校验规则：

#### **打卡时间**
- **校验规则：** 打卡时间不能超出合理范围，不能重复打卡
- **错误提示文案：** "打卡时间异常，请确认后重新打卡"

#### **排班时间**
- **校验规则：** 下班时间必须晚于上班时间，班次不能重叠
- **错误提示文案：** "班次时间设置有误，请检查时间安排"

#### **考勤规则**
- **校验规则：** 容忍时间必须为正数，不能超过班次时长
- **错误提示文案：** "考勤规则参数设置有误，请重新设置"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **打卡记录**:
  - **员工ID (employee_id)**: String, 必填, 引用员工主数据
  - **打卡时间 (clock_time)**: DateTime, 必填, 精确到秒
  - **打卡类型 (clock_type)**: String, 必填, 上班/下班/外勤
  - **打卡地点 (location)**: String, 可选, GPS坐标或地址
- **排班信息**:
  - **班次名称 (shift_name)**: String, 必填, 最大20字符
  - **上班时间 (start_time)**: Time, 必填
  - **下班时间 (end_time)**: Time, 必填

### 展示数据
- **考勤列表**: 员工的考勤记录和状态
- **考勤统计**: 出勤率、异常率等统计数据
- **排班计划**: 员工的排班安排信息
- **异常记录**: 考勤异常的详细信息

### 空状态/零数据
- **无考勤记录**: 显示"暂无考勤记录，请先进行打卡"
- **无排班安排**: 显示"暂无排班安排，请联系HR设置排班"
- **无异常记录**: 显示"考勤正常，无异常记录"

### API接口
- **考勤记录**: GET /api/hr/attendance
- **打卡接口**: POST /api/hr/clock-in
- **排班管理**: GET/POST/PUT /api/hr/schedules
- **考勤统计**: GET /api/hr/attendance/statistics
- **异常处理**: GET/PUT /api/hr/attendance/exceptions

## 5. 异常与边界处理 (Error & Edge Cases)

### **重复打卡**
- **提示信息**: "您已经打过卡了，请勿重复打卡"
- **用户操作**: 显示最近的打卡记录和时间

### **异常打卡时间**
- **提示信息**: "打卡时间异常，请确认当前时间是否正确"
- **用户操作**: 提供时间校准和补卡申请选项

### **设备离线**
- **提示信息**: "考勤设备离线，请联系管理员或使用移动端打卡"
- **用户操作**: 提供移动端打卡入口和设备状态查询

### **GPS定位失败**
- **提示信息**: "无法获取位置信息，请检查GPS设置或网络连接"
- **用户操作**: 提供手动选择位置和网络检测功能

### **排班冲突**
- **提示信息**: "检测到排班时间冲突，请调整班次安排"
- **用户操作**: 显示冲突的班次信息和调整建议

## 6. 验收标准 (Acceptance Criteria)

- [ ] 对接考勤机，自动获取打卡记录，准确率≥99.5%
- [ ] 支持移动端打卡和GPS定位验证
- [ ] 自动识别迟到、早退、缺勤等异常，识别准确率≥95%
- [ ] 支持固定班次、倒班等多种排班规则配置
- [ ] 考勤数据查询响应时间<2秒
- [ ] 支持考勤数据的统计分析和报表导出
- [ ] 异常考勤处理流程完整，支持审核和调整
- [ ] 移动端考勤功能完善，支持离线打卡
- [ ] 所有页面元素符合全局设计规范
- [ ] 系统性能稳定，支持大量考勤数据处理
