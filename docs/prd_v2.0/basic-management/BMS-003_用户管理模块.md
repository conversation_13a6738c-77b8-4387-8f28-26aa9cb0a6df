# 功能模块规格说明书：用户管理模块

- **模块ID**: BMS-003
- **所属子系统**: 基础管理子系统
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 系统管理员, **I want to** 管理用户的完整生命周期, **so that** 确保账号安全和合规。
- **As a** 系统管理员, **I want to** 批量导入和操作用户, **so that** 提高用户管理效率。
- **As a** HR专员, **I want to** 快速完成新员工的系统配置, **so that** 新员工能立即开始工作。
- **As a** 部门经理, **I want to** 查看和维护本部门员工信息, **so that** 保持员工信息的准确性。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有用户管理权限（参考业务规则R1.1 RBAC权限控制）
- 组织架构已建立
- 相关角色已定义

### 核心流程

#### 2.1 新用户创建流程
1. 系统管理员点击"新增用户"按钮
2. 填写用户基本信息（工号、姓名、部门、岗位等）
3. 系统验证工号和登录账号的唯一性
4. 设置初始密码或系统自动生成
5. 分配用户角色和权限
6. 保存用户信息并发送登录凭证
7. 记录用户创建日志

#### 2.2 用户状态管理流程
1. 选择需要变更状态的用户
2. 选择目标状态（启用/禁用/锁定）
3. 填写状态变更原因
4. 确认变更操作
5. 系统立即更新用户状态
6. 发送状态变更通知
7. 记录状态变更日志

#### 2.3 批量用户导入流程
1. 下载用户导入模板
2. 按模板格式填写用户信息
3. 上传Excel文件
4. 系统验证数据格式和完整性
5. 预览导入结果，显示错误信息
6. 确认导入，系统批量创建用户
7. 生成导入结果报告

### 后置条件
- 用户信息更新立即生效
- 用户权限变更实时同步
- 所有操作记录到审计日志

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：用户管理页
### 页面目标：提供高效的用户信息管理和操作界面

### 信息架构：
- **顶部工具栏**：包含 新增用户, 批量导入, 导出用户, 刷新按钮
- **筛选区域**：包含 部门筛选, 状态筛选, 角色筛选, 搜索框
- **用户列表区域**：包含 用户表格, 分页器, 批量操作工具
- **用户详情面板**：包含 用户信息, 角色权限, 操作历史

### 交互逻辑与状态：

#### **新增用户按钮**
- **默认状态：** 蓝色背景(#1890FF)，白色文字"新增用户"
- **悬停状态：** 背景色加深至#096DD9
- **权限不足状态：** 灰色背景，禁用状态
- **交互行为：** 点击打开新增用户对话框

#### **用户搜索框**
- **默认状态：** 占位符"搜索工号、姓名或邮箱"，搜索图标
- **聚焦状态：** 蓝色边框，显示搜索历史下拉
- **搜索中状态：** 显示加载图标，实时搜索
- **交互行为：** 支持模糊搜索，Enter键确认搜索

#### **用户状态筛选器**
- **默认状态：** 显示"全部状态"，下拉箭头
- **展开状态：** 显示状态选项列表（全部/启用/禁用/锁定）
- **选中状态：** 显示选中的状态，蓝色文字
- **交互行为：** 点击切换筛选条件，自动刷新列表

#### **用户表格**
- **表头样式：** 灰色背景(#FAFAFA)，加粗文字，支持排序
- **行样式：** 奇偶行背景色区分，悬停高亮
- **状态列显示：** 
  - 启用：绿色标签(#52C41A)
  - 禁用：灰色标签(#8C8C8C)
  - 锁定：红色标签(#F5222D)
- **操作列：** 编辑、重置密码、禁用/启用、删除按钮

#### **批量操作工具栏**
- **默认状态：** 隐藏状态，选中用户后显示
- **显示状态：** 浅蓝背景，显示选中数量和操作按钮
- **操作按钮：** 批量启用、批量禁用、批量删除、批量分配角色
- **交互行为：** 操作前显示确认对话框

#### **用户详情面板**
- **默认状态：** 右侧滑出面板，显示用户详细信息
- **编辑状态：** 表单字段可编辑，显示保存/取消按钮
- **加载状态：** 显示骨架屏，数据加载中
- **关闭状态：** 点击遮罩或关闭按钮隐藏面板

### 数据校验规则：

#### **工号字段**
- **校验规则：** 必填，4-20位字符，全局唯一
- **错误提示文案：** "工号不能为空" / "工号已存在"

#### **登录账号字段**
- **校验规则：** 必填，4-50位字符，全局唯一
- **错误提示文案：** "登录账号不能为空" / "登录账号已存在"

#### **邮箱字段**
- **校验规则：** 可选，邮箱格式验证，全局唯一
- **错误提示文案：** "邮箱格式不正确" / "邮箱已被使用"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **工号 (employee_id)**: String, 必填, 4-20位字符
- **姓名 (name)**: String, 必填, 2-20位字符
- **登录账号 (username)**: String, 必填, 4-50位字符
- **邮箱 (email)**: String, 可选, 邮箱格式
- **手机号 (phone)**: String, 可选, 11位数字
- **所属部门 (department_id)**: String, 必填, 部门ID
- **岗位 (position)**: String, 可选, 2-50位字符
- **用户状态 (status)**: Enum, 必填, [启用/禁用/锁定]

### 展示数据
- **用户基本信息**: 工号、姓名、部门、岗位、状态
- **登录信息**: 最后登录时间、登录次数、在线状态
- **角色权限**: 分配的角色列表、权限范围
- **操作历史**: 创建时间、最后修改时间、操作记录

### 空状态/零数据
- **无用户数据**: 显示"暂无用户数据，请先添加用户"
- **搜索无结果**: 显示"未找到匹配的用户，请尝试其他关键词"
- **筛选无结果**: 显示"当前筛选条件下无用户数据"

### API接口
- **获取用户列表**: GET /api/users
- **创建用户**: POST /api/users
- **更新用户**: PUT /api/users/{id}
- **删除用户**: DELETE /api/users/{id}
- **批量导入**: POST /api/users/batch-import
- **重置密码**: POST /api/users/{id}/reset-password

## 5. 异常与边界处理 (Error & Edge Cases)

### **工号或登录账号重复**
- **提示信息**: "工号/登录账号已存在，请使用其他账号"
- **用户操作**: 相关字段标红，聚焦到错误字段

### **删除有业务数据关联的用户**
- **提示信息**: "该用户存在业务数据关联，不能直接删除"
- **用户操作**: 提供"禁用用户"选项，或显示关联数据清单

### **批量导入数据格式错误**
- **提示信息**: "第X行数据格式错误：具体错误信息"
- **用户操作**: 显示错误详情列表，支持下载错误报告

### **用户状态变更失败**
- **提示信息**: "用户状态变更失败，请稍后重试"
- **用户操作**: 状态恢复到变更前，提供重试按钮

### **权限不足**
- **提示信息**: "您没有权限执行此操作"
- **用户操作**: 相关按钮禁用，操作被阻止

### **密码重置邮件发送失败**
- **提示信息**: "密码重置邮件发送失败，请检查邮箱地址"
- **用户操作**: 提供重新发送选项，或手动设置密码

## 6. 验收标准 (Acceptance Criteria)

- [ ] 用户可以创建、编辑、删除、查询用户信息
- [ ] 工号和登录账号全局唯一性校验正确执行
- [ ] 支持Excel批量导入用户，错误处理完善
- [ ] 用户状态变更立即生效，权限同步更新
- [ ] 密码复杂度策略可配置且正确执行
- [ ] 搜索功能支持工号、姓名、邮箱模糊查找
- [ ] 筛选功能支持部门、状态、角色多维度筛选
- [ ] 批量操作功能正常工作，支持批量状态变更
- [ ] 用户详情面板显示完整信息和操作历史
- [ ] 所有操作记录到审计日志
- [ ] 界面支持响应式设计，移动端可正常使用
- [ ] 用户列表分页加载，支持大数据量展示
- [ ] 批量导入1000条记录时间小于30秒
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持键盘导航和无障碍访问
