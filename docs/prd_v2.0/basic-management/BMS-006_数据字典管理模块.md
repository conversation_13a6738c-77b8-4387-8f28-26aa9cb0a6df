# 功能模块规格说明书：数据字典管理模块

- **模块ID**: BMS-006
- **所属子系统**: 基础管理子系统
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 系统管理员, **I want to** 维护系统基础数据字典, **so that** 规范业务数据录入标准。
- **As a** 系统管理员, **I want to** 分类管理数据字典, **so that** 便于维护和查找字典项。
- **As a** 业务用户, **I want to** 在表单中使用标准化的字典值, **so that** 确保数据的一致性和准确性。
- **As a** 开发人员, **I want to** 通过API获取字典数据, **so that** 业务表单能够自动关联字典。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有数据字典管理权限
- 系统基础架构已建立
- 业务表单已定义字典关联需求

### 核心流程

#### 2.1 字典分类创建流程
1. 系统管理员点击"新增字典分类"按钮
2. 填写分类信息（分类编码、名称、描述）
3. 设置分类层级关系（支持多级分类）
4. 配置分类权限和可见性
5. 保存分类信息
6. 系统自动生成分类树结构

#### 2.2 字典值维护流程
1. 选择字典分类
2. 点击"新增字典值"按钮
3. 填写字典值信息（编码、名称、值、排序）
4. 设置字典值状态（启用/禁用）
5. 配置字典值的业务属性
6. 保存字典值
7. 系统验证编码唯一性

#### 2.3 业务表单关联流程
1. 业务表单定义字典字段
2. 指定关联的字典分类
3. 系统自动加载字典值列表
4. 用户在表单中选择字典值
5. 系统验证选择值的有效性
6. 保存业务数据时关联字典值

### 后置条件
- 字典数据变更立即生效
- 业务表单自动同步字典更新
- 字典操作记录到审计日志

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：数据字典管理页
### 页面目标：提供高效的数据字典分类和值的管理界面

### 信息架构：
- **左侧区域**：包含 字典分类树, 分类搜索, 新增分类按钮
- **右侧区域**：包含 字典值列表, 字典值编辑面板, 批量操作工具
- **顶部工具栏**：包含 导入字典, 导出字典, 刷新按钮, 帮助文档

### 交互逻辑与状态：

#### **字典分类树**
- **默认状态：** 展示根分类，子分类折叠显示
- **展开状态：** 点击分类前的展开图标，显示子分类
- **选中状态：** 蓝色背景(#E6F7FF)，蓝色左边框
- **悬停状态：** 浅灰背景(#FAFAFA)，显示操作图标
- **交互行为：** 点击选中分类，右侧显示对应字典值

#### **分类节点**
- **默认状态：** 显示分类图标、名称、字典值数量
- **编辑状态：** 双击进入编辑模式，显示输入框
- **新增状态：** 显示"新增子分类"按钮
- **删除确认：** 右键菜单显示删除选项，需要确认

#### **新增分类按钮**
- **默认状态：** 蓝色背景(#1890FF)，白色文字"新增分类"
- **悬停状态：** 背景色加深至#096DD9
- **禁用状态：** 权限不足时灰色背景，不可点击
- **交互行为：** 点击打开新增分类对话框

#### **字典值列表**
- **表头样式：** 灰色背景(#FAFAFA)，包含编码、名称、值、排序、状态列
- **行样式：** 奇偶行背景色区分，悬停高亮
- **状态列显示：**
  - 启用：绿色标签(#52C41A)，文字"启用"
  - 禁用：灰色标签(#8C8C8C)，文字"禁用"
- **排序功能：** 支持拖拽调整排序，实时保存

#### **字典值编辑面板**
- **默认状态：** 右侧滑出面板，显示字典值详细信息
- **编辑状态：** 表单字段可编辑，显示保存/取消按钮
- **新增状态：** 清空表单，聚焦到编码字段
- **加载状态：** 显示骨架屏，数据加载中

#### **批量操作工具栏**
- **默认状态：** 选中字典值后显示在列表上方
- **操作按钮：** 批量启用、批量禁用、批量删除、批量导出
- **选中计数：** 显示"已选中X项"
- **交互行为：** 操作前显示确认对话框

#### **字典搜索框**
- **默认状态：** 占位符"搜索字典分类或值"，搜索图标
- **聚焦状态：** 蓝色边框，显示搜索历史
- **搜索结果：** 匹配的分类和字典值高亮显示
- **交互行为：** 实时搜索，支持编码和名称查找

### 数据校验规则：

#### **分类编码**
- **校验规则：** 必填，3-20位字符，同级唯一
- **错误提示文案：** "分类编码不能为空" / "同级分类编码不能重复"

#### **字典值编码**
- **校验规则：** 必填，1-50位字符，分类内唯一
- **错误提示文案：** "字典值编码不能为空" / "该分类下编码已存在"

#### **字典值名称**
- **校验规则：** 必填，1-100位字符
- **错误提示文案：** "字典值名称不能为空"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **分类编码 (category_code)**: String, 必填, 3-20位字符
- **分类名称 (category_name)**: String, 必填, 2-50位字符
- **字典值编码 (dict_code)**: String, 必填, 1-50位字符
- **字典值名称 (dict_name)**: String, 必填, 1-100位字符
- **字典值 (dict_value)**: String, 必填, 实际存储的值
- **排序号 (sort_order)**: Integer, 可选, 默认0
- **状态 (status)**: Enum, 必填, [启用/禁用]
- **描述 (description)**: String, 可选, 最大200字符

### 展示数据
- **分类树结构**: 层级关系、分类信息、字典值统计
- **字典值列表**: 编码、名称、值、排序、状态、创建时间
- **使用统计**: 字典值在业务表单中的使用次数
- **关联信息**: 字典值关联的业务表单和字段

### 空状态/零数据
- **无分类数据**: 显示"暂无字典分类，请先创建分类"
- **无字典值**: 显示"该分类下暂无字典值，请添加字典值"
- **搜索无结果**: 显示"未找到匹配的字典项，请尝试其他关键词"

### API接口
- **获取分类树**: GET /api/dict/categories/tree
- **获取字典值**: GET /api/dict/values/{category_code}
- **创建分类**: POST /api/dict/categories
- **创建字典值**: POST /api/dict/values
- **更新字典值**: PUT /api/dict/values/{id}
- **批量导入**: POST /api/dict/batch-import

## 5. 异常与边界处理 (Error & Edge Cases)

### **删除被业务表单引用的字典值**
- **提示信息**: "该字典值正在被业务表单使用，不能直接删除"
- **用户操作**: 显示引用详情，提供"禁用"选项替代删除

### **分类编码重复**
- **提示信息**: "同级分类编码已存在，请使用其他编码"
- **用户操作**: 编码字段标红，聚焦到编码输入框

### **字典值导入格式错误**
- **提示信息**: "第X行数据格式错误：具体错误信息"
- **用户操作**: 显示错误详情列表，支持下载错误报告

### **分类删除检查失败**
- **提示信息**: "该分类下还有字典值，请先清空后再删除"
- **用户操作**: 显示子字典值列表，提供批量转移功能

### **字典值排序冲突**
- **提示信息**: "排序号冲突，系统将自动调整"
- **用户操作**: 自动重新排序，显示调整结果

### **网络异常导致保存失败**
- **提示信息**: "保存失败，请检查网络后重试"
- **用户操作**: 保持编辑状态，提供重试按钮

## 6. 验收标准 (Acceptance Criteria)

- [ ] 用户可以创建、编辑、删除字典分类和字典值
- [ ] 支持多级字典分类，层级关系清晰
- [ ] 字典值编码在分类内唯一性校验正确执行
- [ ] 字典值排序功能正常，支持拖拽调整
- [ ] 字典值状态管理功能完善，启用/禁用立即生效
- [ ] 搜索功能支持分类和字典值的模糊查找
- [ ] 批量操作功能正常工作，支持批量状态变更
- [ ] 业务表单自动关联字典，下拉框数据同步更新
- [ ] 被引用的字典值限制删除，提供合理的替代方案
- [ ] 支持Excel格式的字典数据导入导出
- [ ] 所有操作记录到审计日志
- [ ] 界面支持响应式设计，移动端可正常使用
- [ ] 字典数据加载性能良好，大量字典值响应时间小于2秒
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持键盘导航和无障碍访问
