# 功能模块规格说明书：计量单位管理模块

- **模块ID**: BMS-009
- **所属子系统**: 基础管理子系统(BMS)
- **最后更新**: 2025-07-31

## 1. 用户故事 (User Stories)

- **As a** 系统管理员, **I want to** 建立标准化的计量单位主数据库, **so that** 确保全系统计量单位的统一性和准确性。
- **As a** 物料管理员, **I want to** 为物料配置正确的计量单位, **so that** 确保物料数据的准确性和一致性。
- **As a** 库存管理员, **I want to** 使用标准化的计量单位进行库存管理, **so that** 避免单位换算错误导致的库存偏差。
- **As a** 采购员, **I want to** 在采购过程中使用统一的计量单位, **so that** 确保采购数据与库存数据的一致性。
- **As a** 财务人员, **I want to** 基于标准计量单位进行成本核算, **so that** 确保成本计算的准确性。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有计量单位管理权限
- 系统基础配置已完成
- 量纲分类体系已建立

### 核心流程

#### 2.1 基本单位创建流程
1. 选择单位所属量纲分类
2. 填写单位基本信息（编码、名称、符号）
3. 设置单位类型和精度要求
4. 配置单位的显示格式
5. 设置单位状态和有效期
6. 提交单位审核
7. 审核通过后单位生效
8. 通知相关系统更新单位信息

#### 2.2 单位换算关系配置流程
1. 选择需要建立换算关系的单位组
2. 设置基准单位（通常为最小单位）
3. 配置其他单位与基准单位的换算比例
4. 设置换算精度和舍入规则
5. 验证换算关系的正确性
6. 测试换算计算结果
7. 保存换算关系配置
8. 更新相关业务模块的换算规则

#### 2.3 行业专用单位管理流程
1. 识别玻璃行业特有的计量需求
2. 定义行业专用单位（如玻璃厚度、强度等）
3. 建立专用单位与标准单位的对应关系
4. 配置专用单位的使用场景和限制
5. 设置专用单位的换算规则
6. 验证专用单位在业务中的适用性
7. 发布行业专用单位标准
8. 培训相关人员使用专用单位

#### 2.4 单位标准化管理流程
1. 定期检查系统中单位使用情况
2. 识别不规范或重复的单位使用
3. 制定单位标准化方案
4. 执行单位数据清理和标准化
5. 更新相关业务数据
6. 验证标准化结果
7. 建立单位使用监控机制
8. 持续优化单位管理体系

### 后置条件
- 计量单位信息完整准确
- 换算关系配置正确
- 相关业务模块已更新
- 单位使用标准化完成

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：计量单位管理页面
### 页面目标：提供计量单位的创建、维护和换算关系管理功能

### 信息架构：
- **顶部区域**：包含 单位搜索, 新建单位, 批量操作, 导入导出
- **左侧区域**：包含 量纲分类树, 单位类型筛选, 状态筛选
- **中间区域**：包含 单位列表, 单位详情, 换算关系配置
- **右侧区域**：包含 单位统计, 使用情况, 操作历史

### 交互逻辑与状态：

#### **量纲分类树**
- **默认状态：** 展示主要量纲分类，子分类折叠显示
- **展开状态：** 点击分类前的展开图标，显示子分类和单位
- **选中状态：** 蓝色背景(#E6F7FF)，蓝色左边框
- **悬停状态：** 浅灰背景(#FAFAFA)，显示操作图标
- **交互行为：** 点击选中分类，中间区域显示对应单位列表

#### **单位列表**
- **列表项：**
  - **单位编码：** 显示唯一单位编码，点击进入详情
  - **单位名称：** 显示单位名称和符号
  - **量纲分类：** 彩色标签显示所属量纲
  - **单位类型：** 显示基本单位/换算单位/专用单位
  - **使用状态：** 显示启用/停用状态
  - **使用频次：** 显示在系统中的使用次数
- **排序功能：** 支持按编码、名称、类型、使用频次排序
- **筛选功能：** 支持按量纲、类型、状态筛选
- **搜索功能：** 支持单位编码、名称、符号的模糊搜索

#### **单位详情/编辑界面**
- **基本信息：**
  - **单位编码：** 输入框，自动生成或手工输入，必填
  - **单位名称：** 输入框，必填，最大30字符
  - **单位符号：** 输入框，必填，最大10字符
  - **量纲分类：** 下拉选择，长度/重量/面积/体积等
- **单位属性：**
  - **单位类型：** 单选框，基本单位/换算单位/专用单位
  - **精度要求：** 数字输入框，小数位数
  - **显示格式：** 输入框，单位显示格式模板
  - **舍入规则：** 下拉选择，四舍五入/向上取整/向下取整
- **换算关系：**
  - **基准单位：** 下拉选择，同量纲的基准单位
  - **换算比例：** 数字输入框，与基准单位的换算比例
  - **换算公式：** 文本显示，自动生成的换算公式
  - **精度设置：** 数字输入框，换算结果精度

#### **换算关系管理**
- **换算组配置：**
  - **量纲选择：** 下拉选择，选择要配置的量纲
  - **基准单位：** 单选框，选择该量纲的基准单位
  - **换算单位：** 多选表格，配置其他单位的换算关系
  - **批量设置：** 支持批量设置换算比例
- **换算测试：**
  - **源单位：** 下拉选择，选择转换前单位
  - **目标单位：** 下拉选择，选择转换后单位
  - **数值输入：** 数字输入框，输入要转换的数值
  - **转换结果：** 显示字段，显示换算结果
- **关系图表：**
  - **换算关系图：** 图形化显示单位间的换算关系
  - **关系验证：** 验证换算关系的一致性
  - **循环检测：** 检测是否存在循环换算关系

### 数据校验规则：

#### **单位编码**
- **校验规则：** 编码必须全局唯一，符合编码规则格式
- **错误提示文案：** "单位编码已存在或格式不正确"

#### **换算比例**
- **校验规则：** 换算比例必须大于0，不能为空
- **错误提示文案：** "换算比例必须为正数且不能为空"

#### **单位符号**
- **校验规则：** 符号在同量纲内必须唯一
- **错误提示文案：** "该量纲下单位符号已存在"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **单位基本信息**:
  - **单位编码 (unit_code)**: String, 必填, 全局唯一
  - **单位名称 (unit_name)**: String, 必填, 最大30字符
  - **单位符号 (unit_symbol)**: String, 必填, 最大10字符
  - **量纲分类 (dimension_type)**: Enum, 必填, 长度/重量/面积/体积等
- **单位属性**:
  - **单位类型 (unit_type)**: Enum, 必填, 基本单位/换算单位/专用单位
  - **精度要求 (precision)**: Integer, 必填, 小数位数
  - **显示格式 (display_format)**: String, 可选, 显示格式模板
- **换算关系**:
  - **基准单位 (base_unit_id)**: String, 可选, 关联基准单位ID
  - **换算比例 (conversion_rate)**: Decimal, 可选, 与基准单位的换算比例

### 展示数据
- **单位列表**: 单位的基本信息和使用状态
- **换算关系**: 单位间的换算关系和比例
- **使用统计**: 单位在系统中的使用情况统计
- **关系图表**: 单位换算关系的图形化展示

### 空状态/零数据
- **无单位数据**: 显示"暂无计量单位，请先创建基本单位"
- **无换算关系**: 显示"暂未配置换算关系"
- **无使用记录**: 显示"该单位暂未被系统使用"

### API接口
- **单位查询**: GET /api/bms/units
- **单位创建**: POST /api/bms/units
- **单位更新**: PUT /api/bms/units/{id}
- **换算关系**: GET/POST /api/bms/units/conversions
- **单位换算**: POST /api/bms/units/convert

## 5. 异常与边界处理 (Error & Edge Cases)

### **单位编码重复**
- **提示信息**: "单位编码已存在，请使用其他编码"
- **用户操作**: 编码字段标红，聚焦到编码输入框

### **删除被使用的单位**
- **提示信息**: "该单位正在被系统使用，不能直接删除"
- **用户操作**: 显示使用详情，提供"停用单位"选项

### **换算关系冲突**
- **提示信息**: "检测到换算关系冲突，请检查配置"
- **用户操作**: 高亮冲突项，提供修复建议

### **换算精度丢失**
- **提示信息**: "换算可能导致精度丢失，建议调整精度设置"
- **用户操作**: 显示精度影响，提供精度调整选项

### **循环换算关系**
- **提示信息**: "检测到循环换算关系，请重新配置"
- **用户操作**: 显示循环路径，提供关系重构建议

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持计量单位的创建、编辑、查询、删除操作
- [ ] 单位编码全局唯一，支持自定义编码规则
- [ ] 完整的量纲分类管理和单位分组功能
- [ ] 灵活的单位换算关系配置和管理
- [ ] 支持玻璃行业专用单位的定义和使用
- [ ] 提供单位换算计算和验证功能
- [ ] 与物料管理、库存管理等模块的无缝集成
- [ ] 支持单位使用情况的统计和分析
- [ ] 数据准确性≥99.9%，换算精度可配置
- [ ] 页面响应时间<2秒，换算计算<1秒
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持单位数据的导入导出功能
