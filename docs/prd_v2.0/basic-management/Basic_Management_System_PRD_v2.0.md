# PRD-01: 基础管理子系统 产品需求文档

> **版本**: 2.0  
> **状态**: 重构版  
> **撰写人**: 产品团队  
> **日期**: 2025-07-30  
> **术语表**: 参考 [全局术语表](../_Glossary.md)  
> **业务规则**: 参考 [核心业务规则库](../_Business_Rules.md)

---

## 1. 核心问题与价值主张

### 1.1 核心问题
**玻璃深加工企业缺乏统一的用户权限和基础数据管理平台，导致信息孤岛、数据不一致、安全风险高。**

### 1.2 价值主张
建立企业级统一身份认证和权限管理中心，为所有业务子系统提供安全、一致、可扩展的基础服务，实现"一次登录，全系统访问"的用户体验。

### 1.3 商业价值量化
- **安全风险降低**: 通过RBAC权限控制，降低数据泄露风险90%
- **管理效率提升**: 新员工入职配置时间从30分钟缩短至5分钟
- **运维成本降低**: 统一用户管理减少重复配置工作60%
- **合规支持**: 完整的操作审计日志支持企业合规要求

---

## 2. 目标用户与使用场景

### 2.1 目标用户
参考 [全局术语表](../_Glossary.md) 中的用户角色定义：

| 用户角色 | 职责描述 | 核心需求 |
|----------|----------|----------|
| **系统管理员** | 负责系统配置、用户管理、权限分配 | 需要强大而直观的管理后台，支持批量操作 |
| **部门经理** | 管理本部门员工的基础信息 | 需要查看和维护本部门员工信息的权限 |
| **HR专员** | 负责员工入职离职流程 | 需要与HR系统集成的用户生命周期管理 |

### 2.2 核心使用场景

#### 场景一：新员工快速入职
**用户故事**: 作为一个系统管理员，我想要在5分钟内完成新员工的系统配置，以便新员工能立即开始工作。

**操作流程**:
1. 系统管理员接收HR系统的入职通知
2. 在用户管理界面创建新用户账号
3. 根据岗位自动分配预设角色
4. 系统自动发送登录凭证给新员工
5. 新员工首次登录强制修改密码

**成功标准**: 整个流程在5分钟内完成，零错误率

#### 场景二：组织架构调整
**用户故事**: 作为一个系统管理员，我想要通过拖拽操作调整组织架构，以便快速响应业务变化。

**操作流程**:
1. 在组织架构管理界面选择需要调整的部门
2. 拖拽部门到新的上级部门下
3. 系统自动更新所有相关员工的部门归属
4. 数据权限根据新的组织关系自动调整

**成功标准**: 调整即时生效，相关权限自动更新

---

## 3. 功能需求（用户故事格式）

### 3.1 组织架构管理

#### 需求 3.1.1: 多级组织架构维护
**用户故事**: 作为一个系统管理员，我想要维护多级组织架构，以便反映真实的企业组织关系。

**功能描述**:
- 支持无限层级的树状组织结构
- 支持拖拽调整组织关系
- 支持多法人公司架构
- 每个组织节点包含：编码、名称、负责人、类型

**验收标准**:
- [ ] 支持创建、编辑、删除、查询组织节点
- [ ] 组织树支持拖拽调整，实时生效
- [ ] 组织编码全局唯一性校验
- [ ] 删除组织前检查是否有关联员工

#### 需求 3.1.2: 组织权限继承
**用户故事**: 作为一个系统管理员，我想要设置组织级别的权限继承规则，以便简化权限管理。

**功能描述**:
- 支持设置组织级别的默认角色
- 子组织自动继承父组织的基础权限
- 支持权限覆盖和扩展机制

**验收标准**:
- [ ] 新员工加入组织时自动获得组织默认角色
- [ ] 权限继承关系清晰可追溯
- [ ] 支持权限例外处理

### 3.2 用户与身份管理

#### 需求 3.2.1: 用户生命周期管理
**用户故事**: 作为一个系统管理员，我想要管理用户的完整生命周期，以便确保账号安全和合规。

**功能描述**:
- 用户创建、激活、禁用、删除全流程管理
- 支持批量导入和批量操作
- 密码策略和安全设置
- 用户状态变更自动通知

**验收标准**:
- [ ] 用户信息包含：工号、姓名、登录账号、所属部门、岗位、状态
- [ ] 工号和登录账号全局唯一性校验
- [ ] 支持Excel批量导入用户
- [ ] 禁用用户立即失去系统访问权限
- [ ] 密码复杂度策略可配置

#### 需求 3.2.2: 单点登录集成
**用户故事**: 作为一个普通用户，我想要一次登录就能访问所有授权系统，以便提高工作效率。

**功能描述**:
- 统一身份认证中心
- 支持LDAP/AD集成
- JWT令牌管理
- 会话管理和超时控制

**验收标准**:
- [ ] 用户登录后获得全系统访问权限
- [ ] 支持记住登录状态
- [ ] 会话超时自动退出
- [ ] 支持强制下线功能

### 3.3 权限管理（RBAC）

#### 需求 3.3.1: 角色权限管理
**用户故事**: 作为一个系统管理员，我想要创建和管理角色权限，以便实现标准化的权限分配。

**功能描述**:
- 基于RBAC的权限模型
- 角色创建、编辑、删除、复制
- 菜单权限和操作权限分离管理
- 权限模板和快速配置

**验收标准**:
- [ ] 支持创建自定义角色
- [ ] 权限配置界面直观易用
- [ ] 角色权限变更实时生效
- [ ] 支持角色权限对比和审计

#### 需求 3.3.2: 数据权限控制
**用户故事**: 作为一个系统管理员，我想要配置数据权限规则，以便控制用户的数据访问范围。

**功能描述**:
- 基于组织架构的数据权限
- 支持多种权限范围：本人、本部门、本部门及下级、全部
- 数据权限规则引擎
- 权限范围可视化展示

**验收标准**:
- [ ] 数据权限规则可按角色配置
- [ ] 支持自定义数据权限范围
- [ ] 权限变更立即生效
- [ ] 提供权限测试工具

### 3.4 基础数据管理

#### 需求 3.4.1: 数据字典管理
**用户故事**: 作为一个系统管理员，我想要维护系统基础数据字典，以便规范业务数据录入标准。

**功能描述**:
- 统一的数据字典管理中心
- 支持分类管理和层级结构
- 字典值排序和状态管理
- 业务表单自动关联字典

**验收标准**:
- [ ] 支持字典类型和字典值的增删改查
- [ ] 字典值支持排序和启用/禁用
- [ ] 被引用的字典值限制删除
- [ ] 业务表单下拉框自动关联字典

---

## 4. 验收标准（可测试列表）

### 4.1 功能验收标准
- [ ] 新用户创建后能正常登录系统
- [ ] 角色权限变更后用户权限立即更新
- [ ] 组织架构调整后数据权限自动调整
- [ ] 数据字典变更后业务表单同步更新
- [ ] 用户禁用后立即失去系统访问权限

### 4.2 性能验收标准
- [ ] 用户登录响应时间 < 500ms
- [ ] 权限校验响应时间 < 200ms
- [ ] 1000节点组织树加载时间 < 2s
- [ ] 批量用户导入1000条记录 < 30s

### 4.3 安全验收标准
- [ ] 密码加密存储，不可逆
- [ ] 所有API请求经过身份认证
- [ ] 关键操作记录完整审计日志
- [ ] 防止SQL注入和XSS攻击

### 4.4 可用性验收标准
- [ ] 系统可用性 ≥ 99.9%
- [ ] 支持主流浏览器最新版本
- [ ] 界面响应式设计，支持移动端访问

---

## 5. 交互设计要求

### 5.1 界面设计原则
- **简洁直观**: 管理界面布局清晰，操作路径最短
- **一致性**: 所有管理界面遵循统一的设计规范
- **反馈及时**: 所有操作提供即时反馈和状态提示
- **容错性**: 关键操作提供确认机制和撤销功能

### 5.2 关键界面要求
- **组织架构管理**: 树状结构展示，支持拖拽操作
- **用户管理**: 表格列表 + 详情面板的布局
- **权限配置**: 树状权限列表 + 勾选框的组合
- **数据字典**: 分类导航 + 列表编辑的结构

---

## 6. 数据埋点需求

### 6.1 用户行为埋点
- 用户登录/登出行为
- 权限配置操作行为
- 组织架构变更行为
- 数据字典维护行为

### 6.2 系统性能埋点
- 登录响应时间
- 权限校验耗时
- 页面加载时间
- API调用成功率

### 6.3 安全审计埋点
- 登录失败次数和IP
- 权限变更操作记录
- 敏感数据访问记录
- 异常操作行为记录

---

## 7. 未来展望/V-Next

### 7.1 暂不开发功能
- **多租户支持**: 当前专注单租户场景，多租户架构留待V2.0
- **高级审计分析**: 基础审计日志满足当前需求，高级分析功能后续考虑
- **移动端原生应用**: 当前响应式设计满足移动访问需求
- **第三方身份提供商集成**: 当前LDAP集成满足需求，OAuth等留待后续

### 7.2 技术债务管理
- **性能优化**: 大规模用户场景下的性能优化
- **缓存策略**: 权限数据缓存机制优化
- **监控告警**: 完善的系统监控和告警机制

---

## 版本控制

| 版本 | 日期 | 变更说明 | 责任人 |
|------|------|----------|--------|
| 1.0 | 2025-07-29 | 初始版本 | Roo |
| 2.0 | 2025-07-30 | 重构版本，应用五大核心原则 | 产品团队 |

---

**文档状态**: 已重构 ✅  
**应用原则**: 第一性原理 ✅ DRY ✅ KISS ✅ SOLID ✅ YAGNI ✅
