# 功能模块规格说明书：角色权限管理模块

- **模块ID**: BMS-004
- **所属子系统**: 基础管理子系统
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 系统管理员, **I want to** 创建和管理角色权限, **so that** 实现标准化的权限分配。
- **As a** 系统管理员, **I want to** 复制现有角色创建新角色, **so that** 快速配置相似权限的角色。
- **As a** 系统管理员, **I want to** 对比不同角色的权限差异, **so that** 确保权限配置的合理性。
- **As a** 安全审计员, **I want to** 查看角色权限变更历史, **so that** 进行安全合规审计。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有角色权限管理权限（参考业务规则R1.1 RBAC权限控制）
- 系统菜单和功能权限已定义
- 组织架构已建立

### 核心流程

#### 2.1 角色创建流程
1. 系统管理员点击"新增角色"按钮
2. 填写角色基本信息（角色名称、描述、类型）
3. 配置菜单权限（勾选可访问的菜单）
4. 配置操作权限（设置增删改查权限）
5. 设置数据权限范围
6. 预览权限配置
7. 保存角色并记录创建日志

#### 2.2 权限配置流程
1. 选择需要配置的角色
2. 在权限树中勾选/取消权限项
3. 系统实时显示权限变更预览
4. 配置特殊权限和例外情况
5. 验证权限配置的完整性
6. 保存权限配置
7. 通知相关用户权限变更

#### 2.3 角色权限审计流程
1. 选择需要审计的角色
2. 查看角色的完整权限清单
3. 对比角色间的权限差异
4. 查看权限变更历史记录
5. 生成权限审计报告
6. 标记异常权限配置

### 后置条件
- 角色权限变更实时生效
- 相关用户权限立即更新
- 权限变更记录到审计日志

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：角色权限管理页
### 页面目标：提供直观的角色权限配置和管理界面

### 信息架构：
- **左侧区域**：包含 角色列表, 角色搜索, 角色分类筛选
- **中间区域**：包含 权限树结构, 权限搜索, 全选/反选控制
- **右侧区域**：包含 权限预览, 角色详情, 操作按钮组
- **顶部工具栏**：包含 新增角色, 复制角色, 权限对比, 导出权限

### 交互逻辑与状态：

#### **角色列表**
- **默认状态：** 显示角色名称、类型、用户数量
- **选中状态：** 蓝色背景(#E6F7FF)，蓝色左边框
- **悬停状态：** 浅灰背景(#FAFAFA)，显示操作图标
- **禁用状态：** 灰色文字，系统内置角色不可删除
- **交互行为：** 点击选中角色，右侧显示权限配置

#### **权限树结构**
- **默认状态：** 树状展示菜单和功能权限
- **展开状态：** 显示子权限项，支持无限层级
- **勾选状态：** 
  - 全选：蓝色勾选框(#1890FF)
  - 半选：蓝色方块，表示部分子项被选中
  - 未选：空白勾选框
- **禁用状态：** 灰色勾选框，某些权限不可配置

#### **权限搜索框**
- **默认状态：** 占位符"搜索权限名称"，搜索图标
- **聚焦状态：** 蓝色边框，显示搜索建议
- **搜索结果状态：** 匹配的权限项高亮显示
- **交互行为：** 实时搜索，支持权限名称和编码查找

#### **新增角色按钮**
- **默认状态：** 蓝色背景(#1890FF)，白色文字"新增角色"
- **悬停状态：** 背景色加深至#096DD9
- **权限不足状态：** 灰色背景，禁用状态
- **交互行为：** 点击打开新增角色对话框

#### **权限预览面板**
- **默认状态：** 显示当前角色的权限摘要
- **变更状态：** 高亮显示权限变更项（新增/删除）
- **保存状态：** 显示保存按钮，变更项待确认
- **交互行为：** 实时更新权限预览

#### **角色对比功能**
- **选择状态：** 支持选择2-3个角色进行对比
- **对比视图：** 表格形式显示权限差异
- **差异高亮：** 不同权限项用不同颜色标识
- **交互行为：** 支持导出对比结果

### 数据校验规则：

#### **角色名称**
- **校验规则：** 必填，2-50位字符，全局唯一
- **错误提示文案：** "角色名称不能为空" / "角色名称已存在"

#### **权限配置**
- **校验规则：** 至少选择一个权限项
- **错误提示文案：** "请至少选择一个权限项"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **角色名称 (role_name)**: String, 必填, 2-50位字符
- **角色编码 (role_code)**: String, 必填, 3-20位字符
- **角色类型 (role_type)**: Enum, 必填, [系统角色/业务角色/临时角色]
- **角色描述 (description)**: String, 可选, 最大200字符
- **权限列表 (permissions)**: Array, 必填, 权限ID数组
- **数据权限范围 (data_scope)**: Enum, 必填, [全部/本部门/本部门及下级/仅本人]

### 展示数据
- **角色基本信息**: 名称、编码、类型、描述、状态
- **权限统计**: 菜单权限数量、操作权限数量
- **用户统计**: 分配该角色的用户数量
- **权限树结构**: 完整的权限层级关系
- **权限变更历史**: 变更时间、操作人、变更内容

### 空状态/零数据
- **无角色数据**: 显示"暂无角色数据，请先创建角色"
- **无权限配置**: 显示"该角色暂未配置权限"
- **搜索无结果**: 显示"未找到匹配的权限项"

### API接口
- **获取角色列表**: GET /api/roles
- **创建角色**: POST /api/roles
- **更新角色**: PUT /api/roles/{id}
- **删除角色**: DELETE /api/roles/{id}
- **获取权限树**: GET /api/permissions/tree
- **角色权限对比**: POST /api/roles/compare

## 5. 异常与边界处理 (Error & Edge Cases)

### **删除已分配给用户的角色**
- **提示信息**: "该角色已分配给用户，不能直接删除"
- **用户操作**: 显示关联用户列表，提供批量转移角色功能

### **权限配置冲突**
- **提示信息**: "权限配置存在冲突，请检查权限依赖关系"
- **用户操作**: 高亮冲突的权限项，提供修复建议

### **角色名称重复**
- **提示信息**: "角色名称已存在，请使用其他名称"
- **用户操作**: 角色名称字段标红，聚焦到名称字段

### **权限树加载失败**
- **提示信息**: "权限数据加载失败，请刷新页面重试"
- **用户操作**: 显示重新加载按钮，保持当前配置状态

### **权限保存失败**
- **提示信息**: "权限配置保存失败，请检查网络后重试"
- **用户操作**: 保持编辑状态，提供重试按钮

### **系统内置角色修改限制**
- **提示信息**: "系统内置角色不允许修改"
- **用户操作**: 相关编辑功能禁用，提供复制角色选项

## 6. 验收标准 (Acceptance Criteria)

- [ ] 用户可以创建、编辑、删除、查询角色
- [ ] 角色名称全局唯一性校验正确执行
- [ ] 权限配置界面直观易用，支持树状结构展示
- [ ] 角色权限变更实时生效，用户权限同步更新
- [ ] 支持角色权限对比和审计功能
- [ ] 权限搜索功能支持名称和编码查找
- [ ] 复制角色功能正常工作，快速创建相似角色
- [ ] 权限预览功能实时显示配置变更
- [ ] 系统内置角色受保护，不可删除或修改关键权限
- [ ] 所有操作记录到审计日志
- [ ] 界面支持响应式设计，移动端可正常使用
- [ ] 权限树支持大量权限项，加载性能良好
- [ ] 权限配置保存响应时间小于2秒
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持键盘导航和无障碍访问
