# 功能模块规格说明书：组织架构管理模块

- **模块ID**: BMS-002
- **所属子系统**: 基础管理子系统
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 系统管理员, **I want to** 维护多级组织架构, **so that** 反映真实的企业组织关系。
- **As a** 系统管理员, **I want to** 通过拖拽操作调整组织架构, **so that** 快速响应业务变化。
- **As a** 系统管理员, **I want to** 设置组织级别的权限继承规则, **so that** 简化权限管理工作。
- **As a** 部门经理, **I want to** 查看本部门的组织结构, **so that** 了解团队构成和汇报关系。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有组织架构管理权限
- 系统中至少存在根组织节点
- 相关的用户数据已同步

### 核心流程

#### 2.1 组织节点创建流程
1. 用户在组织树中选择父级节点
2. 点击"新增子部门"按钮
3. 填写组织信息（编码、名称、负责人、类型）
4. 系统验证组织编码唯一性
5. 保存组织信息并更新组织树
6. 自动继承父级组织的基础权限设置

#### 2.2 组织架构调整流程
1. 用户选择需要调整的组织节点
2. 拖拽节点到新的父级节点下
3. 系统检查调整的合法性（避免循环引用）
4. 确认调整后更新所有相关员工的部门归属
5. 根据新的组织关系自动调整数据权限
6. 记录组织变更日志

#### 2.3 组织权限继承设置流程
1. 选择组织节点
2. 配置该组织的默认角色
3. 设置权限继承规则
4. 系统自动应用到该组织下的所有员工
5. 子组织自动继承父组织的基础权限

### 后置条件
- 组织架构变更立即生效
- 相关员工的数据权限自动更新
- 组织变更记录到审计日志

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：组织架构管理页
### 页面目标：提供直观的组织架构可视化管理界面

### 信息架构：
- **左侧区域**：包含 组织树结构, 搜索框, 展开/折叠控制
- **右侧区域**：包含 组织详情面板, 编辑表单, 操作按钮组
- **顶部工具栏**：包含 新增根部门, 批量导入, 导出组织架构, 刷新按钮

### 交互逻辑与状态：

#### **组织树结构**
- **默认状态：** 展示根节点，子节点折叠显示
- **展开状态：** 点击节点前的展开图标，显示子节点
- **选中状态：** 蓝色背景(#E6F7FF)，蓝色左边框
- **拖拽状态：** 半透明显示，拖拽目标高亮
- **交互行为：** 支持点击选中、拖拽调整、右键菜单

#### **组织节点**
- **默认状态：** 显示组织图标、名称、员工数量
- **悬停状态：** 浅灰背景(#FAFAFA)，显示操作图标
- **拖拽源状态：** 半透明(opacity: 0.5)，虚线边框
- **拖拽目标状态：** 绿色边框(#52C41A)，绿色背景(#F6FFED)
- **禁用状态：** 灰色文字，不可拖拽（有员工的部门）

#### **搜索框**
- **默认状态：** 占位符"搜索部门名称"，搜索图标
- **聚焦状态：** 蓝色边框，清除按钮显示
- **搜索结果状态：** 匹配的节点高亮显示，其他节点半透明
- **交互行为：** 实时搜索，支持拼音搜索

#### **新增部门按钮**
- **默认状态：** 蓝色背景(#1890FF)，白色文字"新增部门"
- **悬停状态：** 背景色加深至#096DD9
- **禁用状态：** 未选中父节点时禁用，灰色背景
- **交互行为：** 点击打开新增部门对话框

#### **组织详情面板**
- **默认状态：** 显示选中组织的详细信息
- **编辑状态：** 表单字段可编辑，显示保存/取消按钮
- **加载状态：** 显示骨架屏，数据加载中
- **空状态：** 未选中任何组织时显示提示信息

### 数据校验规则：

#### **组织编码**
- **校验规则：** 必填，3-20位字符，全局唯一
- **错误提示文案：** "组织编码不能为空" / "组织编码已存在"

#### **组织名称**
- **校验规则：** 必填，2-50位字符，同级唯一
- **错误提示文案：** "组织名称不能为空" / "同级部门名称不能重复"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **组织编码 (org_code)**: String, 必填, 3-20位字符
- **组织名称 (org_name)**: String, 必填, 2-50位字符
- **负责人 (manager_id)**: String, 可选, 员工ID
- **组织类型 (org_type)**: Enum, 必填, [公司/部门/小组]
- **排序号 (sort_order)**: Integer, 可选, 默认0
- **描述 (description)**: String, 可选, 最大200字符

### 展示数据
- **组织树结构**: 层级关系、节点信息
- **员工数量**: 各组织下的员工统计
- **负责人信息**: 姓名、工号、联系方式
- **创建时间**: 组织创建的时间戳
- **最后修改时间**: 最近一次修改的时间戳

### 空状态/零数据
- **无组织数据**: 显示"暂无组织架构，请先创建根部门"
- **搜索无结果**: 显示"未找到匹配的部门，请尝试其他关键词"
- **无员工部门**: 显示"该部门暂无员工"

### API接口
- **获取组织树**: GET /api/org/tree
- **创建组织**: POST /api/org/create
- **更新组织**: PUT /api/org/{id}
- **删除组织**: DELETE /api/org/{id}
- **调整组织关系**: PUT /api/org/move

## 5. 异常与边界处理 (Error & Edge Cases)

### **拖拽到自身或子节点**
- **提示信息**: "不能将部门移动到自身或其子部门下"
- **用户操作**: 拖拽操作被阻止，显示禁止图标

### **删除包含员工的部门**
- **提示信息**: "该部门下还有员工，请先转移员工后再删除"
- **用户操作**: 显示员工列表，提供批量转移功能

### **组织编码重复**
- **提示信息**: "组织编码已存在，请使用其他编码"
- **用户操作**: 编码输入框标红，聚焦到编码字段

### **网络异常导致拖拽失败**
- **提示信息**: "组织调整失败，请检查网络后重试"
- **用户操作**: 组织树恢复到调整前状态，提供重试按钮

### **权限不足**
- **提示信息**: "您没有权限执行此操作"
- **用户操作**: 相关按钮禁用，操作被阻止

### **组织层级过深**
- **限制规则**: 最大支持10级组织层级
- **提示信息**: "组织层级不能超过10级"
- **用户操作**: 阻止创建更深层级的组织

## 6. 验收标准 (Acceptance Criteria)

- [ ] 用户可以创建、编辑、删除组织节点
- [ ] 组织树支持拖拽调整，实时生效
- [ ] 组织编码全局唯一性校验正确执行
- [ ] 删除组织前正确检查是否有关联员工
- [ ] 组织权限继承规则正确应用
- [ ] 搜索功能支持组织名称和编码查找
- [ ] 组织调整后相关员工的数据权限自动更新
- [ ] 所有操作记录到审计日志
- [ ] 界面支持响应式设计，移动端可正常使用
- [ ] 组织树加载1000个节点时间小于2秒
- [ ] 拖拽操作流畅，无明显延迟
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持键盘导航和无障碍访问
- [ ] 批量导入组织架构功能正常工作
- [ ] 组织架构导出功能支持Excel格式
