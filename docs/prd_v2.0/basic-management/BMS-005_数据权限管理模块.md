# 功能模块规格说明书：数据权限管理模块

- **模块ID**: BMS-005
- **所属子系统**: 基础管理子系统
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 系统管理员, **I want to** 配置数据权限规则, **so that** 控制用户的数据访问范围。
- **As a** 系统管理员, **I want to** 可视化展示权限范围, **so that** 直观了解用户的数据访问边界。
- **As a** 系统管理员, **I want to** 测试数据权限配置, **so that** 验证权限规则的正确性。
- **As a** 安全审计员, **I want to** 审计数据权限配置, **so that** 确保数据访问的合规性。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有数据权限管理权限（参考业务规则R1.2 数据访问权限）
- 组织架构已建立
- 角色权限已配置

### 核心流程

#### 2.1 数据权限规则配置流程
1. 选择需要配置数据权限的角色
2. 选择数据权限类型（组织数据/业务数据）
3. 配置权限范围（本人/本部门/本部门及下级/全部）
4. 设置特殊权限规则和例外情况
5. 预览权限范围影响的数据
6. 保存权限配置
7. 测试权限规则有效性

#### 2.2 权限范围可视化流程
1. 选择用户或角色
2. 系统分析其数据权限配置
3. 生成权限范围可视化图表
4. 显示可访问的组织范围
5. 展示具体的数据访问边界
6. 提供权限范围调整建议

#### 2.3 权限测试流程
1. 选择测试用户
2. 选择测试数据类型
3. 模拟用户访问数据
4. 显示权限验证结果
5. 对比预期权限范围
6. 生成测试报告

### 后置条件
- 数据权限规则立即生效
- 用户数据访问范围实时更新
- 权限配置记录到审计日志

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：数据权限管理页
### 页面目标：提供直观的数据权限配置和可视化管理界面

### 信息架构：
- **左侧区域**：包含 角色/用户选择器, 权限类型筛选, 组织树结构
- **中间区域**：包含 权限规则配置, 权限范围设置, 例外规则配置
- **右侧区域**：包含 权限可视化图表, 影响范围预览, 测试工具
- **底部区域**：包含 保存按钮, 测试按钮, 重置按钮, 权限预览

### 交互逻辑与状态：

#### **角色/用户选择器**
- **默认状态：** 下拉选择框，显示"请选择角色或用户"
- **展开状态：** 显示角色和用户列表，支持搜索
- **选中状态：** 显示选中的角色/用户名称
- **交互行为：** 选择后自动加载对应的权限配置

#### **权限范围设置**
- **选项样式：** 单选按钮组，四个选项垂直排列
  - 仅本人：橙色图标，适用于个人数据
  - 本部门：蓝色图标，适用于部门数据
  - 本部门及下级：绿色图标，适用于管理岗位
  - 全部数据：红色图标，适用于高级管理员
- **选中状态：** 蓝色背景(#E6F7FF)，蓝色边框
- **交互行为：** 选择后实时更新权限预览

#### **组织树权限可视化**
- **默认状态：** 显示完整组织树结构
- **权限范围标识：**
  - 可访问：绿色背景(#F6FFED)，绿色边框
  - 不可访问：灰色背景(#F5F5F5)，灰色文字
  - 部分访问：黄色背景(#FFFBE6)，橙色边框
- **交互行为：** 悬停显示具体权限说明

#### **权限测试工具**
- **测试用户选择：** 下拉选择框，支持搜索
- **测试数据类型：** 复选框组，支持多选
- **测试按钮：** 蓝色背景，白色文字"开始测试"
- **测试结果：** 表格形式显示测试结果
  - 通过：绿色对勾图标
  - 失败：红色叉号图标
  - 警告：橙色感叹号图标

#### **权限规则配置面板**
- **基础规则：** 权限范围单选按钮组
- **例外规则：** 可添加特殊权限例外
- **继承规则：** 显示从上级角色继承的权限
- **自定义规则：** 支持配置复杂的权限逻辑

#### **权限影响预览**
- **影响用户数：** 数字徽章显示受影响的用户数量
- **影响数据量：** 显示权限范围内的数据统计
- **变更对比：** 对比权限变更前后的差异
- **风险提示：** 高风险权限变更的警告提示

### 数据校验规则：

#### **权限范围配置**
- **校验规则：** 必须选择一个权限范围
- **错误提示文案：** "请选择数据权限范围"

#### **例外规则配置**
- **校验规则：** 例外规则不能与基础规则冲突
- **错误提示文案：** "例外规则与基础权限范围冲突"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **权限主体 (subject_id)**: String, 必填, 角色ID或用户ID
- **权限主体类型 (subject_type)**: Enum, 必填, [角色/用户]
- **数据权限范围 (data_scope)**: Enum, 必填, [本人/本部门/本部门及下级/全部]
- **权限类型 (permission_type)**: Enum, 必填, [组织数据/业务数据/财务数据]
- **例外规则 (exception_rules)**: Array, 可选, 特殊权限规则
- **生效时间 (effective_time)**: DateTime, 可选, 权限生效时间
- **失效时间 (expiry_time)**: DateTime, 可选, 权限失效时间

### 展示数据
- **权限配置摘要**: 权限范围、例外规则、生效状态
- **组织权限树**: 可访问的组织节点及权限级别
- **数据统计**: 权限范围内的数据量统计
- **用户影响分析**: 受权限变更影响的用户列表
- **权限测试结果**: 测试用例执行结果和详细说明

### 空状态/零数据
- **无权限配置**: 显示"该角色/用户暂未配置数据权限"
- **无测试结果**: 显示"暂无测试结果，请先执行权限测试"
- **无例外规则**: 显示"暂无例外规则配置"

### API接口
- **获取数据权限配置**: GET /api/data-permissions/{subject_id}
- **更新数据权限**: PUT /api/data-permissions/{subject_id}
- **权限测试**: POST /api/data-permissions/test
- **权限可视化**: GET /api/data-permissions/visualization/{subject_id}
- **权限影响分析**: POST /api/data-permissions/impact-analysis

## 5. 异常与边界处理 (Error & Edge Cases)

### **权限范围配置冲突**
- **提示信息**: "权限配置存在冲突，请检查例外规则设置"
- **用户操作**: 高亮冲突的配置项，提供修复建议

### **权限测试失败**
- **提示信息**: "权限测试执行失败：具体错误信息"
- **用户操作**: 显示错误详情，提供重新测试选项

### **权限变更影响过大**
- **提示信息**: "此权限变更将影响X个用户，请谨慎操作"
- **用户操作**: 显示影响详情，要求二次确认

### **数据权限计算超时**
- **提示信息**: "权限范围计算超时，请稍后重试"
- **用户操作**: 提供重新计算按钮，或简化权限规则

### **权限继承链过长**
- **提示信息**: "权限继承链过长，可能影响性能"
- **用户操作**: 显示继承链详情，建议优化权限结构

### **例外规则过多**
- **提示信息**: "例外规则过多，建议重新设计权限结构"
- **用户操作**: 显示例外规则列表，提供优化建议

## 6. 验收标准 (Acceptance Criteria)

- [ ] 用户可以配置四种数据权限范围（本人/本部门/本部门及下级/全部）
- [ ] 权限范围可视化图表正确显示用户的数据访问边界
- [ ] 权限测试工具能够准确验证权限配置的有效性
- [ ] 例外规则配置功能正常工作，支持复杂权限场景
- [ ] 权限变更影响分析准确显示受影响的用户和数据
- [ ] 权限配置变更立即生效，数据访问范围实时更新
- [ ] 权限继承机制正确工作，子角色继承父角色权限
- [ ] 组织架构变更后数据权限自动调整
- [ ] 所有权限操作记录到审计日志
- [ ] 界面支持响应式设计，移动端可正常使用
- [ ] 权限计算性能良好，复杂权限规则响应时间小于3秒
- [ ] 权限可视化图表支持大型组织架构展示
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持键盘导航和无障碍访问
- [ ] 权限配置导出功能支持Excel格式
