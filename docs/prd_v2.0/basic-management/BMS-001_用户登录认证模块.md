# 功能模块规格说明书：用户登录认证模块

- **模块ID**: BMS-001
- **所属子系统**: 基础管理子系统
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 企业员工, **I want to** 使用工号和密码登录系统, **so that** 我能够访问被授权的业务功能。
- **As a** 系统管理员, **I want to** 配置密码策略和会话超时, **so that** 确保系统安全性。
- **As a** 普通用户, **I want to** 系统记住我的登录状态, **so that** 我不需要频繁重新登录。
- **As a** 用户, **I want to** 在忘记密码时能够重置密码, **so that** 我能够重新获得系统访问权限。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户已在系统中创建账号且状态为"启用"
- 用户具备有效的工号和密码
- 系统网络连接正常

### 核心流程

#### 2.1 用户登录流程
1. 用户访问系统登录页面
2. 用户输入工号和密码
3. 系统验证用户凭证（参考业务规则R1.1 RBAC权限控制）
4. 验证成功后生成JWT令牌
5. 系统跳转到用户默认首页
6. 建立用户会话，记录登录日志

#### 2.2 密码重置流程
1. 用户点击"忘记密码"链接
2. 输入工号或邮箱地址
3. 系统发送重置链接到用户邮箱
4. 用户点击邮箱中的重置链接
5. 设置新密码（符合密码策略）
6. 系统更新密码并通知用户

### 后置条件
- 用户获得系统访问权限
- 用户会话建立，可访问授权功能
- 登录行为记录到审计日志

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：用户登录页
### 页面目标：提供安全、便捷的用户身份认证入口

### 信息架构：
- **顶部区域**：包含 公司Logo, 系统名称"玻璃深加工ERP系统", 版本信息
- **中央登录区域**：包含 登录表单卡片, 背景图片/渐变
- **底部区域**：包含 版权信息, 技术支持联系方式

### 交互逻辑与状态：

#### **工号输入框**
- **默认状态：** 白色背景，灰色边框(#D9D9D9)，占位符文字"请输入工号"
- **聚焦状态：** 蓝色边框(#1890FF)，蓝色阴影，占位符文字消失
- **错误状态：** 红色边框(#F5222D)，显示错误提示"请输入正确的工号"
- **交互行为：** 支持Tab键切换，Enter键提交表单

#### **密码输入框**
- **默认状态：** 白色背景，灰色边框，占位符文字"请输入密码"，密码掩码显示
- **聚焦状态：** 蓝色边框，蓝色阴影
- **显示/隐藏密码按钮：** 眼睛图标，点击切换密码可见性
- **错误状态：** 红色边框，显示错误提示

#### **记住登录状态复选框**
- **默认状态：** 未选中，灰色边框
- **选中状态：** 蓝色背景(#1890FF)，白色对勾
- **交互行为：** 点击切换选中状态，影响会话保持时长

#### **登录按钮**
- **默认状态：** 蓝色背景(#1890FF)，白色文字"登录"，圆角4px
- **悬停状态：** 背景色加深至#096DD9
- **加载状态：** 按钮禁用，显示旋转图标和"登录中..."文字
- **禁用状态：** 灰色背景(#BFBFBF)，当表单验证失败时触发
- **交互行为：** 点击后验证表单，发送登录请求

#### **忘记密码链接**
- **默认状态：** 蓝色文字(#1890FF)，无下划线
- **悬停状态：** 文字加深，显示下划线
- **交互行为：** 点击打开密码重置对话框

### 数据校验规则：

#### **工号输入框**
- **校验规则：** 必填，长度4-20位，支持字母数字组合
- **错误提示文案：** "工号不能为空" / "工号格式不正确"

#### **密码输入框**
- **校验规则：** 必填，长度8-20位，包含字母和数字
- **错误提示文案：** "密码不能为空" / "密码长度为8-20位" / "密码必须包含字母和数字"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **工号 (employee_id)**: String, 必填, 4-20位字符
- **密码 (password)**: String, 必填, 8-20位字符
- **记住登录 (remember_me)**: Boolean, 可选, 默认false

### 展示数据
- **系统名称**: "玻璃深加工ERP系统"
- **版本信息**: 从系统配置获取
- **公司Logo**: 从系统配置获取
- **背景图片**: 可配置的登录背景

### API接口
- **登录接口**: POST /api/auth/login
- **密码重置接口**: POST /api/auth/reset-password
- **验证令牌接口**: GET /api/auth/verify-token

## 5. 异常与边界处理 (Error & Edge Cases)

### **网络请求失败**
- **提示信息**: "网络连接失败，请检查网络后重试"
- **用户操作**: 提供"重试"按钮，点击重新发送请求

### **用户名或密码错误**
- **提示信息**: "工号或密码错误，请重新输入"
- **用户操作**: 清空密码框，保留工号，聚焦到密码框

### **账号被锁定**
- **提示信息**: "账号已被锁定，请联系系统管理员"
- **用户操作**: 显示管理员联系方式，禁用登录按钮

### **密码即将过期**
- **提示信息**: "密码将在X天后过期，建议及时修改"
- **用户操作**: 提供"立即修改"和"稍后提醒"选项

### **会话超时**
- **提示信息**: "会话已超时，请重新登录"
- **用户操作**: 自动跳转到登录页，清除本地存储的令牌

### **浏览器兼容性问题**
- **检测机制**: 检测浏览器版本，不支持时显示升级提示
- **提示信息**: "当前浏览器版本过低，建议升级到最新版本"

## 6. 验收标准 (Acceptance Criteria)

- [ ] 用户可以使用有效的工号和密码成功登录系统
- [ ] 登录失败时显示明确的错误提示信息
- [ ] 记住登录状态功能正常工作，下次访问无需重新登录
- [ ] 密码重置功能完整可用，邮件发送正常
- [ ] 会话超时后自动退出并跳转到登录页
- [ ] 所有表单验证规则正确执行
- [ ] 登录页面在不同屏幕尺寸下正常显示
- [ ] 登录响应时间小于500ms
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持键盘导航和无障碍访问
- [ ] 登录行为正确记录到审计日志
- [ ] 防止暴力破解攻击（连续失败锁定机制）
