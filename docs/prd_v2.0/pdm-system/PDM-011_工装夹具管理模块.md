# 功能模块规格说明书：工装夹具管理模块

- **模块ID**: PDM-011
- **所属子系统**: 工艺管理子系统(PDM)
- **最后更新**: 2025-08-01
- **功能边界说明**: 本模块专注于生产工装夹具的主数据管理、状态跟踪和工序关联，为玻璃深加工企业提供完整的工装资源管理能力
- **业务价值**: 提升生产资源利用率，减少生产准备时间，确保工装夹具的标准化管理和有效配置

## 1. 用户故事 (User Stories)

- **As a** 工艺工程师, **I want to** 建立工装夹具主数据库, **so that** 为工艺路线设计提供标准化的工装信息。
- **As a** 工艺工程师, **I want to** 定义工装夹具与工序的关联关系, **so that** 确保每个工序都有合适的工装支持。
- **As a** 生产计划员, **I want to** 查询工装夹具的可用状态, **so that** 制定合理的生产排程和资源分配。
- **As a** 设备管理员, **I want to** 管理工装夹具的维护和保养, **so that** 确保工装设备的正常运行。
- **As a** 车间主任, **I want to** 跟踪工装夹具的使用情况, **so that** 优化工装配置和提升生产效率。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有工装夹具管理权限
- 工序主数据已建立（参考PDM-005工序管理模块）
- 设备主数据已建立
- 供应商主数据已建立

### 核心流程

#### 2.1 工装夹具创建流程
1. 工艺工程师选择工装类型（夹具/模具/刀具/量具）
2. 填写工装基本信息（编码、名称、规格）
3. 设置工装技术参数和适用范围
4. 定义工装与工序的关联关系
5. 配置工装的维护要求和周期
6. 上传工装图纸和技术文档
7. 设置工装的采购和库存信息
8. 提交工装信息审核
9. 审核通过后工装信息生效

#### 2.2 工装配置流程
1. 在工艺路线设计中选择工序
2. 系统自动推荐适用的工装夹具
3. 工艺工程师确认或调整工装选择
4. 检查工装的可用性和状态
5. 设置工装的使用参数和注意事项
6. 保存工装配置到工艺路线
7. 更新工装的使用计划

#### 2.3 工装维护管理流程
1. 系统自动监控工装使用时间和次数
2. 到达维护周期时自动发出预警
3. 设备管理员安排维护计划
4. 执行维护作业并记录维护内容
5. 更新工装状态和下次维护时间
6. 维护完成后恢复工装可用状态
7. 记录维护成本和效果评估

### 后置条件
- 工装夹具信息完整准确
- 工装与工序关联关系明确
- 工装状态实时更新
- 维护记录完整可追溯

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：工装夹具管理页面
### 页面目标：提供全面的工装夹具管理和配置界面

### 信息架构：
- **左侧区域**：包含 工装分类树, 状态筛选器, 工序关联筛选
- **中间区域**：包含 工装列表, 工装详情, 配置界面
- **右侧区域**：包含 使用统计, 维护计划, 关联工序, 库存状态

### 交互逻辑与状态：

#### **工装分类树**
- **默认状态：** 按工装类型分类显示（夹具/模具/刀具/量具）
- **展开状态：** 显示具体的工装子类别
- **选中状态：** 蓝色背景(#E6F7FF)，显示对应工装列表
- **悬停状态：** 浅灰背景(#FAFAFA)，显示工装数量统计

#### **工装列表**
- **列表项：**
  - **工装编码：** 显示唯一工装编码，点击进入详情
  - **工装名称：** 显示工装名称和规格型号
  - **工装类型：** 彩色标签显示工装类型
  - **适用工序：** 显示关联的工序名称
  - **状态标识：** 显示工装状态（可用/使用中/维护中/停用）
  - **库存数量：** 显示当前可用数量和总数量
  - **下次维护：** 显示下次维护的预计时间
- **状态颜色：**
  - **可用：** 绿色标签(#52C41A)
  - **使用中：** 蓝色标签(#1890FF)
  - **维护中：** 橙色标签(#FAAD14)
  - **停用：** 红色标签(#FF4D4F)

#### **工装详情/编辑界面**
- **基本信息：**
  - **工装编码：** 输入框，自动生成或手工输入
  - **工装名称：** 输入框，必填，最大50字符
  - **工装类型：** 下拉选择，夹具/模具/刀具/量具
  - **规格型号：** 输入框，详细规格描述
  - **制造商：** 下拉选择，关联供应商信息
- **技术参数：**
  - **适用材料：** 多选框，玻璃/铝型材/五金件等
  - **适用尺寸范围：** 输入框，最小-最大尺寸
  - **精度等级：** 下拉选择，工装精度要求
  - **使用寿命：** 数字输入框，预期使用次数或时间
- **工序关联：**
  - **适用工序：** 多选框，选择适用的工序类型
  - **必需/可选：** 单选按钮，标识工装的必要性
  - **替代工装：** 下拉选择，可替代的其他工装
  - **使用优先级：** 数字输入框，多个工装的使用优先级

#### **维护管理界面**
- **维护计划：**
  - **维护类型：** 下拉选择（日常保养/定期检修/大修）
  - **维护周期：** 数字输入框，维护间隔时间
  - **维护内容：** 文本域，详细维护项目
  - **维护时长：** 数字输入框，预计维护时间
- **维护记录：**
  - **维护日期：** 日期选择器
  - **维护人员：** 下拉选择，维护负责人
  - **维护结果：** 单选按钮（正常/异常/需更换）
  - **维护费用：** 数字输入框，维护成本
  - **备注说明：** 文本域，维护详细记录

#### **库存管理界面**
- **库存信息：**
  - **总数量：** 数字输入框，工装总数量
  - **可用数量：** 显示当前可用数量
  - **最小库存：** 数字输入框，库存预警阈值
  - **存放位置：** 输入框，工装存放地点
- **采购信息：**
  - **采购价格：** 数字输入框，单价信息
  - **供应商：** 下拉选择，主要供应商
  - **采购周期：** 数字输入框，采购交期
  - **最后采购：** 日期显示，最后采购时间

### 数据校验规则：

#### **工装编码**
- **校验规则：** 编码必须全局唯一，符合编码规则格式
- **错误提示文案：** "工装编码已存在或格式不正确"

#### **适用尺寸范围**
- **校验规则：** 最小尺寸 < 最大尺寸，且符合物理约束
- **错误提示文案：** "尺寸范围设置不合理，请检查最小值和最大值"

#### **库存数量**
- **校验规则：** 可用数量 ≤ 总数量，且均为非负整数
- **错误提示文案：** "库存数量设置错误，可用数量不能超过总数量"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **工装基本信息**:
  - **工装编码 (tooling_code)**: String, 必填, 全局唯一
  - **工装名称 (tooling_name)**: String, 必填, 最大50字符
  - **工装类型 (tooling_type)**: Enum, 必填, [夹具/模具/刀具/量具]
  - **规格型号 (specification)**: String, 可选, 最大100字符
- **技术参数**:
  - **适用材料 (applicable_materials)**: Array, 材料类型列表
  - **尺寸范围 (size_range)**: Object, {min_size, max_size}
  - **精度等级 (precision_level)**: Enum, [高精度/标准/一般]
  - **使用寿命 (service_life)**: Integer, 预期使用次数
- **关联信息**:
  - **适用工序 (applicable_operations)**: Array, 工序ID列表
  - **必需标识 (is_required)**: Boolean, 是否必需
  - **替代工装 (alternative_toolings)**: Array, 可替代工装ID

### 展示数据
- **工装列表**: 工装的基本信息和状态
- **工装详情**: 完整的工装技术信息和关联关系
- **使用统计**: 工装的使用频次和效率统计
- **维护记录**: 工装的维护历史和计划
- **库存状态**: 工装的库存和采购信息

### 空状态/零数据
- **无工装数据**: 显示"暂无工装夹具数据，请先创建工装信息"
- **无维护记录**: 显示"该工装暂无维护记录"
- **无使用统计**: 显示"该工装暂未被使用"

### API接口
- **工装管理**: GET/POST/PUT/DELETE /api/pdm/toolings
- **工装配置**: POST /api/pdm/toolings/configure
- **维护管理**: GET/POST /api/pdm/toolings/{id}/maintenance
- **库存管理**: GET/PUT /api/pdm/toolings/{id}/inventory
- **使用统计**: GET /api/pdm/toolings/usage-statistics

## 5. 异常与边界处理 (Error & Edge Cases)

### **工装冲突**
- **提示信息**: "检测到工装资源冲突：同一时间段内多个工单需要相同工装"
- **用户操作**: 显示冲突的工单，提供调度建议

### **工装维护超期**
- **提示信息**: "工装维护已超期，存在安全风险，建议立即停用"
- **用户操作**: 自动标记为维护状态，提供维护计划入口

### **库存不足**
- **提示信息**: "工装库存不足，可能影响生产计划"
- **用户操作**: 显示库存详情，提供采购申请入口

### **工装规格不匹配**
- **提示信息**: "选择的工装规格与产品要求不匹配"
- **用户操作**: 高亮不匹配的参数，推荐合适的工装

### **删除被引用的工装**
- **提示信息**: "该工装正在被工艺路线使用，不能直接删除"
- **用户操作**: 显示引用详情，提供"停用工装"选项

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持工装夹具的创建、编辑、查询、删除操作
- [ ] 工装编码全局唯一，支持自定义编码规则
- [ ] 完整的工装技术参数定义和适用范围管理
- [ ] 支持工装与工序的灵活关联和配置
- [ ] 工装状态实时跟踪，支持多种状态管理
- [ ] 完善的维护计划和维护记录管理
- [ ] 库存管理功能完整，支持库存预警
- [ ] 与工艺路线设计模块的无缝集成
- [ ] 工装使用统计和效率分析功能
- [ ] 工装资源冲突检测和调度建议
- [ ] 支持工装信息的导入导出功能
- [ ] 数据准确性≥99%，系统可用性≥99.5%
- [ ] 页面响应时间<2秒，操作流畅
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持移动端访问和基本操作
