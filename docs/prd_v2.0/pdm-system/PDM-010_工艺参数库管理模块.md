# 功能模块规格说明书：工艺参数库管理模块

- **模块ID**: PDM-010
- **所属子系统**: 工艺管理子系统(PDM)
- **最后更新**: 2025-08-01
- **功能边界说明**: 本模块专注于精细化工艺参数的定义、管理和应用，为玻璃深加工企业提供专业的工艺参数控制能力
- **业务价值**: 支撑精密工艺控制，提升产品质量稳定性，实现工艺参数的标准化和智能化管理

## 1. 用户故事 (User Stories)

- **As a** 工艺工程师, **I want to** 建立精细化的工艺参数库, **so that** 为不同产品和工序提供标准化的工艺参数。
- **As a** 工艺工程师, **I want to** 定义工艺参数与产品特征的关联规则, **so that** 系统能根据产品参数自动推荐合适的工艺参数。
- **As a** 工艺工程师, **I want to** 管理工艺参数的版本和变更, **so that** 确保工艺参数的准确性和可追溯性。
- **As a** 生产技术员, **I want to** 查询标准工艺参数, **so that** 按照标准参数进行生产操作。
- **As a** 质量工程师, **I want to** 分析工艺参数与质量的关联, **so that** 优化工艺参数提升产品质量。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有工艺参数管理权限
- 工序主数据已建立（参考PDM-005工序管理模块）
- 产品分类体系已配置
- 设备主数据已建立

### 核心流程

#### 2.1 工艺参数模板创建流程
1. 工艺工程师选择工序类型（切割/磨边/钢化/合片等）
2. 定义工艺参数类别和参数项
3. 设置参数的数据类型和取值范围
4. 配置参数的计算公式和关联规则
5. 定义参数与产品特征的映射关系
6. 设置参数的质量控制标准
7. 提交参数模板审核
8. 审核通过后参数模板生效

#### 2.2 工艺参数配置流程
1. 选择适用的工艺参数模板
2. 输入产品特征参数（尺寸、材质、厚度等）
3. 系统自动计算推荐工艺参数
4. 工艺工程师确认或微调参数值
5. 验证参数的合理性和安全性
6. 保存工艺参数配置
7. 关联到具体的工艺路线

#### 2.3 工艺参数优化流程
1. 收集生产实际数据和质量反馈
2. 分析工艺参数与质量指标的关联
3. 识别参数优化机会
4. 制定参数调整方案
5. 进行小批量试验验证
6. 更新工艺参数标准
7. 推广应用优化后的参数

### 后置条件
- 工艺参数数据完整准确
- 参数与产品特征关联明确
- 参数变更记录完整可追溯
- 相关工艺路线已更新

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：工艺参数库管理页面
### 页面目标：提供专业的工艺参数定义、配置和管理界面

### 信息架构：
- **左侧区域**：包含 工序分类树, 参数模板库, 参数类别筛选
- **中间区域**：包含 参数列表, 参数配置界面, 关联规则设置
- **右侧区域**：包含 参数详情, 计算预览, 历史版本, 质量分析

### 交互逻辑与状态：

#### **工艺参数分类树**
- **默认状态：** 按工序类型分类显示（切割/磨边/钢化/合片）
- **展开状态：** 显示具体的参数类别（速度/温度/压力/时间）
- **选中状态：** 蓝色背景(#E6F7FF)，显示对应参数列表
- **悬停状态：** 浅灰背景(#FAFAFA)，显示参数统计信息

#### **参数模板管理**
- **模板列表：**
  - **模板名称：** 显示参数模板名称和适用工序
  - **参数数量：** 显示模板包含的参数项数量
  - **适用范围：** 彩色标签显示适用的产品类型
  - **状态标识：** 显示模板状态（草稿/生效/停用）
  - **最后更新：** 显示最后修改时间和修改人
- **创建模板：** 蓝色按钮，打开模板创建向导
- **复制模板：** 支持基于现有模板创建新模板

#### **参数配置界面**
- **基本信息区：**
  - **参数编码：** 输入框，自动生成或手工输入
  - **参数名称：** 输入框，如"切割速度"、"钢化温度"
  - **参数类型：** 下拉选择（数值/文本/枚举/布尔）
  - **计量单位：** 下拉选择（mm/min、℃、MPa、秒等）
- **取值范围设置：**
  - **最小值：** 数字输入框，设置参数下限
  - **最大值：** 数字输入框，设置参数上限
  - **标准值：** 数字输入框，设置推荐标准值
  - **精度控制：** 下拉选择，设置小数位数
- **关联规则配置：**
  - **产品特征：** 多选框，选择关联的产品特征
  - **计算公式：** 公式编辑器，支持复杂计算逻辑
  - **条件规则：** 规则编辑器，设置参数适用条件
  - **依赖关系：** 图形化显示参数间的依赖关系

#### **参数计算器**
- **输入区域：** 产品特征参数输入（长度、宽度、厚度等）
- **计算按钮：** 触发参数自动计算
- **结果显示：** 表格形式显示所有相关工艺参数
- **参数调整：** 支持手动微调计算结果
- **保存配置：** 将参数配置保存到工艺路线

#### **质量分析面板**
- **参数趋势图：** 显示参数值的历史变化趋势
- **质量关联图：** 显示参数与质量指标的关联度
- **异常预警：** 高亮显示超出正常范围的参数
- **优化建议：** 基于历史数据提供参数优化建议

### 数据校验规则：

#### **参数取值范围**
- **校验规则：** 最小值 < 标准值 < 最大值，且符合物理约束
- **错误提示文案：** "参数取值范围设置不合理，请检查最小值、标准值、最大值的逻辑关系"

#### **计算公式**
- **校验规则：** 公式语法正确，引用的变量存在
- **错误提示文案：** "计算公式语法错误或引用了不存在的变量"

#### **参数依赖关系**
- **校验规则：** 不能存在循环依赖
- **错误提示文案：** "检测到参数循环依赖，请调整参数关联关系"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **参数基本信息**:
  - **参数编码 (parameter_code)**: String, 必填, 全局唯一
  - **参数名称 (parameter_name)**: String, 必填, 最大50字符
  - **参数类型 (parameter_type)**: Enum, 必填, [数值/文本/枚举/布尔]
  - **计量单位 (unit)**: String, 可选, 如"mm/min"、"℃"
- **取值范围**:
  - **最小值 (min_value)**: Number, 可选
  - **最大值 (max_value)**: Number, 可选
  - **标准值 (standard_value)**: Number, 必填
  - **精度 (precision)**: Integer, 可选, 小数位数
- **关联规则**:
  - **适用工序 (applicable_operations)**: Array, 工序ID列表
  - **产品特征 (product_features)**: Array, 关联的产品特征
  - **计算公式 (calculation_formula)**: String, 可选
  - **条件规则 (condition_rules)**: JSON, 参数适用条件

### 展示数据
- **参数列表**: 参数的基本信息和状态
- **参数详情**: 完整的参数定义和关联信息
- **计算结果**: 基于产品特征计算的参数值
- **质量分析**: 参数与质量指标的关联分析
- **版本历史**: 参数的变更历史和版本对比

### 空状态/零数据
- **无参数模板**: 显示"暂无工艺参数模板，请先创建参数模板"
- **无计算结果**: 显示"请输入产品特征参数进行计算"
- **无质量数据**: 显示"暂无质量数据，无法进行关联分析"

### API接口
- **参数模板管理**: GET/POST/PUT/DELETE /api/pdm/parameter-templates
- **参数配置**: POST /api/pdm/parameter-configurations
- **参数计算**: POST /api/pdm/parameters/calculate
- **质量分析**: GET /api/pdm/parameters/quality-analysis
- **版本管理**: GET/POST /api/pdm/parameters/{id}/versions

## 5. 异常与边界处理 (Error & Edge Cases)

### **参数计算异常**
- **提示信息**: "参数计算失败：输入的产品特征超出适用范围"
- **用户操作**: 高亮异常的输入参数，提供合理范围提示

### **公式计算错误**
- **提示信息**: "计算公式执行错误：除零或数值溢出"
- **用户操作**: 显示错误的公式部分，提供修复建议

### **参数冲突**
- **提示信息**: "检测到参数冲突：多个规则产生不同的参数值"
- **用户操作**: 显示冲突的规则，提供优先级设置选项

### **版本兼容性问题**
- **提示信息**: "参数模板版本不兼容，可能影响现有工艺路线"
- **用户操作**: 显示影响范围，提供版本迁移工具

### **权限限制**
- **提示信息**: "您没有权限修改此工艺参数"
- **用户操作**: 显示只读模式，提供权限申请入口

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持工艺参数模板的创建、编辑、查询、删除操作
- [ ] 参数编码全局唯一，支持自定义编码规则
- [ ] 完整的参数定义，包含取值范围、计量单位、关联规则
- [ ] 支持参数与产品特征的智能关联和自动计算
- [ ] 计算公式支持复杂的数学运算和条件逻辑
- [ ] 参数依赖关系检查，防止循环依赖
- [ ] 完善的版本管理和变更控制
- [ ] 与工艺路线设计模块的无缝集成
- [ ] 支持参数的质量分析和优化建议
- [ ] 参数计算准确率≥99.9%，响应时间<1秒
- [ ] 支持参数模板的导入导出功能
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持移动端访问和操作
