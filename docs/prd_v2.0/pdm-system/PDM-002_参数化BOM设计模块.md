# 功能模块规格说明书：参数化BOM设计模块

- **模块ID**: PDM-002
- **所属子系统**: 工艺管理子系统(PDM)
- **最后更新**: 2025-08-01
- **功能重构说明**: 建立物料BOM与工艺BOM的清晰分离和关联机制，支持玻璃深加工企业的复杂工艺需求
- **业务价值**: 实现产品结构与工艺流程的解耦，提升工艺设计灵活性和生产适应性

## 1. 用户故事 (User Stories)

- **As a** 产品工程师, **I want to** 创建物料BOM定义产品组成, **so that** 明确产品的物理结构和材料清单。
- **As a** 工艺工程师, **I want to** 基于物料BOM创建工艺BOM, **so that** 定义生产过程的工艺步骤和参数配置。
- **As a** 工艺工程师, **I want to** 管理工艺BOM与工艺路线的关联, **so that** 确保工艺数据的一致性和完整性。
- **As a** 工艺工程师, **I want to** 定义参数变量和计算公式, **so that** 实现物料用量和工艺参数的自动计算。
- **As a** 工艺工程师, **I want to** 可视化设计BOM结构, **so that** 直观管理产品的组成关系和工艺流程。
- **As a** 生产计划员, **I want to** 查看工艺BOM的资源需求, **so that** 制定合理的生产计划和资源配置。
- **As a** 销售代表, **I want to** 预览参数化BOM的计算结果, **so that** 验证配置的正确性并进行成本估算。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有BOM设计权限
- 物料主数据已建立
- 成品物料已创建
- 工艺路线已设计（参考PDM-006工艺路线设计模块）
- 工艺参数库已建立（参考PDM-010工艺参数库管理模块）

### 核心流程

#### 2.1 物料BOM创建流程
1. 产品工程师选择成品物料
2. 点击"新建物料BOM"按钮
3. 定义产品参数变量（如长度L、宽度W、厚度T、材质等）
4. 设置变量类型、取值范围和默认值
5. 构建物料BOM树状结构
6. 为每个BOM行添加物料和用量公式
7. 设置物料替代关系和选择规则
8. 测试公式计算结果
9. 保存物料BOM模板

#### 2.2 工艺BOM创建流程
1. 工艺工程师选择已有物料BOM或成品物料
2. 点击"新建工艺BOM"或"从物料BOM生成"
3. 关联对应的工艺路线
4. 继承物料BOM的基础结构和参数
5. 为每个工序添加工艺参数配置
6. 配置工装夹具需求和工时计算
7. 设置工艺替代方案和选择条件
8. 定义工艺BOM与物料BOM的同步规则
9. 验证工艺BOM的完整性和一致性
10. 保存工艺BOM模板

#### 2.3 BOM关联管理流程
1. 建立物料BOM与工艺BOM的关联关系
2. 设置关联同步规则和触发条件
3. 配置变更影响分析规则
4. 定义版本兼容性和升级策略
5. 测试关联关系的正确性
6. 保存关联配置

#### 2.4 参数化公式配置流程
1. 选择BOM行物料或工艺参数
2. 在公式编辑器中输入计算公式
3. 系统实时进行语法校验
4. 输入测试参数值
5. 查看公式计算结果
6. 验证公式在不同参数组合下的正确性
7. 调整公式直到满足要求

### 后置条件
- 物料BOM和工艺BOM模板保存成功
- 公式计算结果准确无误
- BOM关联关系建立正确
- BOM数据可供销售系统和生产系统调用

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：双重BOM设计器
### 页面目标：提供物料BOM和工艺BOM的统一设计和管理界面

### 信息架构：
- **顶部区域**：包含 BOM类型切换, 关联关系显示, 同步状态指示
- **左侧区域**：包含 物料选择器, 工艺参数库, 工装夹具库, 分类树
- **中间区域**：包含 BOM树状结构, 拖拽操作区域, 层级展示, 关联线
- **右侧区域**：包含 配置面板, 公式编辑器, 工艺参数配置, 计算测试工具
- **底部区域**：包含 保存按钮, 预览按钮, 测试按钮, 同步按钮, 重置按钮

### 交互逻辑与状态：

#### **BOM类型切换器**
- **物料BOM标签：** 绿色标签，显示物料组成结构
- **工艺BOM标签：** 蓝色标签，显示工艺流程结构
- **关联状态：** 显示两种BOM的关联关系和同步状态
- **切换动画：** 平滑切换，保持上下文连续性

#### **双重BOM树状结构**
- **物料BOM视图：** 显示成品物料为根节点，子物料可展开
- **工艺BOM视图：** 显示工艺路线为根节点，工序和工艺参数可展开
- **关联线显示：** 虚线连接物料BOM和工艺BOM的对应节点
- **展开状态：** 点击节点前的展开图标，显示下级结构
- **选中状态：** 蓝色背景(#E6F7FF)，蓝色左边框
- **编辑状态：** 双击节点进入编辑模式，显示输入框
- **拖拽状态：** 支持节点拖拽调整层级关系
- **同步状态：** 显示节点的同步状态（已同步/待同步/冲突）
- **交互行为：** 选中节点后右侧显示详细配置

#### **参数变量定义区域**
- **变量列表：** 显示已定义的参数变量（L、W、T等）
- **新增变量按钮：** 蓝色背景，点击打开变量定义对话框
- **变量编辑：** 支持修改变量名称、类型、范围、默认值
- **变量删除：** 删除前检查是否被公式引用

#### **智能配置面板**
- **物料BOM配置：** 物料信息、用量公式、替代关系
- **工艺BOM配置：** 工艺参数、工装配置、工时计算、质量要求
- **关联配置：** 物料BOM与工艺BOM的关联设置
- **同步规则：** 配置自动同步的触发条件和规则

#### **增强公式编辑器**
- **默认状态：** 显示当前物料或工艺参数的计算公式
- **编辑状态：** 代码编辑器样式，支持语法高亮
- **语法校验：** 实时检查公式语法，错误处红色下划线
- **智能提示：** 输入时显示可用变量、函数和工艺参数
- **模板库：** 提供常用公式模板快速选择
- **交互行为：** 支持快捷键操作，Ctrl+S保存

#### **物料选择器**
- **分类导航：** 左侧树状分类结构
- **搜索功能：** 顶部搜索框，支持模糊查找
- **物料列表：** 右侧物料列表，支持拖拽到BOM树
- **筛选器：** 按类型、状态筛选物料

#### **计算测试工具**
- **参数输入区：** 为每个变量提供输入框
- **计算按钮：** 蓝色背景，白色文字"计算测试"
- **结果展示：** 表格形式显示每个物料的计算用量
- **错误提示：** 计算失败时显示具体错误信息

#### **BOM预览面板**
- **树状展示：** 完整的BOM层级结构
- **用量显示：** 每个物料的计算用量和单位
- **成本预估：** 基于物料单价的成本计算
- **导出功能：** 支持导出为Excel格式

### 数据校验规则：

#### **参数变量名称**
- **校验规则：** 必填，字母开头，不能包含特殊字符
- **错误提示文案：** "变量名称格式不正确"

#### **用量公式**
- **校验规则：** 语法正确，引用的变量必须已定义
- **错误提示文案：** "公式语法错误" / "引用了未定义的变量"

#### **物料层级**
- **校验规则：** 不能形成循环引用
- **错误提示文案：** "检测到循环引用，请调整BOM结构"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **BOM基本信息**:
  - **BOM编码 (bom_code)**: String, 必填, 自动生成
  - **BOM类型 (bom_type)**: Enum, 必填, [物料BOM/工艺BOM]
  - **成品物料 (product_material_id)**: String, 必填, 物料ID
  - **关联工艺路线 (process_route_id)**: String, 工艺BOM必填
  - **关联物料BOM (material_bom_id)**: String, 工艺BOM可选
- **参数变量定义**:
  - **变量名 (variable_name)**: String, 必填, 如"L"、"W"、"T"
  - **变量类型 (variable_type)**: Enum, 必填, [数值/文本/选择]
  - **取值范围 (value_range)**: String, 可选, 如"100-3000"
  - **默认值 (default_value)**: String, 可选
- **物料BOM行数据**:
  - **物料ID (material_id)**: String, 必填
  - **用量公式 (quantity_formula)**: String, 必填, 如"L*W/1000000"
  - **层级 (level)**: Integer, 必填, 从1开始
  - **父级ID (parent_id)**: String, 可选
  - **替代物料 (alternative_materials)**: Array, 可选
- **工艺BOM行数据**:
  - **工序ID (operation_id)**: String, 必填
  - **工艺参数配置 (process_parameters)**: JSON, 关联工艺参数库
  - **工装配置 (tooling_configuration)**: Array, 关联工装夹具
  - **工时公式 (time_formula)**: String, 可选
  - **质量要求 (quality_requirements)**: JSON, 可选
  - **层级 (level)**: Integer, 必填, 从1开始
  - **父级ID (parent_id)**: String, 可选
- **关联配置**:
  - **同步规则 (sync_rules)**: JSON, 物料BOM与工艺BOM同步规则
  - **关联映射 (relation_mapping)**: JSON, 两种BOM的节点映射关系

### 展示数据
- **BOM基本信息**: 编码、名称、类型、版本、状态、创建时间
- **参数变量列表**: 变量名、类型、范围、默认值
- **物料BOM结构**: 物料层级关系、用量公式、计算结果
- **工艺BOM结构**: 工序层级关系、工艺参数、工装配置、工时计算
- **关联关系**: 物料BOM与工艺BOM的关联映射和同步状态
- **公式测试结果**: 各物料计算用量、工艺参数计算、总成本预估
- **版本历史**: 历史版本列表、变更记录、影响分析

### 空状态/零数据
- **新建物料BOM**: 显示"请添加子物料构建BOM结构"
- **新建工艺BOM**: 显示"请选择工艺路线或关联物料BOM"
- **无参数变量**: 显示"请先定义参数变量"
- **无关联关系**: 显示"请建立物料BOM与工艺BOM的关联"
- **计算无结果**: 显示"请输入参数值进行计算测试"

### API接口
- **创建物料BOM**: POST /api/pdm/boms/material
- **创建工艺BOM**: POST /api/pdm/boms/process
- **更新BOM**: PUT /api/pdm/boms/{id}
- **获取BOM详情**: GET /api/pdm/boms/{id}
- **BOM关联管理**: POST /api/pdm/boms/associate
- **BOM同步**: POST /api/pdm/boms/sync
- **从物料BOM生成工艺BOM**: POST /api/pdm/boms/generate-process-bom
- **公式计算**: POST /api/boms/{id}/calculate
- **BOM预览**: GET /api/boms/{id}/preview
- **公式语法校验**: POST /api/boms/validate-formula

## 5. 异常与边界处理 (Error & Edge Cases)

### **公式语法错误**
- **提示信息**: "公式语法错误：具体错误位置和原因"
- **用户操作**: 错误位置高亮显示，提供修复建议

### **参数变量未定义**
- **提示信息**: "公式中引用了未定义的变量：{变量名}"
- **用户操作**: 高亮未定义变量，提供快速定义选项

### **循环引用检测**
- **提示信息**: "检测到BOM循环引用，请调整结构"
- **用户操作**: 高亮循环引用路径，阻止保存操作

### **计算结果异常**
- **提示信息**: "计算结果异常，请检查公式和参数"
- **用户操作**: 显示异常的物料和公式，提供调试信息

### **物料数据缺失**
- **提示信息**: "物料信息不完整，无法进行计算"
- **用户操作**: 高亮缺失信息的物料，提供补充入口

### **保存失败**
- **提示信息**: "BOM保存失败，请检查网络后重试"
- **用户操作**: 保持编辑状态，提供重试按钮

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持物料BOM和工艺BOM的独立创建、编辑、删除
- [ ] 物料BOM与工艺BOM的关联关系建立正确
- [ ] 支持从物料BOM自动生成工艺BOM
- [ ] 工艺BOM与工艺路线的关联功能正常
- [ ] 支持定义多个参数变量（长度、宽度、厚度等）
- [ ] 物料用量和工艺参数公式支持四则运算和常用函数
- [ ] 公式语法错误实时提示和校验
- [ ] BOM树状结构支持拖拽调整层级关系
- [ ] 双重BOM结构的可视化展示清晰
- [ ] 参数化BOM可预览和测试计算结果
- [ ] 工艺BOM的工艺参数配置功能完整
- [ ] 工装夹具配置与工艺BOM的集成正常
- [ ] BOM同步机制工作正常，支持增量同步
- [ ] 公式计算结果精度满足业务要求（≥99.9%）
- [ ] 支持BOM结构的循环引用检测
- [ ] 物料选择器支持分类导航和搜索过滤
- [ ] BOM设计器界面专业易用，符合工程师习惯
- [ ] 参数化BOM计算响应时间小于1秒（10个变量内）
- [ ] BOM关联和同步响应时间<2秒
- [ ] 版本管理和变更控制功能正常
- [ ] 所有操作记录到审计日志
- [ ] 界面支持响应式设计，移动端可正常使用
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持键盘导航和快捷键操作
