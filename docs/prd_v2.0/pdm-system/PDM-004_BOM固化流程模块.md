# 功能模块规格说明书：BOM固化流程模块

- **模块ID**: PDM-004
- **所属子系统**: 工艺管理子系统(PDM)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 工艺工程师, **I want to** 审核销售订单的BOM配置, **so that** 生成准确的生产BOM。
- **As a** 工艺工程师, **I want to** 对销售BOM进行微调, **so that** 适应实际生产需求。
- **As a** 生产计划员, **I want to** 获得固化的生产BOM, **so that** 制定准确的生产计划。
- **As a** 质量管理员, **I want to** 追踪BOM固化过程, **so that** 确保生产数据的可追溯性。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有BOM固化权限
- 销售订单已确认并生成BOM快照
- 参数化BOM模板已激活

### 核心流程

#### 2.1 BOM快照接收流程
1. 销售订单确认后自动触发BOM快照生成
2. 系统基于参数化BOM和客户参数计算物料用量
3. 生成待审核的BOM快照
4. 工艺工程师接收审核通知
5. BOM快照状态设为"待审核"

#### 2.2 BOM审核和微调流程
1. 工艺工程师查看BOM快照内容
2. 检查物料配置和用量计算的准确性
3. 根据生产实际情况进行微调：
   - 替换物料（如供应商变更）
   - 调整用量（如增加损耗系数）
   - 添加辅料（如包装材料）
   - 删除不必要的物料
4. 填写调整说明和理由
5. 预览调整后的BOM结构

#### 2.3 BOM固化和锁定流程
1. 审核通过后点击"固化BOM"按钮
2. 系统生成独立的生产BOM
3. BOM状态变更为"已固化"
4. 固化BOM与原始模板解除关联
5. 自动传递给MRP和生产系统
6. 记录固化操作的完整审计日志

### 后置条件
- 固化BOM数据准确完整
- 生产BOM独立于原始模板
- BOM数据成功传递给下游系统

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：BOM固化审核页
### 页面目标：提供高效的BOM审核和固化操作界面

### 信息架构：
- **顶部区域**：包含 订单信息, BOM状态, 操作按钮组
- **左侧区域**：包含 原始BOM结构, 参数配置信息
- **中间区域**：包含 当前BOM快照, 微调操作工具
- **右侧区域**：包含 调整历史, 审核意见, 固化预览

### 交互逻辑与状态：

#### **BOM快照状态指示器**
- **待审核状态：** 橙色标签(#FAAD14)，显示"待审核"
- **审核中状态：** 蓝色标签(#1890FF)，显示"审核中"
- **已固化状态：** 绿色标签(#52C41A)，显示"已固化"
- **已驳回状态：** 红色标签(#F5222D)，显示"已驳回"

#### **BOM对比视图**
- **原始BOM：** 左侧显示参数化BOM模板
- **快照BOM：** 中间显示计算后的BOM快照
- **差异高亮：**
  - 新增物料：绿色背景(#F6FFED)
  - 删除物料：红色背景(#FFF2F0)
  - 修改用量：黄色背景(#FFFBE6)

#### **微调操作工具栏**
- **替换物料按钮：** 蓝色背景，点击打开物料选择器
- **调整用量按钮：** 绿色背景，点击打开用量编辑器
- **添加辅料按钮：** 橙色背景，点击添加新的BOM行
- **删除物料按钮：** 红色背景，确认后删除选中物料

#### **用量调整编辑器**
- **当前用量显示：** 显示计算得出的用量值
- **调整系数输入：** 数值输入框，支持小数
- **调整原因选择：** 下拉选择（损耗补偿/工艺要求/质量要求）
- **自定义原因：** 文本输入框，填写具体原因

#### **物料替换选择器**
- **原物料信息：** 显示当前物料的详细信息
- **替换物料搜索：** 搜索框，支持按名称、编码查找
- **替换物料列表：** 显示可替换的物料选项
- **替换原因：** 必填项，选择替换原因

#### **审核操作区域**
- **通过按钮：** 绿色背景(#52C41A)，白色文字"审核通过"
- **驳回按钮：** 红色背景(#F5222D)，白色文字"驳回订单"
- **暂缓按钮：** 橙色背景(#FAAD14)，白色文字"暂缓处理"
- **审核意见框：** 文本域，必填项

#### **固化预览面板**
- **BOM结构树：** 显示固化后的完整BOM结构
- **成本计算：** 显示物料成本和总成本
- **工时预估：** 显示预计的生产工时
- **固化按钮：** 蓝色背景，白色文字"确认固化"

### 数据校验规则：

#### **用量调整**
- **校验规则：** 调整后用量必须大于0
- **错误提示文案：** "物料用量必须大于0"

#### **审核意见**
- **校验规则：** 必填，最少5个字符
- **错误提示文案：** "请填写审核意见，至少5个字符"

#### **替换物料**
- **校验规则：** 替换物料不能与原物料相同
- **错误提示文案：** "替换物料不能与原物料相同"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **订单编号 (order_number)**: String, 必填, 来源于销售订单
- **BOM快照ID (snapshot_id)**: String, 系统生成
- **客户参数 (customer_parameters)**: Object, 必填, 如{"L":1200,"W":800}
- **微调操作记录**:
  - **操作类型 (operation_type)**: Enum, [替换/调整/添加/删除]
  - **物料ID (material_id)**: String, 必填
  - **调整前值 (before_value)**: String, 可选
  - **调整后值 (after_value)**: String, 必填
  - **调整原因 (reason)**: String, 必填

### 展示数据
- **订单基本信息**: 订单号、客户名称、产品规格、交期
- **原始BOM结构**: 参数化BOM模板的完整结构
- **计算后BOM**: 基于客户参数计算的物料用量
- **微调历史**: 所有调整操作的详细记录
- **成本分析**: 物料成本、人工成本、总成本预估

### 空状态/零数据
- **无待审核BOM**: 显示"暂无待审核的BOM快照"
- **无微调记录**: 显示"该BOM暂未进行微调"
- **计算失败**: 显示"BOM计算失败，请检查参数配置"

### API接口
- **获取BOM快照**: GET /api/bom-snapshots/{snapshot_id}
- **BOM微调操作**: POST /api/bom-snapshots/{snapshot_id}/adjust
- **BOM审核**: POST /api/bom-snapshots/{snapshot_id}/review
- **BOM固化**: POST /api/bom-snapshots/{snapshot_id}/solidify
- **获取微调历史**: GET /api/bom-snapshots/{snapshot_id}/adjustments

## 5. 异常与边界处理 (Error & Edge Cases)

### **BOM计算异常**
- **提示信息**: "BOM计算失败：参数配置错误或公式异常"
- **用户操作**: 显示具体错误信息，提供重新计算选项

### **物料替换冲突**
- **提示信息**: "替换物料与BOM结构冲突，请重新选择"
- **用户操作**: 高亮冲突的物料，提供替代方案

### **固化权限不足**
- **提示信息**: "您没有权限固化此BOM"
- **用户操作**: 固化按钮禁用，显示当前审核人

### **下游系统传递失败**
- **提示信息**: "BOM数据传递失败，请联系系统管理员"
- **用户操作**: 记录传递失败日志，提供重试机制

### **订单状态异常**
- **提示信息**: "订单状态异常，无法进行BOM固化"
- **用户操作**: 显示订单当前状态，提供状态修复入口

### **重复固化检测**
- **提示信息**: "该订单的BOM已经固化，不能重复操作"
- **用户操作**: 显示已固化的BOM信息，提供查看入口

## 6. 验收标准 (Acceptance Criteria)

- [ ] 销售订单确认后自动生成BOM快照
- [ ] 支持对BOM快照进行微调：替换物料、调整用量、增加辅料
- [ ] 审核通过后BOM状态变为固化，数据锁定
- [ ] 固化BOM不受原始模板变更影响
- [ ] 重大问题可驳回至销售环节，状态正确流转
- [ ] BOM微调操作记录完整的审计轨迹
- [ ] 固化BOM自动传递给MRP和生产系统
- [ ] 支持BOM固化前的预览和成本分析
- [ ] 物料替换功能正常工作，支持替换原因记录
- [ ] 用量调整支持损耗系数和工艺要求
- [ ] 审核权限控制正确，非审核人员不能操作
- [ ] 所有操作记录到审计日志
- [ ] 界面支持响应式设计，移动端可正常使用
- [ ] BOM固化响应时间小于5秒
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持键盘导航和无障碍访问
