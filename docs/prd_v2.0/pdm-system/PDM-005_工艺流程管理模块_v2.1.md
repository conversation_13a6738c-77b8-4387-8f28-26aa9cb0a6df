# 功能模块规格说明书：工艺流程管理模块

- **模块ID**: PDM-005
- **所属子系统**: 工艺管理子系统(PDM)
- **最后更新**: 2025-08-02
- **版本**: v2.1 (整合版)
- **整合说明**: 整合原PDM-005工序管理、PDM-006工艺路线设计、PDM-007外协工序管理为统一模块
- **业务价值**: 提供端到端的工艺流程管理，从工序标准化到工艺路线设计，再到外协决策的一体化解决方案

## 1. 用户故事 (User Stories)

### 1.1 工序标准管理
- **As a** 工艺工程师, **I want to** 建立标准化的工序主数据库, **so that** 为工艺路线设计提供标准化的工序信息。
- **As a** 工艺工程师, **I want to** 定义工序的标准工时和质量要求, **so that** 支持精确的生产计划和成本核算。
- **As a** 工艺工程师, **I want to** 管理工序的作业指导和安全规范, **so that** 确保生产操作的标准化和安全性。
- **As a** 质量工程师, **I want to** 查看工序的质量标准, **so that** 制定相应的质量检验方案。

### 1.2 工艺路线设计
- **As a** 工艺工程师, **I want to** 设计参数化的工艺路线模板, **so that** 根据产品参数自动生成适配的工艺流程。
- **As a** 工艺工程师, **I want to** 可视化设计工艺流程, **so that** 直观展示工序间的逻辑关系和参数依赖。
- **As a** 工艺工程师, **I want to** 为同一产品创建多个可选工艺路线, **so that** 根据生产条件灵活选择最优路线。
- **As a** 工艺工程师, **I want to** 配置工序的工艺参数和工装要求, **so that** 支持精确的生产控制和资源配置。
- **As a** 工艺工程师, **I want to** 管理工艺路线的完整生命周期, **so that** 从创建、编辑、审批到发布的全流程可控。
- **As a** 工艺工程师, **I want to** 复制和修改现有工艺路线, **so that** 快速创建相似产品的工艺路线。
- **As a** 工艺工程师, **I want to** 对比不同版本的工艺路线, **so that** 了解变更内容和影响范围。
- **As a** 生产计划员, **I want to** 查看工艺路线的工时和资源信息, **so that** 制定合理的生产排程和资源分配。

### 1.3 工艺路线审批管理
- **As a** 工艺主管, **I want to** 审批工艺路线的技术可行性, **so that** 确保工艺路线符合技术标准和规范。
- **As a** 生产主管, **I want to** 审批工艺路线的生产可行性, **so that** 确保工艺路线能够在现有条件下实施。
- **As a** 质量主管, **I want to** 审批工艺路线的质量要求, **so that** 确保产品质量符合标准。
- **As a** 技术总监, **I want to** 审批重要工艺路线, **so that** 确保关键工艺的技术先进性和可靠性。
- **As a** 工艺工程师, **I want to** 跟踪工艺路线的审批进度, **so that** 及时了解审批状态和处理审批意见。
- **As a** 系统管理员, **I want to** 配置审批流程和权限, **so that** 确保审批流程的规范性和有效性。

### 1.4 工艺路线列表管理
- **As a** 工艺工程师, **I want to** 查看所有工艺路线的列表, **so that** 快速找到需要的工艺路线。
- **As a** 工艺工程师, **I want to** 按多种条件筛选工艺路线, **so that** 精确定位特定的工艺路线。
- **As a** 工艺工程师, **I want to** 批量操作工艺路线, **so that** 提高工作效率。
- **As a** 工艺工程师, **I want to** 查看工艺路线的使用统计, **so that** 了解工艺路线的应用情况。
- **As a** 数据分析师, **I want to** 分析工艺路线的效果数据, **so that** 为工艺优化提供数据支持。

### 1.5 外协工序管理
- **As a** 工艺工程师, **I want to** 标识工序的外协属性, **so that** 区分内制和外协的生产方式。
- **As a** 工艺工程师, **I want to** 定义外协工序的技术要求和质量标准, **so that** 为采购系统提供准确的外协规范。
- **As a** 工艺工程师, **I want to** 管理外协供应商的工艺能力信息, **so that** 为外协工序选择合适的供应商。
- **As a** 采购员, **I want to** 获取外协工序的技术要求和成本基准, **so that** 进行外协采购和成本控制。
- **As a** 生产计划员, **I want to** 了解外协工序的标准交期, **so that** 制定合理的生产计划。

### 1.6 综合管理
- **As a** 工艺工程师, **I want to** 在统一界面中完成从工序定义到工艺路线设计的全流程, **so that** 提高工艺设计效率。
- **As a** 工艺工程师, **I want to** 分析不同工艺路线的质量表现和成本效益, **so that** 优化工艺路线提升整体效率。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有工艺流程管理权限
- 工艺分类体系已建立
- 质量标准体系已配置
- 设备主数据已建立
- 工作中心主数据已建立（参考MES-012工作中心管理模块）
- 工艺参数库已建立（参考PDM-010工艺参数库管理模块）
- 工装夹具信息已配置（参考PDM-011工装夹具管理模块）
- 外协供应商主数据已建立

### 核心流程

#### 2.1 工序标准化管理流程
1. **工序创建子流程**
   - 工艺工程师选择工序分类
   - 填写工序基本信息（编码、名称、描述）
   - 设置工序类型和工艺特性
   - 配置标准工时和工艺参数（关联PDM-010工艺参数库）
   - 定义质量要求和检验标准
   - 编写作业指导和安全规范
   - 设置设备和技能要求（关联PDM-011工装夹具管理）
   - 提交工序审核，审核通过后工序生效

2. **工序标准维护子流程**
   - 查询需要更新的工序
   - 修改工序标准信息
   - 记录变更原因和影响分析
   - 提交变更审批
   - 审批通过后更新工序标准
   - 通知相关人员工序变更
   - 自动更新相关工艺路线

#### 2.2 工艺路线列表管理流程
1. **路线列表查询子流程**
   - 用户进入工艺路线管理界面
   - 系统展示工艺路线列表（编码、名称、版本、状态、产品类型、创建时间）
   - 支持多条件筛选（状态、产品类型、创建人、时间范围）
   - 支持关键字搜索（路线编码、名称、描述）
   - 支持多字段排序（创建时间、更新时间、使用频次）
   - 分页显示，支持每页条数设置

2. **路线CRUD操作子流程**
   - **创建路线**：点击新建按钮 → 选择创建方式（空白创建/模板创建/复制创建） → 填写基本信息 → 进入设计器
   - **查看路线**：点击路线名称 → 查看路线详情 → 显示工序列表、参数配置、统计信息
   - **编辑路线**：点击编辑按钮 → 检查权限和状态 → 进入编辑模式（列表编辑/设计器编辑）
   - **删除路线**：选择路线 → 点击删除 → 检查引用关系 → 确认删除 → 执行删除操作
   - **批量操作**：选择多个路线 → 选择操作类型（批量删除/批量审批/批量导出） → 确认执行

3. **路线版本管理子流程**
   - 查看版本历史：显示所有版本列表，包含版本号、创建时间、变更说明
   - 版本对比：选择两个版本进行对比，高亮显示差异内容
   - 版本发布：将草稿版本发布为正式版本，更新版本状态
   - 版本回滚：将当前版本回滚到历史版本，创建新的版本记录

#### 2.3 参数化工艺路线设计流程
1. **工艺路线创建子流程**
   - 工艺工程师选择产品物料或产品族
   - 点击"新建参数化工艺路线"按钮
   - 设置路线基本信息（名称、版本、适用范围、参数变量）
   - 定义产品特征参数（长度L、宽度W、厚度T、材质等）
   - 在流程设计器中从工序库添加工序节点
   - 配置工序间的连接关系和条件分支
   - 设置工序的参数化配置（工艺参数、工装选择、工时计算）
   - 定义路线选择规则和适用条件
   - 验证工艺路线的完整性和参数逻辑
   - 保存参数化工艺路线模板

2. **智能工序配置子流程**
   - 在工艺路线中选择工序节点
   - 设置工序基本信息（工序号、名称、描述）
   - 选择工作中心和设备资源
   - 配置参数化工艺参数（关联PDM-010工艺参数库）
   - 智能选择工装夹具（关联PDM-011工装夹具管理）
   - 设置参数化工时计算公式
   - 配置质量要求和检验标准
   - 添加工序说明和注意事项
   - 保存工序配置

#### 2.4 工艺路线审批流程
1. **审批流程配置子流程**
   - 系统管理员配置审批流程模板
   - 定义审批节点和审批人（工艺主管、生产主管、质量主管、技术总监）
   - 设置审批条件和触发规则（按路线重要性、影响范围确定审批级别）
   - 配置审批权限和数据范围
   - 设置审批时限和超时处理机制

2. **提交审批子流程**
   - 工艺工程师完成路线设计
   - 执行路线完整性验证和逻辑检查
   - 填写审批申请信息（申请原因、变更说明、影响分析）
   - 选择审批流程模板
   - 提交审批申请，路线状态变更为"待审批"
   - 系统自动通知相关审批人

3. **审批处理子流程**
   - 审批人收到审批通知
   - 查看路线详情和审批申请信息
   - 进行技术评审和可行性分析
   - 填写审批意见（通过/拒绝/退回修改）
   - 提交审批结果
   - 系统自动流转到下一审批节点或完成审批
   - 通知申请人审批结果

4. **审批完成子流程**
   - 所有审批节点完成后，路线状态变更为"生效"
   - 系统记录完整的审批历史和意见
   - 通知相关人员路线已生效
   - 路线可供生产系统调用使用
   - 如审批被拒绝，路线状态变更为"草稿"，申请人可修改后重新提交

#### 2.5 外协工序管理流程
1. **外协标识配置子流程**
   - 在工艺路线中选择需要外协的工序
   - 设置工序为"外协"属性
   - 选择外协类型（完全外协/部分外协/临时外协）
   - 配置外协工序的技术要求
   - 设置外协工序的质量标准
   - 保存外协工序配置

2. **外协供应商管理子流程**
   - 为外协工序选择合格供应商
   - 设置主供应商和备选供应商
   - 配置供应商的工序能力信息
   - 设置外协价格和交期
   - 建立供应商评价体系
   - 定期更新供应商信息

3. **内制外协切换子流程**
   - 根据生产负荷和成本考虑
   - 评估内制和外协的优劣
   - 制定切换决策和时间计划
   - 更新工艺路线的工序属性
   - 通知相关部门和供应商
   - 跟踪切换效果和成本变化

#### 2.6 多路线设计与优化流程
1. **多路线设计子流程**
   - 基于现有工艺路线创建变体路线
   - 定义路线差异和适用条件
   - 设置路线选择规则（成本优先/质量优先/交期优先）
   - 配置路线切换条件和触发机制
   - 验证多路线的逻辑一致性
   - 测试路线选择算法的正确性

2. **并行工序设计子流程**
   - 识别可并行执行的工序
   - 在流程设计器中创建并行分支
   - 设置分支条件和汇聚点
   - 配置并行工序的资源需求和工装分配
   - 验证并行逻辑的正确性和资源冲突
   - 测试并行路径的可行性

### 后置条件
- 工序信息完整准确，工序标准已审核生效
- 工艺路线数据完整准确，工序逻辑关系清晰明确
- 外协工序信息准确完整，供应商关联关系明确
- 工艺路线可供生产系统调用
- 外协成本数据及时更新
- 变更记录完整可追溯

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：工艺流程管理工作台
### 页面目标：提供工序标准化、工艺路线设计、外协管理的一体化操作界面

### 信息架构：
- **顶部区域**：包含 功能导航栏, 搜索工具, 新建按钮, 批量操作, 导入导出
- **左侧区域**：包含 功能模块树, 工序分类树, 工艺路线树, 筛选器组
- **中间区域**：包含 主工作区, 流程设计画布, 列表视图, 详情编辑区
- **右侧区域**：包含 属性配置面板, 工艺参数配置, 外协信息面板, 统计分析

### 交互逻辑与状态：

#### **功能导航栏**
- **工序管理标签**：蓝色激活状态(#1890FF)，显示工序库和标准管理
- **路线设计标签**：绿色激活状态(#52C41A)，显示工艺路线设计器
- **外协管理标签**：橙色激活状态(#FAAD14)，显示外协工序配置
- **综合分析标签**：紫色激活状态(#722ED1)，显示统计分析和对比
- **切换动画**：平滑的标签页切换效果，保持上下文状态

#### **工序库管理界面**
- **工序分类树**：
  - **默认状态**：展示根分类，子分类折叠显示
  - **展开状态**：点击分类前的展开图标，显示子分类
  - **选中状态**：蓝色背景(#E6F7FF)，蓝色左边框
  - **悬停状态**：浅灰背景(#FAFAFA)，显示操作图标

- **工序列表**：
  - **列表项**：工序编码、名称、类型、标准工时、状态、最后更新
  - **排序功能**：支持按编码、名称、类型、更新时间排序
  - **筛选功能**：支持按工序类型、状态、工艺分类筛选
  - **搜索功能**：支持工序编码、名称的模糊搜索

- **工序详情编辑**：
  - **基本信息区**：工序编码、名称、描述、类型
  - **工艺参数区**：准备工时、单件工时、拆卸工时、工艺温度
  - **质量要求区**：质量标准、检验项目、合格标准
  - **资源要求区**：设备要求、技能要求、人员数量
  - **作业指导区**：SOP文档、操作步骤、注意事项、安全规范

#### **工艺路线列表管理界面**
- **路线列表视图**：
  - **列表头部**：包含搜索框、筛选器、新建按钮、批量操作按钮
  - **列表内容**：路线编码、名称、版本、状态、产品类型、工序数、总工时、创建人、创建时间、操作按钮
  - **状态标识**：不同颜色标识路线状态（草稿-灰色、待审批-黄色、生效-绿色、停用-红色）
  - **排序功能**：支持按创建时间、更新时间、使用频次、名称排序
  - **分页控件**：页码导航、每页条数选择、总数统计

- **搜索和筛选功能**：
  - **关键字搜索**：支持路线编码、名称、描述的模糊搜索
  - **状态筛选**：下拉选择器，支持按路线状态筛选
  - **产品类型筛选**：下拉选择器，支持按产品类型筛选
  - **创建人筛选**：下拉选择器，支持按创建人筛选
  - **时间范围筛选**：日期选择器，支持按创建时间范围筛选
  - **高级筛选**：弹出面板，支持多条件组合筛选

- **批量操作功能**：
  - **全选/反选**：支持全选当前页或全部数据
  - **批量删除**：选择多个路线进行批量删除，检查引用关系
  - **批量审批**：选择多个路线进行批量提交审批
  - **批量导出**：选择多个路线导出为Excel或PDF格式
  - **批量状态变更**：批量修改路线状态（停用/启用）

#### **工艺路线设计器界面**
- **流程设计画布**：
  - **默认状态**：空白画布，显示网格背景和操作提示
  - **拖拽状态**：支持从工序库拖拽工序到画布，显示拖拽预览
  - **选中状态**：工序节点蓝色边框，显示操作手柄和连接点
  - **连接状态**：鼠标悬停显示连接点，支持拖拽连线，显示连线预览
  - **缩放状态**：支持鼠标滚轮缩放（25%-400%），显示缩放比例和缩放控件
  - **画布操作**：支持平移、框选、对齐、分布等操作

- **工序节点样式**：
  - **标准工序**：圆角矩形，蓝色边框(#1890FF)，白色背景，显示工序号和名称
  - **外协工序**：圆角矩形，橙色边框(#FAAD14)，浅橙背景，右上角显示外协图标
  - **并行节点**：菱形形状，绿色边框(#52C41A)，显示并行标识
  - **开始节点**：圆形形状，绿色填充(#52C41A)，显示"开始"文字
  - **结束节点**：圆形形状，红色填充(#FF4D4F)，显示"结束"文字
  - **判断节点**：菱形形状，黄色边框(#FAAD14)，显示判断条件
  - **节点内容**：工序号、工序名称、工作中心、标准工时、外协标识

- **工序模板库**：
  - **分类导航**：按工序类型分类（切割/磨边/钢化/合片/铝材加工）
  - **模板列表**：显示预定义的工序模板，包含工序图标、名称、标准工时
  - **搜索功能**：支持按工序名称、编码的实时搜索
  - **拖拽操作**：支持拖拽模板到设计画布，自动生成工序节点
  - **收藏功能**：支持收藏常用工序模板，快速访问

- **工序属性面板**：
  - **基本信息区**：工序号、名称、描述输入框，支持实时验证
  - **工作中心选择**：下拉选择器，显示可用工作中心和负荷情况
  - **参数化工时配置**：支持公式编辑器的工时计算，提供常用公式模板
  - **工艺参数配置**：关联工艺参数库，支持参数模板选择和自定义参数
  - **工装夹具配置**：智能推荐和手动选择工装夹具，显示可用性状态
  - **质量要求区**：质量标准、检验项目、合格标准配置
  - **外协属性区**：外协标识开关、供应商选择、价格配置、交期设置
  - **执行条件区**：工序执行的前置条件和触发条件配置

#### **工艺路线审批界面**
- **审批流程配置**：
  - **流程模板管理**：创建、编辑、删除审批流程模板
  - **审批节点配置**：设置审批人、审批条件、审批时限
  - **权限配置**：配置不同角色的审批权限和数据范围
  - **通知配置**：设置审批通知方式和模板

- **审批任务列表**：
  - **待审批任务**：显示待处理的审批任务，按优先级和时限排序
  - **已审批任务**：显示已处理的审批任务，支持查看审批详情
  - **审批统计**：显示审批效率、通过率等统计信息
  - **超时提醒**：高亮显示超时的审批任务

- **审批详情界面**：
  - **路线信息展示**：完整显示待审批的工艺路线信息
  - **变更对比**：如果是修改审批，显示变更前后的对比
  - **审批表单**：审批意见输入、审批结果选择（通过/拒绝/退回）
  - **审批历史**：显示之前的审批记录和意见
  - **相关文档**：显示相关的技术文档和参考资料

#### **外协管理界面**
- **外协工序标识**：
  - **内制工序**：灰色标签(#8C8C8C)，显示"内制"
  - **外协工序**：橙色标签(#FAAD14)，显示"外协"
  - **混合工序**：蓝色标签(#1890FF)，显示"混合"
  - **切换按钮**：开关组件，支持内制/外协切换

- **外协配置面板**：
  - **外协类型选择**：完全外协/部分外协/临时外协
  - **供应商管理**：主供应商、备选供应商选择
  - **价格配置**：单价、计价方式、价格有效期
  - **交期配置**：标准交期、加急交期
  - **质量要求**：技术标准、质量标准、检验要求
  - **成本分析**：外协成本、内制成本对比

#### **综合分析界面**
- **工艺路线对比**：
  - **路线选择器**：下拉选择不同的工艺路线版本
  - **并排对比**：显示不同路线的差异
  - **工时统计**：计算总工时和关键路径
  - **成本分析**：基于工艺参数和工装配置计算成本

- **统计分析面板**：
  - **工序使用统计**：工序在不同路线中的使用频次
  - **外协比例分析**：内制vs外协的比例和趋势
  - **成本效益分析**：不同工艺路线的成本效益对比
  - **质量表现分析**：不同路线的质量指标对比

### 数据校验规则：

#### **工序编码**
- **校验规则**：编码必须全局唯一，符合编码规则格式
- **错误提示文案**："工序编码已存在或格式不正确"

#### **工序名称**
- **校验规则**：必填，2-50位字符
- **错误提示文案**："工序名称不能为空"

#### **工时配置**
- **校验规则**：工时必须大于0，准备工时+单件工时不能为0
- **错误提示文案**："工时配置不能为空且必须大于0"

#### **工艺路线完整性**
- **校验规则**：必须有开始和结束节点，工序连接逻辑正确，无循环依赖
- **错误提示文案**："工艺路线不完整，请检查开始和结束节点"

#### **审批流程校验**
- **校验规则**：审批流程必须完整，审批人不能为空，审批权限有效
- **错误提示文案**："审批流程配置不完整，请检查审批人设置"

#### **版本号校验**
- **校验规则**：版本号格式正确，版本号不能重复
- **错误提示文案**："版本号格式错误或已存在"

#### **外协价格**
- **校验规则**：必填，数值类型，大于0
- **错误提示文案**："外协价格必须大于0"

#### **供应商选择**
- **校验规则**：外协工序必须选择供应商
- **错误提示文案**："外协工序必须选择供应商"

## 4. 数据规格 (Data Requirements)

### 输入数据

#### 4.1 工序标准数据
- **工序基本信息**:
  - **工序编码 (operation_code)**: String, 必填, 全局唯一
  - **工序名称 (operation_name)**: String, 必填, 最大50字符
  - **工序类型 (operation_type)**: Enum, 必填, [切割/磨边/钢化/合片/铝材加工]
  - **工序描述 (description)**: String, 可选, 最大500字符
  - **工序分类 (category_id)**: String, 必填, 关联工序分类
- **工艺参数**:
  - **准备工时 (setup_time)**: Decimal, 必填, 单位分钟
  - **单件工时 (cycle_time)**: Decimal, 必填, 单位分钟
  - **拆卸工时 (teardown_time)**: Decimal, 可选, 单位分钟
  - **工艺参数配置 (process_parameters)**: JSON, 关联PDM-010工艺参数库
- **质量要求**:
  - **质量标准 (quality_standard)**: String, 可选, 最大1000字符
  - **检验项目 (inspection_items)**: Array, 关联检验项目ID
  - **合格标准 (acceptance_criteria)**: String, 可选, 最大500字符
- **资源要求**:
  - **设备要求 (equipment_requirements)**: Array, 设备类型ID列表
  - **技能要求 (skill_requirements)**: Enum, [初级/中级/高级/专家]
  - **人员数量 (operator_count)**: Integer, 标准作业人员数量
  - **工装配置 (tooling_configuration)**: Array, 关联PDM-011工装夹具

#### 4.2 工艺路线数据
- **路线基本信息**:
  - **路线编码 (route_code)**: String, 必填, 自动生成
  - **路线名称 (route_name)**: String, 必填, 2-100位字符
  - **路线类型 (route_type)**: Enum, 必填, [标准路线/参数化路线/变体路线]
  - **适用物料 (material_id)**: String, 必填, 物料ID或产品族ID
  - **路线版本 (version)**: String, 必填, 版本号
  - **路线状态 (status)**: Enum, 必填, [草稿/待审批/生效/停用/归档]
  - **创建人 (created_by)**: String, 必填, 创建人ID
  - **创建时间 (created_at)**: DateTime, 必填, 创建时间
  - **更新时间 (updated_at)**: DateTime, 必填, 最后更新时间
- **参数化配置**:
  - **产品参数 (product_parameters)**: Array, 产品特征参数定义
  - **路线选择规则 (selection_rules)**: JSON, 路线选择逻辑
  - **适用条件 (applicable_conditions)**: JSON, 路线适用条件
- **工序信息**:
  - **工序号 (operation_number)**: Integer, 必填, 如10、20、30
  - **工序ID (operation_id)**: String, 必填, 关联工序主数据
  - **工作中心 (work_center_id)**: String, 必填
  - **工时公式 (time_formula)**: String, 参数化工时计算公式
  - **执行条件 (execution_conditions)**: JSON, 工序执行条件
  - **并行标识 (parallel_flag)**: Boolean, 是否并行工序
  - **前置工序 (predecessor_operations)**: Array, 前置工序号列表
  - **后续工序 (successor_operations)**: Array, 后续工序号列表

#### 4.3 审批流程数据
- **审批流程配置**:
  - **流程ID (process_id)**: String, 必填, 流程唯一标识
  - **流程名称 (process_name)**: String, 必填, 流程名称
  - **流程类型 (process_type)**: Enum, 必填, [单级审批/多级审批/并行审批]
  - **适用范围 (applicable_scope)**: JSON, 流程适用的路线类型和条件
- **审批节点配置**:
  - **节点ID (node_id)**: String, 必填, 节点唯一标识
  - **节点名称 (node_name)**: String, 必填, 节点名称
  - **审批人 (approver_id)**: String, 必填, 审批人ID或角色ID
  - **审批条件 (approval_conditions)**: JSON, 审批触发条件
  - **审批时限 (time_limit)**: Integer, 可选, 审批时限（小时）
  - **节点顺序 (node_order)**: Integer, 必填, 节点执行顺序
- **审批实例数据**:
  - **实例ID (instance_id)**: String, 必填, 审批实例唯一标识
  - **路线ID (route_id)**: String, 必填, 关联的工艺路线ID
  - **申请人 (applicant_id)**: String, 必填, 申请人ID
  - **申请时间 (apply_time)**: DateTime, 必填, 申请时间
  - **当前节点 (current_node)**: String, 必填, 当前审批节点
  - **审批状态 (approval_status)**: Enum, 必填, [待审批/审批中/已通过/已拒绝/已撤回]
  - **审批历史 (approval_history)**: Array, 审批历史记录
  - **审批意见 (approval_comments)**: Array, 审批意见列表

#### 4.4 版本管理数据
- **版本信息**:
  - **版本ID (version_id)**: String, 必填, 版本唯一标识
  - **路线ID (route_id)**: String, 必填, 关联的工艺路线ID
  - **版本号 (version_number)**: String, 必填, 版本号（如1.0、1.1、2.0）
  - **版本类型 (version_type)**: Enum, 必填, [主版本/次版本/修订版本]
  - **版本状态 (version_status)**: Enum, 必填, [开发中/测试中/已发布/已归档]
  - **父版本 (parent_version)**: String, 可选, 父版本ID
  - **变更说明 (change_description)**: Text, 必填, 版本变更说明
  - **发布时间 (release_time)**: DateTime, 可选, 版本发布时间
- **版本对比数据**:
  - **对比ID (comparison_id)**: String, 必填, 对比记录唯一标识
  - **源版本 (source_version)**: String, 必填, 源版本ID
  - **目标版本 (target_version)**: String, 必填, 目标版本ID
  - **差异内容 (differences)**: JSON, 版本间的差异内容
  - **对比时间 (comparison_time)**: DateTime, 必填, 对比时间

#### 4.5 外协工序数据
- **外协基本信息**:
  - **工序ID (operation_id)**: String, 必填, 关联工艺路线工序
  - **外协类型 (outsourcing_type)**: Enum, 必填, [完全外协/部分外协/临时外协]
  - **外协标识 (is_outsourced)**: Boolean, 必填, 是否外协
- **供应商信息**:
  - **主供应商 (primary_supplier_id)**: String, 必填
  - **备选供应商 (backup_suppliers)**: Array, 可选, 供应商ID列表
  - **供应商能力 (supplier_capabilities)**: JSON, 供应商工序能力信息
- **外协价格信息**:
  - **单价 (unit_price)**: Number, 必填, 外协单价
  - **计价方式 (pricing_method)**: Enum, 必填, [按件/按时/按面积/按重量]
  - **价格有效期 (price_valid_until)**: Date, 必填
  - **价格历史 (price_history)**: Array, 历史价格记录
- **交期信息**:
  - **标准交期 (standard_lead_time)**: Integer, 必填, 单位天
  - **加急交期 (urgent_lead_time)**: Integer, 可选, 单位天
  - **交期计算规则 (lead_time_formula)**: String, 交期计算公式

### 展示数据
- **工序库视图**: 工序的基本信息、标准参数、使用统计
- **工艺路线视图**: 路线基本信息、工序序列、参数化配置、可视化流程图
- **外协管理视图**: 外协工序列表、供应商信息、价格趋势、成本对比
- **综合分析视图**: 多路线对比、工时统计、成本分析、质量表现
- **统计数据**: 工序使用频次、外协比例、成本效益、质量指标

### 空状态/零数据
- **无工序数据**: 显示"暂无工序数据，请先创建工序"
- **新建路线**: 显示"请添加工序构建工艺路线"
- **无外协工序**: 显示"该工艺路线暂无外协工序"
- **无参数配置**: 显示"请配置产品参数以启用参数化功能"
- **无历史版本**: 显示"暂无历史版本记录"

### API接口

#### 4.4 工序管理接口
- **工序查询**: GET /api/pdm/operations
- **工序创建**: POST /api/pdm/operations
- **工序更新**: PUT /api/pdm/operations/{id}
- **工序删除**: DELETE /api/pdm/operations/{id}
- **工序版本管理**: GET/POST /api/pdm/operations/{id}/versions
- **工序模板**: GET /api/pdm/operations/templates

#### 4.5 工艺路线接口
- **创建工艺路线**: POST /api/pdm/routes
- **更新工艺路线**: PUT /api/pdm/routes/{id}
- **获取路线详情**: GET /api/pdm/routes/{id}
- **参数化路线生成**: POST /api/pdm/routes/generate-parametric
- **路线选择**: POST /api/pdm/routes/select-optimal
- **工艺路线验证**: POST /api/pdm/routes/{id}/validate
- **路线对比**: GET /api/pdm/routes/compare
- **导出路线图**: GET /api/pdm/routes/{id}/export

#### 4.6 外协管理接口
- **设置外协工序**: POST /api/pdm/operations/{id}/outsourcing
- **更新外协配置**: PUT /api/pdm/operations/{id}/outsourcing
- **获取供应商列表**: GET /api/pdm/suppliers/by-capability
- **外协成本基准**: GET /api/pdm/operations/outsourcing/cost-baseline
- **内制外协切换**: PUT /api/pdm/operations/{id}/switch-mode
- **外协数据同步**: POST /api/pdm/outsourcing/sync-to-procurement

#### 4.7 审批流程接口
- **获取审批流程模板**: GET /api/pdm/approval/templates
- **创建审批流程**: POST /api/pdm/approval/processes
- **提交审批申请**: POST /api/pdm/routes/{id}/submit-approval
- **处理审批任务**: POST /api/pdm/approval/tasks/{id}/process
- **获取审批历史**: GET /api/pdm/routes/{id}/approval-history
- **撤回审批申请**: POST /api/pdm/approval/tasks/{id}/withdraw
- **审批任务列表**: GET /api/pdm/approval/tasks
- **审批统计**: GET /api/pdm/approval/statistics

#### 4.8 版本管理接口
- **创建新版本**: POST /api/pdm/routes/{id}/versions
- **获取版本列表**: GET /api/pdm/routes/{id}/versions
- **版本对比**: GET /api/pdm/routes/versions/compare
- **版本回滚**: POST /api/pdm/routes/{id}/versions/{versionId}/rollback
- **发布版本**: POST /api/pdm/routes/{id}/versions/{versionId}/release
- **删除版本**: DELETE /api/pdm/routes/{id}/versions/{versionId}

#### 4.9 列表管理接口
- **路线列表查询**: GET /api/pdm/routes/list
- **批量删除路线**: DELETE /api/pdm/routes/batch
- **批量审批路线**: POST /api/pdm/routes/batch-approval
- **批量导出路线**: POST /api/pdm/routes/batch-export
- **路线搜索**: GET /api/pdm/routes/search
- **路线筛选**: GET /api/pdm/routes/filter

#### 4.10 综合分析接口
- **工序使用统计**: GET /api/pdm/operations/usage-statistics
- **路线成本分析**: GET /api/pdm/routes/{id}/cost-analysis
- **外协比例分析**: GET /api/pdm/outsourcing/ratio-analysis
- **质量表现分析**: GET /api/pdm/routes/quality-analysis

## 5. 异常与边界处理 (Error & Edge Cases)

### 5.1 工序管理异常处理

#### **工序编码重复**
- **提示信息**: "工序编码已存在，请使用其他编码"
- **用户操作**: 编码字段标红，聚焦到编码输入框
- **系统处理**: 提供编码建议，显示相似工序列表

#### **删除被引用的工序**
- **提示信息**: "该工序正在被工艺路线使用，不能直接删除"
- **用户操作**: 显示引用详情，提供"停用工序"选项
- **系统处理**: 列出所有引用该工序的工艺路线

#### **工时配置异常**
- **提示信息**: "工时配置不合理，请检查工时设置"
- **用户操作**: 高亮异常字段，提供合理范围建议
- **系统处理**: 基于历史数据提供工时参考值

#### **版本冲突**
- **提示信息**: "检测到版本冲突，请刷新后重试"
- **用户操作**: 提供刷新按钮和冲突解决指导
- **系统处理**: 显示冲突详情，支持合并或覆盖操作

### 5.2 工艺路线设计异常处理

#### **工序逻辑错误**
- **提示信息**: "检测到工序逻辑错误：存在循环依赖"
- **用户操作**: 高亮错误的连接线，提供修复建议
- **系统处理**: 自动检测循环依赖，提供解决方案

#### **工作中心冲突**
- **提示信息**: "工作中心资源冲突，请调整工序安排"
- **用户操作**: 显示冲突的工序，提供替代工作中心
- **系统处理**: 智能推荐可用的工作中心

#### **并行工序配置错误**
- **提示信息**: "并行工序配置错误：缺少汇聚节点"
- **用户操作**: 高亮错误的并行分支，提供修复工具
- **系统处理**: 自动添加汇聚节点选项

#### **参数化配置错误**
- **提示信息**: "参数化配置错误：公式语法不正确"
- **用户操作**: 高亮错误的公式，提供语法帮助
- **系统处理**: 提供公式验证和语法检查

### 5.3 工艺路线审批异常处理

#### **审批人不存在**
- **提示信息**: "审批人[姓名]不存在或已离职，请重新配置审批流程"
- **用户操作**: 显示审批流程配置界面，引导重新设置审批人
- **系统处理**: 暂停审批流程，通知管理员重新配置

#### **审批权限不足**
- **提示信息**: "您没有权限审批此类型的工艺路线"
- **用户操作**: 显示权限说明，提供申请权限入口
- **系统处理**: 跳转到下一审批节点或通知管理员

#### **审批超时**
- **提示信息**: "审批任务已超时，将自动流转到下一节点"
- **用户操作**: 显示超时原因，提供加急处理选项
- **系统处理**: 自动流转或升级到上级审批

#### **审批流程配置错误**
- **提示信息**: "审批流程配置不完整，请检查审批节点设置"
- **用户操作**: 高亮错误的配置项，提供配置指导
- **系统处理**: 阻止提交审批，引导完善配置

### 5.4 版本管理异常处理

#### **版本号冲突**
- **提示信息**: "版本号[版本号]已存在，请使用其他版本号"
- **用户操作**: 版本号字段标红，自动建议下一个可用版本号
- **系统处理**: 检查版本号唯一性，提供版本号生成规则

#### **版本依赖错误**
- **提示信息**: "该版本被其他版本依赖，无法删除"
- **用户操作**: 显示依赖关系图，提供依赖处理建议
- **系统处理**: 检查版本依赖关系，阻止删除操作

#### **版本对比失败**
- **提示信息**: "版本对比失败：数据格式不兼容"
- **用户操作**: 提供数据修复工具，引导格式转换
- **系统处理**: 尝试自动修复数据格式，提供手动修复选项

#### **路线验证失败**
- **提示信息**: "工艺路线验证失败：缺少必要的工序节点"
- **用户操作**: 显示验证结果，高亮缺失的节点
- **系统处理**: 提供完整性检查报告

### 5.5 外协管理异常处理

#### **供应商能力不匹配**
- **提示信息**: "选择的供应商不具备该工序的加工能力"
- **用户操作**: 高亮不匹配的供应商，提供能力匹配的供应商列表
- **系统处理**: 智能筛选具备相应能力的供应商

#### **外协价格异常**
- **提示信息**: "外协价格异常：超出合理范围"
- **用户操作**: 价格字段标红，显示合理价格范围
- **系统处理**: 基于历史数据提供价格参考

#### **交期冲突**
- **提示信息**: "外协交期与生产计划冲突"
- **用户操作**: 显示冲突的订单，提供交期调整建议
- **系统处理**: 计算最优交期安排

#### **供应商状态异常**
- **提示信息**: "选择的供应商已被禁用或删除"
- **用户操作**: 自动移除异常供应商，提示重新选择
- **系统处理**: 自动检查供应商状态，提供替代方案

#### **成本计算失败**
- **提示信息**: "外协成本计算失败：缺少必要的价格信息"
- **用户操作**: 高亮缺失的价格信息，提供补充入口
- **系统处理**: 列出所有缺失的价格数据

#### **切换模式限制**
- **提示信息**: "该工序正在生产中，不能切换外协模式"
- **用户操作**: 显示正在进行的生产订单，提供计划切换功能
- **系统处理**: 检查工序使用状态，提供切换时间建议

### 5.6 系统级异常处理

#### **权限不足**
- **提示信息**: "您没有权限执行此操作"
- **用户操作**: 显示只读模式，提供权限申请入口
- **系统处理**: 记录权限访问日志

#### **数据同步失败**
- **提示信息**: "数据同步失败，请稍后重试"
- **用户操作**: 提供重试按钮，显示同步状态
- **系统处理**: 自动重试机制，记录失败日志

#### **导出异常**
- **提示信息**: "工艺路线导出失败，请稍后重试"
- **用户操作**: 提供重新导出选项，记录错误日志
- **系统处理**: 检查导出数据完整性

#### **保存失败**
- **提示信息**: "数据保存失败，请检查网络后重试"
- **用户操作**: 保持编辑状态，提供重试按钮
- **系统处理**: 本地缓存编辑数据，防止数据丢失

## 6. 验收标准 (Acceptance Criteria)

### 6.1 工序管理功能验收
- [ ] 支持工序的创建、编辑、查询、删除操作
- [ ] 工序编码全局唯一，支持自定义编码规则
- [ ] 完整的工序标准定义，包含工时、质量、资源要求
- [ ] 支持工序分类管理和层级结构
- [ ] 完善的版本管理和变更控制
- [ ] 支持作业指导书的在线编辑和管理
- [ ] 工序模板库功能正常，支持快速创建常用工序
- [ ] 与工艺参数库(PDM-010)集成正常，支持参数模板应用
- [ ] 与工装夹具库(PDM-011)集成正常，支持工装配置

### 6.2 工艺路线设计功能验收
- [ ] 支持参数化工艺路线的创建、编辑、删除操作
- [ ] 支持可视化的工艺流程设计，拖拽操作流畅
- [ ] 工序配置完整：工序号、名称、工作中心、工艺参数、工装配置
- [ ] 支持基于产品参数自动生成工艺路线
- [ ] 支持一个产品对应多个可选工艺路线
- [ ] 工艺路线选择算法正确，支持多种选择策略
- [ ] 支持并行工序和条件分支的设计
- [ ] 工艺路线逻辑验证正确，防止循环依赖
- [ ] 参数化工时计算准确，支持公式配置
- [ ] 工时统计准确，支持关键路径分析
- [ ] 工艺路线可视化展示清晰，支持参数关联显示
- [ ] 支持多路线对比和差异分析
- [ ] 支持工艺路线的导出和打印功能
- [ ] 工作中心和工装资源冲突检测正常工作

### 6.3 工艺路线审批功能验收
- [ ] 审批流程配置功能完整，支持多级和并行审批
- [ ] 审批权限控制有效，不同角色有不同审批权限
- [ ] 审批任务列表显示正确，支持按优先级排序
- [ ] 审批详情界面信息完整，支持变更对比
- [ ] 审批意见录入和审批结果选择功能正常
- [ ] 审批状态流转正确，状态变更记录完整
- [ ] 审批超时提醒和自动流转功能正常
- [ ] 审批通知及时发送，相关人员能及时收到
- [ ] 审批历史记录完整，支持审批轨迹查询
- [ ] 审批撤回和重新提交功能正常

### 6.4 工艺路线列表管理功能验收
- [ ] 路线列表显示完整，包含所有关键信息
- [ ] 搜索功能正常，支持多字段模糊搜索
- [ ] 筛选功能完整，支持多条件组合筛选
- [ ] 排序功能正常，支持多字段排序
- [ ] 分页功能正常，支持每页条数设置
- [ ] 批量操作功能完整，支持批量删除、审批、导出
- [ ] 路线状态标识清晰，不同状态有不同颜色
- [ ] CRUD操作功能完整，支持创建、查看、编辑、删除
- [ ] 版本管理功能正常，支持版本对比和回滚
- [ ] 路线复制和模板创建功能正常

### 6.5 版本管理功能验收
- [ ] 版本创建和发布功能正常
- [ ] 版本号生成规则正确，支持自定义版本号
- [ ] 版本对比功能完整，差异显示清晰
- [ ] 版本回滚功能正常，支持回滚到任意历史版本
- [ ] 版本依赖关系检查正确，防止误删除
- [ ] 版本状态管理正确，支持多种版本状态
- [ ] 版本变更记录完整，包含变更原因和影响分析
- [ ] 版本权限控制有效，不同角色有不同版本操作权限

### 6.6 外协管理功能验收
- [ ] 用户可以将工序标识为外协或内制
- [ ] 支持完全外协、部分外协、临时外协等类型
- [ ] 外协工序可关联主供应商和备选供应商
- [ ] 支持外协价格配置和历史价格管理
- [ ] 外协交期设置和交期预警功能正常
- [ ] 支持内制和外协模式的灵活切换
- [ ] 外协成本计算准确，支持成本对比分析
- [ ] 供应商能力匹配验证正确
- [ ] 外协工序的质量要求配置完整
- [ ] 外协信息变更记录完整的审计轨迹
- [ ] 外协工序数据可实时同步到采购系统(PMS-007~011)
- [ ] 与采购系统的外协订单管理模块集成正常
- [ ] 外协技术要求和质量标准传递准确
- [ ] 外协成本基准数据为采购提供参考

### 6.7 综合功能验收
- [ ] 统一的用户界面，功能模块间切换流畅
- [ ] 工序库与工艺路线设计的无缝集成
- [ ] 工艺路线与外协管理的数据一致性
- [ ] 支持端到端的工艺流程设计和管理
- [ ] 提供工序使用统计和分析功能
- [ ] 支持多维度的成本效益分析
- [ ] 工艺路线版本管理正确
- [ ] 所有操作记录到审计日志
- [ ] 数据准确性≥99%，系统可用性≥99.5%

### 6.8 性能和用户体验验收
- [ ] 工序查询响应时间<1秒
- [ ] 参数化路线生成响应时间<3秒
- [ ] 路线选择算法响应时间<1秒
- [ ] 工艺路线加载响应时间<2秒
- [ ] 页面响应时间<2秒，操作流畅
- [ ] 界面支持响应式设计，移动端可正常使用
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持键盘导航和快捷键操作
- [ ] 支持无障碍访问功能

### 6.9 集成和兼容性验收
- [ ] 与PDM系统其他模块集成正常
- [ ] 与MES系统工作中心管理集成正常
- [ ] 与采购系统外协订单管理集成正常
- [ ] 支持数据导入导出功能
- [ ] API接口功能完整，响应格式标准
- [ ] 支持多浏览器兼容性
- [ ] 数据备份和恢复功能正常

---

**模块状态**: 整合完成 ✅
**功能覆盖**: 工序管理 ✅ 工艺路线设计 ✅ 路线列表管理 ✅ 审批流程 ✅ 版本管理 ✅ 外协管理 ✅
**集成状态**: PDM-010工艺参数库 ✅ PDM-011工装夹具管理 ✅ 采购系统 ✅
**设计原则**: 高内聚低耦合 ✅ 用户体验一致性 ✅ 数据完整性 ✅
**新增功能**: 完整的列表+CRUD界面 ✅ 多级审批流程 ✅ 版本对比和回滚 ✅
