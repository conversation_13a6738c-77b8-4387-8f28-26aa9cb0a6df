# 功能模块规格说明书：BOM版本管理模块

- **模块ID**: PDM-003
- **所属子系统**: 工艺管理子系统(PDM)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 工艺工程师, **I want to** 管理BOM的多个版本, **so that** 追踪产品设计变更历史。
- **As a** 工艺工程师, **I want to** 比较不同版本的BOM差异, **so that** 了解产品设计的演进过程。
- **As a** 工艺工程师, **I want to** 控制BOM版本状态, **so that** 确保生产使用正确的BOM版本。
- **As a** 质量管理员, **I want to** 审批BOM版本变更, **so that** 确保产品设计变更的合规性。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有BOM版本管理权限
- BOM模板已创建
- 版本变更审批流程已配置

### 核心流程

#### 2.1 BOM版本创建流程
1. 工艺工程师在现有BOM基础上进行修改
2. 点击"创建新版本"按钮
3. 系统自动生成新版本号
4. 填写版本变更说明
5. 选择版本类型（主版本/次版本/修订版本）
6. 提交版本创建申请
7. 新版本状态设为"草稿"

#### 2.2 版本审批流程
1. 工艺工程师提交版本审批申请
2. 质量管理员接收审批通知
3. 审批人员查看版本变更内容
4. 对比新旧版本差异
5. 审批通过后版本状态变为"激活"
6. 原激活版本自动变为"归档"状态

#### 2.3 版本比较流程
1. 用户选择需要比较的两个版本
2. 系统分析版本间的差异
3. 以并排对比方式显示差异
4. 高亮显示新增、删除、修改的内容
5. 生成版本差异报告

### 后置条件
- 版本状态变更立即生效
- 被生产订单引用的版本自动锁定
- 版本变更记录完整的审计轨迹

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：BOM版本管理页
### 页面目标：提供直观的BOM版本控制和差异分析界面

### 信息架构：
- **左侧区域**：包含 BOM列表, BOM搜索, 筛选器
- **中间区域**：包含 版本列表, 版本状态, 操作按钮组
- **右侧区域**：包含 版本详情, 变更说明, 审批信息
- **底部区域**：包含 版本对比工具, 差异展示区域

### 交互逻辑与状态：

#### **版本列表**
- **默认状态：** 按时间倒序显示版本列表
- **版本卡片：** 显示版本号、状态、创建时间、创建人
- **状态标识：**
  - 草稿：橙色标签(#FAAD14)，可编辑
  - 激活：绿色标签(#52C41A)，当前使用版本
  - 归档：灰色标签(#8C8C8C)，历史版本
  - 锁定：红色标签(#F5222D)，被订单引用
- **选中状态：** 蓝色边框，右侧显示详情

#### **创建新版本按钮**
- **默认状态：** 蓝色背景(#1890FF)，白色文字"创建新版本"
- **悬停状态：** 背景色加深至#096DD9
- **禁用状态：** 当前版本为草稿状态时禁用
- **交互行为：** 点击打开版本创建对话框

#### **版本状态切换**
- **草稿状态：** 显示"提交审批"按钮
- **审批中状态：** 显示"审批中"标识，不可操作
- **激活状态：** 显示"当前版本"标识，绿色背景
- **归档状态：** 显示"已归档"标识，灰色背景

#### **版本对比工具**
- **版本选择器：** 两个下拉选择框，选择对比版本
- **对比按钮：** 蓝色背景，白色文字"开始对比"
- **对比结果：** 并排显示两个版本的差异
- **差异高亮：**
  - 新增内容：绿色背景(#F6FFED)
  - 删除内容：红色背景(#FFF2F0)
  - 修改内容：黄色背景(#FFFBE6)

#### **审批操作区域**
- **审批按钮组：** 通过、驳回、暂缓按钮
- **审批意见框：** 文本输入框，必填项
- **审批历史：** 显示历史审批记录
- **权限控制：** 非审批人员不显示审批按钮

#### **版本详情面板**
- **基本信息：** 版本号、创建时间、创建人、状态
- **变更说明：** 显示版本变更的详细说明
- **BOM内容：** 显示该版本的完整BOM结构
- **引用情况：** 显示被哪些订单引用

### 数据校验规则：

#### **版本变更说明**
- **校验规则：** 必填，最少10个字符
- **错误提示文案：** "请填写版本变更说明，至少10个字符"

#### **审批意见**
- **校验规则：** 审批操作时必填
- **错误提示文案：** "请填写审批意见"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **版本号 (version_number)**: String, 自动生成, 如"V1.0"、"V1.1"
- **版本类型 (version_type)**: Enum, 必填, [主版本/次版本/修订版本]
- **变更说明 (change_description)**: String, 必填, 最少10字符
- **版本状态 (status)**: Enum, 系统控制, [草稿/审批中/激活/归档/锁定]
- **审批信息**:
  - **审批人 (approver_id)**: String, 审批时必填
  - **审批意见 (approval_comment)**: String, 审批时必填
  - **审批时间 (approval_time)**: DateTime, 系统自动

### 展示数据
- **版本基本信息**: 版本号、类型、状态、创建时间、创建人
- **BOM内容快照**: 该版本的完整BOM结构和参数
- **变更历史**: 版本间的差异对比结果
- **审批记录**: 审批人、审批时间、审批意见、审批结果
- **引用统计**: 被订单引用的次数和详情

### 空状态/零数据
- **无版本历史**: 显示"暂无版本历史，当前为初始版本"
- **对比无差异**: 显示"两个版本内容完全相同"
- **无审批记录**: 显示"该版本暂无审批记录"

### API接口
- **获取版本列表**: GET /api/boms/{bom_id}/versions
- **创建新版本**: POST /api/boms/{bom_id}/versions
- **版本对比**: GET /api/boms/versions/compare
- **版本审批**: POST /api/boms/versions/{version_id}/approve
- **版本状态变更**: PUT /api/boms/versions/{version_id}/status

## 5. 异常与边界处理 (Error & Edge Cases)

### **删除被引用的版本**
- **提示信息**: "该版本正在被生产订单引用，不能删除"
- **用户操作**: 显示引用详情，版本自动锁定

### **激活版本冲突**
- **提示信息**: "同一BOM只能有一个激活版本"
- **用户操作**: 系统自动将原激活版本设为归档

### **版本审批权限不足**
- **提示信息**: "您没有权限审批此版本"
- **用户操作**: 审批按钮禁用，显示当前审批人

### **版本对比数据异常**
- **提示信息**: "版本数据异常，无法进行对比"
- **用户操作**: 提供重新加载按钮，联系管理员

### **审批流程异常**
- **提示信息**: "审批流程配置异常，请联系管理员"
- **用户操作**: 暂停审批操作，记录异常日志

### **版本创建失败**
- **提示信息**: "版本创建失败，请稍后重试"
- **用户操作**: 保持当前编辑状态，提供重试按钮

## 6. 验收标准 (Acceptance Criteria)

- [ ] 用户可以基于现有BOM创建新版本
- [ ] 每次重大修改可生成新版本，版本号自动递增
- [ ] 同一BOM只有一个版本处于激活状态
- [ ] 被生产订单引用的版本自动锁定，不可修改
- [ ] 支持版本间差异对比，差异内容高亮显示
- [ ] 版本变更审批流程正常工作
- [ ] 版本状态管理正确：草稿、激活、归档、锁定
- [ ] 版本变更记录完整的审计轨迹
- [ ] 版本对比结果准确，支持并排显示
- [ ] 审批权限控制正确，非审批人员不能审批
- [ ] 版本历史查询功能正常工作
- [ ] 所有操作记录到审计日志
- [ ] 界面支持响应式设计，移动端可正常使用
- [ ] 版本对比响应时间小于3秒
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持键盘导航和无障碍访问
