# 功能模块规格说明书：物料主数据管理模块

- **模块ID**: PDM-001
- **所属子系统**: 工艺管理子系统(PDM)
- **最后更新**: 2025-07-31

## 1. 用户故事 (User Stories)

- **As a** 工艺工程师, **I want to** 维护统一的物料主数据库, **so that** 为BOM设计提供标准化的物料信息。
- **As a** 工艺工程师, **I want to** 对物料进行分类管理, **so that** 快速查找和选择所需物料。
- **As a** 工艺工程师, **I want to** 管理玻璃类物料的特殊属性, **so that** 支持玻璃深加工行业的专业需求。
- **As a** 系统管理员, **I want to** 控制物料编码规则, **so that** 确保物料编码的规范性和唯一性。
- **As a** 产品经理, **I want to** 建立专门的产品分类体系, **so that** 对成品和半成品进行精细化分类管理。
- **As a** 销售人员, **I want to** 通过产品分类快速查找产品, **so that** 提高产品推荐和报价效率。
- **As a** 市场分析师, **I want to** 按产品分类进行销售分析, **so that** 获得更精准的市场洞察。
- **As a** 工艺工程师, **I want to** 为物料配置变体管理, **so that** 高效管理同一基础物料的不同规格变体。
- **As a** 采购员, **I want to** 按具体变体规格进行采购, **so that** 确保采购的物料规格准确无误。
- **As a** 库存管理员, **I want to** 独立管理每个变体的库存, **so that** 精确掌握各规格物料的库存情况。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有物料管理权限
- 物料分类体系已建立
- 物料编码规则已配置

### 核心流程

#### 2.1 物料创建流程
1. 工艺工程师点击"新增物料"按钮
2. 选择物料类型（原材料/半成品/成品/辅料）
3. 系统根据类型自动生成物料编码
4. 填写物料基本信息（名称、规格、单位等）
5. 如为玻璃类物料，填写特殊属性（长、宽、厚度、颜色、品级）
6. 选择物料分类和供应商信息
7. 保存物料信息并生成审计日志

#### 2.2 物料分类管理流程
1. 系统管理员进入分类管理界面
2. 创建或编辑物料分类节点
3. 设置分类层级关系
4. 配置分类权限控制
5. 保存分类结构并同步到物料选择器

#### 2.3 产品分类管理流程
1. 产品经理进入产品分类管理界面
2. 创建产品分类体系（独立于物料分类）
3. 为产品分类定义特有属性模板
4. 设置产品分类的权限和可见性
5. 配置分类关联规则和继承关系
6. 建立产品分类与物料分类的映射关系
7. 保存产品分类配置并生效

#### 2.4 产品属性管理流程
1. 选择目标产品分类
2. 定义该分类下产品的特有属性
3. 设置属性的数据类型和取值范围
4. 配置属性的必填性和默认值
5. 建立属性间的依赖关系
6. 验证属性配置的合理性
7. 应用属性模板到该分类下的所有产品

#### 2.5 物料查询和筛选流程
1. 用户进入物料管理页面
2. 通过分类导航或搜索框查找物料
3. 应用筛选条件（类型、状态、供应商等）
4. 查看物料详细信息
5. 执行编辑、禁用等操作

#### 2.6 产品分类统计分析流程
1. 选择分析时间范围和维度
2. 按产品分类汇总销售数据
3. 生成分类销售排行榜
4. 分析各分类的库存周转情况
5. 识别热销和滞销产品分类
6. 生成产品分类分析报告
7. 为产品策略提供数据支持

#### 2.7 物料变体配置流程
1. 工艺工程师进入物料分类管理界面
2. 选择需要启用变体管理的物料分类
3. 配置变体维度（如长度、宽度、厚度等）
4. 设置变体维度的数据类型和单位
5. 定义变体编码规则和命名规范
6. 保存变体配置并应用到该分类

#### 2.8 物料变体创建流程
1. 创建基础物料（变体主物料）
2. 系统检测该分类是否启用变体管理
3. 如启用变体，显示"创建变体"功能入口
4. 选择变体维度值（如2440×3660mm）
5. 系统自动生成变体编码和名称
6. 填写变体特有属性和参数
7. 保存变体并建立与基础物料的关联
8. 支持批量创建多个变体规格

#### 2.9 变体管理和维护流程
1. 查看基础物料的所有变体列表
2. 编辑变体的属性和参数
3. 启用或停用特定变体
4. 管理变体间的替代关系
5. 监控变体的使用情况和库存状态
6. 定期清理无用的变体规格

### 后置条件
- 物料信息更新立即生效
- 相关BOM和工艺路线自动同步
- 所有操作记录到审计日志

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：物料主数据管理页
### 页面目标：提供高效的物料信息管理和查询界面

### 信息架构：
- **左侧区域**：包含 物料分类树, 产品分类树, 分类搜索, 新增分类按钮, 变体配置入口
- **中间区域**：包含 物料列表, 变体列表, 搜索筛选工具栏, 批量操作工具, 分类切换标签
- **右侧区域**：包含 物料详情面板, 变体管理面板, 编辑表单, 关联信息展示, 产品分类统计

### 交互逻辑与状态：

#### **分类切换标签**
- **默认状态：** 显示"物料分类"和"产品分类"两个标签
- **选中状态：** 蓝色背景(#1890FF)，白色文字
- **未选中状态：** 白色背景，灰色文字
- **交互行为：** 点击切换分类视图，左侧树结构相应切换

#### **物料分类树**
- **默认状态：** 展示根分类，子分类折叠显示
- **展开状态：** 点击分类前的展开图标，显示子分类
- **选中状态：** 蓝色背景(#E6F7FF)，蓝色左边框
- **悬停状态：** 浅灰背景(#FAFAFA)，显示操作图标
- **交互行为：** 点击选中分类，中间区域显示对应物料列表

#### **产品分类树**
- **默认状态：** 展示产品分类根节点，专注于成品和半成品
- **分类标识：** 不同图标区分产品分类和物料分类
- **选中状态：** 绿色背景(#F6FFED)，绿色左边框
- **悬停状态：** 浅绿背景(#F6FFED)，显示产品分类操作图标
- **交互行为：** 点击选中产品分类，显示该分类下的产品列表
- **统计信息：** 显示每个分类下的产品数量和销售统计

#### **新增物料按钮**
- **默认状态：** 蓝色背景(#1890FF)，白色文字"新增物料"
- **悬停状态：** 背景色加深至#096DD9
- **权限不足状态：** 灰色背景，禁用状态
- **交互行为：** 点击打开新增物料对话框

#### **物料搜索框**
- **默认状态：** 占位符"搜索物料编码、名称或规格"，搜索图标
- **聚焦状态：** 蓝色边框，显示搜索历史下拉
- **搜索中状态：** 显示加载图标，实时搜索
- **交互行为：** 支持模糊搜索，Enter键确认搜索

#### **物料类型筛选器**
- **默认状态：** 显示"全部类型"，下拉箭头
- **展开状态：** 显示类型选项列表（全部/原材料/半成品/成品/辅料）
- **选中状态：** 显示选中的类型，蓝色文字
- **交互行为：** 点击切换筛选条件，自动刷新列表

#### **物料列表表格**
- **表头样式：** 灰色背景(#FAFAFA)，加粗文字，支持排序
- **行样式：** 奇偶行背景色区分，悬停高亮
- **状态列显示：**
  - 启用：绿色标签(#52C41A)
  - 禁用：灰色标签(#8C8C8C)
- **操作列：** 编辑、查看、禁用/启用、删除按钮

#### **物料详情面板**
- **默认状态：** 右侧滑出面板，显示物料详细信息
- **编辑状态：** 表单字段可编辑，显示保存/取消按钮
- **玻璃类物料：** 显示特殊属性区域（尺寸、颜色、品级）
- **关联信息：** 显示BOM使用情况、供应商信息
- **产品分类信息：** 当物料为成品/半成品时，显示产品分类归属

#### **产品分类配置面板**
- **分类属性配置：**
  - **属性名称：** 输入框，定义产品分类特有属性
  - **属性类型：** 下拉选择，文本/数字/日期/枚举等
  - **是否必填：** 复选框，设置属性必填性
  - **默认值：** 输入框，设置属性默认值
- **分类权限控制：**
  - **可见角色：** 多选框，设置分类可见的用户角色
  - **编辑权限：** 多选框，设置可编辑分类的用户角色
  - **审批流程：** 下拉选择，设置分类变更审批流程
- **关联规则配置：**
  - **物料分类映射：** 设置产品分类与物料分类的对应关系
  - **继承规则：** 配置子分类从父分类继承的属性
  - **约束条件：** 设置分类使用的业务约束条件

#### **产品分类统计面板**
- **销售统计：**
  - **销售额排行：** 柱状图显示各产品分类销售额
  - **销量排行：** 饼图显示各产品分类销量占比
  - **增长趋势：** 折线图显示分类销售增长趋势
- **库存统计：**
  - **库存分布：** 显示各产品分类的库存数量
  - **周转率：** 显示各分类的库存周转情况
  - **预警信息：** 显示库存不足或积压的产品分类
- **时间筛选：**
  - **时间范围：** 日期选择器，选择统计时间范围
  - **对比分析：** 支持同比、环比分析
  - **导出功能：** 支持统计数据的导出

#### **变体配置面板**
- **变体启用设置：**
  - **启用变体管理：** 开关组件，控制分类是否启用变体
  - **变体维度配置：** 表格形式配置变体维度
  - **维度名称：** 输入框，如"长度"、"宽度"、"厚度"
  - **数据类型：** 下拉选择，数字/文本/枚举
  - **单位设置：** 下拉选择，毫米/米/厘米等
  - **是否必填：** 复选框，设置维度必填性
- **编码规则配置：**
  - **变体编码模板：** 输入框，定义变体编码生成规则
  - **分隔符设置：** 下拉选择，"-"、"_"、"."等
  - **预览示例：** 实时显示编码生成效果

#### **变体管理面板**
- **变体列表视图：**
  - **基础物料信息：** 显示变体主物料的基本信息
  - **变体表格：** 表格显示所有变体及其维度值
  - **变体状态：** 显示启用/停用状态，支持批量操作
  - **库存信息：** 显示各变体的当前库存数量
- **变体创建向导：**
  - **维度值输入：** 根据配置的维度动态生成输入字段
  - **批量创建：** 支持Excel导入或表格批量输入
  - **编码预览：** 实时显示生成的变体编码
  - **验证检查：** 检查变体规格是否重复
- **变体关系图：**
  - **树状结构：** 显示基础物料与变体的层级关系
  - **维度筛选：** 按维度值筛选显示特定变体
  - **快速操作：** 支持拖拽排序和快速编辑

#### **物料编码生成器**
- **自动生成：** 根据物料类型和编码规则自动生成
- **手动输入：** 支持手动修改，实时校验唯一性
- **格式验证：** 不符合规则时红色边框提示
- **交互行为：** 失焦时验证编码唯一性

### 数据校验规则：

#### **物料编码**
- **校验规则：** 必填，符合编码规则，全局唯一
- **错误提示文案：** "物料编码不能为空" / "物料编码已存在"

#### **物料名称**
- **校验规则：** 必填，2-100位字符
- **错误提示文案：** "物料名称不能为空"

#### **玻璃尺寸属性**
- **校验规则：** 数值类型，大于0
- **错误提示文案：** "请输入有效的尺寸数值"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **物料编码 (material_code)**: String, 必填, 符合编码规则
- **物料名称 (material_name)**: String, 必填, 2-100位字符
- **物料类型 (material_type)**: Enum, 必填, [原材料/半成品/成品/辅料]
- **规格型号 (specification)**: String, 可选, 最大200字符
- **计量单位 (unit)**: String, 必填, 如"片"、"米"、"公斤"
- **物料分类 (category_id)**: String, 必填, 分类ID
- **玻璃特殊属性**:
  - **长度 (length)**: Number, 可选, 单位毫米
  - **宽度 (width)**: Number, 可选, 单位毫米
  - **厚度 (thickness)**: Number, 可选, 单位毫米
  - **颜色 (color)**: String, 可选
  - **品级 (grade)**: Enum, 可选, [A级/B级/C级]
- **产品分类信息**:
  - **产品分类ID (product_category_id)**: String, 可选, 仅成品/半成品
  - **产品分类路径 (product_category_path)**: String, 可选, 分类层级路径
  - **产品属性 (product_attributes)**: Object, 可选, 动态产品属性
  - **分类标签 (category_tags)**: Array, 可选, 产品分类标签
- **变体管理信息**:
  - **是否变体主物料 (is_variant_master)**: Boolean, 必填, 标识是否为变体主物料
  - **变体主物料ID (variant_master_id)**: String, 可选, 关联的变体主物料ID
  - **变体维度值 (variant_values)**: Object, 可选, 变体的具体维度值
  - **变体编码后缀 (variant_suffix)**: String, 可选, 变体编码的后缀部分

### 展示数据
- **物料基本信息**: 编码、名称、类型、规格、单位、状态
- **分类信息**: 所属分类路径、分类名称
- **产品分类信息**: 产品分类路径、分类属性、分类统计
- **变体信息**: 变体主物料、变体列表、变体维度值、变体状态
- **供应商信息**: 主供应商、备选供应商列表
- **使用统计**: BOM使用次数、最近使用时间
- **销售统计**: 产品销售数据、市场表现（仅产品分类）
- **库存统计**: 变体库存分布、库存预警（仅变体物料）
- **审计信息**: 创建时间、创建人、最后修改时间、修改人

### 空状态/零数据
- **无物料数据**: 显示"暂无物料数据，请先添加物料"
- **搜索无结果**: 显示"未找到匹配的物料，请尝试其他关键词"
- **分类无物料**: 显示"该分类下暂无物料"

### API接口
- **获取物料列表**: GET /api/materials
- **创建物料**: POST /api/materials
- **更新物料**: PUT /api/materials/{id}
- **删除物料**: DELETE /api/materials/{id}
- **获取分类树**: GET /api/material-categories/tree
- **物料编码生成**: POST /api/materials/generate-code
- **产品分类管理**: GET/POST/PUT/DELETE /api/product-categories
- **产品分类统计**: GET /api/product-categories/statistics
- **产品属性配置**: GET/POST/PUT /api/product-categories/{id}/attributes
- **分类关联规则**: GET/POST/PUT /api/product-categories/rules
- **变体配置管理**: GET/POST/PUT /api/material-categories/{id}/variant-config
- **变体创建**: POST /api/materials/{id}/variants
- **变体列表**: GET /api/materials/{id}/variants
- **变体更新**: PUT /api/material-variants/{id}
- **变体删除**: DELETE /api/material-variants/{id}
- **变体编码生成**: POST /api/materials/{id}/variants/generate-code

## 5. 异常与边界处理 (Error & Edge Cases)

### **物料编码重复**
- **提示信息**: "物料编码已存在，请使用其他编码"
- **用户操作**: 编码字段标红，聚焦到编码输入框

### **删除被BOM引用的物料**
- **提示信息**: "该物料正在被BOM使用，不能直接删除"
- **用户操作**: 显示引用详情，提供"禁用物料"选项

### **分类删除检查**
- **提示信息**: "该分类下还有物料，请先转移后再删除"
- **用户操作**: 显示子物料列表，提供批量转移功能

### **玻璃尺寸数据异常**
- **提示信息**: "玻璃尺寸数据不合理，请检查输入"
- **用户操作**: 异常字段标红，提供合理范围提示

### **网络异常导致保存失败**
- **提示信息**: "保存失败，请检查网络后重试"
- **用户操作**: 保持编辑状态，提供重试按钮

### **批量操作部分失败**
- **提示信息**: "批量操作完成，X项成功，Y项失败"
- **用户操作**: 显示失败详情列表，支持重试失败项

### **变体规格重复**
- **提示信息**: "该变体规格已存在，请检查维度值设置"
- **用户操作**: 高亮重复的维度值，提供修改建议

### **删除有变体的基础物料**
- **提示信息**: "该物料存在变体，请先处理所有变体后再删除"
- **用户操作**: 显示变体列表，提供批量删除或转移选项

### **变体维度配置冲突**
- **提示信息**: "变体维度配置与现有变体冲突，请重新配置"
- **用户操作**: 显示冲突详情，提供配置调整建议

### **变体编码生成失败**
- **提示信息**: "变体编码生成失败，请检查编码规则配置"
- **用户操作**: 跳转到编码规则配置页面，提供修复指导

## 6. 验收标准 (Acceptance Criteria)

- [ ] 用户可以创建、编辑、删除、查询物料信息
- [ ] 物料编码全局唯一性校验正确执行
- [ ] 支持原材料、半成品、成品、辅料等全类型物料管理
- [ ] 玻璃类物料特殊属性（长、宽、厚度、颜色、品级）正确管理
- [ ] 物料分类树状结构正常工作，支持多级分类
- [ ] 产品分类体系独立于物料分类，专注成品和半成品管理
- [ ] 产品分类支持动态属性配置和权限控制
- [ ] 产品分类与物料分类的映射关系正确建立
- [ ] 产品分类统计分析功能正常，支持销售和库存分析
- [ ] 物料搜索功能支持编码、名称、规格模糊查找
- [ ] 筛选功能支持类型、状态、分类多维度筛选
- [ ] 被BOM引用的物料限制删除，提供合理的替代方案
- [ ] 物料编码自动生成规则正确执行
- [ ] 分类切换功能正常，物料分类和产品分类视图切换流畅
- [ ] 产品分类权限控制有效，不同角色看到相应的分类内容
- [ ] 变体管理功能完整，支持变体配置、创建、编辑、删除
- [ ] 变体编码自动生成规则正确，编码全局唯一
- [ ] 变体维度配置灵活，支持多种数据类型和单位
- [ ] 变体列表显示正确，支持按维度筛选和排序
- [ ] 变体与基础物料的关联关系正确建立和维护
- [ ] 变体库存独立管理，库存数据准确
- [ ] 变体批量创建功能正常，支持Excel导入
- [ ] 所有操作记录到审计日志
- [ ] 界面支持响应式设计，移动端可正常使用
- [ ] 物料查询响应时间小于500ms（万级数据）
- [ ] 变体查询响应时间<1秒（千级变体）
- [ ] 产品分类统计查询响应时间小于2秒
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持键盘导航和无障碍访问
