# 功能模块规格说明书：技术文档管理模块

- **模块ID**: PDM-008
- **所属子系统**: 工艺管理子系统(PDM)
- **最后更新**: 2025-08-01
- **功能增强说明**: 新增CAD文件深度集成、自动信息提取、智能文档关联等高级功能
- **业务价值**: 实现设计数据与工艺数据的无缝集成，大幅减少手工录入，提升数据准确性

## 1. 用户故事 (User Stories)

- **As a** 工艺工程师, **I want to** 上传CAD图纸并自动提取关键信息, **so that** 减少手工录入工作并提升数据准确性。
- **As a** 工艺工程师, **I want to** 将技术文档智能关联到物料和BOM, **so that** 生产人员能方便查阅相关技术资料。
- **As a** 产品设计师, **I want to** 管理CAD文件的版本和变更, **so that** 确保工艺数据与最新设计保持同步。
- **As a** 生产技术员, **I want to** 在线预览技术图纸和工艺文档, **so that** 快速获取生产所需的技术信息。
- **As a** 质量工程师, **I want to** 查看产品的技术标准和检验要求, **so that** 制定相应的质量控制方案。
- **As a** 系统管理员, **I want to** 配置CAD文件解析规则, **so that** 系统能准确提取不同类型图纸的关键信息。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有文档管理权限
- 物料和BOM数据已建立
- 文档分类体系已配置
- CAD解析引擎已配置
- 工艺参数库已建立（参考PDM-010工艺参数库管理模块）

### 核心流程

#### 2.1 CAD文件智能上传流程
1. 工艺工程师点击"上传CAD文件"按钮
2. 选择CAD文件（支持DWG、DXF、STEP、IGS等格式）
3. 系统自动识别文件类型和版本
4. CAD解析引擎自动提取关键信息：
   - 产品尺寸（长、宽、高、厚度）
   - 材料信息和规格
   - 加工特征（孔位、切割线、倒角等）
   - 技术要求和公差
5. 系统智能推荐关联对象（物料/BOM/工序）
6. 工艺工程师确认或调整提取信息
7. 自动生成工艺参数建议
8. 保存文档信息并建立智能关联

#### 2.2 技术文档上传和分类流程
1. 工艺工程师点击"上传技术文档"按钮
2. 选择本地文件（支持PDF、Word、Excel、图片等格式）
3. 设置文档基本信息（名称、描述、分类）
4. 选择文档关联对象（物料/BOM/工序）
5. 设置文档访问权限和安全级别
6. 系统自动生成文档预览和缩略图
7. OCR识别文档中的关键信息
8. 保存文档信息并建立关联

#### 2.3 文档版本管理和同步流程
1. 检测到CAD文件或技术文档变更
2. 上传新版本文档文件
3. 系统自动对比版本差异
4. 分析变更对工艺数据的影响
5. 填写版本变更说明和影响评估
6. 系统自动生成新版本号
7. 原版本自动归档
8. 更新相关工艺路线和参数配置
9. 通知相关人员文档变更

#### 2.4 智能文档查看和应用流程
1. 用户通过分类、搜索或关联对象找到文档
2. 检查用户访问权限
3. 在线预览文档内容（支持CAD文件3D预览）
4. 显示文档提取的关键信息
5. 提供相关工艺参数和工装信息
6. 记录文档访问日志
7. 支持文档下载和打印（权限允许时）
8. 统计文档使用情况和效果分析

### 后置条件
- 文档信息完整准确
- 文档关联关系明确
- 文档访问权限正确控制

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：智能技术文档管理页
### 页面目标：提供CAD集成的智能文档管理和信息提取功能

### 信息架构：
- **左侧区域**：包含 文档分类树, CAD文件库, 文档类型筛选, 关联对象筛选
- **中间区域**：包含 文档列表, CAD预览器, 信息提取面板, 智能上传界面
- **右侧区域**：包含 文档详情, 提取信息, 关联配置, 版本历史, 影响分析

### 交互逻辑与状态：

#### **智能文档上传区域**
- **CAD文件识别：** 自动识别CAD文件类型和版本
- **拖拽上传：** 支持拖拽文件到上传区域
- **点击上传：** 点击上传按钮选择文件
- **实时解析：** 上传过程中实时解析CAD文件
- **进度显示：** 上传和解析过程显示进度条
- **格式检查：** 自动检查文件格式、大小和完整性
- **批量上传：** 支持同时上传多个文件
- **解析预览：** 显示解析出的关键信息预览

#### **文档列表**
- **列表视图：** 表格形式显示文档信息
- **卡片视图：** 卡片形式显示文档缩略图
- **排序功能：** 支持按名称、时间、大小排序
- **筛选功能：** 按类型、状态、关联对象筛选
- **搜索功能：** 支持文档名称和内容搜索

#### **文档状态标识**
- **草稿状态：** 橙色标签(#FAAD14)，显示"草稿"
- **发布状态：** 绿色标签(#52C41A)，显示"已发布"
- **归档状态：** 灰色标签(#8C8C8C)，显示"已归档"
- **锁定状态：** 红色标签(#F5222D)，显示"已锁定"

#### **智能CAD预览器**
- **3D模型预览：** 支持CAD文件的3D模型显示和旋转
- **2D图纸预览：** 支持DWG/DXF等2D图纸预览
- **图层控制：** 支持图层的显示/隐藏控制
- **尺寸标注：** 自动识别和显示关键尺寸
- **特征高亮：** 高亮显示加工特征（孔、槽、倒角等）
- **PDF预览：** 内嵌PDF查看器，支持缩放和翻页
- **图片预览：** 支持常见图片格式的预览
- **Office预览：** 支持Word/Excel/PPT文件预览
- **全屏模式：** 支持全屏查看文档
- **测量工具：** 支持在预览中进行尺寸测量

#### **信息提取面板**
- **尺寸信息：** 自动提取的产品尺寸（长×宽×高×厚）
- **材料信息：** 识别的材料类型和规格
- **加工特征：** 提取的孔位、切割线、倒角等特征
- **技术要求：** 识别的公差、表面处理等要求
- **工艺建议：** 基于提取信息生成的工艺参数建议
- **确认编辑：** 允许用户确认或修改提取的信息
- **应用到工艺：** 一键应用提取信息到工艺参数库

#### **智能文档关联工具**
- **物料关联：** 基于提取信息智能推荐关联物料
- **BOM关联：** 选择关联的BOM版本
- **工序关联：** 根据加工特征推荐适用工序
- **工艺参数关联：** 自动关联相关工艺参数模板
- **工装推荐：** 基于产品特征推荐合适的工装夹具
- **工序关联：** 选择关联的工艺工序
- **批量关联：** 支持批量建立关联关系

#### **权限控制面板**
- **访问权限：** 设置文档的访问权限级别
- **下载权限：** 控制文档的下载权限
- **编辑权限：** 控制文档的编辑权限
- **权限继承：** 支持从关联对象继承权限

#### **版本管理工具**
- **版本列表：** 显示文档的所有版本
- **版本对比：** 支持版本间的差异对比
- **版本回退：** 支持回退到历史版本
- **版本分支：** 支持创建文档分支版本

### 数据校验规则：

#### **文档名称**
- **校验规则：** 必填，2-100位字符，不能包含特殊字符
- **错误提示文案：** "文档名称格式不正确"

#### **文件大小**
- **校验规则：** 单个文件不超过100MB
- **错误提示文案：** "文件大小超出限制（100MB）"

#### **文件格式**
- **校验规则：** 支持的格式列表验证
- **错误提示文案：** "不支持的文件格式"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **文档基本信息**:
  - **文档名称 (document_name)**: String, 必填, 2-100位字符
  - **文档描述 (description)**: String, 可选, 最大500字符
  - **文档分类 (category_id)**: String, 必填, 分类ID
  - **文档类型 (document_type)**: Enum, 必填, [CAD图纸/技术文档/工艺文档]
- **文件信息**:
  - **文件名 (file_name)**: String, 必填, 原始文件名
  - **文件大小 (file_size)**: Number, 系统自动, 单位字节
  - **文件格式 (file_format)**: String, 系统自动, 如"PDF"、"DWG"、"DXF"
  - **CAD版本 (cad_version)**: String, 可选, CAD软件版本信息
- **提取信息**:
  - **产品尺寸 (dimensions)**: JSON, 自动提取, {length, width, height, thickness}
  - **材料信息 (material_info)**: JSON, 自动提取, {material_type, specification}
  - **加工特征 (features)**: Array, 自动提取, 孔位、切割线、倒角等
  - **技术要求 (technical_requirements)**: JSON, 自动提取, 公差、表面处理等
- **关联信息**:
  - **关联类型 (relation_type)**: Enum, [物料/BOM/工序/工艺参数]
  - **关联对象ID (relation_object_id)**: String, 关联对象的ID
  - **智能推荐 (smart_recommendations)**: Array, 系统推荐的关联对象

### 展示数据
- **文档基本信息**: 名称、描述、分类、状态、大小、文档类型
- **提取信息展示**: 产品尺寸、材料信息、加工特征、技术要求
- **版本信息**: 版本号、创建时间、创建人、变更说明、影响分析
- **关联信息**: 关联的物料、BOM、工序、工艺参数列表
- **智能推荐**: 系统推荐的关联对象和工艺参数
- **访问统计**: 查看次数、下载次数、最近访问时间
- **权限信息**: 访问权限、下载权限、编辑权限

### 空状态/零数据
- **无文档数据**: 显示"暂无技术文档，请先上传文档"
- **CAD解析失败**: 显示"CAD文件解析失败，请检查文件格式"
- **无提取信息**: 显示"未能提取到关键信息，请手工配置"
- **搜索无结果**: 显示"未找到匹配的文档"
- **无关联对象**: 显示"该文档暂未关联任何对象"

### API接口
- **上传CAD文档**: POST /api/pdm/documents/upload-cad
- **上传技术文档**: POST /api/pdm/documents/upload
- **CAD信息提取**: POST /api/pdm/documents/extract-cad-info
- **获取文档列表**: GET /api/pdm/documents
- **获取文档详情**: GET /api/pdm/documents/{id}
- **获取提取信息**: GET /api/pdm/documents/{id}/extracted-info
- **智能关联推荐**: GET /api/pdm/documents/{id}/recommendations
- **版本对比**: GET /api/pdm/documents/compare-versions
- **下载文档**: GET /api/pdm/documents/{id}/download
- **文档预览**: GET /api/documents/{id}/preview
- **建立关联**: POST /api/documents/{id}/relations

## 5. 异常与边界处理 (Error & Edge Cases)

### **文件上传失败**
- **提示信息**: "文件上传失败：网络异常或文件损坏"
- **用户操作**: 提供重新上传选项，检查网络连接

### **文档预览失败**
- **提示信息**: "文档预览失败：文件格式不支持或文件损坏"
- **用户操作**: 提供下载选项，建议使用专业软件打开

### **权限不足**
- **提示信息**: "您没有权限访问此文档"
- **用户操作**: 隐藏无权限的操作按钮，显示申请权限入口

### **文档关联冲突**
- **提示信息**: "文档关联冲突：该对象已关联其他版本文档"
- **用户操作**: 显示冲突信息，提供替换或并存选项

### **存储空间不足**
- **提示信息**: "存储空间不足，无法上传文档"
- **用户操作**: 提示清理空间或联系管理员扩容

### **文档版本冲突**
- **提示信息**: "文档版本冲突：其他用户正在编辑此文档"
- **用户操作**: 提供只读模式或等待其他用户完成编辑

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持CAD文件智能解析：DWG、DXF、STEP、IGS等格式
- [ ] CAD信息自动提取准确率≥90%（尺寸、材料、特征）
- [ ] 支持多种文档格式上传：PDF、DOC、XLS、PPT、JPG等
- [ ] 文档可与物料、BOM、工序、工艺参数建立智能关联
- [ ] 支持CAD文件3D预览和2D图纸预览
- [ ] 支持文档在线预览，包括图层控制和测量工具
- [ ] 智能关联推荐准确率≥85%
- [ ] 文档版本管理正常工作，支持版本对比和影响分析
- [ ] 文档访问权限控制正确，支持角色权限
- [ ] 文档搜索功能支持名称、内容和提取信息搜索
- [ ] 文档分类管理功能正常工作
- [ ] 支持文档批量上传和批量操作
- [ ] 文档下载功能正常，支持权限控制
- [ ] CAD解析响应时间<10秒（50MB文件）
- [ ] 文档访问日志记录完整
- [ ] 文档存储安全可靠，支持备份恢复
- [ ] 提取信息可编辑和确认，支持手工调整
- [ ] 工艺参数自动生成功能正常工作
- [ ] 版本变更影响分析准确可靠
- [ ] 所有操作记录到审计日志
- [ ] 界面支持响应式设计，移动端可正常使用
- [ ] 文档上传响应时间小于30秒（100MB文件）
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持键盘导航和无障碍访问
