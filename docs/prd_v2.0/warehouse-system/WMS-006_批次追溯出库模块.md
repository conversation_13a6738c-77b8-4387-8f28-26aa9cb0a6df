# 功能模块规格说明书：批次追溯出库模块

- **模块ID**: WMS-006
- **所属子系统**: 仓储管理子系统(WMS)
- **最后更新**: 2025-07-31
- **追溯数据模型统一说明**: 本模块与MES-007产品追溯管理模块、QMS-005质量追溯模块共享统一的追溯数据模型，确保批次流向追溯的完整性和准确性

## 1. 用户故事 (User Stories)

- **As a** 仓管员, **I want to** 按指定批次进行精确出库, **so that** 满足客户对特定批次产品的需求。
- **As a** 质量管理员, **I want to** 追溯产品的完整批次信息, **so that** 在质量问题发生时快速定位和召回。
- **As a** 销售人员, **I want to** 查看批次的详细信息和质量状态, **so that** 向客户提供准确的产品信息。
- **As a** 仓库主管, **I want to** 管理批次混合出库规则, **so that** 在保证质量的前提下提高出库效率。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 出库订单明确指定批次要求
- 批次信息完整准确
- 用户具有批次出库权限
- 相关批次库存充足

### 核心流程

#### 2.1 指定批次出库流程
1. 接收客户指定批次的出库需求
2. 系统查询指定批次的库存分布
3. 验证批次状态和质量等级
4. 生成批次出库任务和拣货清单
5. 按批次精确拣货和出库
6. 记录批次出库信息和去向

#### 2.2 批次追溯查询流程
1. 输入产品编码或批次号
2. 系统查询批次的完整生命周期
3. 显示原料来源、生产过程、质检结果
4. 展示库存分布和出库记录
5. 提供批次关联产品的追溯链
6. 生成批次追溯报告

#### 2.3 批次混合出库流程
1. 分析出库需求和批次库存情况
2. 根据混合规则确定可混合的批次
3. 计算最优的批次组合方案
4. 生成混合出库清单和标识
5. 执行混合出库并记录批次比例
6. 更新混合产品的追溯信息

#### 2.4 批次召回处理流程
1. 接收批次召回指令和范围
2. 查询该批次的所有出库记录
3. 确定需要召回的产品和客户
4. 生成召回通知和处理方案
5. 跟踪召回进度和处理结果
6. 更新批次状态和处理记录

#### 2.5 批次有效期管理流程
1. 系统自动监控批次有效期
2. 提前预警即将过期的批次
3. 优先安排临期批次的出库
4. 对过期批次进行隔离处理
5. 记录过期批次的处理方式
6. 更新库存和批次状态

### 后置条件
- 批次出库记录完整
- 追溯信息准确可查
- 库存状态实时更新
- 质量追溯链完整

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：批次追溯出库页面
### 页面目标：提供精确的批次管理和追溯功能

### 信息架构：
- **顶部区域**：包含 批次查询, 出库类型, 操作状态
- **中间区域**：包含 批次信息, 库存分布, 追溯链条
- **底部区域**：包含 出库操作, 混合规则, 历史记录

### 交互逻辑与状态：

#### **批次查询区域**
- **查询方式：**
  - **扫码查询：** 扫描批次条码快速查询
  - **编码查询：** 输入批次号精确查询
  - **产品查询：** 通过产品编码查询相关批次
  - **高级查询：** 多条件组合查询批次
- **查询结果：**
  - **批次列表：** 显示查询到的批次列表
  - **批次状态：** 显示每个批次的当前状态
  - **库存数量：** 显示批次的可用库存
  - **有效期：** 显示批次的有效期信息

#### **批次信息展示**
- **基本信息卡片：**
  - **批次号：** 大号字体显示批次唯一标识
  - **产品信息：** 显示产品编码、名称、规格
  - **生产信息：** 显示生产日期、生产线、班次
  - **质量状态：** 显示质检结果和质量等级
  - **有效期：** 显示生产日期和到期日期
- **生产信息：**
  - **生产工单：** 显示关联的生产工单号
  - **生产设备：** 显示使用的生产设备
  - **操作员：** 显示生产操作员信息
  - **工艺参数：** 显示关键工艺参数
- **质量信息：**
  - **质检报告：** 显示质检报告编号和结果
  - **质检员：** 显示执行质检的人员
  - **质检日期：** 显示质检完成日期
  - **质量等级：** 显示产品质量等级
- **原料追溯：**
  - **原料批次：** 显示使用的原料批次信息
  - **供应商：** 显示原料供应商信息
  - **原料质检：** 显示原料质检结果
  - **追溯链条：** 显示完整的原料追溯链

#### **库存分布展示**
- **库位分布：**
  - **库位列表：** 显示批次在各库位的分布
  - **库存数量：** 显示每个库位的库存数量
  - **库位状态：** 显示库位的当前状态
  - **最后更新：** 显示库存最后更新时间
- **容器分布：**
  - **容器信息：** 显示批次所在的容器信息
  - **容器位置：** 显示容器的当前位置
  - **装载情况：** 显示容器的装载情况
  - **容器状态：** 显示容器的当前状态
- **库存统计：**
  - **总库存：** 显示批次的总库存数量
  - **可用库存：** 显示可用于出库的数量
  - **预留库存：** 显示已预留的库存数量
  - **冻结库存：** 显示被冻结的库存数量

#### **出库操作区域**
- **出库类型选择：**
  - **指定批次出库：** 按客户指定的批次出库
  - **FIFO出库：** 按先进先出原则出库
  - **混合批次出库：** 多个批次混合出库
  - **紧急出库：** 紧急情况下的快速出库
- **出库数量设置：**
  - **出库数量：** 输入框设置出库数量
  - **可用数量：** 显示当前可用的库存数量
  - **单位：** 显示计量单位
  - **数量验证：** 实时验证出库数量的合理性
- **批次选择：**
  - **批次列表：** 显示可选择的批次列表
  - **批次信息：** 显示每个批次的详细信息
  - **优先级：** 显示批次的出库优先级
  - **选择状态：** 显示批次的选择状态

#### **混合规则设置**
- **混合条件：**
  - **产品类型：** 相同产品类型才能混合
  - **质量等级：** 相同质量等级才能混合
  - **生产日期：** 生产日期差异在允许范围内
  - **供应商：** 是否允许不同供应商的批次混合
- **混合比例：**
  - **比例设置：** 设置各批次的混合比例
  - **比例验证：** 验证混合比例的合理性
  - **自动计算：** 系统自动计算最优混合比例
  - **手动调整：** 支持手动调整混合比例
- **混合标识：**
  - **混合编号：** 生成混合产品的唯一编号
  - **混合标签：** 生成混合产品的标识标签
  - **追溯信息：** 记录混合产品的追溯信息

#### **追溯链条展示**
- **上游追溯：**
  - **原料来源：** 显示原料的来源信息
  - **供应商：** 显示原料供应商信息
  - **原料批次：** 显示原料的批次信息
  - **原料质检：** 显示原料的质检结果
- **生产过程：**
  - **生产工单：** 显示生产工单信息
  - **生产设备：** 显示使用的生产设备
  - **工艺参数：** 显示关键工艺参数
  - **质量控制：** 显示生产过程中的质量控制点
- **下游追溯：**
  - **出库记录：** 显示批次的出库记录
  - **客户信息：** 显示出库的客户信息
  - **销售订单：** 显示关联的销售订单
  - **物流信息：** 显示物流配送信息

#### **操作按钮组**
- **确认出库按钮：**
  - **默认状态：** 绿色背景(#52C41A)，"确认出库"
  - **禁用状态：** 批次未选择或数量未设置时禁用
  - **点击效果：** 确认批次出库操作
- **批次混合按钮：**
  - **默认状态：** 蓝色边框，"批次混合"
  - **点击效果：** 打开批次混合设置对话框
- **追溯报告按钮：**
  - **默认状态：** 橙色边框，"追溯报告"
  - **点击效果：** 生成批次追溯报告
- **召回处理按钮：**
  - **默认状态：** 红色边框，"召回处理"
  - **点击效果：** 启动批次召回处理流程

#### **批次召回对话框**
- **召回范围：**
  - **批次范围：** 选择需要召回的批次范围
  - **时间范围：** 设置召回的时间范围
  - **客户范围：** 选择涉及的客户范围
  - **产品范围：** 选择涉及的产品范围
- **召回原因：**
  - **质量问题：** 产品质量问题导致的召回
  - **安全隐患：** 安全隐患导致的召回
  - **法规要求：** 法规要求导致的召回
  - **其他原因：** 其他原因导致的召回
- **召回计划：**
  - **召回通知：** 生成召回通知文档
  - **联系客户：** 联系涉及的客户
  - **回收安排：** 安排产品回收
  - **处理方案：** 制定召回产品的处理方案

#### **追溯报告生成**
- **报告类型：**
  - **批次报告：** 单个批次的完整追溯报告
  - **产品报告：** 产品的批次追溯报告
  - **客户报告：** 客户收到产品的追溯报告
  - **时间段报告：** 特定时间段的追溯报告
- **报告内容：**
  - **基本信息：** 批次基本信息和产品信息
  - **生产信息：** 生产过程和质量控制信息
  - **原料信息：** 原料来源和质检信息
  - **出库信息：** 出库记录和客户信息
- **报告格式：**
  - **PDF格式：** 生成PDF格式的报告
  - **Excel格式：** 生成Excel格式的报告
  - **打印输出：** 直接打印追溯报告
  - **邮件发送：** 通过邮件发送报告

#### **历史记录查询**
- **出库历史：**
  - **出库时间：** 显示批次的出库时间
  - **出库数量：** 显示出库的数量
  - **客户信息：** 显示出库的客户信息
  - **操作员：** 显示执行出库的操作员
- **追溯历史：**
  - **查询时间：** 显示追溯查询的时间
  - **查询人员：** 显示执行查询的人员
  - **查询内容：** 显示查询的内容
  - **查询结果：** 显示查询的结果
- **召回历史：**
  - **召回时间：** 显示批次召回的时间
  - **召回原因：** 显示召回的原因
  - **召回范围：** 显示召回的范围
  - **处理结果：** 显示召回的处理结果

### 数据校验规则：

#### **批次有效性**
- **校验规则：** 批次必须存在且状态正常，未过期
- **错误提示文案：** "批次不存在、已过期或状态异常"

#### **出库数量**
- **校验规则：** 出库数量不能超过可用库存
- **错误提示文案：** "出库数量超出可用库存"

#### **混合规则**
- **校验规则：** 混合批次必须符合混合规则
- **错误提示文案：** "所选批次不符合混合规则"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **批次信息**:
  - **批次号 (batch_no)**: String, 必填, 唯一标识
  - **产品编码 (product_code)**: String, 必填, 产品标识
  - **生产日期 (production_date)**: Date, 必填
  - **有效期 (expiry_date)**: Date, 可选
- **出库信息**:
  - **出库数量 (outbound_quantity)**: Number, 必填
  - **客户编码 (customer_code)**: String, 必填
  - **出库类型 (outbound_type)**: Enum, 指定批次/FIFO/混合
  - **混合比例 (mix_ratio)**: Object, 混合出库时必填

### 展示数据
- **批次详情**: 批次信息、生产信息、质量信息
- **库存分布**: 库位分布、容器分布、库存统计
- **追溯链条**: 上游追溯、生产过程、下游追溯
- **出库记录**: 出库历史、客户信息、物流信息

### 空状态/零数据
- **无批次信息**: 显示"未找到相关批次信息"
- **无库存**: 显示"该批次无可用库存"
- **无追溯信息**: 显示"暂无追溯信息"

### API接口
- **批次查询**: GET /api/batch/query
- **批次出库**: POST /api/batch/outbound
- **追溯查询**: GET /api/batch/trace
- **召回处理**: POST /api/batch/recall

## 5. 异常与边界处理 (Error & Edge Cases)

### **批次不存在**
- **提示信息**: "输入的批次号不存在，请检查后重新输入"
- **用户操作**: 提供批次查询功能，显示相似批次

### **批次已过期**
- **提示信息**: "该批次已过期，不能正常出库"
- **用户操作**: 提供过期批次处理选项

### **库存不足**
- **提示信息**: "批次库存不足，可用库存X件，需求Y件"
- **用户操作**: 提供部分出库或替代批次选项

### **混合规则冲突**
- **提示信息**: "所选批次不符合混合规则，无法混合出库"
- **用户操作**: 显示冲突原因，提供规则调整选项

### **追溯信息缺失**
- **提示信息**: "部分追溯信息缺失，可能影响追溯完整性"
- **用户操作**: 显示缺失信息，提供补充录入功能

### **召回处理异常**
- **提示信息**: "召回处理异常，请联系质量管理部门"
- **用户操作**: 提供异常上报功能

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持精确的批次查询和信息展示
- [ ] 批次追溯信息完整，包含原料到成品的全链条
- [ ] 支持指定批次出库和混合批次出库
- [ ] 批次混合规则可配置，自动验证混合条件
- [ ] 批次有效期自动监控和预警
- [ ] 支持批次召回处理和跟踪
- [ ] 追溯报告自动生成，格式规范
- [ ] 出库记录完整，支持反向追溯
- [ ] 界面操作便捷，支持扫码快速查询
- [ ] 数据准确率≥99.9%，追溯完整率≥99%
- [ ] 批次查询响应时间<3秒
- [ ] 所有页面元素符合全局设计规范
- [ ] 追溯效率提升≥70%
