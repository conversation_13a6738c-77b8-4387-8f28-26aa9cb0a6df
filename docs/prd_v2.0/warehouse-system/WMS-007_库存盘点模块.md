# 功能模块规格说明书：库存盘点模块

- **模块ID**: WMS-007
- **所属子系统**: 仓储管理子系统(WMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 仓库主管, **I want to** 创建和安排库存盘点任务, **so that** 定期核实库存数据的准确性。
- **As a** 仓管员, **I want to** 使用PDA进行移动盘点, **so that** 高效完成盘点作业并实时上传数据。
- **As a** 仓管员, **I want to** 系统自动计算盘点差异, **so that** 快速发现和处理库存差异问题。
- **As a** 财务人员, **I want to** 获得准确的盘点报告, **so that** 进行库存价值核算和财务分析。
- **As a** 仓管员, **I want to** 在盘点时准确识别和记录变体规格, **so that** 确保变体库存数据的准确性。
- **As a** 仓库主管, **I want to** 按变体维度分析盘点差异, **so that** 识别特定变体的管理问题。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 盘点计划已制定
- 盘点人员已分配
- 盘点区域已确定
- PDA设备准备就绪

### 核心流程

#### 2.1 盘点任务创建流程
1. 制定盘点计划和范围（全盘、抽盘、循环盘点）
2. 选择盘点区域和物料范围
3. 设置盘点时间和截止日期
4. 分配盘点人员和责任区域
5. 生成盘点任务单和盘点清单
6. 冻结盘点区域的库存变动

#### 2.2 移动盘点执行流程
1. 盘点员扫描盘点任务条码
2. 系统显示分配的盘点区域和清单
3. 到达库位扫描库位条码确认
4. 扫描物料批次条码识别物料
5. 录入实际盘点数量
6. 系统实时计算账面差异
7. 对差异项目进行复盘确认
8. 完成区域盘点并提交数据

#### 2.3 盘点差异处理流程
1. 系统自动生成盘点差异报告
2. 分析差异原因和类型
3. 对重大差异进行复盘验证
4. 确认差异后进行库存调整
5. 记录差异原因和处理方式
6. 更新库存数据和账面记录

#### 2.4 盘点结果审核流程
1. 盘点主管审核盘点结果
2. 检查差异处理的合理性
3. 确认库存调整的准确性
4. 审批盘点报告和调整单
5. 解除库存冻结状态
6. 归档盘点资料和记录

#### 2.5 循环盘点流程
1. 系统根据ABC分类自动安排循环盘点
2. 高价值物料增加盘点频次
3. 自动生成循环盘点计划
4. 按计划执行日常盘点
5. 持续监控库存准确率
6. 动态调整盘点策略

### 后置条件
- 库存数据准确更新
- 盘点差异得到处理
- 盘点报告完整生成
- 库存准确率提升

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：库存盘点操作页面
### 页面目标：提供高效便捷的库存盘点操作界面

### 信息架构：
- **顶部区域**：包含 盘点任务信息, 进度指示, 操作状态
- **中间区域**：包含 盘点清单, 扫码区域, 差异显示
- **底部区域**：包含 数量录入, 操作按钮, 历史记录

### 交互逻辑与状态：

#### **盘点任务信息**
- **任务基本信息：**
  - **任务编号：** 显示盘点任务唯一编号
  - **盘点类型：** 显示盘点类型（全盘/抽盘/循环）
  - **盘点范围：** 显示盘点的区域范围
  - **计划时间：** 显示计划开始和结束时间
  - **负责人员：** 显示盘点负责人和参与人员
- **任务状态：**
  - **未开始：** 灰色圆点，"盘点未开始"
  - **进行中：** 蓝色圆点，"盘点进行中"
  - **已暂停：** 黄色圆点，"盘点已暂停"
  - **已完成：** 绿色圆点，"盘点已完成"
  - **已审核：** 紫色圆点，"盘点已审核"

#### **进度指示器**
- **整体进度：**
  - **进度条：** 蓝色进度条显示盘点完成进度
  - **完成率：** 数字显示盘点完成百分比
  - **剩余项目：** 显示剩余待盘点项目数
  - **预计完成时间：** 显示预计完成时间
- **区域进度：**
  - **已完成区域：** 显示已完成盘点的区域
  - **进行中区域：** 显示正在盘点的区域
  - **待盘点区域：** 显示待盘点的区域
  - **区域完成率：** 显示各区域的完成率

#### **盘点清单展示**
- **清单表头：**
  - **库位编码：** 显示库位编码，支持排序
  - **物料编码：** 显示物料编码和名称
  - **批次号：** 显示批次号和生产日期
  - **账面数量：** 显示系统账面数量
  - **实盘数量：** 输入框录入实际盘点数量
  - **差异：** 自动计算并显示差异
  - **状态：** 显示盘点状态
- **盘点状态标识：**
  - **待盘点：** 白色背景，等待盘点
  - **盘点中：** 黄色背景，正在盘点
  - **已完成：** 绿色背景，盘点完成
  - **有差异：** 橙色背景，存在差异
  - **需复盘：** 红色背景，需要复盘

#### **扫码操作区域**
- **扫码状态指示：**
  - **待扫库位：** 蓝色虚线框，"请扫描库位条码"
  - **待扫物料：** 绿色虚线框，"请扫描物料条码"
  - **扫码成功：** 绿色实线框，显示扫码结果
  - **扫码错误：** 红色边框，显示错误信息
- **扫码验证：**
  - **库位验证：** 验证扫描的库位是否在盘点范围内
  - **物料验证：** 验证扫描的物料是否存在于该库位
  - **批次验证：** 验证批次信息的准确性
- **手动输入：**
  - **库位输入：** 手动输入库位编码
  - **物料输入：** 手动输入物料编码
  - **批次输入：** 手动输入批次号

#### **数量录入区域**
- **数量输入：**
  - **实盘数量：** 数字输入框录入实际盘点数量
  - **快捷按钮：** +1、+10、+100等快捷增减按钮
  - **清零按钮：** 一键清零当前输入
  - **账面数量：** 显示系统账面数量作为参考
- **差异计算：**
  - **差异数量：** 自动计算实盘与账面的差异
  - **差异率：** 计算并显示差异百分比
  - **差异类型：** 显示盈亏类型（盘盈/盘亏）
  - **差异原因：** 下拉选择差异可能原因
- **单位信息：**
  - **计量单位：** 显示物料的计量单位
  - **换算关系：** 显示单位换算关系（如有）
  - **精度控制：** 根据物料特性控制录入精度

#### **差异处理区域**
- **差异分析：**
  - **差异统计：** 显示盘盈、盘亏的数量和金额
  - **差异分布：** 显示差异在各区域的分布情况
  - **重大差异：** 高亮显示超过阈值的重大差异
  - **差异趋势：** 显示差异的变化趋势
- **差异原因：**
  - **系统错误：** 系统记录错误导致的差异
  - **操作失误：** 人工操作失误导致的差异
  - **物料损耗：** 自然损耗或损坏导致的差异
  - **盗损：** 盗窃或丢失导致的差异
  - **其他原因：** 其他未知原因导致的差异
- **处理方式：**
  - **账面调整：** 调整系统账面数量
  - **实物调整：** 调整实际库存数量
  - **差异确认：** 确认差异并记录原因
  - **复盘验证：** 安排复盘重新验证

#### **操作按钮组**
- **确认盘点按钮：**
  - **默认状态：** 绿色背景(#52C41A)，"确认盘点"
  - **禁用状态：** 数量未录入时禁用
  - **点击效果：** 确认当前项目的盘点结果
- **跳过项目按钮：**
  - **默认状态：** 橙色边框，"跳过项目"
  - **点击效果：** 跳过当前项目，标记为待处理
- **复盘按钮：**
  - **默认状态：** 黄色边框，"复盘"
  - **点击效果：** 对当前项目进行复盘
- **完成盘点按钮：**
  - **默认状态：** 蓝色背景(#1890FF)，"完成盘点"
  - **显示条件：** 所有项目完成后显示
  - **点击效果：** 完成整个盘点任务

#### **盘点报告预览**
- **报告头部：**
  - **报告编号：** 盘点报告唯一编号
  - **盘点时间：** 盘点执行的时间范围
  - **盘点范围：** 盘点涉及的区域和物料
  - **盘点人员：** 参与盘点的人员列表
- **盘点统计：**
  - **盘点项目：** 总盘点项目数和完成数
  - **差异统计：** 盘盈、盘亏的项目数和金额
  - **准确率：** 盘点准确率统计
  - **效率统计：** 盘点效率和用时统计
- **差异明细：**
  - **差异列表：** 详细的差异项目列表
  - **差异原因：** 各项差异的原因分析
  - **处理建议：** 对差异的处理建议
  - **调整方案：** 库存调整的具体方案

#### **历史盘点记录**
- **记录列表：**
  - **盘点时间：** 显示历史盘点的时间
  - **盘点类型：** 显示盘点类型
  - **盘点范围：** 显示盘点范围
  - **差异情况：** 显示差异统计
  - **处理状态：** 显示处理状态
- **查询功能：**
  - **时间筛选：** 按时间范围筛选记录
  - **类型筛选：** 按盘点类型筛选
  - **区域筛选：** 按盘点区域筛选
  - **状态筛选：** 按处理状态筛选

#### **循环盘点设置**
- **盘点策略：**
  - **ABC分类：** 按物料价值ABC分类设置盘点频次
  - **盘点周期：** 设置不同类别的盘点周期
  - **盘点比例：** 设置每次盘点的物料比例
  - **优先级：** 设置盘点优先级规则
- **自动安排：**
  - **计划生成：** 自动生成循环盘点计划
  - **任务分配：** 自动分配盘点任务
  - **提醒通知：** 自动发送盘点提醒
  - **进度跟踪：** 自动跟踪盘点进度

#### **盘点审核界面**
- **审核信息：**
  - **盘点结果：** 显示盘点的整体结果
  - **差异分析：** 显示差异的详细分析
  - **处理方案：** 显示差异的处理方案
  - **调整影响：** 显示调整对库存的影响
- **审核操作：**
  - **通过审核：** 审核通过，执行库存调整
  - **退回修改：** 审核不通过，退回修改
  - **部分通过：** 部分项目通过，部分退回
  - **暂缓处理：** 暂缓处理，等待进一步确认

### 数据校验规则：

#### **盘点数量**
- **校验规则：** 数量必须为非负数，符合物料特性
- **错误提示文案：** "盘点数量不能为负数或超出合理范围"

#### **差异阈值**
- **校验规则：** 重大差异必须提供差异原因
- **错误提示文案：** "重大差异必须填写差异原因"

#### **盘点完整性**
- **校验规则：** 所有计划盘点项目必须完成或标记跳过
- **错误提示文案：** "存在未完成的盘点项目"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **盘点任务**:
  - **任务编号 (task_no)**: String, 自动生成, 唯一标识
  - **盘点类型 (count_type)**: Enum, 全盘/抽盘/循环
  - **盘点范围 (count_scope)**: Object, 盘点区域和物料范围
  - **计划时间 (planned_time)**: DateTime, 计划执行时间
- **盘点记录**:
  - **库位编码 (location_code)**: String, 必填, 库位标识
  - **物料编码 (material_code)**: String, 必填, 物料标识
  - **批次号 (batch_no)**: String, 必填, 批次标识
  - **实盘数量 (actual_quantity)**: Number, 必填, 实际盘点数量

### 展示数据
- **盘点任务**: 任务信息、进度状态、参与人员
- **盘点清单**: 物料信息、账面数量、实盘数量、差异
- **差异报告**: 差异统计、原因分析、处理建议
- **历史记录**: 盘点历史、趋势分析、准确率统计

### 空状态/零数据
- **无盘点任务**: 显示"暂无盘点任务"
- **无盘点项目**: 显示"该区域无需盘点项目"
- **无差异**: 显示"盘点结果无差异"

### API接口
- **创建盘点任务**: POST /api/stocktaking/tasks
- **提交盘点结果**: POST /api/stocktaking/results
- **生成差异报告**: GET /api/stocktaking/variance-report
- **库存调整**: POST /api/stocktaking/adjustment

## 5. 异常与边界处理 (Error & Edge Cases)

### **盘点区域被占用**
- **提示信息**: "盘点区域有正在进行的作业，请稍后再试"
- **用户操作**: 提供等待选项或调整盘点计划

### **扫码设备故障**
- **提示信息**: "扫码设备异常，请检查设备或使用手动输入"
- **用户操作**: 提供手动输入选项和设备检测

### **重大差异异常**
- **提示信息**: "发现重大差异，差异率超过X%，请复盘确认"
- **用户操作**: 强制要求复盘或提供差异说明

### **盘点数据丢失**
- **提示信息**: "盘点数据上传失败，数据已本地保存"
- **用户操作**: 提供数据恢复和重新上传功能

### **权限不足**
- **提示信息**: "您没有权限执行此盘点任务"
- **用户操作**: 显示权限要求，提供权限申请

### **网络中断**
- **提示信息**: "网络连接中断，盘点数据将在恢复后同步"
- **用户操作**: 本地缓存数据，网络恢复后同步

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持全盘、抽盘、循环盘点等多种盘点方式
- [ ] 移动端盘点操作便捷，支持扫码和手动输入
- [ ] 差异自动计算准确，重大差异自动预警
- [ ] 盘点进度实时跟踪，状态准确更新
- [ ] 支持盘点任务的暂停和恢复
- [ ] 差异处理流程完整，支持复盘验证
- [ ] 盘点报告自动生成，数据准确完整
- [ ] 循环盘点自动安排，策略可配置
- [ ] 界面适配PDA设备，操作响应快速
- [ ] 盘点数据实时同步，支持离线操作
- [ ] 库存准确率提升≥95%
- [ ] 所有页面元素符合全局设计规范
- [ ] 盘点效率提升≥50%
