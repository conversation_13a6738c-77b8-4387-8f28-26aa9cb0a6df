# 功能模块规格说明书：收货入库模块

- **模块ID**: WMS-003
- **所属子系统**: 仓储管理子系统(WMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 仓管员, **I want to** 通过扫码快速完成收货入库, **so that** 提高入库效率和准确性。
- **As a** 仓管员, **I want to** 系统自动生成批次条码, **so that** 确保每批物料的唯一标识和可追溯性。
- **As a** 仓管员, **I want to** 获得智能库位推荐, **so that** 快速找到最适合的存储位置。
- **As a** 仓库主管, **I want to** 处理收货差异和异常情况, **so that** 确保库存数据的准确性。
- **As a** 仓管员, **I want to** 在收货时明确物料的具体变体规格, **so that** 确保库存记录的精确性和可追溯性。
- **As a** 采购员, **I want to** 收货时验证变体规格与采购订单的一致性, **so that** 避免规格错误和后续问题。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 采购订单或生产任务已创建
- 仓储结构和库位已设置
- 用户具有收货入库权限
- PDA设备正常工作

### 核心流程

#### 2.1 采购收货入库流程
1. 供应商送货到达，仓管员扫描采购订单号
2. 系统显示待收货物料清单和计划数量
3. **变体规格确认**：对于变体物料，系统显示采购的具体变体规格
4. 逐项核对实物，验证变体规格与订单一致性
5. 录入实际收货数量，确认变体维度值
6. 对于数量差异或规格差异，选择差异处理方式
7. 系统自动生成唯一批次号和条码（包含变体信息）
8. 打印批次标签并贴在物料包装上
9. 扫描推荐库位码，完成上架操作
10. 系统实时更新变体库存数据和订单状态

#### 2.2 生产入库流程
1. 扫描生产工单号或产品条码
2. 系统显示生产完工信息
3. 确认入库产品和数量
4. 选择产品质量等级和状态
5. 生成产品批次号和追溯码
6. 根据产品特性推荐存储库位
7. 完成产品上架和库存更新

#### 2.3 退货入库流程
1. 扫描销售退货单号
2. 核对退货产品和原因
3. 检查产品状态和质量
4. 根据产品状态选择处理方式
5. 合格品正常入库，不合格品隔离
6. 更新库存和客户退货记录

#### 2.4 其他入库流程
1. 选择入库类型（调拨入库、盘盈入库等）
2. 录入入库单据信息
3. 扫描或录入物料信息
4. 确认入库数量和批次
5. 完成上架和库存更新

### 后置条件
- 库存数据准确更新
- 批次信息完整记录
- 物料成功上架到指定库位
- 相关单据状态更新

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：收货入库操作页面
### 页面目标：提供高效便捷的收货入库操作界面

### 信息架构：
- **顶部区域**：包含 入库类型选择, 扫码输入区, 操作状态指示
- **中间区域**：包含 待收货清单, 收货进度, 差异处理
- **底部区域**：包含 库位推荐, 操作按钮, 历史记录

### 交互逻辑与状态：

#### **入库类型选择**
- **类型标签页：**
  - **采购入库：** 蓝色标签，"采购入库"
  - **生产入库：** 绿色标签，"生产入库"
  - **退货入库：** 橙色标签，"退货入库"
  - **其他入库：** 灰色标签，"其他入库"
- **快捷切换：** 支持左右滑动切换入库类型

#### **扫码输入区域**
- **扫码状态指示：**
  - **待扫码：** 蓝色虚线框，"请扫描单据条码"
  - **扫码中：** 绿色实线框，显示扫码动画
  - **扫码成功：** 绿色背景，显示扫码结果
  - **扫码失败：** 红色边框，显示错误信息
- **输入方式：**
  - **扫码输入：** 主要输入方式，支持各种条码格式
  - **手动输入：** 备用输入方式，输入框手动录入
  - **语音输入：** 语音识别输入单据号
- **扫码结果显示：**
  - **单据号：** 大号字体显示扫描的单据号
  - **单据类型：** 显示单据类型图标和名称
  - **单据状态：** 显示单据当前状态
  - **供应商/客户：** 显示相关方信息

#### **待收货清单**
- **清单表头：**
  - **物料编码：** 显示物料编码，支持点击查看详情
  - **物料名称：** 显示物料名称和规格
  - **计划数量：** 显示计划收货数量
  - **实收数量：** 输入框，录入实际收货数量
  - **差异：** 自动计算并显示差异
  - **状态：** 显示收货状态
- **物料行状态：**
  - **待收货：** 白色背景，等待收货
  - **收货中：** 黄色背景，正在收货
  - **已完成：** 绿色背景，收货完成
  - **有差异：** 橙色背景，存在数量差异
  - **异常：** 红色背景，收货异常

#### **收货操作区域**
- **数量录入：**
  - **实收数量输入框：** 数字键盘输入
  - **快捷数量按钮：** +1、+10、+100快捷按钮
  - **全收按钮：** 一键设置为计划数量
  - **清零按钮：** 清空当前输入
- **批次信息：**
  - **批次号显示：** 自动生成的批次号
  - **生产日期：** 日期选择器
  - **有效期：** 自动计算或手动输入
  - **供应商批次：** 输入供应商提供的批次号
- **质量状态：**
  - **合格：** 绿色标签，正常入库
  - **待检：** 黄色标签，需要质检
  - **不合格：** 红色标签，隔离处理
  - **免检：** 蓝色标签，免检入库

#### **差异处理对话框**
- **差异类型：**
  - **数量多收：** 实收数量大于计划数量
  - **数量少收：** 实收数量小于计划数量
  - **物料替换：** 收到不同的物料
  - **质量问题：** 物料质量不符合要求
- **处理方式：**
  - **正常入库：** 按实收数量入库
  - **拒收处理：** 拒绝收货，退回供应商
  - **部分收货：** 部分收货，剩余数量等待
  - **质量隔离：** 隔离存放，等待处理
- **差异原因：**
  - **供应商发货错误：** 供应商发货数量或物料错误
  - **运输损耗：** 运输过程中的损耗
  - **包装破损：** 包装破损导致的损失
  - **其他原因：** 其他原因，需要文字说明

#### **库位推荐区域**
- **推荐算法：**
  - **就近原则：** 推荐距离收货区最近的库位
  - **同类聚集：** 推荐存放相同物料的库位附近
  - **空间优化：** 推荐能最大化利用空间的库位
  - **先进先出：** 考虑FIFO策略的库位安排
- **推荐结果显示：**
  - **推荐库位：** 显示推荐的库位编码
  - **库位状态：** 显示库位当前状态和可用容量
  - **距离信息：** 显示从当前位置到库位的距离
  - **推荐理由：** 显示推荐此库位的原因
- **库位选择：**
  - **接受推荐：** 使用系统推荐的库位
  - **手动选择：** 手动选择其他库位
  - **扫码确认：** 扫描库位条码确认

#### **操作按钮组**
- **确认收货按钮：**
  - **默认状态：** 蓝色背景(#1890FF)，"确认收货"
  - **禁用状态：** 灰色背景，数量未录入时禁用
  - **点击效果：** 确认当前物料的收货操作
- **批量收货按钮：**
  - **默认状态：** 绿色背景(#52C41A)，"批量收货"
  - **点击效果：** 批量确认多个物料的收货
- **打印标签按钮：**
  - **默认状态：** 橙色边框，"打印标签"
  - **点击效果：** 打印批次标签和库位标签
- **完成入库按钮：**
  - **默认状态：** 绿色背景，"完成入库"
  - **点击效果：** 完成整个入库操作

#### **批次标签打印**
- **标签内容：**
  - **批次条码：** 一维或二维条码
  - **物料信息：** 物料编码、名称、规格
  - **批次信息：** 批次号、生产日期、有效期
  - **数量信息：** 入库数量、单位
- **打印设置：**
  - **标签规格：** 选择标签纸规格
  - **打印份数：** 设置打印份数
  - **打印机：** 选择目标打印机
- **打印预览：** 显示标签打印效果预览

#### **收货历史记录**
- **记录列表：**
  - **收货时间：** 显示收货完成时间
  - **单据号：** 显示相关单据号
  - **物料信息：** 显示收货物料信息
  - **收货数量：** 显示实际收货数量
  - **操作员：** 显示收货操作员
  - **库位：** 显示入库库位
- **查询功能：**
  - **时间筛选：** 按时间范围筛选记录
  - **物料筛选：** 按物料类型筛选
  - **操作员筛选：** 按操作员筛选
  - **状态筛选：** 按收货状态筛选

### 数据校验规则：

#### **收货数量**
- **校验规则：** 数量必须大于0，不能超过合理范围
- **错误提示文案：** "收货数量必须大于0且在合理范围内"

#### **批次号**
- **校验规则：** 批次号必须唯一，符合编码规则
- **错误提示文案：** "批次号已存在或格式不正确"

#### **库位选择**
- **校验规则：** 库位必须可用且容量足够
- **错误提示文案：** "所选库位不可用或容量不足"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **收货信息**:
  - **单据号 (document_no)**: String, 必填, 采购订单号等
  - **物料编码 (material_code)**: String, 必填, 物料标识
  - **变体ID (variant_id)**: String, 可选, 变体物料的具体规格标识
  - **变体规格 (variant_spec)**: Object, 可选, 变体维度值（如长×宽）
  - **实收数量 (received_quantity)**: Number, 必填, 实际收货数量
  - **批次号 (batch_no)**: String, 自动生成, 唯一标识（包含变体信息）
- **批次信息**:
  - **生产日期 (production_date)**: Date, 必填
  - **有效期 (expiry_date)**: Date, 可选
  - **供应商批次 (supplier_batch)**: String, 可选
  - **质量状态 (quality_status)**: Enum, 合格/待检/不合格

### 展示数据
- **待收货清单**: 物料信息、变体规格、计划数量、收货状态
- **变体信息**: 变体编码、变体名称、变体维度值、规格描述
- **收货进度**: 已收货数量、剩余数量、完成率
- **库位推荐**: 推荐库位、可用容量、推荐理由
- **差异信息**: 差异类型、差异数量、规格差异、处理状态

### 空状态/零数据
- **无待收货物料**: 显示"该单据下无待收货物料"
- **无库位推荐**: 显示"暂无可用库位，请联系管理员"
- **无收货记录**: 显示"暂无收货记录"

### API接口
- **获取待收货清单**: GET /api/receiving/pending
- **确认收货**: POST /api/receiving/confirm
- **生成批次号**: POST /api/receiving/batch
- **获取库位推荐**: GET /api/receiving/location-recommend

## 5. 异常与边界处理 (Error & Edge Cases)

### **单据不存在**
- **提示信息**: "扫描的单据号不存在或已完成收货"
- **用户操作**: 提供重新扫码选项，显示单据查询功能

### **数量差异过大**
- **提示信息**: "收货数量差异超过允许范围，请确认后处理"
- **用户操作**: 强制要求选择差异处理方式

### **库位容量不足**
- **提示信息**: "推荐库位容量不足，请选择其他库位"
- **用户操作**: 提供其他库位选择，支持分批入库

### **批次号重复**
- **提示信息**: "批次号已存在，系统将自动生成新的批次号"
- **用户操作**: 自动处理，无需用户干预

### **打印机故障**
- **提示信息**: "标签打印失败，请检查打印机状态"
- **用户操作**: 提供打印机检测，支持稍后补打

### **网络中断**
- **提示信息**: "网络连接中断，数据将在恢复后同步"
- **用户操作**: 本地缓存操作，网络恢复后自动同步

### **变体规格不匹配**
- **提示信息**: "收货物料的变体规格与订单不符，请确认规格信息"
- **用户操作**: 提供规格对比界面，支持规格调整或拒收处理

### **变体信息缺失**
- **提示信息**: "该物料需要变体信息，请补充完整的变体规格"
- **用户操作**: 强制要求输入变体维度值，提供常用规格选择

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持采购入库、生产入库、退货入库等多种入库类型
- [ ] 扫码操作响应时间<1秒，成功率≥99.8%
- [ ] 批次号生成规则可配置，保证全局唯一性
- [ ] 实收数量与单据数量差异处理机制完善
- [ ] 库位推荐算法考虑物料特性和空间优化
- [ ] 支持批次标签的自动生成和打印
- [ ] 收货差异和异常情况处理流程完整
- [ ] 界面适配PDA设备，支持移动端操作
- [ ] 库存数据实时更新，准确率≥99.5%
- [ ] 支持离线操作和数据同步
- [ ] 操作历史记录完整可追溯
- [ ] 所有页面元素符合全局设计规范
- [ ] 收货作业效率提升≥60%
