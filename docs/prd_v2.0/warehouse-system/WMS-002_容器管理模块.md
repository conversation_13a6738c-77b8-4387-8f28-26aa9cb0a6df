# 功能模块规格说明书：容器管理模块

- **模块ID**: WMS-002
- **所属子系统**: 仓储管理子系统(WMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 仓管员, **I want to** 管理铁架等容器, **so that** 实现玻璃产品的专业化存储。
- **As a** 仓管员, **I want to** 扫描容器条码关联产品信息, **so that** 实现整架调拨和批量管理。
- **As a** 仓库主管, **I want to** 跟踪容器的位置和装载状态, **so that** 优化容器利用率和管理效率。
- **As a** 物料计划员, **I want to** 查看容器的使用情况和周转率, **so that** 合理配置容器资源。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 容器类型已定义完成
- 容器编码规则已制定
- 用户具有容器管理权限
- 相关库位已创建

### 核心流程

#### 2.1 容器注册流程
1. 选择容器类型（铁架、托盘等）
2. 生成唯一容器编码
3. 录入容器基本信息和属性
4. 生成容器条码并打印
5. 设置容器初始状态为"空闲"
6. 将容器信息保存到系统

#### 2.2 容器装载流程
1. 扫描容器条码确认容器身份
2. 验证容器状态为"空闲"或"部分装载"
3. 扫描产品条码关联到容器
4. 检查容器容量和承重限制
5. 更新容器装载信息和状态
6. 记录装载时间和操作员

#### 2.3 容器上架流程
1. 扫描装载完成的容器条码
2. 系统推荐适合的库位
3. 扫描目标库位条码确认
4. 更新容器位置信息
5. 设置容器状态为"在库"
6. 更新库位占用状态

#### 2.4 容器出库流程
1. 根据出库需求查找相关容器
2. 扫描容器条码确认
3. 检查容器内产品信息
4. 确认出库数量和产品
5. 更新容器装载状态
6. 如容器清空，设置状态为"空闲"

#### 2.5 容器维护流程
1. 定期检查容器物理状态
2. 记录容器损坏或维修信息
3. 设置容器状态为"维修中"
4. 安排容器维修或报废
5. 维修完成后恢复容器状态
6. 更新容器维护记录

### 后置条件
- 容器信息准确完整
- 容器状态实时更新
- 装载信息清晰可追溯
- 容器位置准确定位

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：容器管理页面
### 页面目标：提供全面的容器管理和跟踪功能

### 信息架构：
- **顶部区域**：包含 容器统计概览, 快捷操作按钮, 搜索筛选
- **中间区域**：包含 容器列表, 容器详情, 装载信息
- **底部区域**：包含 操作历史, 维护记录, 分页导航

### 交互逻辑与状态：

#### **容器统计概览**
- **统计卡片布局：**
  - **总容器数：** 蓝色卡片，显示系统中总容器数量
  - **在用容器：** 绿色卡片，显示正在使用的容器数
  - **空闲容器：** 橙色卡片，显示空闲可用的容器数
  - **维修容器：** 红色卡片，显示维修中的容器数
- **利用率指标：**
  - **容器利用率：** 环形进度条显示整体利用率
  - **平均装载率：** 显示容器平均装载程度
  - **周转次数：** 显示容器平均周转次数

#### **快捷操作按钮组**
- **新增容器按钮：**
  - **默认状态：** 蓝色背景(#1890FF)，"新增容器"
  - **点击效果：** 弹出容器信息录入对话框
- **批量导入按钮：**
  - **默认状态：** 绿色边框，"批量导入"
  - **点击效果：** 打开Excel批量导入功能
- **打印标签按钮：**
  - **默认状态：** 橙色边框，"打印标签"
  - **点击效果：** 打开容器标签打印对话框
- **扫码查询按钮：**
  - **默认状态：** 紫色边框，"扫码查询"
  - **点击效果：** 启动扫码功能查询容器

#### **搜索筛选区域**
- **搜索输入框：**
  - **占位符：** "输入容器编码或名称搜索"
  - **搜索按钮：** 蓝色背景，放大镜图标
  - **扫码输入：** 支持扫码枪直接输入
- **筛选条件：**
  - **容器类型：** 下拉多选，铁架/托盘/其他
  - **容器状态：** 下拉多选，空闲/装载/在库/维修
  - **所在库位：** 级联选择，仓库-库区-货架-库位
  - **时间范围：** 日期选择器，按创建或更新时间筛选

#### **容器列表**
- **列表表头：**
  - **容器编码：** 支持排序，点击可查看详情
  - **容器类型：** 显示类型图标和名称
  - **当前状态：** 彩色状态标签
  - **装载情况：** 进度条显示装载率
  - **所在位置：** 显示当前库位
  - **最后操作：** 显示最后操作时间
  - **操作：** 查看、编辑、维护等按钮
- **容器状态标识：**
  - **空闲：** 灰色圆点，"空闲可用"
  - **装载中：** 黄色圆点，"装载中"
  - **已满载：** 绿色圆点，"已满载"
  - **在库：** 蓝色圆点，"在库存储"
  - **维修中：** 红色圆点，"维修中"
  - **报废：** 黑色圆点，"已报废"

#### **容器详情面板**
- **基本信息卡片：**
  - **容器编码：** 大号字体显示，支持复制
  - **容器类型：** 显示类型名称和图标
  - **创建时间：** 显示注册时间
  - **当前状态：** 彩色状态标签
  - **所在位置：** 显示完整库位路径
- **物理属性：**
  - **外形尺寸：** 长×宽×高（厘米）
  - **承重能力：** 最大承重（公斤）
  - **容积：** 有效容积（立方米）
  - **材质：** 容器材质说明
- **装载信息：**
  - **装载产品：** 表格显示装载的产品列表
  - **装载重量：** 当前装载重量/最大承重
  - **装载体积：** 当前装载体积/最大容积
  - **装载时间：** 最后装载操作时间

#### **装载产品列表**
- **产品信息展示：**
  - **产品编码：** 显示产品唯一标识
  - **产品名称：** 显示产品名称和规格
  - **装载数量：** 显示装载的数量
  - **装载时间：** 显示装载时间
  - **操作员：** 显示装载操作员
- **批量操作：**
  - **全选复选框：** 控制产品列表的全选
  - **批量出库：** 批量将选中产品出库
  - **批量调拨：** 批量调拨到其他容器
  - **打印清单：** 打印装载产品清单

#### **容器操作对话框**
- **新增容器：**
  - **容器类型：** 下拉选择容器类型
  - **容器编码：** 自动生成或手动输入
  - **容器名称：** 输入框，可选项
  - **物理属性：** 输入尺寸、承重等信息
  - **初始位置：** 选择容器初始存放位置
- **装载操作：**
  - **扫码区域：** 扫描产品条码进行装载
  - **产品信息：** 显示扫描的产品信息
  - **装载数量：** 输入装载数量
  - **容量检查：** 实时显示容器剩余容量
- **维护记录：**
  - **维护类型：** 选择维护类型（清洁/维修/检查）
  - **维护内容：** 文本域描述维护内容
  - **维护人员：** 选择维护人员
  - **维护时间：** 选择维护时间
  - **维护结果：** 选择维护结果（正常/需要进一步处理）

#### **容器标签打印**
- **打印范围：**
  - **当前容器：** 只打印当前选中容器
  - **批量打印：** 选择多个容器批量打印
  - **按类型打印：** 按容器类型批量打印
- **标签内容：**
  - **基础标签：** 容器编码和二维码
  - **详细标签：** 包含容器类型和属性
  - **位置标签：** 包含当前位置信息
- **打印设置：**
  - **标签规格：** 选择标签纸规格
  - **打印份数：** 设置打印份数
  - **打印机：** 选择目标打印机

#### **维护记录列表**
- **记录信息：**
  - **维护时间：** 显示维护日期和时间
  - **维护类型：** 显示维护类型图标
  - **维护内容：** 显示维护内容摘要
  - **维护人员：** 显示维护人员姓名
  - **维护结果：** 显示维护结果状态
- **操作功能：**
  - **查看详情：** 查看维护记录详细信息
  - **添加记录：** 添加新的维护记录
  - **导出记录：** 导出维护记录到Excel

### 数据校验规则：

#### **容器编码**
- **校验规则：** 编码必须全局唯一，符合编码规则
- **错误提示文案：** "容器编码已存在或格式不正确"

#### **装载容量**
- **校验规则：** 装载重量不能超过容器承重，体积不能超过容器容积
- **错误提示文案：** "装载量超出容器承载能力"

#### **状态变更**
- **校验规则：** 只有空闲状态的容器才能进行装载操作
- **错误提示文案：** "容器当前状态不允许此操作"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **容器基本信息**:
  - **容器编码 (container_code)**: String, 必填, 唯一标识
  - **容器类型 (container_type)**: String, 必填, 铁架/托盘/其他
  - **容器名称 (container_name)**: String, 可选, 最大50字符
  - **物理属性 (physical_attrs)**: Object, 尺寸承重信息
- **装载信息**:
  - **产品编码 (product_code)**: String, 必填, 装载产品标识
  - **装载数量 (loaded_quantity)**: Number, 必填, 装载数量
  - **装载时间 (loaded_time)**: DateTime, 自动记录
  - **操作员 (operator)**: String, 必填, 操作员标识

### 展示数据
- **容器列表**: 编码、类型、状态、位置、装载情况
- **装载详情**: 装载产品列表、重量体积统计
- **统计信息**: 容器总数、利用率、周转率
- **维护记录**: 维护历史、维护计划、维护状态

### 空状态/零数据
- **无容器**: 显示"暂无容器信息，请先添加容器"
- **无装载**: 显示"容器为空，暂无装载产品"
- **无维护记录**: 显示"暂无维护记录"

### API接口
- **获取容器列表**: GET /api/containers
- **创建容器**: POST /api/containers
- **更新容器**: PUT /api/containers/{id}
- **容器装载**: POST /api/containers/{id}/load
- **容器出库**: POST /api/containers/{id}/unload

## 5. 异常与边界处理 (Error & Edge Cases)

### **容器超载**
- **提示信息**: "装载量超出容器承载能力，请减少装载数量"
- **用户操作**: 显示当前装载情况，提供装载建议

### **容器状态冲突**
- **提示信息**: "容器当前状态不允许此操作，请检查容器状态"
- **用户操作**: 显示容器当前状态，提供状态变更选项

### **扫码识别失败**
- **提示信息**: "无法识别容器条码，请检查条码是否清晰"
- **用户操作**: 提供手动输入选项，重新扫码功能

### **容器位置冲突**
- **提示信息**: "目标库位已被其他容器占用"
- **用户操作**: 推荐其他可用库位，显示库位占用情况

### **维护期间操作限制**
- **提示信息**: "容器正在维护中，暂时无法进行装载操作"
- **用户操作**: 显示维护信息，提供维护完成确认

### **数据同步异常**
- **提示信息**: "数据同步异常，请稍后重试"
- **用户操作**: 提供手动同步功能，显示同步状态

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持多种容器类型（铁架、托盘等）的管理
- [ ] 容器编码唯一，支持条码生成和打印
- [ ] 实时跟踪容器位置和装载状态
- [ ] 容器容量控制和超载预警
- [ ] 支持扫码装载和出库操作
- [ ] 装载产品信息完整记录和追溯
- [ ] 容器维护记录完整可查
- [ ] 支持容器利用率和周转率统计
- [ ] 界面支持移动端操作，适配PDA设备
- [ ] 容器状态变更实时更新
- [ ] 支持批量操作和批量打印
- [ ] 所有页面元素符合全局设计规范
- [ ] 操作响应时间<2秒，扫码识别成功率≥99%
