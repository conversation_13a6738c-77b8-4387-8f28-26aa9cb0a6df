# 功能模块规格说明书：变体库存管理模块

- **模块ID**: WMS-012
- **所属子系统**: 仓储管理子系统(WMS)
- **最后更新**: 2025-07-31

## 1. 用户故事 (User Stories)

- **As a** 仓管员, **I want to** 独立管理每个变体的库存记录, **so that** 精确掌握各规格物料的库存情况。
- **As a** 生产计划员, **I want to** 查看基础物料的所有变体库存汇总, **so that** 制定合理的生产计划和物料需求。
- **As a** 采购员, **I want to** 按变体设置安全库存和预警规则, **so that** 及时补充特定规格的物料。
- **As a** 销售人员, **I want to** 查询特定变体的可用库存, **so that** 为客户提供准确的规格和交期承诺。
- **As a** 仓库主管, **I want to** 分析变体库存的周转情况, **so that** 优化库存结构和降低库存成本。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 物料变体管理功能已启用
- 变体配置已完成
- 库存数据实时同步
- 用户具有相应权限

### 核心流程

#### 2.1 变体库存初始化流程
1. 系统检测启用变体管理的物料分类
2. 为现有库存数据关联变体信息
3. 建立变体库存独立记录
4. 设置变体安全库存和预警规则
5. 验证变体库存数据完整性
6. 生成变体库存初始化报告

#### 2.2 变体库存入库流程
1. 扫描物料条码，识别基础物料
2. 系统判断是否为变体物料
3. 如是变体物料，要求确认具体变体规格
4. 验证变体规格与单据一致性
5. 生成包含变体信息的批次号
6. 更新对应变体的库存记录
7. 触发变体库存预警检查

#### 2.3 变体库存出库流程
1. 接收出库需求（基础物料或具体变体）
2. 如为基础物料需求，展示所有可用变体
3. 根据业务规则推荐最优变体选择
4. 确认出库的具体变体规格
5. 检查变体库存可用量
6. 执行变体库存扣减
7. 记录变体出库明细

#### 2.4 变体库存查询流程
1. 选择查询模式：汇总查询或明细查询
2. **汇总查询**：按基础物料汇总所有变体库存
3. **明细查询**：查询具体变体的库存分布
4. 支持多维度筛选（仓库、库位、批次等）
5. 提供变体库存对比分析
6. 生成变体库存查询报告

#### 2.5 变体库存预警流程
1. 系统定时检查变体库存水位
2. 按变体独立判断是否触发预警
3. 生成变体库存预警清单
4. 按紧急程度对预警进行分级
5. 发送预警通知给相关人员
6. 提供变体补货建议

#### 2.6 变体库存调拨流程
1. 创建变体库存调拨申请
2. 指定源变体和目标变体（可相同）
3. 确认调拨数量和调拨原因
4. 执行源变体库存扣减
5. 执行目标变体库存增加
6. 记录变体调拨历史
7. 更新变体库存分布

### 后置条件
- 变体库存数据准确更新
- 库存事务记录完整
- 预警规则正常触发
- 相关报表数据同步

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：变体库存管理页面
### 页面目标：提供全面的变体库存管理和查询功能

### 信息架构：
- **左侧区域**：包含 物料分类树, 变体筛选, 快捷查询
- **中间区域**：包含 变体库存列表, 汇总视图, 明细视图
- **右侧区域**：包含 变体详情, 库存分析, 操作面板

### 交互逻辑与状态：

#### **变体筛选区域**
- **查询模式：**
  - **汇总模式：** 单选按钮，按基础物料汇总显示
  - **明细模式：** 单选按钮，按具体变体明细显示
- **变体筛选：**
  - **基础物料：** 输入框支持编码和名称搜索
  - **变体维度：** 动态生成的维度筛选器
  - **变体规格：** 下拉选择具体变体规格
  - **库存状态：** 多选正常、预警、不足等状态

#### **变体库存列表**
- **汇总视图：**
  - **基础物料信息：** 编码、名称、分类、图片
  - **变体统计：** 变体总数、有库存变体数
  - **库存汇总：** 总库存、可用库存、预留库存
  - **预警状态：** 显示是否有变体触发预警
  - **操作按钮：** 查看明细、库存分析、设置预警
- **明细视图：**
  - **变体信息：** 变体编码、变体名称、规格描述
  - **变体维度：** 显示具体的维度值
  - **库存数量：** 总库存、可用库存、在途库存
  - **库位分布：** 主要库位和分布情况
  - **安全库存：** 安全库存设置和当前状态
  - **最后更新：** 库存最后更新时间

#### **变体库存操作**
- **库存调整：**
  - **调整类型：** 盘盈、盘亏、报废、调拨
  - **调整数量：** 数字输入框，支持正负数
  - **调整原因：** 下拉选择或文本输入
  - **审批流程：** 根据调整金额触发审批
- **预警设置：**
  - **安全库存：** 为每个变体设置安全库存
  - **预警规则：** 设置预警触发条件
  - **通知设置：** 配置预警通知方式
  - **批量设置：** 支持批量设置相似变体

#### **变体库存分析**
- **库存分布图：**
  - **饼图：** 显示变体库存占比分布
  - **柱状图：** 显示各变体的库存数量
  - **趋势图：** 显示变体库存变化趋势
- **周转分析：**
  - **周转率：** 计算各变体的库存周转率
  - **周转天数：** 显示库存周转天数
  - **ABC分析：** 按价值和周转进行ABC分类
- **预警分析：**
  - **预警统计：** 统计各类预警的数量
  - **预警趋势：** 显示预警触发的趋势
  - **改进建议：** 提供库存优化建议

## 4. 数据规格 (Data Requirements)

### 输入数据
- **变体库存信息**:
  - **变体ID (variant_id)**: String, 必填, 变体唯一标识
  - **仓库编码 (warehouse_code)**: String, 必填, 仓库标识
  - **库位编码 (location_code)**: String, 必填, 库位标识
  - **库存数量 (stock_quantity)**: Number, 必填, 当前库存数量
  - **可用数量 (available_quantity)**: Number, 必填, 可用库存数量
  - **预留数量 (reserved_quantity)**: Number, 必填, 预留库存数量
- **变体预警配置**:
  - **安全库存 (safety_stock)**: Number, 必填, 安全库存数量
  - **最小库存 (min_stock)**: Number, 必填, 最小库存数量
  - **最大库存 (max_stock)**: Number, 可选, 最大库存数量
  - **预警级别 (alert_level)**: Enum, 必填, 低/中/高

### 展示数据
- **变体库存汇总**: 基础物料、变体统计、库存汇总、预警状态
- **变体库存明细**: 变体信息、库存分布、批次明细、预警状态
- **库存分析数据**: 周转率、ABC分类、趋势分析、预警统计
- **操作历史**: 库存变动记录、调整历史、预警历史

### 空状态/零数据
- **无变体库存**: 显示"该物料暂无变体库存记录"
- **无库存数据**: 显示"暂无库存数据，请检查筛选条件"
- **无预警信息**: 显示"当前无库存预警信息"

### API接口
- **获取变体库存汇总**: GET /api/variant-inventory/summary
- **获取变体库存明细**: GET /api/variant-inventory/details
- **变体库存调整**: POST /api/variant-inventory/adjust
- **变体预警设置**: PUT /api/variant-inventory/alert-config
- **变体库存分析**: GET /api/variant-inventory/analysis

## 5. 异常与边界处理 (Error & Edge Cases)

### **变体信息缺失**
- **提示信息**: "该库存记录缺少变体信息，请联系管理员处理"
- **用户操作**: 提供变体信息补充功能，支持批量处理

### **变体库存不一致**
- **提示信息**: "变体库存数据不一致，建议进行库存盘点"
- **用户操作**: 提供库存核对功能，支持差异调整

### **预警配置冲突**
- **提示信息**: "预警配置存在冲突，请检查安全库存设置"
- **用户操作**: 高亮冲突项，提供配置修正建议

### **变体库存不足**
- **提示信息**: "该变体库存不足，无法满足出库需求"
- **用户操作**: 提供替代变体建议，支持部分出库

## 6. 验收标准 (Acceptance Criteria)

- [ ] 变体库存独立管理功能正常工作
- [ ] 变体库存汇总和明细查询准确
- [ ] 变体预警规则设置和触发正常
- [ ] 变体库存调整和审批流程完整
- [ ] 变体库存分析报告准确生成
- [ ] 变体库存数据与其他模块同步
- [ ] 所有异常情况得到妥善处理
- [ ] 界面响应时间<2秒（千级变体）
- [ ] 支持并发操作，数据一致性保证
- [ ] 所有操作记录到审计日志
