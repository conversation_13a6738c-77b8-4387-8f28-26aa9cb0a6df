# 功能模块规格说明书：物流成本管理模块

- **模块ID**: WMS-011
- **所属子系统**: 仓储管理子系统(Warehouse Management System)
- **最后更新**: 2025-07-31

## 1. 用户故事 (User Stories)

- **As a** 物流成本分析师, **I want to** 计算和分析各项物流成本, **so that** 为成本控制和优化提供数据支持。
- **As a** 财务经理, **I want to** 监控物流成本预算执行情况, **so that** 确保物流成本在可控范围内。
- **As a** 物流经理, **I want to** 评估不同物流方案的成本效益, **so that** 选择最优的物流配送方案。
- **As a** 采购经理, **I want to** 分析物流供应商的成本表现, **so that** 优化供应商选择和合作策略。
- **As a** 管理层, **I want to** 查看物流成本报告和趋势分析, **so that** 制定物流成本控制策略。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 运输配送任务已在WMS-010中执行
- 物流供应商信息已录入系统
- 成本核算规则已配置
- 用户具有成本管理权限

### 核心流程

#### 2.1 物流成本核算流程
1. 收集配送任务的基础数据
2. 计算直接运输成本
3. 分摊间接物流成本
4. 核算人工和设备成本
5. 计算保险和风险成本
6. 汇总总物流成本
7. 分配成本到具体订单
8. 生成成本核算报告
9. 更新成本数据库

#### 2.2 运费计算管理流程
1. 获取货物重量体积信息
2. 确定配送距离和路线
3. 选择适用的计费规则
4. 计算基础运输费用
5. 加算附加费用项目
6. 应用折扣和优惠政策
7. 生成运费清单
8. 审核运费计算结果
9. 确认并记录运费

#### 2.3 供应商成本评估流程
1. 收集供应商报价信息
2. 分析服务质量指标
3. 评估时效性表现
4. 计算综合成本效益
5. 对比不同供应商表现
6. 生成供应商评估报告
7. 制定供应商优化建议
8. 更新供应商评级
9. 调整合作策略

#### 2.4 成本分析优化流程
1. 收集历史成本数据
2. 分析成本构成和趋势
3. 识别成本异常和问题
4. 分析成本驱动因素
5. 制定成本优化方案
6. 评估优化方案效果
7. 实施成本控制措施
8. 监控优化效果
9. 持续改进成本管理

### 后置条件
- 物流成本准确核算
- 成本数据实时更新
- 成本分析报告生成
- 优化建议及时提供

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：物流成本管理页面
### 页面目标：提供物流成本核算、分析和优化管理功能

### 信息架构：
- **顶部区域**：包含 成本查询, 核算计算, 报表生成, 数据导出
- **左侧区域**：包含 成本类型, 时间筛选, 供应商筛选, 区域筛选
- **中间区域**：包含 成本概览, 详细分析, 趋势图表
- **右侧区域**：包含 关键指标, 预警信息, 快速操作

### 交互逻辑与状态：

#### **成本查询筛选区域**
- **基础查询：**
  - **订单编号：** 输入框，查询特定订单成本
  - **客户名称：** 输入框，支持模糊搜索
  - **成本类型：** 下拉选择，运输/仓储/人工/设备/其他
  - **核算状态：** 下拉选择，待核算/已核算/已审核/已结算
- **时间筛选：**
  - **核算期间：** 日期范围选择器
  - **发生时间：** 日期范围选择器
  - **结算时间：** 日期范围选择器
  - **快速选择：** 按钮，本月/本季/本年
- **高级筛选：**
  - **成本范围：** 数值范围输入框
  - **供应商：** 多选下拉，选择物流供应商
  - **配送区域：** 多选下拉，选择配送区域
  - **车辆类型：** 复选框，小型/中型/大型车

#### **成本概览仪表板**
- **总体指标：**
  - **总成本：** 显示当期总物流成本
  - **平均成本：** 显示单票平均成本
  - **成本率：** 显示成本占收入比例
  - **预算执行率：** 显示预算执行情况
- **成本构成：**
  - **运输成本：** 饼图显示运输成本占比
  - **仓储成本：** 饼图显示仓储成本占比
  - **人工成本：** 饼图显示人工成本占比
  - **其他成本：** 饼图显示其他成本占比
- **趋势分析：**
  - **成本趋势：** 折线图显示成本变化趋势
  - **同比分析：** 柱状图显示同比成本变化
  - **环比分析：** 柱状图显示环比成本变化
  - **预算对比：** 对比图显示预算与实际差异

#### **运费计算界面**
- **基础信息：**
  - **货物重量：** 数字输入框，货物总重量(kg)
  - **货物体积：** 数字输入框，货物总体积(m³)
  - **配送距离：** 数字输入框，配送距离(km)
  - **货物类型：** 下拉选择，普通/易碎/危险品/冷链
- **计费规则：**
  - **计费方式：** 单选按钮，按重量/按体积/按距离/按件数
  - **基础费率：** 数字输入框，基础运费费率
  - **起步价：** 数字输入框，最低运费标准
  - **阶梯价格：** 表格编辑，阶梯计费规则
- **附加费用：**
  - **燃油附加费：** 数字输入框，燃油附加费率
  - **保险费：** 数字输入框，货物保险费
  - **装卸费：** 数字输入框，装卸服务费
  - **等待费：** 数字输入框，等待时间费用
  - **过路费：** 数字输入框，过路过桥费
  - **停车费：** 数字输入框，停车费用
- **优惠政策：**
  - **客户折扣：** 数字输入框，客户折扣率
  - **批量优惠：** 数字输入框，批量运输优惠
  - **长期合作优惠：** 数字输入框，长期合作折扣
  - **促销优惠：** 数字输入框，促销活动优惠
- **费用汇总：**
  - **基础运费：** 只读显示，计算的基础运费
  - **附加费用：** 只读显示，各项附加费用合计
  - **优惠金额：** 只读显示，各项优惠金额合计
  - **应收运费：** 只读显示，最终应收运费

#### **成本核算界面**
- **直接成本：**
  - **运输费用：** 数字输入框，实际运输费用
  - **燃油费用：** 数字输入框，燃油消耗费用
  - **过路费用：** 数字输入框，过路过桥费用
  - **装卸费用：** 数字输入框，装卸作业费用
- **间接成本：**
  - **车辆折旧：** 数字输入框，车辆折旧分摊
  - **保险费用：** 数字输入框，车辆保险分摊
  - **维修保养：** 数字输入框，维修保养分摊
  - **管理费用：** 数字输入框，管理费用分摊
- **人工成本：**
  - **司机工资：** 数字输入框，司机工资成本
  - **装卸工资：** 数字输入框，装卸工工资
  - **管理工资：** 数字输入框，管理人员工资
  - **社保费用：** 数字输入框，社保费用分摊
- **其他成本：**
  - **仓储费用：** 数字输入框，仓储相关费用
  - **包装费用：** 数字输入框，包装材料费用
  - **信息费用：** 数字输入框，信息系统费用
  - **其他费用：** 数字输入框，其他相关费用
- **成本分配：**
  - **分配方式：** 单选按钮，按重量/按体积/按金额/按件数
  - **分配比例：** 表格编辑，各订单分配比例
  - **分配结果：** 只读显示，各订单分配成本
  - **核算说明：** 文本域，成本核算说明

#### **供应商成本分析**
- **供应商列表：**
  - **供应商名称：** 显示物流供应商名称
  - **合作时间：** 显示合作开始时间
  - **服务范围：** 显示服务区域和类型
  - **合作规模：** 显示年度合作金额
  - **成本水平：** 显示平均成本水平
  - **服务评级：** 显示服务质量评级
- **成本对比：**
  - **单价对比：** 表格对比各供应商单价
  - **总成本对比：** 柱状图对比总成本
  - **成本构成：** 饼图显示成本构成差异
  - **性价比分析：** 散点图分析性价比
- **服务质量：**
  - **准时率：** 显示配送准时率
  - **完好率：** 显示货物完好率
  - **客户满意度：** 显示客户满意度评分
  - **投诉率：** 显示客户投诉率
- **综合评估：**
  - **成本评分：** 显示成本竞争力评分
  - **质量评分：** 显示服务质量评分
  - **综合评分：** 显示综合评估评分
  - **推荐等级：** 显示供应商推荐等级

#### **成本分析报告**
- **成本趋势分析：**
  - **月度趋势：** 折线图显示月度成本趋势
  - **季度对比：** 柱状图显示季度成本对比
  - **年度分析：** 显示年度成本分析结果
  - **预测分析：** 显示成本预测趋势
- **成本结构分析：**
  - **成本构成：** 饼图显示各类成本占比
  - **成本驱动：** 分析主要成本驱动因素
  - **成本效率：** 分析成本效率指标
  - **对标分析：** 与行业标准对比分析
- **异常分析：**
  - **成本异常：** 识别成本异常项目
  - **原因分析：** 分析异常原因
  - **影响评估：** 评估异常影响程度
  - **改进建议：** 提供改进建议
- **优化建议：**
  - **成本优化：** 提供成本优化建议
  - **流程改进：** 提供流程改进建议
  - **供应商优化：** 提供供应商优化建议
  - **预期效果：** 预估优化效果

### 数据校验规则：

#### **成本金额**
- **校验规则：** 成本金额必须大于等于0，格式正确
- **错误提示文案：** "成本金额格式不正确，请输入有效数值"

#### **费率设置**
- **校验规则：** 费率必须在合理范围内，不能为负数
- **错误提示文案：** "费率设置不合理，请检查费率范围"

#### **分配比例**
- **校验规则：** 分配比例总和必须等于100%
- **错误提示文案：** "分配比例总和不等于100%，请调整分配比例"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **成本核算**:
  - **订单ID (order_id)**: String, 必填, 引用订单信息
  - **成本类型 (cost_type)**: String, 必填, 运输/仓储/人工/设备/其他
  - **成本金额 (cost_amount)**: Decimal, 必填, 大于等于0
  - **核算日期 (accounting_date)**: Date, 必填
- **运费计算**:
  - **货物重量 (weight)**: Decimal, 必填, 单位kg
  - **货物体积 (volume)**: Decimal, 必填, 单位m³
  - **配送距离 (distance)**: Decimal, 必填, 单位km
  - **计费方式 (billing_method)**: String, 必填, 计费方式枚举
- **供应商评估**:
  - **供应商ID (supplier_id)**: String, 必填, 引用供应商主数据
  - **评估期间 (evaluation_period)**: String, 必填, 评估时间范围
  - **成本评分 (cost_score)**: Integer, 必填, 1-100分
  - **质量评分 (quality_score)**: Integer, 必填, 1-100分

### 展示数据
- **成本概览**: 总成本、平均成本、成本率、预算执行率
- **成本明细**: 各类成本明细、成本构成、成本分配
- **供应商分析**: 供应商成本对比、服务质量、综合评估
- **趋势分析**: 成本趋势、同比环比、预测分析

### 空状态/零数据
- **无成本数据**: 显示"暂无成本数据"
- **无供应商**: 显示"暂无供应商信息"
- **无分析结果**: 显示"暂无分析结果"

### API接口
- **成本核算**: GET/POST/PUT /api/logistics-cost/accounting
- **运费计算**: POST /api/logistics-cost/freight-calculation
- **供应商评估**: GET/POST /api/logistics-cost/supplier-evaluation
- **成本分析**: GET /api/logistics-cost/analysis
- **成本报告**: POST /api/logistics-cost/reports

## 5. 异常与边界处理 (Error & Edge Cases)

### **成本数据缺失**
- **提示信息**: "成本数据不完整，无法进行准确核算"
- **用户操作**: 提供数据补录和估算选项

### **费率配置错误**
- **提示信息**: "费率配置存在错误，请检查计费规则"
- **用户操作**: 提供费率检查和修正功能

### **分配比例异常**
- **提示信息**: "成本分配比例异常，请重新设置"
- **用户操作**: 提供自动调整和手动修正选项

### **供应商数据异常**
- **提示信息**: "供应商数据异常，影响成本分析准确性"
- **用户操作**: 提供数据验证和修正功能

### **计算结果异常**
- **提示信息**: "成本计算结果异常，请检查基础数据"
- **用户操作**: 提供重新计算和人工审核选项

## 6. 验收标准 (Acceptance Criteria)

- [ ] 物流成本核算功能准确，计算逻辑正确
- [ ] 运费计算功能完整，支持多种计费方式
- [ ] 供应商成本分析功能有效，评估维度全面
- [ ] 成本分析报告功能完善，数据准确
- [ ] 成本预警功能及时，阈值设置合理
- [ ] 与运输配送系统(WMS-010)集成正常
- [ ] 与财务系统集成正常，数据同步准确
- [ ] 支持多币种和汇率转换
- [ ] 数据校验规则完善，错误提示友好
- [ ] 所有页面元素符合全局设计规范
- [ ] 响应时间<5秒，支持大数据量处理
- [ ] 操作日志完整，支持审计追踪
