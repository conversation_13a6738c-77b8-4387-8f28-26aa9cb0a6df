# 功能模块规格说明书：智能拣货模块

- **模块ID**: WMS-005
- **所属子系统**: 仓储管理子系统(WMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 仓管员, **I want to** 按照系统优化的拣货路径执行拣货, **so that** 提高拣货效率和减少行走距离。
- **As a** 仓管员, **I want to** 通过PDA扫码确认拣货数量, **so that** 确保拣货准确性和实时更新库存。
- **As a** 仓管员, **I want to** 系统自动按FIFO策略推荐批次, **so that** 确保先进先出和库存周转。
- **As a** 仓库主管, **I want to** 监控拣货进度和效率, **so that** 优化拣货作业安排和人员配置。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 出库订单已审核通过
- 库存数据准确完整
- 拣货人员已分配
- PDA设备正常工作

### 核心流程

#### 2.1 拣货任务生成流程
1. 系统接收出库订单或拣货需求
2. 检查库存可用性和批次信息
3. 按FIFO策略分配具体批次和库位
4. 计算最优拣货路径和顺序
5. 生成拣货任务单和拣货清单
6. 分配拣货人员并推送任务

#### 2.2 拣货路径优化流程
1. 分析所有待拣货库位的位置
2. 考虑仓库布局和通道限制
3. 应用路径优化算法计算最短路径
4. 考虑拣货工具和容器限制
5. 生成最优拣货顺序和路径指引
6. 支持动态路径调整和重新规划

#### 2.3 拣货执行流程
1. 拣货员扫描拣货任务条码
2. PDA显示第一个拣货位置和路径
3. 到达库位后扫描库位条码确认
4. 扫描物料批次条码确认批次
5. 录入实际拣货数量
6. 系统验证数量并更新库存
7. 指引到下一个拣货位置
8. 重复直到完成所有拣货

#### 2.4 拣货异常处理流程
1. 发现库存不足或批次异常
2. 系统提示异常信息和处理选项
3. 选择替代批次或部分拣货
4. 记录异常原因和处理方式
5. 更新拣货任务和库存状态
6. 通知相关人员处理异常

#### 2.5 拣货完成流程
1. 完成所有拣货项目
2. 系统生成拣货完成报告
3. 打印拣货清单和出库标签
4. 将拣货物料转移到出库区
5. 更新订单状态为"已拣货"
6. 记录拣货完成时间和效率

### 后置条件
- 库存数据准确更新
- 拣货任务状态完成
- 物料准确拣选到位
- 拣货效率数据记录

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：智能拣货操作页面
### 页面目标：提供高效便捷的智能拣货操作界面

### 信息架构：
- **顶部区域**：包含 任务信息, 进度指示, 路径导航
- **中间区域**：包含 当前拣货项, 库位信息, 扫码区域
- **底部区域**：包含 操作按钮, 异常处理, 完成确认

### 交互逻辑与状态：

#### **任务信息显示**
- **任务基本信息：**
  - **任务编号：** 显示拣货任务唯一编号
  - **订单信息：** 显示关联的出库订单号
  - **客户信息：** 显示客户名称和要求
  - **紧急程度：** 显示任务优先级和紧急程度
  - **预计时间：** 显示预计完成时间
- **任务统计：**
  - **总拣货项：** 显示总拣货项目数量
  - **已完成：** 显示已完成的拣货项数
  - **剩余项目：** 显示剩余待拣货项数
  - **完成率：** 环形进度条显示完成百分比

#### **进度指示器**
- **整体进度：**
  - **进度条：** 蓝色进度条显示整体完成进度
  - **百分比：** 数字显示完成百分比
  - **预计剩余时间：** 显示预计剩余完成时间
- **当前状态：**
  - **进行中：** 绿色圆点，"拣货进行中"
  - **暂停：** 黄色圆点，"任务暂停"
  - **异常：** 红色圆点，"存在异常"
  - **完成：** 蓝色圆点，"拣货完成"

#### **路径导航区域**
- **当前位置：**
  - **位置显示：** 显示当前所在库位
  - **位置图标：** 蓝色定位图标
- **目标位置：**
  - **目标库位：** 显示下一个拣货库位
  - **距离信息：** 显示到目标位置的距离
  - **路径指引：** 箭头指示前进方向
- **路径优化：**
  - **最优路径：** 显示系统推荐的最优路径
  - **路径调整：** 支持手动调整拣货顺序
  - **重新规划：** 重新计算最优路径

#### **当前拣货项信息**
- **物料信息卡片：**
  - **物料编码：** 大号字体显示物料编码
  - **物料名称：** 显示物料名称和规格
  - **物料图片：** 显示物料图片（如有）
  - **拣货数量：** 显示需要拣货的数量
  - **单位：** 显示计量单位
- **批次信息：**
  - **推荐批次：** 显示FIFO推荐的批次号
  - **生产日期：** 显示批次生产日期
  - **有效期：** 显示批次有效期
  - **库存数量：** 显示该批次的库存数量
- **库位信息：**
  - **库位编码：** 显示目标库位编码
  - **库位路径：** 显示完整的库位路径
  - **库位状态：** 显示库位当前状态
  - **到达指引：** 显示到达库位的指引

#### **扫码操作区域**
- **扫码状态指示：**
  - **待扫库位：** 橙色虚线框，"请扫描库位条码"
  - **待扫批次：** 蓝色虚线框，"请扫描批次条码"
  - **扫码成功：** 绿色实线框，显示扫码结果
  - **扫码错误：** 红色边框，显示错误信息
- **扫码验证：**
  - **库位验证：** 验证扫描的库位是否正确
  - **批次验证：** 验证扫描的批次是否匹配
  - **数量验证：** 验证库存数量是否充足
- **手动输入：**
  - **库位输入：** 手动输入库位编码
  - **批次输入：** 手动输入批次号
  - **数量调整：** 手动调整拣货数量

#### **数量确认区域**
- **数量输入：**
  - **实拣数量：** 数字输入框录入实际拣货数量
  - **快捷按钮：** +1、+10、全拣等快捷按钮
  - **数量验证：** 实时验证输入数量的合理性
- **数量对比：**
  - **计划数量：** 显示计划拣货数量
  - **实拣数量：** 显示实际拣货数量
  - **差异显示：** 显示数量差异和原因
- **库存更新：**
  - **剩余库存：** 显示拣货后的剩余库存
  - **库存状态：** 显示库存状态变化
  - **更新确认：** 确认库存数据更新

#### **异常处理区域**
- **异常类型：**
  - **库存不足：** 实际库存少于拣货需求
  - **批次异常：** 推荐批次不可用或过期
  - **库位异常：** 库位被占用或不可访问
  - **物料异常：** 物料损坏或质量问题
- **处理选项：**
  - **替代批次：** 选择其他可用批次
  - **部分拣货：** 按实际库存部分拣货
  - **跳过项目：** 暂时跳过当前项目
  - **联系主管：** 联系仓库主管处理
- **异常记录：**
  - **异常原因：** 记录异常发生的原因
  - **处理方式：** 记录采取的处理方式
  - **处理人员：** 记录处理异常的人员
  - **处理时间：** 记录异常处理时间

#### **操作按钮组**
- **确认拣货按钮：**
  - **默认状态：** 绿色背景(#52C41A)，"确认拣货"
  - **禁用状态：** 扫码未完成或数量未录入时禁用
  - **点击效果：** 确认当前项目拣货完成
- **跳过项目按钮：**
  - **默认状态：** 橙色边框，"跳过项目"
  - **点击效果：** 跳过当前项目，进入下一项
- **暂停任务按钮：**
  - **默认状态：** 黄色边框，"暂停任务"
  - **点击效果：** 暂停当前拣货任务
- **完成任务按钮：**
  - **默认状态：** 蓝色背景(#1890FF)，"完成任务"
  - **显示条件：** 所有项目完成后显示
  - **点击效果：** 完成整个拣货任务

#### **拣货清单预览**
- **清单列表：**
  - **物料信息：** 显示物料编码、名称、规格
  - **拣货数量：** 显示计划和实际拣货数量
  - **拣货状态：** 显示每项的拣货状态
  - **库位信息：** 显示拣货库位
  - **批次信息：** 显示拣货批次
- **状态标识：**
  - **待拣货：** 灰色圆点，"待拣货"
  - **拣货中：** 黄色圆点，"拣货中"
  - **已完成：** 绿色圆点，"已完成"
  - **有异常：** 红色圆点，"有异常"
  - **已跳过：** 橙色圆点，"已跳过"

#### **完成确认对话框**
- **完成统计：**
  - **总拣货项：** 显示总拣货项目数
  - **成功完成：** 显示成功完成的项目数
  - **异常项目：** 显示存在异常的项目数
  - **跳过项目：** 显示跳过的项目数
- **时间统计：**
  - **开始时间：** 显示任务开始时间
  - **完成时间：** 显示任务完成时间
  - **总用时：** 显示总拣货用时
  - **效率评估：** 显示拣货效率评估
- **后续操作：**
  - **打印清单：** 打印拣货完成清单
  - **生成标签：** 生成出库标签
  - **转移出库：** 将物料转移到出库区
  - **新任务：** 接收新的拣货任务

#### **拣货效率统计**
- **实时统计：**
  - **拣货速度：** 显示当前拣货速度（件/小时）
  - **准确率：** 显示拣货准确率
  - **路径效率：** 显示路径优化效果
  - **时间利用率：** 显示时间利用效率
- **历史对比：**
  - **个人最佳：** 显示个人历史最佳成绩
  - **团队平均：** 显示团队平均水平
  - **改进建议：** 提供效率改进建议

### 数据校验规则：

#### **拣货数量**
- **校验规则：** 拣货数量不能超过库存数量，不能为负数
- **错误提示文案：** "拣货数量超出库存或输入无效"

#### **批次验证**
- **校验规则：** 批次必须存在且状态正常，未过期
- **错误提示文案：** "批次不存在、已过期或状态异常"

#### **库位验证**
- **校验规则：** 库位必须存在且可访问
- **错误提示文案：** "库位不存在或当前不可访问"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **拣货任务**:
  - **任务编号 (task_no)**: String, 自动生成, 唯一标识
  - **订单编号 (order_no)**: String, 必填, 关联出库订单
  - **拣货项目 (picking_items)**: Array, 必填, 拣货项目列表
  - **拣货人员 (picker)**: String, 必填, 拣货人员标识
- **拣货记录**:
  - **物料编码 (material_code)**: String, 必填, 物料标识
  - **批次号 (batch_no)**: String, 必填, 拣货批次
  - **拣货数量 (picked_quantity)**: Number, 必填, 实际拣货数量
  - **拣货时间 (picked_time)**: DateTime, 自动记录

### 展示数据
- **拣货任务**: 任务信息、进度状态、拣货清单
- **路径导航**: 当前位置、目标位置、路径指引
- **拣货项目**: 物料信息、批次信息、库位信息
- **效率统计**: 拣货速度、准确率、时间统计

### 空状态/零数据
- **无拣货任务**: 显示"暂无拣货任务"
- **无库存**: 显示"库存不足，无法拣货"
- **无可用批次**: 显示"无可用批次"

### API接口
- **获取拣货任务**: GET /api/picking/tasks
- **确认拣货**: POST /api/picking/confirm
- **路径优化**: GET /api/picking/route-optimize
- **完成任务**: POST /api/picking/complete

## 5. 异常与边界处理 (Error & Edge Cases)

### **库存不足**
- **提示信息**: "库存不足，实际库存X件，需求Y件"
- **用户操作**: 提供部分拣货选项，推荐替代批次

### **批次过期**
- **提示信息**: "推荐批次已过期，请选择其他批次"
- **用户操作**: 自动推荐其他可用批次

### **库位被占用**
- **提示信息**: "目标库位被占用，请稍后再试"
- **用户操作**: 提供等待选项或推荐其他库位

### **扫码设备故障**
- **提示信息**: "扫码设备异常，请检查设备或手动输入"
- **用户操作**: 提供手动输入选项

### **网络连接中断**
- **提示信息**: "网络连接中断，数据将在恢复后同步"
- **用户操作**: 本地缓存操作，网络恢复后同步

### **路径计算失败**
- **提示信息**: "路径计算失败，使用默认顺序"
- **用户操作**: 提供手动排序选项

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持FIFO策略的智能批次推荐
- [ ] 路径优化算法减少行走距离≥30%
- [ ] 扫码确认响应时间<1秒，准确率≥99.8%
- [ ] 实时库存更新，数据准确率≥99.5%
- [ ] 支持拣货异常处理和替代方案
- [ ] 拣货效率统计和分析功能完善
- [ ] 界面适配PDA设备，操作便捷
- [ ] 支持离线拣货和数据同步
- [ ] 拣货任务状态实时跟踪
- [ ] 支持多种拣货模式（单品拣货、批量拣货）
- [ ] 拣货完成率≥98%，准确率≥99.5%
- [ ] 所有页面元素符合全局设计规范
- [ ] 拣货效率提升≥40%
