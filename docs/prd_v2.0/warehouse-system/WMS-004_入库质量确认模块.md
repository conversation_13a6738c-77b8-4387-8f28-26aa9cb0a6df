# 功能模块规格说明书：入库质量确认模块

- **模块ID**: WMS-004
- **所属子系统**: 仓储管理子系统(WMS)
- **最后更新**: 2025-07-31
- **功能整合说明**: 本模块专注于入库时的质量状态确认，详细质量检验由QMS-003检验执行模块负责

## 1. 用户故事 (User Stories)

- **As a** 仓管员, **I want to** 快速确认物料的质量状态, **so that** 决定是否可以正常入库。
- **As a** 仓管员, **I want to** 查看QMS系统的检验结果, **so that** 根据质量状态进行分拣处理。
- **As a** 仓管员, **I want to** 自动获取质检结论, **so that** 避免重复检验和数据录入。
- **As a** 质量主管, **I want to** 确保所有入库物料都经过质量确认, **so that** 保证库存物料的质量可靠性。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 物料已完成收货待入库状态
- QMS-003检验执行模块已完成质量检验（如需要）
- 仓管员具有入库操作权限
- 仓储位置和容器已准备就绪

### 核心流程

#### 2.1 质量状态确认流程
1. 仓管员扫描物料批次条码
2. 系统自动调用QMS-003检验结果接口
3. 显示物料的质量状态（合格/不合格/免检）
4. 确认质量状态信息的准确性
5. 系统记录质量确认时间和操作员
6. 根据质量状态决定后续处理方式

#### 2.2 合格品入库流程
1. 质量状态确认为"合格"的物料
2. 系统自动分配合格品库位
3. 更新物料状态为"可用库存"
4. 生成入库单据和库存记录
5. 释放物料供正常出库使用
6. 记录入库质量确认信息

#### 2.3 不合格品隔离流程
1. 质量状态确认为"不合格"的物料
2. 系统自动分配隔离库位
3. 更新物料状态为"隔离库存"
4. 生成不合格品处理单
5. 通知质量部门进行后续处理
6. 记录隔离原因和处理要求

#### 2.4 免检物料处理流程
1. 质量状态确认为"免检"的物料
2. 系统直接分配正常库位
3. 更新物料状态为"可用库存"
4. 生成入库单据和库存记录
5. 记录免检依据和确认信息
3. 物料转移到不合格品隔离区
4. 生成不合格品处理单
5. 通知质量部门进行后续处理
6. 记录隔离原因和处理要求

### 系统集成说明
- **与QMS-003集成**: 通过API接口获取检验结果，避免重复检验
- **与WMS-003集成**: 收货完成后触发质量确认流程
- **与库存系统集成**: 根据质量状态更新库存可用性

### 后置条件
- 质量状态准确确认
- 物料库存状态正确更新
- 不合格品得到妥善隔离
- 质量确认记录完整保存

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：入库质量确认页面
### 页面目标：提供快速便捷的入库质量状态确认界面

### 信息架构：
- **顶部区域**：包含 物料信息, 扫码区域, 质量状态显示
- **中间区域**：包含 质量检验结果, 确认操作, 处理决定
- **底部区域**：包含 库位分配, 操作记录, 确认按钮

### 交互逻辑与状态：

#### **物料信息显示**
- **基本信息：**
  - **物料编码：** 显示物料唯一编码
  - **物料名称：** 显示物料名称和规格
  - **批次信息：** 显示批次号、生产日期、数量
  - **供应商：** 显示供应商名称和编码
  - **收货时间：** 显示收货完成时间
  - **待入库数量：** 显示待确认入库的数量
- **质量状态指示：**
  - **合格：** 绿色圆点，"质量合格"
  - **不合格：** 红色圆点，"质量不合格"
  - **免检：** 蓝色圆点，"免检物料"
  - **待检：** 橙色圆点，"等待检验"

#### **扫码区域**
- **扫码状态：**
  - **待扫码：** 蓝色虚线框，"请扫描批次条码"
  - **扫码成功：** 绿色实线框，显示扫码结果
  - **扫码失败：** 红色边框，"条码无效或不存在"
- **快捷操作：**
  - **重新扫码：** 重新扫描条码
  - **手动输入：** 手动输入批次号
  - **批次列表：** 查看所有待确认批次

#### **检验项目列表**
- **项目分类：**
  - **外观检验：** 外观、包装、标识等
  - **尺寸检验：** 长度、宽度、厚度等
  - **性能检验：** 强度、硬度、透明度等
  - **化学检验：** 成分、纯度、有害物质等
- **检验项目卡片：**
  - **项目名称：** 显示检验项目名称
  - **检验标准：** 显示标准要求和允许偏差
  - **检验方法：** 显示检验方法和工具
  - **必检标识：** 红色星号标识必检项目
  - **检验状态：** 显示当前项目检验状态
- **项目状态：**
  - **未检：** 灰色背景，等待检验
  - **检验中：** 黄色背景，正在检验
  - **合格：** 绿色背景，检验合格
  - **不合格：** 红色背景，检验不合格
  - **免检：** 蓝色背景，免检项目

#### **检验标准显示**
- **标准信息：**
  - **标准名称：** 显示适用的检验标准
  - **标准编号：** 显示标准编号和版本
  - **技术要求：** 显示具体的技术要求
  - **检验方法：** 显示检验方法和步骤
  - **判定规则：** 显示合格/不合格判定规则
- **标准查看：**
  - **展开详情：** 点击展开查看详细标准
  - **标准文档：** 查看完整的标准文档
  - **历史版本：** 查看标准的历史版本

#### **结果录入区域**
- **数值型检验：**
  - **测量值输入：** 数字输入框录入测量值
  - **单位显示：** 显示测量单位
  - **标准范围：** 显示标准要求的数值范围
  - **偏差计算：** 自动计算与标准值的偏差
  - **判定结果：** 自动判定合格/不合格
- **定性检验：**
  - **选择判定：** 单选按钮选择合格/不合格
  - **等级评定：** 下拉选择质量等级
  - **缺陷记录：** 记录发现的缺陷类型
  - **备注说明：** 文本框输入检验备注
- **照片上传：**
  - **拍照按钮：** 调用摄像头拍照
  - **相册选择：** 从相册选择照片
  - **照片预览：** 预览上传的照片
  - **照片标注：** 在照片上添加标注说明

#### **检验结果汇总**
- **结果统计：**
  - **总检验项：** 显示总检验项目数
  - **已检项目：** 显示已完成检验的项目数
  - **合格项目：** 显示检验合格的项目数
  - **不合格项目：** 显示检验不合格的项目数
- **综合判定：**
  - **整体结果：** 显示整批物料的综合判定结果
  - **合格率：** 显示检验项目的合格率
  - **主要缺陷：** 显示主要的不合格项目
  - **质量等级：** 显示最终的质量等级

#### **处理决定区域**
- **合格品处理：**
  - **正常入库：** 合格品正常入库到指定库位
  - **优先使用：** 标记为优先使用
  - **特殊标识：** 添加特殊质量标识
- **不合格品处理：**
  - **隔离存放：** 转移到不合格品隔离区
  - **退货处理：** 退回供应商处理
  - **返工处理：** 安排返工修复
  - **报废处理：** 直接报废处理
  - **让步接收：** 在一定条件下让步接收
- **处理说明：**
  - **处理原因：** 选择或输入处理原因
  - **处理方式：** 详细说明处理方式
  - **责任方：** 确定责任归属
  - **后续跟踪：** 设置后续跟踪要求

#### **操作按钮组**
- **保存检验按钮：**
  - **默认状态：** 蓝色边框，"保存检验"
  - **点击效果：** 保存当前检验进度
- **提交报告按钮：**
  - **默认状态：** 绿色背景(#52C41A)，"提交报告"
  - **禁用状态：** 必检项目未完成时禁用
  - **点击效果：** 提交完整的质检报告
- **打印报告按钮：**
  - **默认状态：** 橙色边框，"打印报告"
  - **点击效果：** 打印质检报告
- **处理物料按钮：**
  - **默认状态：** 紫色边框，"处理物料"
  - **点击效果：** 执行物料处理决定

#### **质检报告预览**
- **报告头部：**
  - **报告编号：** 质检报告唯一编号
  - **检验日期：** 检验执行日期
  - **检验员：** 执行检验的质检员
  - **审核员：** 报告审核人员
- **检验详情：**
  - **物料信息：** 完整的物料和批次信息
  - **检验项目：** 所有检验项目和结果
  - **检验数据：** 详细的检验数据记录
  - **照片附件：** 检验过程中的照片
- **结论建议：**
  - **检验结论：** 最终的检验结论
  - **处理建议：** 对物料的处理建议
  - **改进建议：** 对供应商的改进建议

#### **质检历史记录**
- **记录列表：**
  - **检验时间：** 显示检验完成时间
  - **物料信息：** 显示检验的物料信息
  - **检验结果：** 显示检验结果
  - **质检员：** 显示执行检验的质检员
  - **处理结果：** 显示最终处理结果
- **筛选功能：**
  - **时间筛选：** 按检验时间筛选
  - **物料筛选：** 按物料类型筛选
  - **结果筛选：** 按检验结果筛选
  - **质检员筛选：** 按质检员筛选

### 数据校验规则：

#### **检验数据**
- **校验规则：** 数值必须在合理范围内，必检项目不能为空
- **错误提示文案：** "检验数据超出合理范围或必检项目未完成"

#### **处理决定**
- **校验规则：** 不合格品必须选择处理方式
- **错误提示文案：** "不合格品必须选择处理方式"

#### **报告完整性**
- **校验规则：** 所有必检项目必须完成检验
- **错误提示文案：** "存在未完成的必检项目，无法提交报告"

## 4. 数据需求 (Data Requirements)

### 核心数据实体

#### **入库质量确认记录 (InboundQualityConfirmation)**
```sql
CREATE TABLE inbound_quality_confirmation (
    confirmation_id VARCHAR(32) PRIMARY KEY COMMENT '确认记录ID',
    batch_no VARCHAR(50) NOT NULL COMMENT '批次号',
    material_id VARCHAR(32) NOT NULL COMMENT '物料ID',
    supplier_id VARCHAR(32) COMMENT '供应商ID',
    qms_inspection_id VARCHAR(32) COMMENT 'QMS检验记录ID',
    quality_status VARCHAR(20) NOT NULL COMMENT '质量状态',
    confirmation_time DATETIME NOT NULL COMMENT '确认时间',
    confirmed_by VARCHAR(32) NOT NULL COMMENT '确认人',
    warehouse_location VARCHAR(50) COMMENT '分配库位',
    remarks TEXT COMMENT '备注说明',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### **质量状态变更记录 (QualityStatusChangeLog)**
```sql
CREATE TABLE quality_status_change_log (
    log_id VARCHAR(32) PRIMARY KEY COMMENT '日志ID',
    batch_no VARCHAR(50) NOT NULL COMMENT '批次号',
    old_status VARCHAR(20) COMMENT '原状态',
    new_status VARCHAR(20) NOT NULL COMMENT '新状态',
    change_reason VARCHAR(200) COMMENT '变更原因',
    changed_by VARCHAR(32) NOT NULL COMMENT '变更人',
    change_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 数据字典

#### **质量状态 (quality_status)**
- QUALIFIED: 合格
- UNQUALIFIED: 不合格
- EXEMPT: 免检
- PENDING: 待确认
- ISOLATED: 已隔离

### 系统集成接口

#### **QMS系统集成接口**
- **获取检验结果**: GET /api/qms/inspection-results/{batch_no}
- **质量状态同步**: POST /api/qms/quality-status-sync
- **检验报告获取**: GET /api/qms/inspection-reports/{inspection_id}

#### **WMS内部接口**
- **质量确认提交**: POST /api/wms/quality-confirmation
- **库位分配**: POST /api/wms/location-allocation
- **库存状态更新**: PUT /api/wms/inventory-status

### 展示数据
- **质量确认任务**: 待确认批次、质量状态、检验结果
- **确认历史**: 确认记录、状态变更、处理结果
- **质量统计**: 合格率、不合格率、处理效率

### 空状态/零数据
- **无待确认任务**: 显示"暂无待确认的入库物料"
- **无检验结果**: 显示"该批次暂无检验结果"
- **无历史记录**: 显示"暂无质量确认记录"

## 5. 错误处理与边界情况 (Error Handling & Edge Cases)

### 业务规则验证

#### **质量状态确认验证**
- **批次状态检查：** 验证批次是否处于可确认状态
- **权限验证：** 检查操作员是否有质量确认权限
- **重复确认检查：** 防止同一批次重复确认
- **库位可用性：** 验证分配库位的可用性

#### **QMS系统集成验证**
- **接口连通性：** 检查QMS系统接口的可用性
- **数据完整性：** 验证从QMS获取的检验数据完整性
- **状态一致性：** 确保WMS和QMS的质量状态一致
- **时效性验证：** 检查检验结果的时效性

### 异常情况处理

#### **系统异常**
- **QMS系统不可用：** QMS系统接口无法访问
  - 错误提示：QMS系统暂时不可用，请稍后重试
  - 处理方式：提供手动质量状态录入功能
- **库位分配失败：** 无法分配合适的库位
  - 错误提示：库位分配失败，请联系仓库管理员
  - 处理方式：提供手动库位选择功能

#### **业务异常**
- **检验结果缺失：** QMS系统中无该批次检验结果
  - 错误提示：该批次暂无检验结果，请联系质量部门
  - 处理方式：提供检验结果补录功能
- **质量状态冲突：** 质量状态与实际情况不符
  - 错误提示：质量状态异常，请核实后重新确认
  - 处理方式：提供状态修正和审批流程

### 边界情况

#### **特殊批次处理**
- **紧急批次：** 需要优先处理的紧急批次
- **大批量物料：** 超大批量物料的分批确认
- **混合批次：** 包含多种质量状态的混合批次

#### **系统限制**
- **并发确认：** 多人同时确认同一批次
- **历史数据：** 历史批次的补充确认
- **跨期处理：** 跨会计期间的质量确认

## 6. 验收标准 (Acceptance Criteria)

### 功能验收标准

#### **质量确认功能**
- [ ] 能够快速扫码获取批次质量状态
- [ ] 支持与QMS-003系统的实时数据集成
- [ ] 质量状态确认操作简洁高效
- [ ] 支持批量质量确认处理

#### **库位分配功能**
- [ ] 能够根据质量状态自动分配库位
- [ ] 支持合格品和不合格品的分离存储
- [ ] 库位分配规则可配置和优化
- [ ] 库位使用情况实时更新

#### **状态管理功能**
- [ ] 质量状态变更能够准确记录
- [ ] 支持质量状态的历史追溯
- [ ] 状态变更通知及时准确
- [ ] 异常状态能够及时处理

### 性能验收标准

#### **响应时间要求**
- [ ] 扫码获取质量状态响应时间 ≤ 2秒
- [ ] 质量确认提交响应时间 ≤ 3秒
- [ ] 库位分配完成时间 ≤ 5秒
- [ ] QMS系统集成响应时间 ≤ 3秒

#### **并发处理能力**
- [ ] 支持50个并发用户同时操作
- [ ] 支持同时处理100个批次确认
- [ ] 系统在高并发下保持稳定

### 集成验收标准

#### **系统集成**
- [ ] 与QMS-003质量检验模块集成正常
- [ ] 与WMS-003收货入库模块集成正常
- [ ] 与库存管理系统集成正常
- [ ] 数据同步准确及时

#### **用户体验**
- [ ] 界面设计符合仓库操作习惯
- [ ] 操作流程简洁高效
- [ ] 移动端适配良好，支持PDA操作
- [ ] 错误提示清晰明确

### 安全验收标准

#### **权限控制**
- [ ] 质量确认权限控制正确
- [ ] 数据访问权限严格控制
- [ ] 操作日志记录完整
- [ ] 敏感操作需要审批

#### **数据安全**
- [ ] 质量数据加密传输
- [ ] 确认记录不可篡改
- [ ] 数据备份恢复机制完善
- [ ] 审计跟踪功能完整
