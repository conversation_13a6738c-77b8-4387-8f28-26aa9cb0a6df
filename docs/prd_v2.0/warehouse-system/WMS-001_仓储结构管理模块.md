# 功能模块规格说明书：仓储结构管理模块

- **模块ID**: WMS-001
- **所属子系统**: 仓储管理子系统(WMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 仓库主管, **I want to** 建立仓库、库区、货架、库位的四级结构, **so that** 实现对库存物料的精确定位。
- **As a** 仓库主管, **I want to** 为每个库位设置唯一编码和属性, **so that** 确保库位管理的标准化和规范化。
- **As a** 仓管员, **I want to** 扫描库位码快速定位, **so that** 提高收发货作业效率。
- **As a** 系统管理员, **I want to** 批量生成和打印库位码标签, **so that** 快速完成仓库标识化改造。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有仓库管理权限
- 仓库物理布局已规划完成
- 库位编码规则已制定

### 核心流程

#### 2.1 仓储结构创建流程
1. 创建仓库基本信息（名称、地址、负责人）
2. 在仓库下创建库区（按功能或区域划分）
3. 在库区下创建货架（物理货架结构）
4. 在货架下创建库位（最小存储单元）
5. 为每个层级设置属性和编码规则
6. 生成并打印库位标识码

#### 2.2 库位属性配置流程
1. 设置库位基本属性（尺寸、承重、高度）
2. 配置适用物料类型和存储规则
3. 设置库位状态（可用、禁用、维修）
4. 配置库位容量和安全库存
5. 关联库位到具体的物理位置
6. 保存配置并生效

#### 2.3 库位码生成流程
1. 选择需要生成标签的库位范围
2. 确认库位编码和二维码内容
3. 选择标签模板和打印参数
4. 批量生成库位二维码
5. 预览打印效果并确认
6. 执行打印并记录打印日志

#### 2.4 结构维护流程
1. 检查库位是否有库存占用
2. 对于有库存的库位，先进行库存转移
3. 修改库位属性或删除库位
4. 更新相关的库存记录和系统配置
5. 重新生成受影响的库位标签
6. 通知相关人员结构变更

### 后置条件
- 仓储结构层次清晰完整
- 库位编码全局唯一
- 库位属性配置合理
- 库位标签准确可用

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：仓储结构管理页面
### 页面目标：提供完整的仓储结构创建、维护和管理功能

### 信息架构：
- **顶部区域**：包含 面包屑导航, 操作按钮组, 搜索筛选
- **左侧区域**：包含 仓储结构树, 层级导航, 快捷操作
- **右侧区域**：包含 详情信息面板, 属性配置, 操作历史

### 交互逻辑与状态：

#### **仓储结构树**
- **树形展示：**
  - **仓库节点：** 蓝色文件夹图标，显示仓库名称
  - **库区节点：** 绿色文件夹图标，显示库区名称
  - **货架节点：** 橙色货架图标，显示货架编号
  - **库位节点：** 灰色方块图标，显示库位编号
- **节点状态：**
  - **正常状态：** 黑色文字，可正常操作
  - **禁用状态：** 灰色文字，显示禁用原因
  - **有库存：** 绿色圆点标识，表示有库存占用
  - **维修中：** 红色感叹号，表示维修状态
- **交互操作：**
  - **单击节点：** 展开/收起子节点，右侧显示详情
  - **右键菜单：** 显示新增、编辑、删除、打印标签等操作
  - **拖拽操作：** 支持库位在货架间的拖拽移动

#### **操作按钮组**
- **新增仓库按钮：**
  - **默认状态：** 蓝色背景(#1890FF)，"新增仓库"
  - **点击效果：** 弹出仓库信息录入对话框
- **批量导入按钮：**
  - **默认状态：** 绿色边框，"批量导入"
  - **点击效果：** 打开Excel模板下载和导入功能
- **批量打印按钮：**
  - **默认状态：** 橙色边框，"批量打印"
  - **点击效果：** 打开库位标签批量打印对话框
- **导出结构按钮：**
  - **默认状态：** 灰色边框，"导出结构"
  - **点击效果：** 导出当前仓储结构到Excel

#### **搜索筛选区域**
- **搜索输入框：**
  - **占位符：** "输入库位编号或名称搜索"
  - **搜索图标：** 右侧放大镜图标
  - **实时搜索：** 输入时实时过滤树形结构
- **状态筛选：**
  - **全部：** 显示所有库位
  - **可用：** 只显示可用状态的库位
  - **禁用：** 只显示禁用状态的库位
  - **有库存：** 只显示有库存的库位

#### **详情信息面板**
- **基本信息卡片：**
  - **编码信息：** 显示完整的层级编码
  - **名称描述：** 显示名称和描述信息
  - **创建信息：** 显示创建时间和创建人
  - **状态信息：** 显示当前状态和状态说明
- **属性信息卡片：**
  - **物理属性：** 长宽高、承重、体积等
  - **存储属性：** 适用物料类型、存储规则
  - **容量信息：** 最大容量、当前占用、可用容量
  - **位置信息：** 具体的物理位置描述

#### **属性配置面板**
- **基本属性设置：**
  - **库位编码：** 输入框，支持自动生成和手动输入
  - **库位名称：** 输入框，必填项
  - **库位描述：** 文本域，可选项
  - **库位状态：** 下拉选择，可用/禁用/维修
- **物理属性设置：**
  - **长度：** 数字输入框，单位厘米
  - **宽度：** 数字输入框，单位厘米
  - **高度：** 数字输入框，单位厘米
  - **承重：** 数字输入框，单位公斤
- **存储规则设置：**
  - **物料类型：** 多选框，选择适用的物料类型
  - **存储策略：** 单选框，FIFO/LIFO/指定批次
  - **混合存储：** 复选框，是否允许混合存储
  - **温度要求：** 下拉选择，常温/低温/冷冻

#### **库位标签打印**
- **打印范围选择：**
  - **当前库位：** 只打印当前选中的库位
  - **当前货架：** 打印整个货架的所有库位
  - **当前库区：** 打印整个库区的所有库位
  - **自定义范围：** 手动选择要打印的库位
- **标签模板选择：**
  - **标准模板：** 包含库位编码和二维码
  - **详细模板：** 包含库位信息和属性
  - **简化模板：** 只包含库位编码
  - **自定义模板：** 用户自定义的模板
- **打印参数设置：**
  - **标签尺寸：** 选择标签纸规格
  - **打印份数：** 设置每个库位的打印份数
  - **打印机：** 选择目标打印机
  - **打印质量：** 选择打印质量等级

#### **批量导入对话框**
- **模板下载：**
  - **下载按钮：** 下载Excel导入模板
  - **模板说明：** 显示模板填写说明
- **文件上传：**
  - **选择文件：** 选择要导入的Excel文件
  - **文件验证：** 验证文件格式和内容
  - **预览数据：** 预览导入的数据内容
- **导入执行：**
  - **开始导入：** 执行批量导入操作
  - **进度显示：** 显示导入进度和状态
  - **结果反馈：** 显示导入成功和失败的记录

### 数据校验规则：

#### **库位编码**
- **校验规则：** 编码必须全局唯一，符合编码规则格式
- **错误提示文案：** "库位编码已存在或格式不正确"

#### **物理属性**
- **校验规则：** 长宽高必须大于0，承重必须大于0
- **错误提示文案：** "物理属性值必须大于0"

#### **层级关系**
- **校验规则：** 不能删除有子节点或库存的节点
- **错误提示文案：** "该节点下还有子节点或库存，无法删除"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **仓库信息**:
  - **仓库编码 (warehouse_code)**: String, 必填, 唯一标识
  - **仓库名称 (warehouse_name)**: String, 必填, 最大50字符
  - **仓库地址 (warehouse_address)**: String, 可选, 最大200字符
  - **负责人 (manager)**: String, 可选, 最大20字符
- **库位信息**:
  - **库位编码 (location_code)**: String, 必填, 唯一标识
  - **库位名称 (location_name)**: String, 必填, 最大30字符
  - **物理属性 (physical_attrs)**: Object, 长宽高承重信息
  - **存储规则 (storage_rules)**: Object, 存储策略和规则

### 展示数据
- **结构树**: 仓库、库区、货架、库位的层级结构
- **库位详情**: 编码、名称、属性、状态、库存占用情况
- **统计信息**: 总库位数、可用库位数、库位利用率
- **操作日志**: 结构变更、标签打印等操作记录

### 空状态/零数据
- **无仓储结构**: 显示"暂无仓储结构，请先创建仓库"
- **无库位**: 显示"该货架下暂无库位，请添加库位"
- **搜索无结果**: 显示"未找到匹配的库位，请调整搜索条件"

### API接口
- **获取仓储结构**: GET /api/warehouse/structure
- **创建库位**: POST /api/warehouse/locations
- **更新库位**: PUT /api/warehouse/locations/{id}
- **删除库位**: DELETE /api/warehouse/locations/{id}
- **生成库位标签**: POST /api/warehouse/locations/labels

## 5. 异常与边界处理 (Error & Edge Cases)

### **编码重复**
- **提示信息**: "库位编码已存在，请使用其他编码"
- **用户操作**: 提供编码建议，支持自动生成唯一编码

### **删除限制**
- **提示信息**: "该库位有库存占用，无法删除，请先转移库存"
- **用户操作**: 显示库存详情，提供库存转移功能入口

### **批量导入错误**
- **提示信息**: "第X行数据格式错误：具体错误信息"
- **用户操作**: 高亮错误行，提供错误修正建议

### **打印机故障**
- **提示信息**: "打印机连接异常，请检查打印机状态"
- **用户操作**: 提供打印机检测功能，支持更换打印机

### **权限不足**
- **提示信息**: "您没有权限执行此操作，请联系管理员"
- **用户操作**: 显示所需权限信息，提供权限申请入口

### **数据量过大**
- **提示信息**: "数据量较大，正在加载中，请稍候"
- **用户操作**: 显示加载进度，支持分页加载

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持仓库、库区、货架、库位四级结构管理
- [ ] 库位编码全局唯一，支持自定义编码规则
- [ ] 库位属性包含尺寸、承重、适用物料类型等信息
- [ ] 已有库存的库位限制删除操作
- [ ] 支持库位码标签的批量生成和打印
- [ ] 支持Excel批量导入仓储结构
- [ ] 树形结构支持搜索和筛选功能
- [ ] 库位状态实时更新，支持禁用和维修状态
- [ ] 操作日志完整记录所有变更
- [ ] 界面响应速度快，支持大量库位的管理
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持移动端访问和基本操作
