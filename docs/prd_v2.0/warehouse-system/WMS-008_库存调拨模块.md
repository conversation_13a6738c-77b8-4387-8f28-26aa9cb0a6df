# 功能模块规格说明书：库存调拨模块

- **模块ID**: WMS-008
- **所属子系统**: 仓储管理子系统(WMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 仓库主管, **I want to** 在不同仓库间调拨库存, **so that** 优化库存分布和满足各地需求。
- **As a** 仓管员, **I want to** 执行库内移位操作, **so that** 优化库位利用率和存储效率。
- **As a** 物料计划员, **I want to** 跟踪调拨进度和状态, **so that** 确保调拨计划的准确执行。
- **As a** 财务人员, **I want to** 获得调拨成本和影响分析, **so that** 进行成本控制和效益评估。
- **As a** 生产计划员, **I want to** 在变体间进行库存调拨, **so that** 根据生产需求优化变体库存分布。
- **As a** 仓管员, **I want to** 明确调拨的变体规格信息, **so that** 确保调拨操作的准确性。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 调拨计划已制定
- 源库存充足可用
- 目标库位已准备
- 调拨权限已确认

### 核心流程

#### 2.1 跨仓库调拨流程
1. 创建跨仓库调拨申请单
2. 指定源仓库、目标仓库和调拨物料
3. 审核调拨申请和必要性
4. 在源仓库执行出库操作
5. 安排物流运输到目标仓库
6. 在目标仓库执行入库操作
7. 更新两个仓库的库存数据

#### 2.2 库内移位流程
1. 分析库位利用率和优化需求
2. 制定库内移位计划
3. 选择需要移位的物料和批次
4. 确定目标库位和移位路径
5. 执行物料移位操作
6. 更新库位占用状态和库存位置

#### 2.3 调拨审批流程
1. 提交调拨申请和理由
2. 系统自动检查库存可用性
3. 相关部门审核调拨必要性
4. 财务部门审核成本影响
5. 仓库主管审批调拨计划
6. 审批通过后执行调拨操作

#### 2.4 调拨执行流程
1. 根据调拨单生成出库任务
2. 在源库位拣货并确认数量
3. 打包并生成调拨运输单
4. 安排物流运输（如跨仓库）
5. 目标仓库接收并验收
6. 在目标库位上架并更新库存

#### 2.5 调拨跟踪流程
1. 实时跟踪调拨进度和状态
2. 记录各环节的时间和人员
3. 监控调拨过程中的异常
4. 提供调拨状态查询和通知
5. 生成调拨完成报告
6. 分析调拨效率和成本

### 后置条件
- 库存数据准确更新
- 调拨状态完整记录
- 成本影响正确计算
- 相关单据完整归档

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：库存调拨操作页面
### 页面目标：提供全面的库存调拨管理和执行功能

### 信息架构：
- **顶部区域**：包含 调拨类型选择, 调拨单信息, 状态指示
- **中间区域**：包含 物料清单, 库位选择, 数量确认
- **底部区域**：包含 操作按钮, 进度跟踪, 成本分析

### 交互逻辑与状态：

#### **调拨类型选择**
- **类型标签页：**
  - **跨仓调拨：** 蓝色标签，"跨仓调拨"
  - **库内移位：** 绿色标签，"库内移位"
  - **紧急调拨：** 红色标签，"紧急调拨"
  - **计划调拨：** 橙色标签，"计划调拨"
- **快捷操作：**
  - **新建调拨：** 创建新的调拨申请
  - **模板调拨：** 使用调拨模板快速创建
  - **批量调拨：** 批量创建多个调拨单

#### **调拨单信息**
- **基本信息：**
  - **调拨单号：** 显示调拨单唯一编号
  - **调拨类型：** 显示调拨类型和优先级
  - **申请人：** 显示调拨申请人信息
  - **申请时间：** 显示调拨申请时间
  - **计划完成时间：** 显示计划完成时间
- **仓库信息：**
  - **源仓库：** 显示调出仓库信息
  - **目标仓库：** 显示调入仓库信息
  - **运输方式：** 显示运输方式和承运商
  - **预计到达：** 显示预计到达时间
- **状态信息：**
  - **当前状态：** 显示调拨单当前状态
  - **审批状态：** 显示审批进度和结果
  - **执行进度：** 显示执行完成进度
  - **异常信息：** 显示异常情况和处理状态

#### **调拨状态指示**
- **状态流程：**
  - **已申请：** 灰色圆点，"调拨申请已提交"
  - **审批中：** 黄色圆点，"调拨审批中"
  - **已审批：** 蓝色圆点，"调拨已审批"
  - **执行中：** 绿色圆点，"调拨执行中"
  - **运输中：** 橙色圆点，"物料运输中"
  - **已完成：** 紫色圆点，"调拨已完成"
  - **已取消：** 红色圆点，"调拨已取消"

#### **物料清单展示**
- **清单表头：**
  - **物料编码：** 显示物料编码和名称
  - **规格型号：** 显示物料规格和型号
  - **当前库位：** 显示物料当前所在库位
  - **调拨数量：** 输入框设置调拨数量
  - **可用库存：** 显示可用于调拨的库存
  - **目标库位：** 选择或显示目标库位
  - **状态：** 显示调拨状态
- **物料信息：**
  - **物料图片：** 显示物料图片（如有）
  - **批次信息：** 显示批次号和生产日期
  - **质量状态：** 显示质量状态和等级
  - **价值信息：** 显示单价和总价值
- **批次选择：**
  - **批次列表：** 显示可选择的批次列表
  - **FIFO推荐：** 系统推荐FIFO批次
  - **批次详情：** 显示批次的详细信息
  - **批次状态：** 显示批次的当前状态

#### **库位选择区域**
- **源库位信息：**
  - **当前库位：** 显示物料当前所在库位
  - **库位状态：** 显示库位的当前状态
  - **库存数量：** 显示库位的库存数量
  - **库位属性：** 显示库位的属性信息
- **目标库位选择：**
  - **库位搜索：** 搜索可用的目标库位
  - **库位推荐：** 系统推荐最优目标库位
  - **库位预览：** 预览目标库位的信息
  - **容量检查：** 检查目标库位的容量
- **路径规划：**
  - **移位路径：** 显示最优移位路径
  - **距离计算：** 计算移位距离
  - **时间估算：** 估算移位所需时间
  - **成本分析：** 分析移位成本

#### **数量确认区域**
- **数量设置：**
  - **调拨数量：** 输入框设置调拨数量
  - **可用数量：** 显示可用于调拨的数量
  - **单位：** 显示计量单位
  - **数量验证：** 实时验证数量的合理性
- **批次分配：**
  - **批次选择：** 选择参与调拨的批次
  - **数量分配：** 分配各批次的调拨数量
  - **批次合并：** 设置是否允许批次合并
  - **分配验证：** 验证批次分配的合理性
- **成本计算：**
  - **单位成本：** 显示物料的单位成本
  - **调拨成本：** 计算调拨的总成本
  - **运输成本：** 计算运输成本（如适用）
  - **总成本：** 显示调拨的总成本

#### **操作按钮组**
- **确认调拨按钮：**
  - **默认状态：** 绿色背景(#52C41A)，"确认调拨"
  - **禁用状态：** 信息不完整时禁用
  - **点击效果：** 确认调拨申请或执行
- **保存草稿按钮：**
  - **默认状态：** 蓝色边框，"保存草稿"
  - **点击效果：** 保存当前调拨信息为草稿
- **取消调拨按钮：**
  - **默认状态：** 红色边框，"取消调拨"
  - **点击效果：** 取消当前调拨申请
- **打印单据按钮：**
  - **默认状态：** 橙色边框，"打印单据"
  - **点击效果：** 打印调拨单据

#### **进度跟踪区域**
- **进度时间线：**
  - **申请提交：** 显示申请提交时间和人员
  - **审批完成：** 显示审批完成时间和审批人
  - **出库完成：** 显示出库完成时间和操作员
  - **运输开始：** 显示运输开始时间和承运商
  - **入库完成：** 显示入库完成时间和接收人
- **当前状态：**
  - **状态描述：** 详细描述当前状态
  - **负责人：** 显示当前环节的负责人
  - **预计完成：** 显示预计完成时间
  - **异常信息：** 显示异常情况和处理方案
- **操作记录：**
  - **操作时间：** 显示各操作的时间
  - **操作人员：** 显示操作人员信息
  - **操作内容：** 显示具体操作内容
  - **操作结果：** 显示操作结果和状态

#### **成本分析区域**
- **成本构成：**
  - **物料成本：** 显示调拨物料的成本
  - **人工成本：** 显示调拨操作的人工成本
  - **运输成本：** 显示运输费用（如适用）
  - **其他成本：** 显示其他相关成本
- **效益分析：**
  - **库存优化：** 分析库存分布优化效果
  - **成本节约：** 分析可能的成本节约
  - **效率提升：** 分析效率提升效果
  - **风险降低：** 分析风险降低效果
- **对比分析：**
  - **调拨前后：** 对比调拨前后的状态
  - **成本效益：** 分析成本效益比
  - **历史对比：** 与历史调拨进行对比
  - **标杆对比：** 与标杆数据进行对比

#### **审批流程界面**
- **审批信息：**
  - **审批流程：** 显示完整的审批流程
  - **当前审批：** 显示当前审批环节
  - **审批人：** 显示各环节的审批人
  - **审批意见：** 显示审批意见和建议
- **审批操作：**
  - **同意：** 审批通过，进入下一环节
  - **拒绝：** 审批拒绝，说明拒绝原因
  - **退回：** 退回修改，提出修改要求
  - **转签：** 转签给其他审批人
- **审批历史：**
  - **审批记录：** 显示完整的审批记录
  - **审批时间：** 显示各环节的审批时间
  - **审批结果：** 显示审批结果和意见
  - **审批附件：** 显示审批相关附件

#### **调拨模板管理**
- **模板列表：**
  - **模板名称：** 显示调拨模板名称
  - **模板类型：** 显示模板适用类型
  - **使用频次：** 显示模板使用频次
  - **创建时间：** 显示模板创建时间
- **模板操作：**
  - **使用模板：** 使用模板创建调拨单
  - **编辑模板：** 编辑模板内容
  - **复制模板：** 复制模板创建新模板
  - **删除模板：** 删除不需要的模板
- **模板内容：**
  - **物料清单：** 模板包含的物料清单
  - **调拨路径：** 模板定义的调拨路径
  - **审批流程：** 模板关联的审批流程
  - **成本预算：** 模板的成本预算信息

### 数据校验规则：

#### **调拨数量**
- **校验规则：** 调拨数量不能超过可用库存，不能为负数
- **错误提示文案：** "调拨数量超出可用库存或输入无效"

#### **库位容量**
- **校验规则：** 目标库位容量必须足够容纳调拨物料
- **错误提示文案：** "目标库位容量不足，无法容纳调拨物料"

#### **审批权限**
- **校验规则：** 用户必须具有相应的审批权限
- **错误提示文案：** "您没有权限审批此调拨申请"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **调拨申请**:
  - **调拨单号 (transfer_no)**: String, 自动生成, 唯一标识
  - **调拨类型 (transfer_type)**: Enum, 跨仓/库内/紧急/计划
  - **源仓库 (source_warehouse)**: String, 必填, 源仓库标识
  - **目标仓库 (target_warehouse)**: String, 必填, 目标仓库标识
- **调拨明细**:
  - **物料编码 (material_code)**: String, 必填, 物料标识
  - **批次号 (batch_no)**: String, 必填, 批次标识
  - **调拨数量 (transfer_quantity)**: Number, 必填, 调拨数量
  - **源库位 (source_location)**: String, 必填, 源库位标识
  - **目标库位 (target_location)**: String, 必填, 目标库位标识

### 展示数据
- **调拨单**: 调拨信息、状态进度、审批记录
- **物料清单**: 物料信息、数量、库位、成本
- **进度跟踪**: 时间线、状态、负责人、异常
- **成本分析**: 成本构成、效益分析、对比数据

### 空状态/零数据
- **无调拨单**: 显示"暂无调拨申请"
- **无可用库存**: 显示"无可用库存进行调拨"
- **无目标库位**: 显示"无可用目标库位"

### API接口
- **创建调拨申请**: POST /api/transfer/create
- **审批调拨**: POST /api/transfer/approve
- **执行调拨**: POST /api/transfer/execute
- **查询调拨状态**: GET /api/transfer/status

## 5. 异常与边界处理 (Error & Edge Cases)

### **库存不足**
- **提示信息**: "源库位库存不足，可用库存X件，需求Y件"
- **用户操作**: 提供部分调拨选项或推荐其他库位

### **目标库位被占用**
- **提示信息**: "目标库位已被占用，请选择其他库位"
- **用户操作**: 推荐其他可用库位或等待释放

### **审批超时**
- **提示信息**: "调拨审批超时，请联系审批人或升级处理"
- **用户操作**: 提供催办功能和升级审批选项

### **运输异常**
- **提示信息**: "运输过程中发生异常，请联系承运商"
- **用户操作**: 提供异常处理流程和联系方式

### **成本超预算**
- **提示信息**: "调拨成本超出预算X%，需要特殊审批"
- **用户操作**: 启动特殊审批流程或调整调拨方案

### **系统故障**
- **提示信息**: "系统暂时无法处理调拨请求，请稍后重试"
- **用户操作**: 提供离线处理选项和故障报告

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持跨仓库调拨和库内移位操作
- [ ] 调拨审批流程完整，支持多级审批
- [ ] 实时跟踪调拨进度和状态变化
- [ ] 自动计算调拨成本和效益分析
- [ ] 支持批次级别的精确调拨
- [ ] 库位容量自动检查和推荐
- [ ] 调拨模板功能提高操作效率
- [ ] 异常处理机制完善，状态准确
- [ ] 界面操作便捷，支持移动端访问
- [ ] 数据准确率≥99.8%，状态同步及时
- [ ] 调拨效率提升≥40%
- [ ] 所有页面元素符合全局设计规范
- [ ] 成本控制准确率≥99%
