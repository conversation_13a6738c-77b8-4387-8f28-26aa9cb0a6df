# 玻璃深加工行业ERP系统 - 基础管理子系统UI/UX设计规格

> **版本**: 1.0  
> **创建日期**: 2025-07-31  
> **设计范围**: 基础管理子系统 (BMS)  
> **目标用户**: 系统管理员、部门经理、HR专员  
> **设计标准**: 参考 [前端设计规范](Frontend_Design_Guidelines.md)

---

## 目录

1. [设计概述](#1-设计概述)
2. [信息架构](#2-信息架构)
3. [系统总览界面设计](#3-系统总览界面设计)
4. [BMS-009计量单位管理界面设计](#4-bms-009计量单位管理界面设计)
5. [设计系统应用](#5-设计系统应用)
6. [用户体验流程](#6-用户体验流程)
7. [响应式设计](#7-响应式设计)
8. [集成模式](#8-集成模式)

---

## 1. 设计概述

### 1.1 设计目标
为玻璃深加工行业ERP系统的基础管理子系统创建**专业、高效、准确、一致**的用户界面，支持系统管理员、部门经理和HR专员的日常管理工作。

### 1.2 核心用户需求
- **系统管理员**: 需要强大而直观的管理后台，支持批量操作和系统配置
- **部门经理**: 需要查看和维护本部门员工信息的权限和界面
- **HR专员**: 需要与HR系统集成的用户生命周期管理界面

### 1.3 设计原则
1. **任务导向**: 界面设计围绕用户的核心任务展开
2. **信息层次**: 重要信息优先显示，次要信息适当隐藏
3. **即时反馈**: 所有用户操作都要有明确的系统反馈
4. **容错设计**: 预防错误发生，错误发生时提供清晰的解决方案
5. **专业性**: 符合制造业用户的操作习惯和心理模型

---

## 2. 信息架构

### 2.1 导航层次结构

```
基础管理系统 (BMS)
├── 用户认证管理 (BMS-001)
│   ├── 登录认证配置
│   ├── 密码策略管理
│   └── 会话管理
├── 组织架构管理 (BMS-002)
│   ├── 组织结构维护
│   ├── 部门信息管理
│   └── 组织关系调整
├── 用户管理 (BMS-003)
│   ├── 用户档案管理
│   ├── 批量用户操作
│   └── 用户状态管理
├── 角色权限管理 (BMS-004)
│   ├── 角色定义管理
│   ├── 权限分配配置
│   └── 权限审计查询
├── 数据权限管理 (BMS-005)
│   ├── 数据范围配置
│   ├── 权限规则管理
│   └── 权限测试工具
├── 数据字典管理 (BMS-006)
│   ├── 字典分类管理
│   ├── 字典值维护
│   └── 字典使用监控
├── 合同档案管理 (BMS-007)
│   ├── 合同信息管理
│   ├── 合同分类配置
│   └── 合同状态跟踪
├── 合同执行跟踪 (BMS-008)
│   ├── 执行进度监控
│   ├── 里程碑管理
│   └── 执行报告生成
└── 计量单位管理 (BMS-009) ★
    ├── 单位主数据管理
    ├── 换算关系配置
    ├── 行业单位定制
    └── 使用情况监控
```

### 2.2 页面关系图

```
系统首页/仪表板
    │
    ├─→ 基础管理系统入口
    │       │
    │       ├─→ 用户与权限管理区域
    │       │   ├─→ BMS-001 用户认证管理
    │       │   ├─→ BMS-002 组织架构管理
    │       │   ├─→ BMS-003 用户管理
    │       │   ├─→ BMS-004 角色权限管理
    │       │   └─→ BMS-005 数据权限管理
    │       │
    │       ├─→ 基础数据管理区域
    │       │   ├─→ BMS-006 数据字典管理
    │       │   └─→ BMS-009 计量单位管理 ★
    │       │
    │       └─→ 合同管理区域
    │           ├─→ BMS-007 合同档案管理
    │           └─→ BMS-008 合同执行跟踪
    │
    └─→ 其他子系统入口
        ├─→ PDM产品数据管理
        ├─→ SMS销售管理
        ├─→ PMS采购管理
        └─→ ... (其他8个子系统)
```

---

## 3. 系统总览界面设计

### 3.1 基础管理系统仪表板

#### 3.1.1 整体布局

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                          顶部导航栏 (60px)                                    │
│  玻璃深加工ERP  [首页] [BMS] [PDM] [SMS] [PMS] [MES] [WMS] [FMS] [...]  [用户] │
├─────────────────────────────────────────────────────────────────────────────┤
│ 侧边栏 │                          主内容区域                                  │
│(240px) │  ┌─────────────────────────────────────────────────────────────┐   │
│        │  │                  页面标题区 (80px)                           │   │
│ [BMS]  │  │  基础管理系统    [系统状态: 正常运行]    [最后同步: 2分钟前]    │   │
│ ┌────┐ │  ├─────────────────────────────────────────────────────────────┤   │
│ │用户│ │  │                                                             │   │
│ │认证│ │  │                    仪表板内容区域                              │   │
│ └────┘ │  │                                                             │   │
│ [组织] │  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │   │
│ [用户] │  │  │   用户统计   │ │   权限状态   │ │   系统健康   │          │   │
│ [角色] │  │  │             │ │             │ │             │          │   │
│ [权限] │  │  └─────────────┘ └─────────────┘ └─────────────┘          │   │
│ [字典] │  │                                                             │   │
│ [单位] │  │  ┌─────────────────────────────────────────────────────┐     │   │
│ [合同] │  │  │                  快速操作面板                        │     │   │
│        │  │  └─────────────────────────────────────────────────────┘     │   │
│        │  └─────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### 3.1.2 仪表板卡片设计

**用户统计卡片**
```
┌─────────────────────────────┐
│ 👥 用户统计                  │
├─────────────────────────────┤
│ 总用户数: 1,258             │
│ 活跃用户: 987 (78.4%)       │
│ 新增用户: +12 (本周)        │
│ 待激活: 23                  │
│                             │
│ [查看详情] [用户管理]        │
└─────────────────────────────┘
```

**权限状态卡片**
```
┌─────────────────────────────┐
│ 🔐 权限状态                  │
├─────────────────────────────┤
│ 角色总数: 45                │
│ 待审核权限: 3               │
│ 异常权限: 0                 │
│ 权限覆盖率: 99.2%           │
│                             │
│ [权限审计] [角色管理]        │
└─────────────────────────────┘
```

**系统健康度卡片**  
```
┌─────────────────────────────┐
│ 📊 系统健康                  │
├─────────────────────────────┤
│ 数据一致性: ✅ 99.8%        │
│ API响应时间: ✅ 156ms       │
│ 错误率: ✅ 0.02%            │
│ 存储使用: ⚠️ 78%           │
│                             │
│ [系统监控] [性能分析]        │
└─────────────────────────────┘
```

#### 3.1.3 快速操作面板

```
┌─────────────────────────────────────────────────────────────┐
│                        快速操作                              │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  [➕ 新建用户]   [👥 批量导入]   [🔄 同步AD]   [📋 权限审计]  │
│                                                             │
│  [🏢 调整组织]   [📚 更新字典]   [📏 管理单位]   [📄 合同管理]  │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 侧边导航设计

#### 3.2.1 导航菜单结构

```
基础管理系统 (BMS)
│
├─ 🔐 用户与权限
│  ├─ BMS-001 用户认证管理
│  ├─ BMS-002 组织架构管理
│  ├─ BMS-003 用户管理
│  ├─ BMS-004 角色权限管理
│  └─ BMS-005 数据权限管理
│
├─ 📊 基础数据
│  ├─ BMS-006 数据字典管理
│  └─ BMS-009 计量单位管理 ★
│
└─ 📄 合同管理
   ├─ BMS-007 合同档案管理
   └─ BMS-008 合同执行跟踪
```

#### 3.2.2 导航交互状态

**默认状态**
- 背景色: #FFFFFF
- 文字颜色: #595959
- 图标颜色: #8C8C8C

**悬停状态** 
- 背景色: #F0F0F0
- 文字颜色: #262626
- 图标颜色: #595959

**激活状态**
- 背景色: #E6F7FF  
- 文字颜色: #1890FF
- 图标颜色: #1890FF
- 左边框: 3px #1890FF

**折叠状态**
- 宽度: 60px
- 只显示图标
- 悬停显示tooltip

---

## 4. BMS-009计量单位管理界面设计

### 4.1 页面整体布局

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                         计量单位管理 - BMS-009                               │
├─────────────────────────────────────────────────────────────────────────────┤
│ 面包屑导航: 首页 > 基础管理 > 计量单位管理                                    │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ ┌─────────────┐ ┌─────────────────────────────────────┐ ┌─────────────────┐ │
│ │   量纲分类   │ │              单位管理区域              │ │   操作面板     │ │
│ │   (240px)   │ │            (自适应宽度)               │ │   (300px)     │ │
│ │             │ │                                       │ │               │ │
│ │ 📏 长度     │ │  ┌─────────────────────────────────┐   │ │ 📊 统计信息   │ │
│ │ ├─ 毫米     │ │  │        工具栏区域               │   │ │               │ │
│ │ ├─ 厘米     │ │  └─────────────────────────────────┘   │ │ 🔧 批量操作   │ │
│ │ ├─ 米       │ │                                       │ │               │ │
│ │ └─ 千米     │ │  ┌─────────────────────────────────┐   │ │ 📈 使用监控   │ │
│ │             │ │  │                                 │   │ │               │ │
│ │ ⚖️ 重量     │ │  │        单位列表区域               │   │ │ ⚠️ 验证工具   │ │
│ │ ├─ 克       │ │  │                                 │   │ │               │ │
│ │ ├─ 千克     │ │  │                                 │   │ │ 🔄 换算测试   │ │
│ │ └─ 吨       │ │  │                                 │   │ │               │ │
│ │             │ │  └─────────────────────────────────┘   │ │               │ │
│ │ 📐 面积     │ │                                       │ │               │ │
│ │ 📦 体积     │ │  ┌─────────────────────────────────┐   │ │               │ │
│ │ 🌡️ 温度     │ │  │      换算关系配置区域             │   │ │               │ │
│ │ ⏱️ 时间     │ │  └─────────────────────────────────┘   │ │               │ │
│ │             │ │                                       │ │               │ │
│ └─────────────┘ └─────────────────────────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 4.2 工具栏区域设计

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 🔍 [搜索单位...]        📁分类: [全部▼]  📊状态: [全部▼]  🏷️类型: [全部▼]   │
│                                                                           │
│ [➕ 新建单位] [📤 导入] [📥 导出] [🔄 批量编辑] [🗑️ 批量删除]     [⚙️ 设置] │ 
└─────────────────────────────────────────────────────────────────────────────┘
```

### 4.3 单位列表设计

#### 4.3.1 列表表头

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ☑️  单位编码 ↕️   单位名称 ↕️    符号   量纲     类型     状态     使用次数 ↕️  操作 │
├─────────────────────────────────────────────────────────────────────────────┤
```

#### 4.3.2 列表行设计

```
│ ☑️  UNIT_001     毫米        mm    📏长度   基本单位   ✅启用    1,247次    [⚙️] │
│ ☑️  UNIT_002     厘米        cm    📏长度   换算单位   ✅启用      834次    [⚙️] │
│ ☑️  UNIT_003     米          m     📏长度   换算单位   ✅启用    2,156次    [⚙️] │
│ ☑️  UNIT_004     千克        kg    ⚖️重量   基本单位   ✅启用    1,892次    [⚙️] │
│ ☑️  UNIT_005     吨          t     ⚖️重量   换算单位   ✅启用      567次    [⚙️] │
│ ☑️  UNIT_006     平方米      m²    📐面积   基本单位   ✅启用    3,421次    [⚙️] │
│ ☑️  UNIT_007     立方米      m³    📦体积   基本单位   ✅启用      789次    [⚙️] │
│ ☑️  UNIT_008     玻璃厚度    厚度   🔧专用   专用单位   ✅启用      234次    [⚙️] │
```

#### 4.3.3 操作下拉菜单

```
[⚙️] → ┌─────────────┐
      │ ✏️ 编辑     │
      │ 👁️ 查看详情  │
      │ 🔄 换算设置  │
      │ 📊 使用统计  │
      │ ❌ 停用     │
      │ 🗑️ 删除     │
      └─────────────┘
```

### 4.4 单位创建/编辑表单设计

#### 4.4.1 基本信息区域

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              单位基本信息                                     │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ 单位编码 *     [UNIT_009                    ] (自动生成，可手动修改)          │
│                                                                             │
│ 单位名称 *     [                           ] (最大30字符)                    │
│                                                                             │
│ 单位符号 *     [       ] (最大10字符)         国际符号: [       ]             │
│                                                                             │
│ 量纲分类 *     [长度 ▼                     ]                                │
│                                                                             │
│ 单位描述       [                                                           ] │
│                (可选，最大200字符)                                            │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### 4.4.2 单位属性区域

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              单位属性配置                                     │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ 单位类型 *     ⚪ 基本单位  ⚪ 换算单位  ⚪ 专用单位                           │
│                                                                             │
│ 精度要求 *     [2    ▼] 小数位          显示格式: [0.00              ]       │
│                                                                             │
│ 舍入规则       [四舍五入 ▼                    ]                              │
│                                                                             │
│ 使用状态       ☑️ 启用      ☑️ 允许修改      ☑️ 系统推荐                    │
│                                                                             │
│ 有效期间       从: [2025-01-01] 至: [2099-12-31] (可选)                     │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### 4.4.3 换算关系区域

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              换算关系配置                                     │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ 基准单位       [毫米 (mm) ▼               ] (仅换算单位需要填写)              │
│                                                                             │
│ 换算比例 *     [1000      ] : [1         ] (当前单位:基准单位)               │
│                ↑当前单位        ↑基准单位                                     │
│                                                                             │
│ 换算公式       米 = 毫米 ÷ 1000                                             │
│                                                                             │
│ 换算精度       [4    ▼] 小数位                                              │
│                                                                             │
│ 测试换算       [100  ] 米 = [100000] 毫米   [🔄 测试转换]                   │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### 4.4.4 表单底部操作区

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│              [💾 保存并继续] [✅ 保存] [❌ 取消] [🔄 重置]                    │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 4.5 换算关系管理界面

#### 4.5.1 换算组管理

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                          长度单位换算关系配置                                │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ 基准单位: 毫米 (mm) ✅                                                       │
│                                                                             │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 单位名称     符号    换算比例          状态      使用频次    操作         │ │
│ ├─────────────────────────────────────────────────────────────────────────┤ │
│ │ 毫米 (基准)   mm     1:1             ✅启用      1,247     -            │ │
│ │ 厘米         cm     1:10            ✅启用        834     [⚙️ 编辑]      │ │
│ │ 米          m      1:1000          ✅启用      2,156     [⚙️ 编辑]      │ │
│ │ 千米        km     1:1000000       ❌停用          0     [⚙️ 编辑]      │ │
│ │ 英寸        in     1:25.4          ⚠️试用         12     [⚙️ 编辑]      │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ [➕ 添加换算单位] [📊 关系图表] [🧪 批量测试] [💾 保存配置]                    │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### 4.5.2 换算测试面板

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              换算测试工具                                     │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ 源单位: [毫米 ▼]    数值: [1000     ]                                       │
│                                                                             │
│                            ⬇️ 转换                                          │
│                                                                             │
│ 目标单位: [米 ▼]    结果: [1.0000    ]                                      │
│                                                                             │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 换算详情:                                                               │ │
│ │ • 换算公式: 米 = 毫米 ÷ 1000                                            │ │
│ │ • 计算过程: 1000 ÷ 1000 = 1.0000                                       │ │
│ │ • 精度设置: 4位小数                                                      │ │
│ │ • 舍入规则: 四舍五入                                                     │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ [🔄 重新计算] [📋 复制结果] [📊 精度分析] [🧪 批量测试]                       │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 4.6 右侧操作面板设计

#### 4.6.1 统计信息面板

```
┌─────────────────────────────┐
│       📊 单位统计            │
├─────────────────────────────┤
│ 总单位数: 156               │
│ 启用单位: 142               │
│ 停用单位: 14                │
│ 专用单位: 23                │
│                             │
│ 量纲分布:                   │
│ • 长度: 12 个               │
│ • 重量: 8 个                │
│ • 面积: 6 个                │
│ • 体积: 4 个                │
│ • 其他: 126 个              │
│                             │
│ [📈 详细报告]               │
└─────────────────────────────┘
```

#### 4.6.2 使用监控面板

```
┌─────────────────────────────┐
│       📈 使用监控            │
├─────────────────────────────┤
│ 今日使用频次: 2,847         │
│ 本周新增: +5                │
│ 异常单位: 0                 │
│                             │
│ 热门单位:                   │
│ 1. 米 (m) - 2,156次         │
│ 2. 千克 (kg) - 1,892次      │
│ 3. 平方米 (m²) - 1,427次    │
│                             │
│ 未使用单位: 12个            │
│ [📋 查看清单]               │
│                             │
│ [⚠️ 异常监控] [🔍 使用分析] │
└─────────────────────────────┘
```

#### 4.6.3 验证工具面板

```
┌─────────────────────────────┐
│       ⚠️ 验证工具            │
├─────────────────────────────┤
│ 最后验证: 2分钟前           │
│                             │
│ 验证结果:                   │
│ ✅ 换算关系: 100%           │
│ ✅ 数据完整性: 100%         │
│ ✅ 编码唯一性: 100%         │
│ ⚠️ 精度一致性: 95%          │
│                             │
│ 发现问题: 3个               │
│ • 精度不一致: 2个           │
│ • 符号重复: 1个             │
│                             │
│ [🔧 立即修复] [📋 问题清单]  │
│ [🔄 重新验证] [📊 验证历史]  │
└─────────────────────────────┘
```

---

## 5. 设计系统应用

### 5.1 色彩应用

#### 5.1.1 主要颜色使用

| 颜色用途 | 色值 | 应用场景 |
|---------|------|----------|
| **主色** | #1890FF | 主要按钮、链接、选中状态、进度条 |
| **成功色** | #52C41A | 成功提示、启用状态、完成状态 |
| **警告色** | #FAAD14 | 警告提示、待处理状态、精度问题 |
| **错误色** | #F5222D | 错误提示、危险操作、停用状态 |
| **信息色** | #1890FF | 信息提示、帮助说明、系统通知 |

#### 5.1.2 业务状态色彩

| 状态 | 色值 | 图标 | 应用 |
|------|------|------|------|
| **启用** | #52C41A | ✅ | 单位启用状态 |
| **停用** | #8C8C8C | ❌ | 单位停用状态 |
| **试用** | #FAAD14 | ⚠️ | 试用期单位 |
| **异常** | #F5222D | 🚨 | 验证失败单位 |

### 5.2 字体应用

#### 5.2.1 中文字体

```css
font-family: "PingFang SC", "Microsoft YaHei", "苹方", "微软雅黑", sans-serif;
```

#### 5.2.2 字号层级

| 用途 | 字号 | 字重 | 行高 | 应用场景 |
|------|------|------|------|----------|
| **页面标题** | 24px | Bold | 1.2 | 系统模块标题 |
| **区块标题** | 20px | Medium | 1.2 | 功能区域标题 |
| **小标题** | 16px | Medium | 1.4 | 表单分组标题 |
| **正文** | 14px | Regular | 1.5 | 表格内容、表单标签 |
| **辅助文字** | 12px | Regular | 1.5 | 提示信息、统计数据 |
| **数字** | 14px | Consolas | 1.4 | 数量、换算比例 |

### 5.3 组件规格

#### 5.3.1 按钮组件规格

**主要按钮 (Primary)**
```css
background: #1890FF;
color: #FFFFFF;
border-radius: 4px;
padding: 8px 16px;
height: 32px;
font-size: 14px;
font-weight: 400;
```

**次要按钮 (Default)**
```css
background: #FFFFFF;
color: #262626;
border: 1px solid #D9D9D9;
border-radius: 4px;
padding: 8px 16px;
height: 32px;
```

**危险按钮 (Danger)**
```css
background: #F5222D;
color: #FFFFFF;
border-radius: 4px;
padding: 8px 16px;
height: 32px;
```

#### 5.3.2 表单组件规格

**输入框 (Input)**
```css
border: 1px solid #D9D9D9;
border-radius: 4px;
padding: 8px 12px;
height: 32px;
font-size: 14px;
background: #FFFFFF;

/* 聚焦状态 */
&:focus {
  border-color: #1890FF;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 错误状态 */
&.error {
  border-color: #F5222D;
}
```

**下拉选择 (Select)**
```css
/* 与输入框样式保持一致 */
min-width: 120px;
```

#### 5.3.3 表格组件规格

**表格表头**
```css
background: #FAFAFA;
color: #262626;
font-weight: 500;
height: 40px;
border-bottom: 1px solid #E8E8E8;
```

**表格行**
```css
height: 48px;
border-bottom: 1px solid #F0F0F0;

/* 悬停状态 */
&:hover {
  background: #FAFAFA;
}

/* 选中状态 */
&.selected {
  background: #E6F7FF;
}
```

---

## 6. 用户体验流程

### 6.1 新建计量单位流程

#### 6.1.1 流程步骤

```
1. 用户点击"新建单位"按钮
   ↓
2. 系统展开单位创建表单
   ↓
3. 用户选择量纲分类
   ↓
4. 系统自动生成单位编码 (可修改)
   ↓
5. 用户填写单位基本信息
   ↓
6. 用户配置单位属性
   ↓
7. 如为换算单位，配置换算关系
   ↓
8. 系统实时验证数据有效性
   ↓
9. 用户确认保存
   ↓
10. 系统保存并通知相关模块
    ↓
11. 返回单位列表，高亮新建单位
```

#### 6.1.2 关键交互细节

**步骤3 - 量纲分类选择**
- 展示量纲分类下拉菜单
- 显示每个分类下已有单位数量
- 提供分类说明和示例

**步骤4 - 编码生成**
- 按照 "UNIT_" + 序号 格式自动生成
- 用户可手动修改，系统实时验证唯一性
- 不符合规则时显示红色边框和错误提示

**步骤7 - 换算关系配置**
- 只有选择"换算单位"时才显示此区域
- 基准单位下拉框只显示同量纲的基本单位
- 实时计算并显示换算公式
- 提供换算测试功能

**步骤8 - 数据验证**
- 必填字段为空时显示红色边框
- 编码重复时显示警告提示
- 换算比例为0或负数时显示错误
- 所有验证通过后"保存"按钮才可点击

### 6.2 换算关系配置流程

#### 6.2.1 配置流程

```
1. 用户选择量纲分类
   ↓
2. 系统展示该量纲下所有单位
   ↓
3. 用户选择或确认基准单位
   ↓
4. 系统展示换算关系配置表格
   ↓
5. 用户逐个配置换算比例
   ↓
6. 系统实时验证换算关系合理性
   ↓
7. 用户使用测试工具验证换算
   ↓
8. 确认无误后保存配置
   ↓
9. 系统更新所有相关换算规则
```

#### 6.2.2 验证机制

**换算关系一致性检查**
- 检查是否存在循环换算
- 验证换算比例是否合理
- 确认精度设置是否一致

**换算测试验证**
- 提供正向和反向换算测试
- 显示详细计算过程
- 标识精度损失风险

### 6.3 错误处理流程

#### 6.3.1 常见错误场景

**场景1: 删除被使用的单位**
```
用户操作: 点击删除按钮
系统检查: 发现单位正在被其他模块使用
系统响应: 
1. 显示确认对话框
2. 列出使用该单位的模块和数据量
3. 提供"停用"替代选项
4. 用户选择停用后更新状态
```

**场景2: 换算关系冲突**
```
用户操作: 配置换算比例
系统检查: 发现与现有关系冲突
系统响应:
1. 高亮冲突的单位行
2. 显示具体冲突信息
3. 提供修复建议
4. 用户调整后重新验证
```

---

## 7. 响应式设计

### 7.1 断点定义

| 设备类型 | 屏幕宽度 | 主要布局特点 |
|----------|----------|-------------|
| **超大屏** | ≥1920px | 三栏布局，所有功能完整展示 |
| **大屏** | ≥1440px | 三栏布局，部分操作面板可折叠 |
| **中屏** | ≥1024px | 两栏布局，右侧面板折叠 |
| **小屏** | ≥768px | 单栏布局，侧边栏折叠 |
| **移动端** | <768px | 全屏布局，所有面板可切换 |

### 7.2 布局适配策略

#### 7.2.1 大屏设备 (≥1440px)

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                         顶部导航栏 (60px)                                    │
├─────────────────────────────────────────────────────────────────────────────┤
│ 侧边栏 │                 主内容区域                │      右侧面板        │
│(240px) │                                          │      (300px)         │
│        │  ┌─────────────────────────────────────┐ │                       │
│[分类树]│  │            工具栏                    │ │  ┌─────────────────┐ │
│        │  ├─────────────────────────────────────┤ │  │   统计信息      │ │
│        │  │                                     │ │  ├─────────────────┤ │
│        │  │            单位列表                  │ │  │   使用监控      │ │
│        │  │                                     │ │  ├─────────────────┤ │
│        │  ├─────────────────────────────────────┤ │  │   验证工具      │ │
│        │  │          换算关系配置                │ │  └─────────────────┘ │
│        │  └─────────────────────────────────────┘ │                       │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### 7.2.2 中屏设备 (1024px-1440px)

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                         顶部导航栏 (60px)                                    │
├─────────────────────────────────────────────────────────────────────────────┤
│ 侧边栏 │                        主内容区域                                   │
│(200px) │                                                                     │
│        │  ┌─────────────────────────────────────────────────────────────┐   │
│[分类树]│  │                        工具栏                                │   │
│        │  ├─────────────────────────────────────────────────────────────┤   │
│        │  │                                                             │   │
│        │  │                      单位列表                                │   │
│        │  │                                                             │   │
│        │  ├─────────────────────────────────────────────────────────────┤   │
│        │  │                    换算关系配置                              │   │
│        │  └─────────────────────────────────────────────────────────────┘   │
│        │  右侧面板通过抽屉方式打开 →                                       │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### 7.2.3 移动端设备 (<768px)

```
┌─────────────────────────────────────┐
│            顶部导航栏                │
│ [☰] 计量单位管理         [🔍] [⚙️] │
├─────────────────────────────────────┤
│                                     │
│  [📁 分类筛选 ▼]  [🔄 状态筛选 ▼]   │
│                                     │
│  🔍 [搜索单位...]                   │
│                                     │
│  ┌─────────────────────────────────┐ │
│  │ 📏 毫米 (mm)        [⚙️]       │ │  
│  │ 基本单位 • 启用 • 1,247次使用   │ │
│  └─────────────────────────────────┘ │
│  ┌─────────────────────────────────┐ │
│  │ 📏 厘米 (cm)        [⚙️]       │ │
│  │ 换算单位 • 启用 • 834次使用     │ │
│  └─────────────────────────────────┘ │
│                                     │
│  [➕ 新建单位]                      │
│                                     │
└─────────────────────────────────────┘
```

### 7.3 组件适配规则

#### 7.3.1 表格组件移动端适配

**桌面端表格**
```
│ 编码    │ 名称  │ 符号 │ 量纲 │ 类型   │ 状态 │ 使用次数 │ 操作 │
│ UNIT_001│ 毫米  │ mm   │ 长度 │ 基本单位│ 启用 │ 1,247   │ [⚙️] │
```

**移动端卡片**
```
┌─────────────────────────────────┐
│ 📏 毫米 (mm) - UNIT_001  [⚙️]  │
│ 基本单位 • 长度量纲              │
│ ✅ 启用 • 使用次数: 1,247       │
└─────────────────────────────────┘
```

#### 7.3.2 表单组件移动端适配

**桌面端两列布局**
```
单位编码: [UNIT_009]     单位名称: [毫米]
单位符号: [mm]          量纲分类: [长度 ▼]
```

**移动端单列布局**
```
单位编码 *
[UNIT_009                    ]

单位名称 *  
[毫米                        ]

单位符号 *
[mm     ]

量纲分类 *
[长度 ▼                     ]
```

---

## 8. 集成模式

### 8.1 与其他子系统的集成

#### 8.1.1 PDM产品数据管理集成

**集成点：物料单位配置**
```
PDM-001 物料主数据管理
    ↓ 获取计量单位列表
BMS-009 计量单位管理
    ↑ 提供标准单位数据
    ↑ 单位变更通知
```

**集成界面设计**

在PDM物料创建页面中：
```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           物料基本信息                                       │
├─────────────────────────────────────────────────────────────────────────────┤
│ 基本单位 *    [千克 (kg) ▼            ] [📏 单位管理]                       │
│ 采购单位      [千克 (kg) ▼            ] 换算比例: 1:1                       │
│ 库存单位      [千克 (kg) ▼            ] 换算比例: 1:1                       │
│ 销售单位      [吨 (t) ▼               ] 换算比例: 1000:1                    │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### 8.1.2 WMS仓储管理集成

**集成点：库存计量**
```
WMS-003 收货入库管理
    ↓ 使用标准单位
BMS-009 计量单位管理
    ↑ 提供换算服务
    ↑ 单位验证服务
```

**换算服务API**
```javascript
// 单位换算API调用示例
POST /api/bms/units/convert
{
  "sourceValue": 1000,
  "sourceUnit": "UNIT_001", // 毫米
  "targetUnit": "UNIT_003", // 米
  "precision": 4
}

// 返回结果
{
  "targetValue": 1.0000,
  "formula": "米 = 毫米 ÷ 1000",
  "precision": 4,
  "roundingRule": "四舍五入"
}
```

#### 8.1.3 FMS财务管理集成

**集成点：成本核算单位**
```
FMS-009 成本要素归集
    ↓ 获取计量单位
BMS-009 计量单位管理
    ↑ 提供成本核算单位
    ↑ 单位标准化服务
```

### 8.2 数据同步机制

#### 8.2.1 实时同步触发点

**单位创建时**
```
1. BMS-009创建新单位
2. 触发单位创建事件
3. 通知PDM、WMS、PMS等系统
4. 各系统更新本地单位缓存
5. 返回同步结果确认
```

**单位修改时**
```
1. BMS-009修改单位信息
2. 检查单位使用情况
3. 如果正在使用，发送变更通知
4. 相关系统确认影响范围
5. 执行统一变更
6. 验证变更结果
```

**单位停用时**
```
1. 用户尝试停用单位
2. 系统检查使用情况
3. 显示影响评估报告
4. 用户确认停用
5. 执行停用操作
6. 通知相关系统更新
```

#### 8.2.2 同步状态监控

**同步状态面板**
```
┌─────────────────────────────┐
│       🔄 同步状态            │
├─────────────────────────────┤
│ 最后同步: 2分钟前           │
│                             │
│ 同步状态:                   │
│ ✅ PDM系统: 正常            │
│ ✅ WMS系统: 正常            │
│ ✅ PMS系统: 正常            │
│ ⚠️ FMS系统: 延迟3s          │
│                             │
│ 待同步变更: 0个             │
│ 同步失败: 0个               │
│                             │
│ [🔄 手动同步] [📊 同步日志]  │
└─────────────────────────────┘
```

### 8.3 权限控制集成

#### 8.3.1 基于角色的权限控制

**权限矩阵**

| 操作 | 系统管理员 | 数据管理员 | 普通用户 | 只读用户 |
|------|-----------|-----------|---------|---------|
| 查看单位 | ✅ | ✅ | ✅ | ✅ |
| 创建单位 | ✅ | ✅ | ❌ | ❌ |
| 修改单位 | ✅ | ✅ | ❌ | ❌ |
| 删除单位 | ✅ | ❌ | ❌ | ❌ |
| 配置换算 | ✅ | ✅ | ❌ | ❌ |
| 系统设置 | ✅ | ❌ | ❌ | ❌ |

#### 8.3.2 数据权限控制

**基于组织架构的数据权限**
```
总公司管理员: 可管理所有单位
分公司管理员: 只能查看，不能修改
部门管理员: 只能查看常用单位
普通用户: 只能查看和使用单位
```

---

## 总结

本设计规格书为玻璃深加工行业ERP系统的基础管理子系统提供了完整的UI/UX设计方案，特别是对BMS-009计量单位管理模块进行了详细的界面设计。

### 核心特色

1. **专业化设计**: 针对制造业用户的操作习惯和业务需求
2. **系统化架构**: 完整的信息架构和导航体系
3. **集成化思维**: 与其他ERP子系统的深度集成设计
4. **响应式布局**: 支持多设备的一致体验
5. **细致化交互**: 详尽的交互状态和错误处理设计

### 实施建议

1. **分阶段开发**: 优先开发核心功能，逐步完善高级特性
2. **用户测试**: 在开发过程中进行持续的用户体验测试
3. **性能优化**: 关注大数据量情况下的界面性能
4. **可扩展性**: 预留未来功能扩展的界面空间

本设计规格可作为开发团队的实施依据，确保最终产品符合业务需求和用户期望。

---

**文档状态**: 已完成 ✅  
**应用原则**: 第一性原理 ✅ DRY ✅ KISS ✅ SOLID ✅ YAGNI ✅  
**设计规范**: Frontend Design Guidelines ✅