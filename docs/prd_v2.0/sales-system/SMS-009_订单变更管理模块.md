# 功能模块规格说明书：订单变更管理模块

- **模块ID**: SMS-009
- **所属子系统**: 销售管理子系统(SMS)
- **最后更新**: 2025-07-31
- **功能说明**: 管理销售订单的变更申请、审批、执行全流程，确保变更的可控性和可追溯性

## 1. 用户故事 (User Stories)

- **As a** 销售员, **I want to** 快速提交订单变更申请, **so that** 及时响应客户的规格或数量调整需求。
- **As a** 销售主管, **I want to** 审批订单变更并评估影响, **so that** 控制变更风险和成本。
- **As a** 生产计划员, **I want to** 查看订单变更对生产的影响, **so that** 及时调整生产计划和资源配置。
- **As a** 财务人员, **I want to** 计算订单变更的价格影响, **so that** 准确核算订单金额和成本。
- **As a** 客户, **I want to** 了解订单变更的处理进度, **so that** 掌握变更执行情况和交期影响。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 原订单已确认且状态允许变更
- 变更申请人具有相应权限
- 变更内容符合业务规则
- 相关审批人员已配置

### 核心流程

#### 2.1 变更申请流程
1. 销售员或客户发起订单变更申请
2. 选择要变更的订单和订单行
3. 填写变更内容（规格、数量、交期等）
4. 系统自动计算变更影响分析
5. 填写变更原因和说明
6. 提交变更申请进入审批流程

#### 2.2 变更影响分析流程
1. 系统自动分析变更对生产的影响
2. 计算变更对交期的影响
3. 评估变更对成本和价格的影响
4. 检查变更对库存和采购的影响
5. 生成变更影响分析报告
6. 为审批人员提供决策依据

#### 2.3 变更审批流程
1. 变更申请进入审批队列
2. 按审批权限分配给相应审批人
3. 审批人查看变更内容和影响分析
4. 做出审批决定（同意/拒绝/退回修改）
5. 填写审批意见和建议
6. 系统记录审批结果和时间

#### 2.4 变更执行流程
1. 审批通过后启动变更执行
2. 更新订单主数据和订单行
3. 同步更新生产计划和采购计划
4. 调整库存分配和预留
5. 重新计算订单价格和交期
6. 通知相关部门和客户

### 系统集成说明
- **与SMS-004集成**: 更新销售订单数据
- **与MES系统集成**: 同步生产计划变更
- **与PMS系统集成**: 调整采购需求
- **与FMS系统集成**: 更新订单金额和成本

### 后置条件
- 订单数据准确更新
- 相关系统数据同步
- 变更记录完整保存
- 客户和内部人员及时通知

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：订单变更申请页面
### 页面目标：提供便捷的订单变更申请和管理界面

### 信息架构：
- **顶部区域**：包含 订单信息, 变更类型, 申请状态
- **中间区域**：包含 变更内容, 影响分析, 审批流程
- **底部区域**：包含 操作按钮, 历史记录, 相关文档

### 交互逻辑与状态：

#### **订单信息显示**
- **基本信息：**
  - **订单编号：** 显示原订单编号和客户信息
  - **订单状态：** 显示当前订单执行状态
  - **订单金额：** 显示原订单金额和变更后金额
  - **交期信息：** 显示原交期和变更后交期
  - **订单行：** 显示订单明细和变更项目
- **变更状态指示：**
  - **草稿：** 灰色圆点，"编辑中"
  - **待审批：** 橙色圆点，"等待审批"
  - **已批准：** 绿色圆点，"审批通过"
  - **已拒绝：** 红色圆点，"审批拒绝"
  - **执行中：** 蓝色圆点，"变更执行中"
  - **已完成：** 绿色圆点，"变更完成"

#### **变更内容编辑**
- **变更类型选择：**
  - **规格变更：** 产品规格、尺寸、材质等
  - **数量变更：** 订单数量增加或减少
  - **交期变更：** 交货日期提前或延后
  - **价格变更：** 单价或总价调整
  - **其他变更：** 包装、运输、付款等
- **变更内容录入：**
  - **原值显示：** 显示变更前的原始值
  - **新值录入：** 录入变更后的新值
  - **变更说明：** 填写变更原因和详细说明
  - **附件上传：** 上传相关证明文件

#### **影响分析展示**
- **生产影响：**
  - **工艺变更：** 是否需要调整生产工艺
  - **物料影响：** 对原材料需求的影响
  - **产能影响：** 对生产能力的影响
  - **交期影响：** 对交货期的具体影响
- **成本影响：**
  - **材料成本：** 材料成本变化金额
  - **人工成本：** 人工成本变化金额
  - **制造费用：** 制造费用变化金额
  - **总成本：** 总成本变化金额
- **价格影响：**
  - **单价变化：** 产品单价变化金额
  - **总价变化：** 订单总价变化金额
  - **利润影响：** 对订单利润的影响

## 4. 数据需求 (Data Requirements)

### 核心数据实体

#### **订单变更申请 (OrderChangeRequest)**
```sql
CREATE TABLE order_change_request (
    change_id VARCHAR(32) PRIMARY KEY COMMENT '变更申请ID',
    order_id VARCHAR(32) NOT NULL COMMENT '原订单ID',
    change_type VARCHAR(20) NOT NULL COMMENT '变更类型',
    change_reason TEXT COMMENT '变更原因',
    applicant_id VARCHAR(32) NOT NULL COMMENT '申请人ID',
    apply_time DATETIME NOT NULL COMMENT '申请时间',
    status VARCHAR(20) NOT NULL COMMENT '变更状态',
    total_amount_change DECIMAL(15,2) COMMENT '总金额变化',
    delivery_date_change INT COMMENT '交期变化天数',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### **变更明细 (OrderChangeDetail)**
```sql
CREATE TABLE order_change_detail (
    detail_id VARCHAR(32) PRIMARY KEY COMMENT '变更明细ID',
    change_id VARCHAR(32) NOT NULL COMMENT '变更申请ID',
    order_line_id VARCHAR(32) NOT NULL COMMENT '订单行ID',
    change_field VARCHAR(50) NOT NULL COMMENT '变更字段',
    old_value TEXT COMMENT '原值',
    new_value TEXT COMMENT '新值',
    impact_analysis TEXT COMMENT '影响分析',
    FOREIGN KEY (change_id) REFERENCES order_change_request(change_id)
);
```

#### **变更审批记录 (ChangeApprovalRecord)**
```sql
CREATE TABLE change_approval_record (
    approval_id VARCHAR(32) PRIMARY KEY COMMENT '审批记录ID',
    change_id VARCHAR(32) NOT NULL COMMENT '变更申请ID',
    approver_id VARCHAR(32) NOT NULL COMMENT '审批人ID',
    approval_level INT NOT NULL COMMENT '审批层级',
    approval_result VARCHAR(20) NOT NULL COMMENT '审批结果',
    approval_comment TEXT COMMENT '审批意见',
    approval_time DATETIME NOT NULL COMMENT '审批时间',
    FOREIGN KEY (change_id) REFERENCES order_change_request(change_id)
);
```

### 数据字典

#### **变更类型 (change_type)**
- SPEC_CHANGE: 规格变更
- QTY_CHANGE: 数量变更
- DATE_CHANGE: 交期变更
- PRICE_CHANGE: 价格变更
- OTHER_CHANGE: 其他变更

#### **变更状态 (status)**
- DRAFT: 草稿
- PENDING: 待审批
- APPROVED: 已批准
- REJECTED: 已拒绝
- EXECUTING: 执行中
- COMPLETED: 已完成
- CANCELLED: 已取消

#### **审批结果 (approval_result)**
- APPROVED: 同意
- REJECTED: 拒绝
- RETURNED: 退回修改

### 数据关系
- 订单变更申请 ←→ 销售订单 (1:N)
- 订单变更申请 ←→ 变更明细 (1:N)
- 订单变更申请 ←→ 审批记录 (1:N)

## 5. 错误处理与边界情况 (Error Handling & Edge Cases)

### 业务规则验证

#### **变更申请验证**
- **订单状态检查：** 只有特定状态的订单才允许变更
- **权限验证：** 验证申请人是否有权限变更该订单
- **变更内容验证：** 检查变更内容的合理性和完整性
- **重复申请检查：** 防止同一订单同时存在多个未完成的变更申请

#### **影响分析验证**
- **库存可用性：** 检查变更后的数量是否有足够库存
- **产能约束：** 验证变更是否超出生产能力限制
- **交期合理性：** 检查变更后的交期是否可行
- **成本计算：** 确保成本和价格计算的准确性

### 异常情况处理

#### **系统异常**
- **数据同步失败：** 变更执行时相关系统数据同步失败
  - 错误提示：系统繁忙，请稍后重试
  - 处理方式：记录失败日志，支持重新同步
- **审批流程异常：** 审批人员不可用或审批流程配置错误
  - 错误提示：审批流程配置异常，请联系管理员
  - 处理方式：提供临时审批人指定功能

#### **业务异常**
- **订单已锁定：** 订单已进入不可变更状态
  - 错误提示：订单已锁定，不允许变更
  - 处理方式：提供解锁申请流程
- **变更冲突：** 多人同时变更同一订单
  - 错误提示：订单正在被其他用户变更，请稍后重试
  - 处理方式：实现乐观锁机制

### 边界情况

#### **极限变更**
- **数量变更为零：** 相当于取消订单行
- **交期大幅提前：** 可能导致生产计划无法满足
- **规格大幅变更：** 可能需要重新设计和报价

#### **级联影响**
- **关联订单影响：** 变更可能影响其他关联订单
- **供应链影响：** 变更可能影响供应商交货计划
- **客户影响：** 变更可能影响客户的后续计划

## 6. 验收标准 (Acceptance Criteria)

### 功能验收标准

#### **变更申请功能**
- [ ] 能够选择订单并发起变更申请
- [ ] 支持多种变更类型的申请
- [ ] 变更内容录入界面友好易用
- [ ] 自动计算变更影响并生成分析报告
- [ ] 变更申请提交后进入正确的审批流程

#### **审批管理功能**
- [ ] 审批人能够查看完整的变更信息和影响分析
- [ ] 支持多级审批和并行审批
- [ ] 审批决定能够及时通知相关人员
- [ ] 审批历史记录完整可追溯

#### **变更执行功能**
- [ ] 审批通过后能够正确执行变更
- [ ] 相关系统数据能够及时同步更新
- [ ] 变更执行过程可监控可回滚
- [ ] 变更完成后通知相关人员

### 性能验收标准

#### **响应时间要求**
- [ ] 变更申请页面加载时间 ≤ 2秒
- [ ] 影响分析计算时间 ≤ 5秒
- [ ] 变更执行完成时间 ≤ 10秒
- [ ] 审批流程响应时间 ≤ 3秒

#### **并发处理能力**
- [ ] 支持100个并发用户同时操作
- [ ] 支持同时处理50个变更申请
- [ ] 系统在高并发下保持稳定

### 集成验收标准

#### **系统集成**
- [ ] 与销售订单系统集成正常
- [ ] 与生产计划系统集成正常
- [ ] 与采购管理系统集成正常
- [ ] 与财务管理系统集成正常
- [ ] 数据同步准确及时

#### **用户体验**
- [ ] 界面设计符合用户操作习惯
- [ ] 操作流程简洁高效
- [ ] 错误提示清晰明确
- [ ] 帮助文档完整易懂

### 安全验收标准

#### **权限控制**
- [ ] 变更申请权限控制正确
- [ ] 审批权限分级管理有效
- [ ] 数据访问权限严格控制
- [ ] 操作日志记录完整

#### **数据安全**
- [ ] 变更数据加密存储
- [ ] 敏感信息脱敏显示
- [ ] 数据备份恢复机制完善
- [ ] 审计跟踪功能完整
