# 功能模块规格说明书：价格策略管理模块

- **模块ID**: SMS-007
- **所属子系统**: 销售管理子系统(SMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 销售经理, **I want to** 创建和管理多套价格本, **so that** 为不同类型客户提供差异化定价。
- **As a** 销售经理, **I want to** 设置复杂的计价规则, **so that** 系统能自动匹配最优价格策略。
- **As a** 销售经理, **I want to** 控制价格策略的生效时间, **so that** 灵活应对市场变化。
- **As a** 销售代表, **I want to** 查看适用的价格策略, **so that** 为客户提供准确的报价。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有价格管理权限
- 产品主数据已建立
- 客户分类体系已配置
- 成本数据已维护

### 核心流程

#### 2.1 价格本创建流程
1. 销售经理进入价格策略管理页面
2. 点击"新建价格本"按钮
3. 填写价格本基本信息（名称、类型、适用范围）
4. 设置价格本的生效时间和有效期
5. 选择价格计算方式（固定价格/成本加成/公式计算）
6. 保存价格本基础配置

#### 2.2 价格规则配置流程
1. 进入已创建的价格本详情页
2. 添加产品价格规则：
   - 选择产品或产品分类
   - 设置价格计算公式
   - 配置数量阶梯价格
   - 设置客户类型差异价格
3. 配置特殊计价规则（按面积、按周长等）
4. 设置价格规则的优先级
5. 保存价格规则配置

#### 2.3 价格策略匹配流程
1. 系统接收报价或订单请求
2. 获取客户信息和产品信息
3. 按优先级匹配适用的价格策略：
   - 客户专属价格策略
   - 客户类型价格策略
   - 产品分类价格策略
   - 默认价格策略
4. 应用数量阶梯和特殊规则
5. 返回最终价格结果

#### 2.4 价格策略审核流程
1. 价格策略创建或修改后提交审核
2. 系统根据影响范围确定审核级别
3. 发送审核通知给相应审核人
4. 审核人查看价格策略详情和影响分析
5. 审核人批准或拒绝，填写审核意见
6. 系统更新策略状态并通知创建人

### 后置条件
- 价格策略配置完整有效
- 价格匹配规则正确执行
- 价格变更记录完整可追溯
- 相关人员收到变更通知

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：价格策略管理页面
### 页面目标：提供灵活强大的价格策略配置界面

### 信息架构：
- **左侧区域**：包含 价格本列表, 策略分类树, 快速筛选器
- **中间区域**：包含 价格规则配置表, 公式编辑器, 预览测试
- **右侧区域**：包含 策略详情面板, 影响分析, 操作历史

### 交互逻辑与状态：

#### **价格本列表**
- **列表项样式：** 卡片形式，显示价格本名称、类型、状态
- **状态标识：**
  - 草稿：灰色标签(#8C8C8C)
  - 待审核：橙色标签(#FAAD14)
  - 已生效：绿色标签(#52C41A)
  - 已过期：红色标签(#F5222D)
- **悬停效果：** 显示快捷操作按钮（编辑、复制、删除）
- **选中状态：** 蓝色左边框，背景色变化

#### **新建价格本按钮**
- **默认状态：** 蓝色背景(#1890FF)，白色文字"新建价格本"
- **悬停状态：** 背景色加深至#096DD9
- **权限不足状态：** 灰色背景，禁用状态
- **交互行为：** 点击打开价格本创建向导

#### **价格本基本信息表单**
- **价格本名称：** 文本输入框，必填，最大50字符
- **价格本类型：** 下拉选择（标准价/经销商价/大客户价/促销价）
- **适用范围：** 多选框（客户类型、产品分类、地区等）
- **生效时间：** 日期时间选择器
- **有效期：** 日期选择器，可选择永久有效

#### **价格规则配置表格**
- **表格样式：** 可编辑表格，支持行内编辑
- **列定义：**
  - 产品/分类：下拉选择器，支持搜索
  - 计价方式：下拉选择（固定价/成本加成/公式计算）
  - 基础价格：数值输入框
  - 数量阶梯：弹窗编辑器
  - 客户折扣：百分比输入
  - 优先级：数值输入，数字越大优先级越高

#### **公式编辑器**
- **公式输入区：** 代码编辑器样式，支持语法高亮
- **变量面板：** 显示可用变量（长度L、宽度W、厚度T等）
- **函数库：** 常用函数列表（MAX、MIN、ROUND等）
- **语法检查：** 实时检查公式语法，错误高亮显示
- **测试功能：** 输入测试参数，预览计算结果

#### **数量阶梯编辑器**
- **阶梯表格：** 显示数量区间和对应价格
- **添加阶梯：** 绿色按钮，添加新的数量区间
- **区间校验：** 自动检查区间重叠和遗漏
- **价格类型：** 选择固定价格或折扣百分比

#### **客户差异价格设置**
- **客户类型列表：** 显示所有客户类型
- **折扣设置：** 为每种客户类型设置专属折扣
- **特殊客户：** 为特定客户设置专属价格
- **继承规则：** 设置价格继承和覆盖规则

#### **价格策略测试工具**
- **测试参数输入：** 客户、产品、数量等测试参数
- **匹配过程显示：** 显示价格匹配的详细过程
- **结果对比：** 对比不同策略下的价格差异
- **批量测试：** 上传测试数据进行批量验证

#### **影响分析面板**
- **影响范围：** 显示策略变更影响的客户和产品数量
- **价格变化：** 统计价格上涨和下降的比例
- **风险提示：** 识别可能的风险点和注意事项
- **建议措施：** 提供策略优化建议

#### **审核控制面板**
- **提交审核按钮：** 绿色按钮，触发审核流程
- **审核状态显示：** 显示当前审核节点和审核人
- **审核历史：** 显示审核记录和意见
- **撤回按钮：** 允许撤回待审核的策略

### 数据校验规则：

#### **价格本名称**
- **校验规则：** 必填，2-50位字符，不能重复
- **错误提示文案：** "价格本名称不能为空" / "价格本名称已存在"

#### **生效时间**
- **校验规则：** 不能早于当前时间
- **错误提示文案：** "生效时间不能早于当前时间"

#### **价格公式**
- **校验规则：** 语法正确，变量有效
- **错误提示文案：** "公式语法错误：{具体错误信息}"

#### **数量阶梯**
- **校验规则：** 区间不能重叠，必须覆盖所有可能数量
- **错误提示文案：** "数量区间存在重叠或遗漏"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **价格本基本信息**:
  - **价格本名称 (price_book_name)**: String, 必填, 2-50字符
  - **价格本类型 (price_book_type)**: Enum, 必填, [标准价/经销商价/大客户价/促销价]
  - **生效时间 (effective_date)**: DateTime, 必填
  - **失效时间 (expiry_date)**: DateTime, 可选
- **价格规则**:
  - **产品ID (product_id)**: String, 必填
  - **计价方式 (pricing_method)**: Enum, [固定价/成本加成/公式计算]
  - **基础价格 (base_price)**: Number, 可选
  - **计价公式 (pricing_formula)**: String, 可选
  - **优先级 (priority)**: Number, 默认100

### 展示数据
- **价格本信息**: 名称、类型、状态、生效时间、创建人
- **价格规则**: 产品、计价方式、价格、优先级
- **数量阶梯**: 数量区间、价格或折扣
- **客户差异**: 客户类型、专属折扣
- **影响分析**: 影响范围、价格变化统计
- **测试结果**: 匹配过程、最终价格、对比分析

### 空状态/零数据
- **无价格本**: 显示"暂无价格本，点击新建开始创建"
- **无价格规则**: 显示"请添加价格规则"
- **测试无结果**: 显示"未找到匹配的价格策略"

### API接口
- **创建价格本**: POST /api/price-books
- **获取价格本列表**: GET /api/price-books
- **更新价格规则**: PUT /api/price-books/{id}/rules
- **价格计算**: POST /api/calculate-price
- **价格策略测试**: POST /api/price-books/test

## 5. 异常与边界处理 (Error & Edge Cases)

### **价格公式计算错误**
- **提示信息**: "价格公式计算错误：{错误详情}"
- **用户操作**: 高亮错误公式，提供修正建议

### **价格策略冲突**
- **提示信息**: "存在冲突的价格策略，请检查优先级设置"
- **用户操作**: 显示冲突详情，提供解决方案

### **成本数据缺失**
- **提示信息**: "产品成本数据缺失，无法计算成本加成价格"
- **用户操作**: 提供成本数据维护入口

### **价格异常波动**
- **提示信息**: "价格变化超过30%，请确认是否正确"
- **用户操作**: 显示价格对比，要求二次确认

### **审核超时**
- **提示信息**: "价格策略审核超时，系统已自动提醒审核人"
- **用户操作**: 显示审核进度，提供催办功能

### **策略生效失败**
- **提示信息**: "价格策略生效失败，请检查配置"
- **用户操作**: 显示失败原因，提供重新生效选项

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持创建、编辑、删除、查询价格本
- [ ] 价格规则配置功能完整，支持多种计价方式
- [ ] 公式编辑器功能正常，支持复杂计价公式
- [ ] 数量阶梯价格设置正确，区间校验有效
- [ ] 客户差异化定价功能正常工作
- [ ] 价格策略匹配算法准确，优先级规则正确
- [ ] 价格策略测试工具功能完整，结果准确
- [ ] 影响分析功能正常，风险识别准确
- [ ] 审核流程正确执行，支持多级审核
- [ ] 价格策略生效和失效机制正常
- [ ] 所有操作记录到审计日志
- [ ] 界面支持响应式设计，移动端可查看
- [ ] 价格计算响应时间小于1秒
- [ ] 策略匹配准确率≥95%
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持键盘导航和无障碍访问
