# 功能模块规格说明书：销售订单录入模块

- **模块ID**: SMS-004
- **所属子系统**: 销售管理子系统(SMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 销售代表, **I want to** 通过多种方式快速创建销售订单, **so that** 高效处理客户的订购需求。
- **As a** 销售代表, **I want to** 在类Excel界面中编辑订单明细, **so that** 享受熟悉的操作体验。
- **As a** 销售代表, **I want to** 实时校验订单数据, **so that** 及时发现和纠正错误。
- **As a** 销售代表, **I want to** 复制和批量修改订单明细, **so that** 提高重复性工作的效率。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有订单录入权限
- 客户信息已建立
- 产品主数据已维护
- 价格策略已配置

### 核心流程

#### 2.1 手动订单创建流程
1. 销售代表点击"新建订单"按钮
2. 选择客户或创建新客户
3. 填写订单基本信息（项目名称、交期、交货地址等）
4. 在订单明细表格中添加产品：
   - 选择产品或使用产品配置器
   - 输入数量和单价
   - 添加备注信息
5. 系统实时计算订单总额
6. 数据校验通过后保存订单

#### 2.2 报价单转订单流程
1. 从报价单页面点击"转为订单"
2. 系统自动继承报价单的客户和产品信息
3. 补充订单特有信息：
   - 交货地址和联系人
   - 期望交期
   - 特殊要求和备注
4. 确认价格信息（可微调）
5. 保存订单并更新报价单状态

#### 2.3 订单明细编辑流程
1. 在订单明细表格中定位到目标行
2. 双击单元格进入编辑模式
3. 修改产品、数量、单价等信息
4. 系统实时校验数据有效性
5. Tab键或Enter键确认修改
6. 系统自动重新计算相关金额

#### 2.4 批量操作流程
1. 选择需要操作的订单明细行
2. 选择批量操作类型：
   - 批量修改单价
   - 批量设置交期
   - 批量复制行
   - 批量删除行
3. 在批量操作对话框中设置参数
4. 确认执行批量操作
5. 系统更新相关数据并重新计算

### 后置条件
- 订单信息完整准确
- 数据校验全部通过
- 订单状态为"待审核"
- 生成BOM快照并通知相关部门

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：销售订单录入页面
### 页面目标：提供高效的订单录入和编辑界面

### 信息架构：
- **顶部区域**：包含 订单基本信息表单, 客户选择器, 保存按钮
- **中间区域**：包含 订单明细表格, 工具栏, 批量操作按钮
- **底部区域**：包含 金额汇总, 备注信息, 操作按钮组

### 交互逻辑与状态：

#### **客户选择器**
- **默认状态：** 显示"请选择客户"，搜索图标
- **搜索状态：** 输入关键词，显示匹配客户列表
- **选中状态：** 显示客户名称，蓝色边框
- **交互行为：** 支持模糊搜索，选中后自动加载客户信息

#### **订单基本信息表单**
- **订单号字段：** 系统自动生成，灰色背景不可编辑
- **项目名称：** 文本输入框，可选填写
- **期望交期：** 日期选择器，默认30天后
- **交货地址：** 文本域，支持从客户信息自动填充
- **联系人：** 下拉选择，显示客户的联系人列表

#### **订单明细表格**
- **表格样式：** 类Excel界面，固定表头，支持水平滚动
- **列定义：**
  - 序号：自动编号，灰色背景
  - 产品：下拉选择器，支持搜索
  - 规格：文本输入，或链接到配置器
  - 数量：数值输入，支持小数
  - 单位：自动填充，灰色显示
  - 单价：数值输入，实时校验
  - 小计：自动计算，灰色背景
  - 交期：日期选择器
  - 备注：文本输入
  - 操作：编辑、删除、复制按钮

#### **表格交互行为**
- **单元格编辑：** 双击进入编辑模式，蓝色边框
- **键盘导航：** Tab键切换单元格，Enter键确认编辑
- **行选择：** 点击行号选中整行，支持Ctrl多选
- **右键菜单：** 复制行、粘贴行、插入行、删除行
- **拖拽排序：** 拖拽行号调整行顺序

#### **产品选择器**
- **下拉列表：** 显示产品名称和编码
- **搜索功能：** 支持产品名称、编码、别名搜索
- **配置器链接：** 参数化产品显示"配置"按钮
- **最近使用：** 显示最近使用的产品列表

#### **数量和单价输入**
- **数值校验：** 实时校验正数，高亮错误
- **单位显示：** 自动显示产品单位
- **价格建议：** 显示建议价格，可手动调整
- **计算提示：** 修改后实时显示小计变化

#### **工具栏按钮组**
- **添加行：** 绿色按钮，在表格末尾添加新行
- **批量导入：** 蓝色按钮，打开Excel导入功能
- **批量操作：** 灰色按钮，对选中行执行批量操作
- **清空表格：** 红色边框按钮，清空所有明细

#### **金额汇总区域**
- **产品总额：** 大号字体显示，实时更新
- **税额：** 根据税率自动计算
- **最终总价：** 突出显示，包含税额
- **优惠金额：** 可手动输入折扣金额

#### **批量操作对话框**
- **操作类型选择：** 单选按钮组
- **参数设置：** 根据操作类型显示相应输入框
- **预览结果：** 显示操作后的预期结果
- **确认按钮：** 执行批量操作

### 数据校验规则：

#### **客户选择**
- **校验规则：** 必须选择有效客户
- **错误提示文案：** "请选择客户"

#### **产品选择**
- **校验规则：** 必须选择有效产品
- **错误提示文案：** "请选择有效产品"

#### **数量输入**
- **校验规则：** 必须为正数，最多3位小数
- **错误提示文案：** "数量必须为正数"

#### **单价输入**
- **校验规则：** 必须为正数，最多2位小数
- **错误提示文案：** "单价必须为正数"

#### **交期设置**
- **校验规则：** 不能早于当前日期
- **错误提示文案：** "交期不能早于今天"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **订单基本信息**:
  - **客户ID (customer_id)**: String, 必填
  - **项目名称 (project_name)**: String, 可选, 最大100字符
  - **期望交期 (expected_delivery_date)**: Date, 必填
  - **交货地址 (delivery_address)**: String, 必填, 最大200字符
  - **联系人 (contact_person)**: String, 可选
- **订单明细 (line_items)**:
  - **产品ID (product_id)**: String, 必填
  - **产品规格 (specifications)**: String, 可选
  - **数量 (quantity)**: Number, 必填, 正数
  - **单价 (unit_price)**: Number, 必填, 正数
  - **交期 (delivery_date)**: Date, 可选
  - **备注 (remarks)**: String, 可选

### 展示数据
- **订单信息**: 订单号、客户、项目、状态、总金额
- **产品信息**: 产品名称、编码、规格、库存状态
- **价格信息**: 建议价格、历史价格、价格策略
- **汇总信息**: 产品总额、税额、优惠金额、最终总价
- **校验结果**: 错误提示、警告信息、校验状态

### 空状态/零数据
- **新订单**: 显示空表格，提示"点击添加产品明细"
- **无产品明细**: 显示"请添加至少一个产品"
- **客户未选择**: 显示"请先选择客户"

### API接口
- **创建订单**: POST /api/sales-orders
- **获取产品列表**: GET /api/products
- **价格计算**: POST /api/calculate-price
- **数据校验**: POST /api/validate-order
- **保存草稿**: POST /api/sales-orders/draft

## 5. 异常与边界处理 (Error & Edge Cases)

### **产品库存不足**
- **提示信息**: "产品库存不足，当前库存：X件"
- **用户操作**: 显示库存信息，允许继续下单或调整数量

### **价格超出权限范围**
- **提示信息**: "单价超出权限范围，需要审批"
- **用户操作**: 标记需要审批，允许保存但不能直接提交

### **客户信用额度不足**
- **提示信息**: "客户信用额度不足，当前可用额度：X元"
- **用户操作**: 显示信用信息，提供联系财务部门的选项

### **交期不合理**
- **提示信息**: "交期过于紧急，建议交期：X天后"
- **用户操作**: 显示建议交期，允许用户确认或调整

### **数据保存失败**
- **提示信息**: "订单保存失败，请检查网络后重试"
- **用户操作**: 保持编辑状态，提供重试按钮

### **表格性能问题**
- **提示信息**: "订单明细过多，建议分批处理"
- **用户操作**: 当超过500行时提示，建议使用Excel导入

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持手动创建和报价单转订单两种方式
- [ ] 订单明细表格支持类Excel操作体验
- [ ] 支持500行订单明细无卡顿编辑
- [ ] 实时数据校验功能正常，错误提示清晰
- [ ] 批量操作功能正常（复制、删除、修改）
- [ ] 产品选择器支持搜索和配置器集成
- [ ] 价格计算准确，支持实时更新
- [ ] 金额汇总计算正确，包含税额处理
- [ ] 键盘导航功能完整，支持Tab和Enter操作
- [ ] 右键菜单功能正常工作
- [ ] 订单保存和草稿功能正常
- [ ] 所有数据校验规则正确执行
- [ ] 界面支持响应式设计，移动端可查看
- [ ] 订单创建响应时间小于3秒
- [ ] 表格滚动和编辑操作流畅
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持键盘导航和无障碍访问
