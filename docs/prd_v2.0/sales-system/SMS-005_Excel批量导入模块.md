# 功能模块规格说明书：Excel批量导入模块

- **模块ID**: SMS-005
- **所属子系统**: 销售管理子系统(SMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 销售代表, **I want to** 批量导入客户的Excel订单文件, **so that** 快速处理大批量订单而不需要逐行录入。
- **As a** 销售代表, **I want to** 获得详细的错误报告, **so that** 快速定位和修正数据问题。
- **As a** 销售代表, **I want to** 使用标准化的Excel模板, **so that** 确保数据格式的一致性。
- **As a** 销售代表, **I want to** 预览导入结果, **so that** 在确认无误后再正式导入。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有订单导入权限
- 客户信息已建立
- 产品主数据已维护
- Excel模板已下载

### 核心流程

#### 2.1 Excel模板下载流程
1. 销售代表进入订单导入页面
2. 点击"下载Excel模板"按钮
3. 选择模板类型（标准订单/参数化产品/特殊订单）
4. 系统生成包含示例数据的Excel文件
5. 下载模板文件到本地

#### 2.2 数据准备和上传流程
1. 销售代表在Excel模板中填写订单数据
2. 检查数据格式和必填字段
3. 保存Excel文件
4. 在导入页面点击"选择文件"
5. 选择准备好的Excel文件
6. 系统显示文件信息和预览

#### 2.3 数据解析和校验流程
1. 系统读取Excel文件内容
2. 进行数据格式校验和清洗：
   - 去除前后空格
   - 统一数据格式
   - 校验必填字段
3. 产品信息匹配和验证
4. 价格计算和校验
5. 生成详细的校验报告

#### 2.4 错误处理和修正流程
1. 系统显示错误报告列表
2. 销售代表查看错误详情和建议
3. 选择处理方式：
   - 在线修正错误数据
   - 下载错误报告重新整理Excel
   - 忽略错误行继续导入
4. 重新上传修正后的文件
5. 再次校验直到通过

#### 2.5 确认导入流程
1. 校验通过后显示导入预览
2. 显示将要创建的订单汇总信息
3. 销售代表确认导入
4. 系统批量创建订单记录
5. 显示导入结果统计

### 后置条件
- 订单数据成功导入系统
- 生成导入日志和统计报告
- 错误数据得到妥善处理
- 相关人员收到导入通知

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：Excel批量导入页面
### 页面目标：提供简单高效的批量导入界面

### 信息架构：
- **顶部区域**：包含 导入步骤指示器, 模板下载按钮, 帮助文档链接
- **中间区域**：包含 文件上传区域, 数据预览表格, 错误报告面板
- **底部区域**：包含 导入统计, 操作按钮组, 进度指示器

### 交互逻辑与状态：

#### **步骤指示器**
- **步骤显示：** 1.下载模板 → 2.上传文件 → 3.数据校验 → 4.确认导入
- **当前步骤：** 蓝色圆圈(#1890FF)，白色数字
- **已完成步骤：** 绿色圆圈(#52C41A)，白色对勾
- **未完成步骤：** 灰色圆圈(#D9D9D9)，灰色数字
- **连接线：** 已完成为绿色，未完成为灰色

#### **模板下载区域**
- **下载按钮：** 蓝色边框按钮"下载Excel模板"
- **模板类型选择：** 单选按钮组（标准/参数化/特殊）
- **示例数据：** 复选框"包含示例数据"
- **帮助链接：** 蓝色文字链接"查看填写说明"

#### **文件上传区域**
- **拖拽区域：**
  - **默认状态：** 虚线边框，上传图标，提示文字
  - **拖拽悬停：** 蓝色边框，背景色变化
  - **上传中：** 显示进度条和百分比
  - **上传完成：** 绿色边框，显示文件信息
- **选择文件按钮：** 蓝色实心按钮
- **文件信息显示：** 文件名、大小、上传时间

#### **数据预览表格**
- **表格样式：** 固定表头，斑马纹行，支持水平滚动
- **数据状态标识：**
  - 正常：无特殊标识
  - 警告：橙色背景(#FFF7E6)
  - 错误：红色背景(#FFF2F0)
- **列标题：** 显示字段名称和数据类型
- **分页控制：** 底部分页器，支持跳转

#### **错误报告面板**
- **错误统计：** 显示错误数量和类型分布
- **错误列表：** 表格显示行号、字段、错误类型、错误描述
- **筛选器：** 按错误类型筛选（格式错误/数据缺失/逻辑错误）
- **导出按钮：** 导出错误报告为Excel文件

#### **在线修正工具**
- **编辑模式：** 双击错误单元格进入编辑
- **建议值：** 显示系统建议的修正值
- **批量修正：** 选择多行进行批量修正
- **撤销功能：** 支持撤销修正操作

#### **导入确认对话框**
- **汇总信息：** 显示将要导入的订单数量和总金额
- **风险提示：** 显示可能的风险和注意事项
- **确认按钮：** 绿色按钮"确认导入"
- **取消按钮：** 灰色边框按钮"取消"

#### **进度指示器**
- **导入进度：** 进度条显示当前处理进度
- **处理状态：** 文字描述当前处理阶段
- **预计时间：** 显示预计剩余时间
- **取消按钮：** 允许取消正在进行的导入

#### **结果统计面板**
- **成功统计：** 绿色数字显示成功导入数量
- **失败统计：** 红色数字显示失败数量
- **详细报告：** 链接查看详细的导入报告
- **后续操作：** 提供查看订单、继续导入等选项

### 数据校验规则：

#### **文件格式**
- **校验规则：** 必须为.xlsx或.xls格式，大小不超过10MB
- **错误提示文案：** "请上传Excel格式文件，大小不超过10MB"

#### **数据行数**
- **校验规则：** 数据行数不超过1000行
- **错误提示文案：** "数据行数超过限制，请分批导入"

#### **必填字段**
- **校验规则：** 产品、数量、单价等必填字段不能为空
- **错误提示文案：** "第X行Y列：该字段为必填项"

#### **数据格式**
- **校验规则：** 数量和价格必须为数字，日期必须为有效日期
- **错误提示文案：** "第X行Y列：数据格式不正确"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **Excel文件信息**:
  - **文件名 (file_name)**: String, 系统获取
  - **文件大小 (file_size)**: Number, 不超过10MB
  - **工作表名称 (sheet_name)**: String, 默认"Sheet1"
- **订单数据列**:
  - **产品编码/名称 (product)**: String, 必填
  - **规格描述 (specifications)**: String, 可选
  - **数量 (quantity)**: Number, 必填, 正数
  - **单价 (unit_price)**: Number, 必填, 正数
  - **交期 (delivery_date)**: Date, 可选
  - **备注 (remarks)**: String, 可选

### 展示数据
- **文件信息**: 文件名、大小、行数、列数
- **数据预览**: 前100行数据的表格显示
- **校验结果**: 错误数量、警告数量、成功数量
- **错误详情**: 行号、列名、错误类型、错误描述
- **导入统计**: 处理进度、成功数量、失败数量

### 空状态/零数据
- **未上传文件**: 显示"请上传Excel文件开始导入"
- **文件为空**: 显示"Excel文件中没有数据"
- **全部错误**: 显示"所有数据都存在错误，请检查后重新上传"

### API接口
- **下载模板**: GET /api/import/template
- **上传文件**: POST /api/import/upload
- **数据校验**: POST /api/import/validate
- **执行导入**: POST /api/import/execute
- **获取进度**: GET /api/import/progress/{task_id}

## 5. 异常与边界处理 (Error & Edge Cases)

### **文件格式不支持**
- **提示信息**: "不支持的文件格式，请上传Excel文件"
- **用户操作**: 清空文件选择，重新选择正确格式文件

### **文件损坏或无法读取**
- **提示信息**: "文件损坏或格式异常，请检查文件完整性"
- **用户操作**: 提供文件修复建议，重新上传文件

### **数据量过大**
- **提示信息**: "数据量过大，建议分批导入（每批不超过1000行）"
- **用户操作**: 提供分批导入指导，建议使用分割工具

### **产品匹配失败**
- **提示信息**: "第X行：未找到匹配的产品，请检查产品编码"
- **用户操作**: 显示相似产品建议，提供手动匹配选项

### **价格异常**
- **提示信息**: "第X行：价格异常，超出正常范围"
- **用户操作**: 显示建议价格，允许确认或修正

### **网络中断导致导入失败**
- **提示信息**: "网络中断，导入已暂停，可稍后继续"
- **用户操作**: 保存导入进度，提供断点续传功能

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持.xlsx和.xls格式文件上传
- [ ] 提供标准化的Excel导入模板
- [ ] 支持拖拽和点击两种文件上传方式
- [ ] 文件大小限制10MB，数据行数限制1000行
- [ ] 数据校验功能完整，错误提示清晰具体
- [ ] 支持产品编码和名称的智能匹配
- [ ] 错误报告详细，包含行号和错误描述
- [ ] 支持在线修正错误数据
- [ ] 导入进度实时显示，支持取消操作
- [ ] 500行数据导入处理时间小于30秒
- [ ] 导入成功率≥98%（标准格式数据）
- [ ] 支持断点续传和错误重试
- [ ] 所有操作记录到审计日志
- [ ] 界面支持响应式设计，移动端可查看
- [ ] 导入结果统计准确，支持导出报告
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持键盘导航和无障碍访问
