# 功能模块规格说明书：订单状态管理模块

- **模块ID**: SMS-006
- **所属子系统**: 销售管理子系统(SMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 销售代表, **I want to** 实时跟踪订单的执行状态, **so that** 及时回应客户的进度询问。
- **As a** 客服专员, **I want to** 查看订单的详细进度信息, **so that** 为客户提供准确的交期预估。
- **As a** 销售经理, **I want to** 监控团队的订单执行情况, **so that** 识别潜在的交期风险。
- **As a** 生产计划员, **I want to** 更新订单的生产状态, **so that** 让销售团队了解最新进展。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 订单已创建并提交
- 用户具有订单查看权限
- 相关部门已配置状态更新权限
- 通知规则已设置

### 核心流程

#### 2.1 订单状态生命周期
1. **草稿** - 订单创建但未提交
2. **待审核** - 订单已提交，等待审核
3. **已审核** - 订单审核通过，等待BOM固化
4. **待生产** - BOM已固化，等待生产排程
5. **生产中** - 订单已排程，正在生产
6. **待发货** - 生产完成，等待发货
7. **已发货** - 货物已发出，在途中
8. **已完成** - 客户已收货，订单完成
9. **已取消** - 订单被取消

#### 2.2 状态自动更新流程
1. 系统监听相关业务事件
2. 根据业务规则自动判断状态变更
3. 更新订单状态和时间戳
4. 触发相关通知和提醒
5. 记录状态变更日志

#### 2.3 手动状态更新流程
1. 有权限用户进入订单详情页
2. 点击"更新状态"按钮
3. 选择新的状态并填写备注
4. 系统校验状态变更的合法性
5. 确认更新并发送通知

#### 2.4 状态异常处理流程
1. 系统检测到状态异常（如超期未更新）
2. 自动标记为异常状态
3. 发送预警通知给相关人员
4. 记录异常原因和处理建议
5. 跟踪异常处理进度

### 后置条件
- 订单状态准确反映实际情况
- 相关人员及时收到状态通知
- 状态变更记录完整可追溯
- 异常情况得到及时处理

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：订单状态跟踪页面
### 页面目标：提供清晰直观的订单状态跟踪界面

### 信息架构：
- **顶部区域**：包含 订单基本信息, 状态概览, 快捷操作按钮
- **中间区域**：包含 状态时间轴, 进度可视化, 关键节点信息
- **底部区域**：包含 操作历史, 备注信息, 相关文档链接

### 交互逻辑与状态：

#### **订单状态概览卡片**
- **当前状态显示：** 大号状态标签，颜色编码
- **状态颜色方案：**
  - 草稿：灰色(#8C8C8C)
  - 待审核：橙色(#FAAD14)
  - 已审核：蓝色(#1890FF)
  - 待生产：紫色(#722ED1)
  - 生产中：青色(#13C2C2)
  - 待发货：绿色(#52C41A)
  - 已发货：深蓝(#096DD9)
  - 已完成：深绿(#389E0D)
  - 已取消：红色(#F5222D)

#### **状态时间轴组件**
- **时间轴样式：** 垂直时间轴，左侧时间，右侧内容
- **节点样式：**
  - **已完成节点：** 绿色实心圆，白色对勾
  - **当前节点：** 蓝色实心圆，脉冲动画
  - **未完成节点：** 灰色空心圆
  - **异常节点：** 红色实心圆，感叹号图标
- **连接线：** 已完成为绿色，未完成为灰色虚线

#### **进度可视化组件**
- **进度条：** 水平进度条，显示整体完成百分比
- **里程碑标记：** 在进度条上标记关键节点
- **预期时间：** 显示预计完成时间和剩余时间
- **延期警告：** 超期时显示红色警告标识

#### **状态更新按钮**
- **默认状态：** 蓝色按钮"更新状态"
- **权限不足：** 灰色禁用状态
- **更新中：** 显示加载动画
- **交互行为：** 点击打开状态更新对话框

#### **状态更新对话框**
- **状态选择器：** 下拉选择可变更的状态
- **备注输入：** 文本域，填写状态变更原因
- **附件上传：** 支持上传相关证明文件
- **确认按钮：** 绿色按钮"确认更新"

#### **异常状态提醒**
- **异常标识：** 红色感叹号图标
- **异常描述：** 显示异常类型和原因
- **处理建议：** 提供具体的处理建议
- **联系方式：** 显示负责人联系方式

#### **通知设置面板**
- **通知开关：** 开关组件，控制是否接收通知
- **通知方式：** 复选框组（邮件/短信/系统消息）
- **通知时机：** 选择在哪些状态变更时通知
- **通知人员：** 设置额外的通知接收人

#### **关键节点信息卡片**
- **节点名称：** 显示状态名称和描述
- **时间信息：** 实际时间、预期时间、耗时
- **负责人：** 显示当前阶段的负责人
- **操作记录：** 显示该节点的操作历史

#### **订单进度统计**
- **时间统计：** 各阶段耗时统计和对比
- **效率指标：** 与标准时间的对比
- **瓶颈分析：** 识别耗时最长的环节
- **趋势图表：** 显示历史订单的时间趋势

### 数据校验规则：

#### **状态变更合法性**
- **校验规则：** 只能按照预定义的状态流转路径变更
- **错误提示文案：** "不能从当前状态直接变更到目标状态"

#### **权限校验**
- **校验规则：** 用户必须有对应状态的更新权限
- **错误提示文案：** "您没有权限更新此状态"

#### **时间逻辑校验**
- **校验规则：** 状态时间不能早于前一状态时间
- **错误提示文案：** "状态时间不能早于前一状态"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **状态更新信息**:
  - **新状态 (new_status)**: Enum, 必填, 预定义状态值
  - **更新时间 (update_time)**: DateTime, 系统自动
  - **更新人 (updated_by)**: String, 系统自动
  - **备注 (remarks)**: String, 可选, 最大500字符
- **通知设置**:
  - **通知方式 (notification_methods)**: Array, 邮件/短信/系统消息
  - **通知人员 (notification_users)**: Array, 用户ID列表

### 展示数据
- **订单基本信息**: 订单号、客户、项目、总金额
- **当前状态**: 状态名称、状态时间、负责人
- **状态历史**: 所有状态变更记录和时间
- **进度信息**: 完成百分比、预计完成时间
- **异常信息**: 异常类型、异常原因、处理建议
- **时间统计**: 各阶段耗时、总耗时、平均耗时

### 空状态/零数据
- **新订单**: 显示"订单刚创建，暂无状态更新"
- **无权限查看**: 显示"您没有权限查看此订单状态"
- **状态异常**: 显示"状态信息异常，请联系管理员"

### API接口
- **获取订单状态**: GET /api/orders/{id}/status
- **更新订单状态**: POST /api/orders/{id}/status
- **获取状态历史**: GET /api/orders/{id}/status-history
- **设置通知**: POST /api/orders/{id}/notifications
- **获取异常订单**: GET /api/orders/exceptions

## 5. 异常与边界处理 (Error & Edge Cases)

### **状态更新冲突**
- **提示信息**: "其他用户正在更新此订单状态，请稍后重试"
- **用户操作**: 显示当前操作用户，提供刷新按钮

### **状态流转异常**
- **提示信息**: "状态流转异常，请检查前置条件是否满足"
- **用户操作**: 显示前置条件检查结果，提供修正建议

### **超期未更新**
- **提示信息**: "订单状态超期未更新，可能存在异常"
- **用户操作**: 自动标记异常，发送提醒通知

### **权限变更**
- **提示信息**: "您的权限已变更，无法继续操作"
- **用户操作**: 刷新页面权限，隐藏无权限操作

### **网络异常**
- **提示信息**: "网络异常，状态更新失败"
- **用户操作**: 保持编辑状态，提供重试按钮

### **数据同步延迟**
- **提示信息**: "数据同步中，状态可能不是最新"
- **用户操作**: 显示同步状态，提供手动刷新

## 6. 验收标准 (Acceptance Criteria)

- [ ] 订单状态生命周期管理正确，支持9种状态
- [ ] 状态时间轴显示清晰，节点状态准确
- [ ] 进度可视化组件正常工作，百分比计算正确
- [ ] 状态自动更新功能正常，触发条件准确
- [ ] 手动状态更新功能正常，权限控制有效
- [ ] 状态变更通知及时发送，通知内容准确
- [ ] 异常状态检测和预警功能正常工作
- [ ] 状态历史记录完整，支持追溯查询
- [ ] 时间统计和分析功能准确，支持效率对比
- [ ] 通知设置功能正常，支持多种通知方式
- [ ] 所有状态变更记录到审计日志
- [ ] 界面支持响应式设计，移动端可正常使用
- [ ] 状态查询响应时间小于500ms
- [ ] 状态更新响应时间小于2秒
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持键盘导航和无障碍访问
