# 功能模块规格说明书：产品配置器模块

- **模块ID**: SMS-002
- **所属子系统**: 销售管理子系统(SMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 销售代表, **I want to** 通过可视化界面配置参数化产品, **so that** 快速为客户生成准确的产品规格和价格。
- **As a** 销售代表, **I want to** 实时查看配置变化对价格的影响, **so that** 在客户面前展示专业性和透明度。
- **As a** 销售代表, **I want to** 保存常用的产品配置模板, **so that** 提高重复订单的处理效率。
- **As a** 销售代表, **I want to** 查看产品的3D预览效果, **so that** 帮助客户更好地理解产品。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有产品配置权限
- PDM系统中已建立参数化BOM
- 价格策略已配置完成
- 产品主数据已维护

### 核心流程

#### 2.1 产品配置流程
1. 销售代表选择产品类别（如"淋浴房门"）
2. 系统显示该产品的参数配置界面
3. 输入客户要求的参数（长度L、宽度W、厚度T等）
4. 系统实时调用PDM的参数化BOM计算用量
5. 根据当前客户的价格策略计算价格
6. 显示配置结果和价格明细
7. 保存配置或添加到报价单/订单

#### 2.2 配置模板管理流程
1. 在产品配置完成后点击"保存为模板"
2. 输入模板名称和描述信息
3. 选择模板的可见范围（个人/团队/公开）
4. 保存模板到模板库
5. 后续可通过模板快速加载配置

#### 2.3 批量配置流程
1. 选择"批量配置"模式
2. 上传包含多个产品参数的Excel文件
3. 系统逐行解析并进行参数化配置
4. 显示配置结果和异常报告
5. 确认后批量添加到订单明细

### 后置条件
- 产品配置信息准确完整
- 价格计算结果正确
- 配置数据可传递给报价单或订单系统

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：产品配置器页面
### 页面目标：提供直观高效的参数化产品配置界面

### 信息架构：
- **左侧区域**：包含 产品分类树, 配置模板库, 历史配置记录
- **中间区域**：包含 参数配置面板, 实时预览区域, 计算结果显示
- **右侧区域**：包含 价格明细, 用量清单, 操作按钮组

### 交互逻辑与状态：

#### **产品分类选择器**
- **默认状态：** 显示产品分类树，支持搜索
- **选中状态：** 蓝色背景高亮，显示产品图标
- **加载状态：** 显示骨架屏，加载产品参数定义
- **交互行为：** 点击分类加载对应的参数配置界面

#### **参数输入区域**
- **数值参数输入框：**
  - **默认状态：** 白色背景，显示参数名称和单位
  - **聚焦状态：** 蓝色边框，显示参数说明提示
  - **错误状态：** 红色边框，显示错误信息
  - **计算中状态：** 显示加载图标，禁用输入
- **选择参数下拉框：**
  - **默认状态：** 显示当前选项和下拉箭头
  - **展开状态：** 显示选项列表，支持搜索
  - **选中状态：** 蓝色文字，收起下拉列表

#### **实时预览区域**
- **2D预览：** 显示产品的平面图，标注关键尺寸
- **3D预览：** 支持旋转、缩放的3D模型（可选）
- **参数标注：** 在预览图上标注当前参数值
- **更新动画：** 参数变化时平滑过渡到新状态

#### **价格计算显示区**
- **实时价格：** 大号字体显示当前配置的总价
- **价格明细：** 展开显示材料成本、加工费、利润等
- **价格变化提示：** 参数调整时高亮显示价格变化
- **计算状态：** 计算中显示加载动画

#### **用量清单区域**
- **材料清单：** 表格显示各材料的用量和单价
- **工序清单：** 显示涉及的工艺工序和费用
- **总计信息：** 显示总重量、总面积等汇总数据
- **详情展开：** 支持展开查看详细计算过程

#### **操作按钮组**
- **重置按钮：** 灰色边框按钮，恢复默认参数
- **保存模板：** 蓝色边框按钮，保存当前配置
- **添加到报价单：** 蓝色实心按钮，主要操作
- **添加到订单：** 绿色实心按钮，直接下单

#### **配置模板库**
- **模板列表：** 卡片形式显示保存的模板
- **模板搜索：** 支持按名称和标签搜索
- **模板预览：** 悬停显示模板的参数概要
- **快速应用：** 点击模板快速加载配置

### 数据校验规则：

#### **尺寸参数**
- **校验规则：** 必须为正数，在产品规格范围内
- **错误提示文案：** "长度必须在300-3000mm范围内"

#### **数量参数**
- **校验规则：** 必须为正整数，不超过最大订购量
- **错误提示文案：** "数量必须为正整数，最大999件"

#### **材质选择**
- **校验规则：** 必须从预定义选项中选择
- **错误提示文案：** "请选择有效的材质类型"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **产品类别 (product_category)**: String, 必填, 产品分类ID
- **参数配置 (parameters)**: Object, 必填, 参数名值对
  - **长度 (length)**: Number, 单位mm, 范围300-3000
  - **宽度 (width)**: Number, 单位mm, 范围200-2000
  - **厚度 (thickness)**: Number, 单位mm, 可选值[6,8,10,12]
  - **材质 (material)**: String, 枚举值, 如"钢化玻璃"
- **数量 (quantity)**: Number, 必填, 正整数, 默认1
- **客户信息 (customer_id)**: String, 可选, 用于价格策略匹配

### 展示数据
- **产品信息**: 产品名称、规格描述、产品图片
- **参数定义**: 参数名称、单位、取值范围、默认值
- **计算结果**: 总价、单价、材料用量、重量
- **价格明细**: 材料成本、加工费、管理费、利润
- **BOM信息**: 主要材料清单、工艺路线、加工时间

### 空状态/零数据
- **未选择产品**: 显示"请选择要配置的产品类别"
- **参数未填写**: 显示"请填写必要的产品参数"
- **计算失败**: 显示"价格计算失败，请检查参数设置"

### API接口
- **获取产品分类**: GET /api/products/categories
- **获取产品参数定义**: GET /api/products/{id}/parameters
- **计算产品价格**: POST /api/products/calculate-price
- **保存配置模板**: POST /api/product-templates
- **获取配置模板**: GET /api/product-templates

## 5. 异常与边界处理 (Error & Edge Cases)

### **参数超出范围**
- **提示信息**: "参数值超出产品规格范围，请调整"
- **用户操作**: 参数框标红，显示有效范围提示

### **价格计算失败**
- **提示信息**: "价格计算失败，请检查参数或联系技术支持"
- **用户操作**: 显示错误详情，提供重试按钮

### **BOM数据缺失**
- **提示信息**: "产品BOM数据不完整，无法进行配置"
- **用户操作**: 禁用配置功能，提示联系管理员

### **网络超时**
- **提示信息**: "网络连接超时，请稍后重试"
- **用户操作**: 保持当前配置状态，提供重试按钮

### **模板保存失败**
- **提示信息**: "模板保存失败，请检查模板名称是否重复"
- **用户操作**: 保持编辑状态，允许修改后重新保存

### **批量配置错误**
- **提示信息**: "批量配置完成，X项成功，Y项失败"
- **用户操作**: 显示错误详情列表，支持单独修正

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持所有参数化产品的可视化配置
- [ ] 参数输入界面直观易用，支持实时校验
- [ ] 价格计算响应时间小于2秒
- [ ] 实时预览功能正常，参数变化及时反映
- [ ] 配置模板保存和加载功能正常工作
- [ ] 支持批量配置和Excel导入
- [ ] 价格明细显示完整，包含成本构成
- [ ] 用量清单计算准确，与PDM系统一致
- [ ] 配置结果可正确传递给报价单和订单系统
- [ ] 参数校验规则正确执行，错误提示清晰
- [ ] 支持产品的2D预览，3D预览（如可用）
- [ ] 所有操作记录到审计日志
- [ ] 界面支持响应式设计，移动端可查看
- [ ] 配置器加载时间小于3秒
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持键盘导航和无障碍访问
