# 功能模块规格说明书：项目现场管理模块

- **模块ID**: PJS-009
- **所属子系统**: 项目管理子系统(PJS)
- **最后更新**: 2025-07-31
- **功能说明**: 管理项目现场的安装进度、质量控制、安全管理、验收交付等全过程，确保项目现场工作的规范化和高效化

## 1. 用户故事 (User Stories)

- **As a** 项目经理, **I want to** 监控现场安装进度, **so that** 确保项目按时完成交付。
- **As a** 现场工程师, **I want to** 记录现场工作日志, **so that** 跟踪工作进展和问题处理。
- **As a** 质量工程师, **I want to** 执行现场质量检查, **so that** 确保安装质量符合标准。
- **As a** 安全员, **I want to** 监督现场安全措施, **so that** 防范安全事故和风险。
- **As a** 客户, **I want to** 了解项目现场进度, **so that** 掌握项目完成情况和验收安排。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 项目已启动并进入现场实施阶段
- 现场人员已到位并完成培训
- 现场设备和材料已准备就绪
- 现场安全措施已落实到位

### 核心流程

#### 2.1 现场准备流程
1. 制定现场实施计划和安排
2. 配置现场人员和设备资源
3. 建立现场安全和质量标准
4. 设置现场管理制度和流程
5. 进行现场开工前检查
6. 启动现场实施工作

#### 2.2 现场安装流程
1. 按计划执行现场安装工作
2. 记录每日工作进度和完成情况
3. 处理现场技术问题和变更
4. 协调现场资源和人员安排
5. 监控安装质量和安全状况
6. 与客户沟通现场进展

#### 2.3 现场质量控制流程
1. 制定现场质量检查计划
2. 执行关键节点质量检查
3. 记录质量问题和整改措施
4. 跟踪质量问题的解决进度
5. 进行现场质量验收
6. 生成质量检查报告

#### 2.4 项目验收交付流程
1. 完成现场安装和调试工作
2. 进行内部验收和自检
3. 邀请客户进行现场验收
4. 处理验收中发现的问题
5. 完成项目交付和移交
6. 进行项目总结和经验积累

### 系统集成说明
- **与PJS-001集成**: 获取项目基础信息和计划
- **与PJS-003集成**: 同步项目进度和里程碑
- **与QMS系统集成**: 共享质量标准和检查数据
- **与HR系统集成**: 获取现场人员信息

### 后置条件
- 现场工作按计划完成
- 质量和安全要求得到满足
- 客户验收顺利通过
- 项目成功交付和移交

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：项目现场管理仪表板
### 页面目标：提供项目现场工作的全面监控和管理界面

### 信息架构：
- **顶部区域**：包含 项目概览, 现场状态, 快捷操作
- **中间区域**：包含 进度跟踪, 质量监控, 安全管理
- **底部区域**：包含 工作日志, 问题处理, 文档管理

### 交互逻辑与状态：

#### **现场概览仪表板**
- **项目信息：**
  - **项目名称：** 显示当前项目的名称和编号
  - **项目阶段：** 显示项目当前所处阶段
  - **完成进度：** 显示项目整体完成百分比
  - **剩余工期：** 显示项目剩余工作天数
- **现场状态：**
  - **在场人员：** 显示当前在场工作人员数量
  - **设备状态：** 显示现场设备的运行状态
  - **安全状况：** 显示现场安全等级和状况
  - **天气情况：** 显示当前天气对施工的影响

#### **进度跟踪管理**
- **进度展示：**
  - **甘特图：** 显示现场工作的时间进度
  - **里程碑：** 显示关键节点的完成状态
  - **工作分解：** 显示具体工作任务的进展
  - **资源使用：** 显示人员和设备的使用情况
- **进度更新：**
  - **日报录入：** 录入每日工作进度报告
  - **照片上传：** 上传现场工作照片
  - **问题记录：** 记录现场遇到的问题
  - **进度确认：** 确认进度更新的准确性

#### **质量监控系统**
- **质量检查：**
  - **检查计划：** 显示质量检查的计划安排
  - **检查记录：** 记录质量检查的结果
  - **问题跟踪：** 跟踪质量问题的处理进度
  - **验收管理：** 管理分阶段验收工作
- **质量状态：**
  - **合格：** 绿色标识，质量检查合格
  - **待整改：** 橙色标识，存在质量问题待整改
  - **不合格：** 红色标识，质量检查不合格
  - **免检：** 蓝色标识，免检项目

#### **安全管理系统**
- **安全检查：**
  - **安全巡检：** 定期进行现场安全巡检
  - **隐患排查：** 排查现场安全隐患
  - **整改跟踪：** 跟踪安全隐患的整改
  - **事故记录：** 记录安全事故和处理
- **安全状态：**
  - **安全：** 绿色标识，现场安全状况良好
  - **注意：** 橙色标识，存在安全注意事项
  - **危险：** 红色标识，存在安全危险
  - **禁止：** 黑色标识，禁止作业区域

## 4. 数据需求 (Data Requirements)

### 核心数据实体

#### **现场工作日志 (SiteWorkLog)**
```sql
CREATE TABLE site_work_log (
    log_id VARCHAR(32) PRIMARY KEY COMMENT '日志ID',
    project_id VARCHAR(32) NOT NULL COMMENT '项目ID',
    work_date DATE NOT NULL COMMENT '工作日期',
    weather VARCHAR(50) COMMENT '天气情况',
    work_content TEXT NOT NULL COMMENT '工作内容',
    progress_percent DECIMAL(5,2) COMMENT '进度百分比',
    personnel_count INT COMMENT '现场人员数量',
    equipment_status VARCHAR(200) COMMENT '设备状态',
    issues_encountered TEXT COMMENT '遇到的问题',
    next_day_plan TEXT COMMENT '次日计划',
    reporter_id VARCHAR(32) NOT NULL COMMENT '报告人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### **现场质量检查 (SiteQualityInspection)**
```sql
CREATE TABLE site_quality_inspection (
    inspection_id VARCHAR(32) PRIMARY KEY COMMENT '检查ID',
    project_id VARCHAR(32) NOT NULL COMMENT '项目ID',
    inspection_date DATE NOT NULL COMMENT '检查日期',
    inspection_type VARCHAR(50) NOT NULL COMMENT '检查类型',
    inspection_area VARCHAR(100) NOT NULL COMMENT '检查区域',
    inspection_items TEXT NOT NULL COMMENT '检查项目',
    inspection_result VARCHAR(20) NOT NULL COMMENT '检查结果',
    issues_found TEXT COMMENT '发现的问题',
    corrective_actions TEXT COMMENT '整改措施',
    inspector_id VARCHAR(32) NOT NULL COMMENT '检查员',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### **现场安全管理 (SiteSafetyManagement)**
```sql
CREATE TABLE site_safety_management (
    safety_id VARCHAR(32) PRIMARY KEY COMMENT '安全记录ID',
    project_id VARCHAR(32) NOT NULL COMMENT '项目ID',
    safety_date DATE NOT NULL COMMENT '安全检查日期',
    safety_type VARCHAR(50) NOT NULL COMMENT '安全检查类型',
    safety_area VARCHAR(100) NOT NULL COMMENT '检查区域',
    safety_status VARCHAR(20) NOT NULL COMMENT '安全状态',
    hazards_identified TEXT COMMENT '识别的隐患',
    safety_measures TEXT COMMENT '安全措施',
    incident_record TEXT COMMENT '事故记录',
    safety_officer_id VARCHAR(32) NOT NULL COMMENT '安全员',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 数据字典

#### **检查类型 (inspection_type)**
- DAILY: 日常检查
- MILESTONE: 里程碑检查
- FINAL: 最终验收
- SPECIAL: 专项检查

#### **检查结果 (inspection_result)**
- QUALIFIED: 合格
- UNQUALIFIED: 不合格
- CONDITIONAL: 有条件合格
- PENDING: 待检查

#### **安全状态 (safety_status)**
- SAFE: 安全
- CAUTION: 注意
- DANGER: 危险
- PROHIBITED: 禁止

#### **安全检查类型 (safety_type)**
- ROUTINE: 例行检查
- SPECIAL: 专项检查
- EMERGENCY: 应急检查
- AUDIT: 安全审计

### 数据关系
- 项目 ←→ 工作日志 (1:N)
- 项目 ←→ 质量检查 (1:N)
- 项目 ←→ 安全管理 (1:N)

## 5. 错误处理与边界情况 (Error Handling & Edge Cases)

### 业务规则验证

#### **现场工作验证**
- **人员资质检查：** 验证现场人员的资质和证书
- **设备状态验证：** 检查现场设备的可用性和安全性
- **工作条件验证：** 验证现场工作条件是否满足要求
- **进度合理性：** 检查进度更新的合理性和连续性

#### **质量安全验证**
- **检查标准验证：** 确保质量检查符合相关标准
- **安全规范验证：** 验证安全措施符合规范要求
- **整改时限验证：** 检查问题整改的时限要求
- **验收条件验证：** 验证验收条件是否满足

### 异常情况处理

#### **系统异常**
- **网络连接中断：** 现场网络连接不稳定
  - 错误提示：网络连接异常，数据将在恢复后同步
  - 处理方式：支持离线工作和数据缓存
- **设备故障：** 现场设备出现故障
  - 错误提示：设备故障，请联系设备管理员
  - 处理方式：提供设备故障报告和维修申请

#### **业务异常**
- **恶劣天气：** 恶劣天气影响现场工作
  - 错误提示：天气条件不适宜施工
  - 处理方式：提供天气预警和工作调整建议
- **安全事故：** 现场发生安全事故
  - 错误提示：发生安全事故，请立即处理
  - 处理方式：启动应急预案和事故报告流程

### 边界情况

#### **极限条件**
- **恶劣环境：** 极端天气或环境条件
- **紧急工期：** 极短工期的紧急项目
- **复杂现场：** 技术难度极高的现场

#### **特殊情况**
- **客户变更：** 客户临时变更需求
- **材料短缺：** 关键材料供应不足
- **人员调整：** 现场人员临时调整

## 6. 验收标准 (Acceptance Criteria)

### 功能验收标准

#### **现场管理功能**
- [ ] 能够记录和跟踪现场工作进度
- [ ] 支持现场工作日志的录入和管理
- [ ] 现场问题能够及时记录和处理
- [ ] 现场资源使用情况能够监控

#### **质量控制功能**
- [ ] 能够执行现场质量检查
- [ ] 支持质量问题的跟踪和整改
- [ ] 质量验收能够规范执行
- [ ] 质量报告能够自动生成

#### **安全管理功能**
- [ ] 能够进行现场安全检查
- [ ] 支持安全隐患的识别和整改
- [ ] 安全事故能够及时记录和处理
- [ ] 安全状态能够实时监控

### 性能验收标准

#### **响应时间要求**
- [ ] 现场数据录入响应时间 ≤ 2秒
- [ ] 进度查询响应时间 ≤ 3秒
- [ ] 报告生成时间 ≤ 10秒
- [ ] 数据同步时间 ≤ 30秒

#### **可用性要求**
- [ ] 支持离线工作模式
- [ ] 移动设备兼容性良好
- [ ] 系统稳定性达到99%以上

### 集成验收标准

#### **系统集成**
- [ ] 与项目管理系统集成正常
- [ ] 与质量管理系统集成正常
- [ ] 与人力资源系统集成正常
- [ ] 数据同步准确及时

#### **用户体验**
- [ ] 界面设计符合现场使用习惯
- [ ] 操作流程简洁高效
- [ ] 移动端使用体验良好
- [ ] 帮助文档完整易懂

### 安全验收标准

#### **权限控制**
- [ ] 现场数据访问权限严格控制
- [ ] 质量检查权限分级有效
- [ ] 安全管理权限正确配置
- [ ] 操作日志记录完整

#### **数据安全**
- [ ] 现场数据加密传输和存储
- [ ] 敏感信息脱敏处理
- [ ] 数据备份恢复机制完善
- [ ] 审计跟踪功能完整
