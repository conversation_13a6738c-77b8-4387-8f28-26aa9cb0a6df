# PRD-08: 项目管理子系统 产品需求文档

> **版本**: 2.0  
> **状态**: 重构版  
> **撰写人**: 产品团队  
> **日期**: 2025-07-30  
> **术语表**: 参考 [全局术语表](../_Glossary.md)  
> **业务规则**: 参考 [核心业务规则库](../_Business_Rules.md)

---

## 1. 核心问题与价值主张

### 1.1 核心问题
**玻璃深加工企业的工程项目（防火窗、幕墙等）周期长、交付复杂、涉及多阶段，传统ERP难以有效管理项目全生命周期的进度、成本和资源协调。**

### 1.2 价值主张
构建项目制管理平台，实现工程项目从立项到交付的全生命周期精细化管控，提供项目可视性、成本核算和多阶段交付管理。

### 1.3 商业价值量化
- **项目可视性提升**: 项目进度透明度从30%提升至95%，管理决策效率提升60%
- **成本核算精度**: 项目成本核算准确率从70%提升至95%，利润分析准确性提升80%
- **交付效率提升**: 多阶段交付管理使项目交付准时率从75%提升至90%
- **资源协调效率**: 内外部资源协同效率提升50%，项目执行周期缩短20%

---

## 2. 目标用户与使用场景

### 2.1 目标用户
参考 [全局术语表](../_Glossary.md) 中的用户角色定义：

| 用户角色 | 职责描述 | 核心需求 |
|----------|----------|----------|
| **项目经理** | 负责项目全生命周期管理、进度控制、成本监控 | 需要项目可视化工具和成本分析功能 |
| **项目主管** | 负责多项目管理、资源分配、绩效评估 | 需要项目组合管理和决策支持工具 |
| **设计工程师** | 负责项目设计、技术方案、图纸深化 | 需要设计任务管理和协作工具 |
| **生产计划员** | 负责项目生产计划、资源调度、进度跟踪 | 需要项目驱动的生产计划工具 |

### 2.2 核心使用场景

#### 场景一：创建工程项目并分解结构
**用户故事**: 作为一个项目经理，我想要创建多层级的项目结构，以便精确管理到楼栋、楼层、房间的产品交付清单。

**操作流程**:
1. 新建项目，填写基本信息（项目名称、客户、合同金额、预计起止日期）
2. 分解项目结构：创建楼栋→楼层→房间的层级结构
3. 在各层级下录入需要交付的产品清单和数量
4. 制定WBS计划，分解为设计、生产、安装等阶段
5. 为每个任务设置计划时间、负责人和预算
6. 生成项目甘特图和里程碑计划

**成功标准**: 项目结构创建灵活，WBS分解清晰，计划制定直观

#### 场景二：项目驱动生产与采购
**用户故事**: 作为一个项目经理，我想要项目需求自动驱动生产和采购，以便确保项目物料供应和生产安排。

**操作流程**:
1. 在WBS中选择特定任务（如"窗框生产"）
2. 系统自动汇总该任务关联的所有产品BOM
3. 筛选出相关物料需求并传递到MRP运算
4. 物料需求打上项目标签，关联到具体项目
5. 采购员和计划员看到带项目标签的需求
6. 创建关联到项目的采购订单和生产订单

**成功标准**: 项目需求自动传递准确率100%，所有单据与项目关联

#### 场景三：实时监控项目成本和进度
**用户故事**: 作为一个项目经理，我想要实时监控项目成本和进度，以便及时发现问题并采取纠正措施。

**操作流程**:
1. 打开项目成本分析视图
2. 系统自动汇总实际材料成本、人工成本、其他费用
3. 实际成本与预算进行实时对比，高亮显示超支项
4. 切换到进度跟踪视图，查看甘特图形式的任务完成情况
5. 分析关键路径和风险任务
6. 生成项目健康状况报告

**成功标准**: 成本数据实时准确，进度可视化清晰，风险预警及时

---

## 3. 功能需求（用户故事格式）

### 3.1 项目主数据管理

#### 需求 3.1.1: 项目立项管理
**用户故事**: 作为一个项目经理，我想要创建和维护项目主数据，以便建立项目管理的基础信息。

**功能描述**:
- 项目立项和基本信息管理
- 项目编码规则和唯一性控制
- 项目状态和生命周期管理
- 项目团队和角色分配

**验收标准**:
- [ ] 支持项目立项，包含项目编码、名称、客户、合同金额等
- [ ] 项目编码全局唯一，支持自定义编码规则
- [ ] 项目状态包含立项、进行中、已完工、已关闭
- [ ] 支持项目经理和团队成员的分配管理

#### 需求 3.1.2: 项目结构分解
**用户故事**: 作为一个项目经理，我想要创建多层级的项目结构，以便精确管理交付地点和产品清单。

**功能描述**:
- 灵活的多层级项目结构创建
- 项目交付清单管理
- 结构节点属性设置
- 销售订单与项目关联

**验收标准**:
- [ ] 支持不限层级的树状结构（楼栋、楼层、房间等）
- [ ] 每个节点可设置属性和交付产品清单
- [ ] 支持结构节点的增删改查操作
- [ ] 销售订单可直接关联到项目节点

### 3.2 项目计划与进度管理

#### 需求 3.2.1: WBS任务管理
**用户故事**: 作为一个项目经理，我想要使用WBS分解项目任务，以便对项目执行进行有效的计划和控制。

**功能描述**:
- WBS任务创建和维护
- 任务依赖关系设置
- 任务属性和资源分配
- 里程碑节点管理

**验收标准**:
- [ ] 支持创建WBS任务，设置计划起止时间、工期、负责人
- [ ] 支持任务前置关系和依赖设置
- [ ] 支持关键里程碑节点的设置和追踪
- [ ] 任务属性完整，包含预算、资源等信息

#### 需求 3.2.2: 项目进度跟踪
**用户故事**: 作为一个项目经理，我想要可视化跟踪项目进度，以便及时发现进度偏差。

**功能描述**:
- 甘特图可视化展示
- 任务进度填报和更新
- 关键路径分析
- 进度偏差预警

**验收标准**:
- [ ] 提供甘特图视图，展示项目计划和任务依赖
- [ ] 支持任务实际进度填报（完成百分比）
- [ ] 甘特图实时反映任务进度状态
- [ ] 自动识别关键路径和风险任务

### 3.3 项目成本管理

#### 需求 3.3.1: 成本归集
**用户故事**: 作为一个项目经理，我想要系统自动归集项目相关成本，以便实时监控项目预算执行情况。

**功能描述**:
- 多维度成本归集
- 业务单据与项目关联
- 成本分类和统计
- 实时成本计算

**验收标准**:
- [ ] 采购订单、生产领料、工序报工等单据可关联项目
- [ ] 按材料费、人工费、制造费用、分包费等科目归集成本
- [ ] 提供项目成本仪表盘，实时显示成本构成
- [ ] 成本数据更新延迟<5分钟

#### 需求 3.3.2: 预算管理
**用户故事**: 作为一个项目经理，我想要制定和管理项目预算，以便控制项目成本。

**功能描述**:
- 项目预算编制
- 预实对比分析
- 成本超支预警
- 预算调整管理

**验收标准**:
- [ ] 支持为项目WBS任务编制预算成本
- [ ] 提供预算与实际成本的对比分析
- [ ] 成本超支自动预警，预警阈值可配置
- [ ] 支持预算调整和版本管理

### 3.4 项目交付管理

#### 需求 3.4.1: 分阶段交付
**用户故事**: 作为一个项目经理，我想要分阶段、分批次安排生产和发货，以便满足复杂的现场交付要求。

**功能描述**:
- 交付计划制定
- 分批次生产需求下达
- 交付状态跟踪
- 现场安装管理

**验收标准**:
- [ ] 支持从项目WBS节点圈选产品清单
- [ ] 一键生成对应的生产需求和发货通知
- [ ] 生成的订单自动关联回源头的项目WBS任务
- [ ] 追踪每个交付批次的状态（待生产、生产中、已发货、已签收）

#### 需求 3.4.2: 项目收款管理
**用户故事**: 作为一个项目经理，我想要管理项目收款进度，以便监控项目现金流。

**功能描述**:
- 收款计划制定
- 收款进度跟踪
- 应收账款管理
- 项目结算

**验收标准**:
- [ ] 支持按项目里程碑制定收款计划
- [ ] 实时跟踪项目收款进度和应收余额
- [ ] 与财务系统集成，自动更新收款状态
- [ ] 支持项目最终结算和利润核算

---

## 4. 验收标准（可测试列表）

### 4.1 功能验收标准
- [ ] 项目结构创建灵活性 100%
- [ ] 成本归集准确率 ≥ 95%
- [ ] 进度跟踪实时性 ≤ 5分钟延迟
- [ ] 交付计划执行准确率 ≥ 90%
- [ ] 预算控制有效性 ≥ 85%

### 4.2 性能验收标准
- [ ] 甘特图操作响应时间 < 2秒（500个任务）
- [ ] 成本报表计算时间 < 10秒（1000张单据）
- [ ] 项目数据查询响应时间 < 3秒
- [ ] 项目看板加载时间 < 5秒
- [ ] 系统并发处理能力 ≥ 30用户

### 4.3 业务效果验收标准
- [ ] 项目进度透明度提升至 ≥ 95%
- [ ] 成本核算准确率提升至 ≥ 95%
- [ ] 项目交付准时率提升至 ≥ 90%
- [ ] 资源协同效率提升 ≥ 50%

---

## 5. 交互设计要求

### 5.1 界面设计原则
- **项目导向**: 以项目为中心的信息架构和导航设计
- **可视化优先**: 甘特图、看板、仪表盘等可视化展示
- **操作便捷**: 拖拽操作、批量处理、快捷键支持
- **信息层次**: 清晰的信息层次和钻取分析能力

### 5.2 关键界面要求
- **项目看板**: 项目概览、状态监控、关键指标展示
- **甘特图**: 任务计划、进度跟踪、依赖关系可视化
- **成本分析**: 成本构成、预实对比、趋势分析
- **交付管理**: 交付计划、批次管理、状态跟踪

---

## 6. 数据埋点需求

### 6.1 项目管理埋点
- 项目创建和结构分解行为
- WBS任务管理和进度更新
- 成本归集和预算管理
- 交付计划和执行情况

### 6.2 业务效果埋点
- 项目进度透明度指标
- 成本核算准确性
- 交付准时率
- 资源协同效率

### 6.3 系统性能埋点
- 甘特图操作响应时间
- 成本计算耗时
- 数据查询性能
- 用户操作行为

---

## 7. 未来展望/V-Next

### 7.1 暂不开发功能
- **AI项目管理**: 智能进度预测和风险识别
- **移动项目管理**: 移动端的项目监控和审批
- **项目协作**: 团队协作和沟通工具集成
- **高级分析**: 项目组合分析和绩效评估

### 7.2 技术演进方向
- **实时协作**: 多人实时编辑项目计划
- **智能调度**: AI驱动的资源优化配置
- **预测分析**: 基于历史数据的项目风险预测

---

## 版本控制

| 版本 | 日期 | 变更说明 | 责任人 |
|------|------|----------|--------|
| 1.0 | 2025-07-29 | 初始版本 | Roo |
| 2.0 | 2025-07-30 | 重构版本，应用五大核心原则 | 产品团队 |

---

**文档状态**: 已重构 ✅  
**应用原则**: 第一性原理 ✅ DRY ✅ KISS ✅ SOLID ✅ YAGNI ✅
