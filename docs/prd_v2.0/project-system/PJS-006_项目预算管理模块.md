# 功能模块规格说明书：项目预算管理模块

- **模块ID**: PJS-006
- **所属子系统**: 项目管理子系统(Project System)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 项目经理, **I want to** 制定详细的项目预算计划, **so that** 有效控制项目成本支出。
- **As a** 财务经理, **I want to** 审核和批准项目预算, **so that** 确保预算的合理性和可执行性。
- **As a** 项目团队成员, **I want to** 查看可用预算余额, **so that** 合理安排资源使用。
- **As a** 项目主管, **I want to** 监控预算执行情况, **so that** 及时发现预算偏差并采取措施。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 项目主数据已创建
- 项目结构已分解
- 成本科目体系已建立
- 预算模板已配置

### 核心流程

#### 2.1 预算编制流程
1. 项目经理选择预算模板或从零开始
2. 按项目结构和成本科目编制预算
3. 设置预算分解到任务和时间维度
4. 填写预算编制说明和依据
5. 提交预算审核申请
6. 预算审核通过后生效执行

#### 2.2 预算审批流程
1. 财务经理接收预算审核申请
2. 审核预算编制的合理性和完整性
3. 对比历史项目和行业标准
4. 提出审核意见和修改建议
5. 审批通过或退回修改
6. 审批通过后预算正式生效

#### 2.3 预算执行监控流程
1. 系统实时监控预算执行情况
2. 对比实际支出与预算计划
3. 计算预算执行率和偏差率
4. 识别预算超支风险
5. 触发预算预警和通知
6. 生成预算执行报告

### 后置条件
- 项目预算计划完整
- 预算审批流程规范
- 预算执行监控有效
- 预算调整控制严格

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：项目预算管理页面
### 页面目标：提供项目预算编制、审批、执行和监控功能

### 信息架构：
- **顶部区域**：包含 项目选择, 预算版本, 操作按钮, 状态显示
- **左侧区域**：包含 预算科目树, 预算模板, 快速导航
- **中间区域**：包含 预算明细, 编制界面, 执行监控
- **右侧区域**：包含 预算统计, 执行分析, 预警信息

### 交互逻辑与状态：

#### **项目和版本选择区域**
- **项目选择：**
  - **项目下拉：** 下拉选择器，选择要管理预算的项目
  - **项目信息：** 显示项目基本信息和合同金额
  - **预算概览：** 显示预算总额和执行情况
- **版本管理：**
  - **当前版本：** 显示当前预算版本号
  - **版本历史：** 下拉查看历史预算版本
  - **版本对比：** 对比不同版本预算差异
  - **新建版本：** 基于当前版本创建新版本

#### **预算科目树区域**
- **科目展示：**
  - **科目树：** 树形结构显示预算科目层级
  - **科目编码：** 显示科目编码和名称
  - **预算金额：** 显示各科目预算金额
  - **执行金额：** 显示各科目实际支出金额
- **科目操作：**
  - **展开/折叠：** 点击展开或折叠科目子级
  - **科目搜索：** 输入框搜索科目名称
  - **科目筛选：** 复选框选择要显示的科目
  - **快速定位：** 点击科目快速定位到预算明细

#### **预算编制界面**
- **基本信息：**
  - **预算名称：** 输入框，预算计划名称
  - **预算版本：** 显示当前编制的版本号
  - **编制人员：** 显示预算编制人员
  - **编制日期：** 显示预算编制日期
- **预算明细：**
  - **成本科目：** 下拉选择成本科目
  - **预算金额：** 数字输入框，输入预算金额
  - **预算说明：** 文本域，说明预算依据
  - **分解维度：** 选择预算分解维度（时间/任务/部门）
- **时间分解：**
  - **按月分解：** 将预算按月份分解
  - **按季度分解：** 将预算按季度分解
  - **按阶段分解：** 将预算按项目阶段分解
  - **自定义分解：** 自定义时间分解方式
- **任务分解：**
  - **关联WBS：** 将预算关联到WBS任务
  - **任务预算：** 设置各任务的预算金额
  - **预算分配：** 自动或手动分配预算到任务
  - **预算汇总：** 汇总任务预算到上级科目

#### **预算模板管理**
- **模板列表：**
  - **模板名称：** 显示预算模板名称
  - **适用类型：** 显示适用的项目类型
  - **创建时间：** 显示模板创建时间
  - **使用次数：** 显示模板使用次数
- **模板应用：**
  - **选择模板：** 下拉选择要应用的模板
  - **参数设置：** 设置模板参数（如项目规模）
  - **预算调整：** 根据项目特点调整模板预算
  - **确认应用：** 确认应用模板到当前项目
- **模板创建：**
  - **模板名称：** 输入框，模板名称
  - **项目类型：** 下拉选择适用项目类型
  - **预算结构：** 从当前预算保存为模板
  - **参数化设置：** 设置可变参数和计算公式

#### **预算审批界面**
- **审批信息：**
  - **申请人：** 显示预算申请人信息
  - **申请时间：** 显示预算申请时间
  - **预算总额：** 显示预算总金额
  - **编制说明：** 显示预算编制说明
- **审批内容：**
  - **预算明细：** 显示详细的预算明细
  - **对比分析：** 对比历史项目和标准预算
  - **合理性分析：** 分析预算的合理性
  - **风险评估：** 评估预算执行风险
- **审批操作：**
  - **审批意见：** 文本域，填写审批意见
  - **修改建议：** 文本域，提出修改建议
  - **审批结果：** 单选按钮，通过/退回/拒绝
  - **审批历史：** 显示预算审批历史记录

#### **预算执行监控**
- **执行概览：**
  - **预算总额：** 显示项目预算总金额
  - **已执行金额：** 显示已实际支出金额
  - **执行率：** 显示预算执行百分比
  - **剩余预算：** 显示剩余可用预算
- **执行明细：**
  - **科目名称：** 显示成本科目名称
  - **预算金额：** 显示科目预算金额
  - **实际金额：** 显示科目实际支出
  - **执行率：** 显示科目预算执行率
  - **偏差金额：** 显示预算偏差金额
  - **偏差率：** 显示预算偏差百分比
- **时间维度：**
  - **月度执行：** 显示按月的预算执行情况
  - **累计执行：** 显示累计预算执行情况
  - **执行趋势：** 折线图显示执行趋势
  - **预测分析：** 基于当前执行预测未来

#### **预算分析图表**
- **执行分析：**
  - **执行率仪表盘：** 显示整体预算执行率
  - **科目执行对比：** 柱状图显示各科目执行情况
  - **时间执行趋势：** 折线图显示时间维度执行趋势
  - **预算vs实际：** 对比图显示预算与实际差异
- **偏差分析：**
  - **偏差分布：** 饼图显示偏差科目分布
  - **偏差趋势：** 折线图显示偏差变化趋势
  - **超支预警：** 高亮显示超支风险科目
  - **节约分析：** 显示预算节约情况
- **对比分析：**
  - **历史对比：** 对比历史同类项目预算
  - **标准对比：** 对比行业标准预算
  - **版本对比：** 对比不同预算版本
  - **项目对比：** 对比不同项目预算水平

#### **预算调整管理**
- **调整申请：**
  - **调整类型：** 选择调整类型（增加/减少/科目调整）
  - **调整科目：** 选择需要调整的预算科目
  - **调整金额：** 数字输入框，调整金额
  - **调整原因：** 文本域，说明调整原因
- **调整审批：**
  - **申请信息：** 显示调整申请详细信息
  - **影响分析：** 分析调整对项目的影响
  - **审批意见：** 文本域，填写审批意见
  - **审批结果：** 单选按钮，同意/拒绝
- **调整执行：**
  - **调整确认：** 确认执行预算调整
  - **版本更新：** 更新预算版本
  - **数据同步：** 同步调整后的预算数据
  - **通知发送：** 通知相关人员调整结果

#### **预算预警监控**
- **预警规则：**
  - **超支预警：** 预算超支时预警
  - **执行率预警：** 执行率异常时预警
  - **时间预警：** 预算执行进度滞后时预警
  - **余额预警：** 预算余额不足时预警
- **预警信息：**
  - **预警级别：** 显示预警级别（严重/警告/提醒）
  - **预警内容：** 显示具体预警信息
  - **影响金额：** 显示预警涉及的金额
  - **处理建议：** 显示系统建议的处理措施
- **预警处理：**
  - **确认预警：** 确认已知晓预警信息
  - **处理记录：** 记录预警处理过程
  - **预警关闭：** 问题解决后关闭预警
  - **预警统计：** 统计预警发生频率和处理情况

#### **预算报表生成**
- **报表类型：**
  - **预算明细表：** 详细的预算科目明细
  - **执行分析表：** 预算执行情况分析
  - **偏差分析表：** 预算偏差分析报表
  - **对比分析表：** 预算对比分析报表
- **报表参数：**
  - **报表期间：** 选择报表统计期间
  - **科目范围：** 选择包含的预算科目
  - **分析维度：** 选择分析维度（时间/部门/任务）
  - **报表格式：** 选择报表输出格式
- **报表输出：**
  - **在线查看：** 在线查看报表内容
  - **PDF导出：** 导出PDF格式报表
  - **Excel导出：** 导出Excel格式报表
  - **定时发送：** 设置报表定时发送

### 数据校验规则：

#### **预算金额**
- **校验规则：** 预算金额必须大于0
- **错误提示文案：** "预算金额必须大于0"

#### **预算总额**
- **校验规则：** 预算总额不能超过合同金额的120%
- **错误提示文案：** "预算总额不能超过合同金额的120%"

#### **时间分解**
- **校验规则：** 时间分解的预算总和必须等于科目预算
- **错误提示文案：** "时间分解预算总和必须等于科目预算"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **预算信息**:
  - **项目ID (project_id)**: String, 必填, 引用项目主数据
  - **成本科目 (cost_subject)**: String, 必填, 引用成本科目
  - **预算金额 (budget_amount)**: Decimal, 必填, 大于0
  - **预算说明 (budget_description)**: String, 可选, 最大500字符
- **预算分解**:
  - **分解维度 (dimension_type)**: Enum, 必填, 时间/任务/部门
  - **分解金额 (allocation_amount)**: Decimal, 必填, 大于0

### 展示数据
- **预算明细**: 详细的预算科目和金额
- **执行监控**: 预算执行情况和偏差分析
- **预警信息**: 预算预警和风险提示
- **分析图表**: 预算分析和对比图表

### 空状态/零数据
- **无预算数据**: 显示"暂无预算数据，请先编制预算"
- **无执行数据**: 显示"暂无执行数据"
- **无预警信息**: 显示"当前无预算预警"

### API接口
- **预算查询**: GET /api/projects/{id}/budget
- **预算创建**: POST /api/projects/{id}/budget
- **预算更新**: PUT /api/projects/budget/{id}
- **预算审批**: POST /api/projects/budget/{id}/approve
- **执行监控**: GET /api/projects/{id}/budget-execution

## 5. 异常与边界处理 (Error & Edge Cases)

### **预算超限**
- **提示信息**: "预算总额超过合同金额限制"
- **用户操作**: 显示超限金额和调整建议

### **分解不平衡**
- **提示信息**: "预算分解金额与科目预算不一致"
- **用户操作**: 提供自动平衡和手动调整选项

### **审批超时**
- **提示信息**: "预算审批超时，请联系审批人员"
- **用户操作**: 显示审批流程和催办功能

### **数据同步失败**
- **提示信息**: "预算数据同步失败，请重试"
- **用户操作**: 提供重试按钮和手动同步选项

### **权限不足**
- **提示信息**: "您没有权限编制/审批预算"
- **用户操作**: 显示所需权限和申请流程

## 6. 验收标准 (Acceptance Criteria)

- [ ] 预算编制功能完整，支持多维度分解
- [ ] 预算模板功能完善，提高编制效率
- [ ] 预算审批流程规范，控制严格
- [ ] 预算执行监控实时，分析准确
- [ ] 预算调整流程完整，审批控制有效
- [ ] 预算预警及时准确，处理流程完善
- [ ] 数据实时性好，延迟<5分钟
- [ ] 支持复杂预算结构（100+科目）
- [ ] 所有页面元素符合全局设计规范
- [ ] 响应时间<3秒，支持并发操作
- [ ] 数据准确性高，异常处理完善
- [ ] 与成本归集模块集成正常
