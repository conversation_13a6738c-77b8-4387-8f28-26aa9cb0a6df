# 功能模块规格说明书：项目结构分解模块

- **模块ID**: PJS-002
- **所属子系统**: 项目管理子系统(Project System)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 项目经理, **I want to** 创建多层级的项目结构, **so that** 精确管理交付地点和产品清单。
- **As a** 项目经理, **I want to** 在项目节点下录入产品清单, **so that** 明确各区域的交付要求。
- **As a** 销售人员, **I want to** 将销售订单关联到项目节点, **so that** 确保订单与项目交付的对应关系。
- **As a** 设计工程师, **I want to** 查看项目结构和产品分布, **so that** 合理安排设计任务。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 项目主数据已创建
- 用户具有项目结构管理权限
- 产品主数据已存在
- 销售订单已创建（如需关联）

### 核心流程

#### 2.1 项目结构创建流程
1. 选择要分解结构的项目
2. 创建第一级结构节点（如楼栋）
3. 在父节点下创建子节点（如楼层）
4. 继续创建更细粒度节点（如房间）
5. 为每个节点设置属性和描述
6. 保存项目结构树

#### 2.2 产品清单管理流程
1. 选择项目结构中的具体节点
2. 添加该节点需要交付的产品
3. 设置产品规格、数量和要求
4. 配置产品的技术参数
5. 关联产品BOM和工艺路线
6. 生成节点产品清单

#### 2.3 销售订单关联流程
1. 选择需要关联的销售订单
2. 在项目结构中选择对应节点
3. 建立订单与节点的关联关系
4. 同步订单产品到节点清单
5. 更新订单交付地址信息
6. 确认关联关系生效

### 后置条件
- 项目结构完整创建
- 产品清单准确录入
- 销售订单正确关联
- 结构数据可用于后续计划

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：项目结构分解管理页面
### 页面目标：提供项目多层级结构创建和产品清单管理功能

### 信息架构：
- **左侧区域**：包含 项目选择, 结构树展示, 节点操作
- **中间区域**：包含 节点详情, 产品清单, 编辑界面
- **右侧区域**：包含 节点属性, 关联信息, 操作历史

### 交互逻辑与状态：

#### **项目选择区域**
- **项目选择：**
  - **项目下拉：** 下拉选择器，显示当前用户有权限的项目
  - **项目信息：** 显示选中项目的基本信息
  - **结构概览：** 显示项目结构统计信息
  - **快速操作：** 新建根节点、导入结构、导出结构

#### **项目结构树区域**
- **树形展示：**
  - **节点图标：** 不同层级使用不同图标（楼栋/楼层/房间）
  - **节点名称：** 显示节点名称，支持内联编辑
  - **展开/折叠：** 点击展开或折叠子节点
  - **节点状态：** 显示节点完成状态（未开始/进行中/已完成）
- **节点操作：**
  - **右键菜单：** 新增子节点、编辑、删除、复制、移动
  - **拖拽操作：** 支持节点拖拽调整层级关系
  - **批量操作：** 支持多选节点进行批量操作
  - **搜索定位：** 输入框搜索节点，快速定位

#### **节点详情编辑**
- **基本信息：**
  - **节点编码：** 输入框，自动生成或手工输入
  - **节点名称：** 输入框，必填，最大50字符
  - **节点类型：** 下拉选择，楼栋/楼层/房间/区域
  - **节点描述：** 文本域，最大200字符
- **位置信息：**
  - **楼栋号：** 输入框，楼栋标识
  - **楼层号：** 输入框，楼层标识
  - **房间号：** 输入框，房间标识
  - **区域标识：** 输入框，特殊区域标识
- **属性设置：**
  - **面积：** 数字输入框，平方米
  - **高度：** 数字输入框，米
  - **朝向：** 下拉选择，东/南/西/北
  - **特殊要求：** 文本域，特殊技术要求

#### **产品清单管理**
- **产品列表：**
  - **产品编码：** 显示产品编码，点击查看详情
  - **产品名称：** 显示产品名称和规格
  - **需求数量：** 数字输入框，可编辑
  - **计量单位：** 显示产品单位
  - **技术要求：** 显示特殊技术要求
  - **操作：** 编辑、删除、查看BOM
- **产品添加：**
  - **产品搜索：** 输入框，搜索产品编码或名称
  - **产品选择：** 下拉选择器，选择要添加的产品
  - **数量设置：** 数字输入框，设置需求数量
  - **规格配置：** 根据产品类型配置具体规格
- **清单操作：**
  - **批量添加：** 支持Excel导入产品清单
  - **模板复制：** 从其他节点复制产品清单
  - **清单导出：** 导出当前节点产品清单
  - **汇总统计：** 显示产品数量和金额汇总

#### **销售订单关联**
- **订单选择：**
  - **订单列表：** 显示可关联的销售订单
  - **订单信息：** 显示订单编号、客户、金额
  - **产品明细：** 显示订单包含的产品清单
  - **关联状态：** 显示订单是否已关联项目
- **关联操作：**
  - **建立关联：** 选择订单和项目节点建立关联
  - **同步产品：** 将订单产品同步到节点清单
  - **解除关联：** 解除订单与节点的关联关系
  - **批量关联：** 支持批量建立订单关联
- **关联信息：**
  - **关联订单：** 显示当前节点关联的订单
  - **交付地址：** 显示订单交付地址信息
  - **交付要求：** 显示订单特殊交付要求
  - **关联历史：** 显示关联操作历史记录

#### **结构统计信息**
- **层级统计：**
  - **总节点数：** 显示项目结构总节点数
  - **各层级数量：** 显示各层级节点数量
  - **完成进度：** 显示各层级完成进度
  - **产品总量：** 显示项目产品总数量
- **产品统计：**
  - **产品种类：** 显示涉及的产品种类数
  - **总数量：** 显示所有产品总数量
  - **总金额：** 显示产品总金额（按标准价）
  - **分类汇总：** 按产品类别汇总数量
- **关联统计：**
  - **关联订单数：** 显示关联的销售订单数
  - **关联金额：** 显示关联订单总金额
  - **未关联节点：** 显示未关联订单的节点数
  - **覆盖率：** 显示订单关联覆盖率

#### **结构可视化**
- **树形图：**
  - **层级展示：** 清晰显示项目层级结构
  - **节点状态：** 用颜色区分节点状态
  - **产品数量：** 在节点上显示产品数量
  - **交互操作：** 支持点击、拖拽等交互
- **平面图：**
  - **楼层平面图：** 显示楼层平面布局
  - **房间分布：** 显示房间位置和编号
  - **产品标注：** 在平面图上标注产品位置
  - **缩放操作：** 支持平面图缩放和平移

### 数据校验规则：

#### **节点编码**
- **校验规则：** 在同一父节点下必须唯一
- **错误提示文案：** "节点编码在当前层级下已存在"

#### **产品数量**
- **校验规则：** 必须大于0，不能为空
- **错误提示文案：** "产品数量必须大于0"

#### **节点层级**
- **校验规则：** 不能超过系统设定的最大层级数
- **错误提示文案：** "节点层级不能超过6级"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **节点信息**:
  - **节点编码 (node_code)**: String, 必填, 父节点下唯一
  - **节点名称 (node_name)**: String, 必填, 最大50字符
  - **父节点ID (parent_id)**: String, 可选, 引用父节点
  - **节点类型 (node_type)**: Enum, 必填, 楼栋/楼层/房间/区域
- **产品清单**:
  - **产品编码 (product_code)**: String, 必填, 引用产品主数据
  - **需求数量 (quantity)**: Decimal, 必填, 大于0

### 展示数据
- **项目结构树**: 完整的项目层级结构
- **节点详情**: 节点属性、产品清单、关联信息
- **统计信息**: 节点数量、产品统计、关联统计
- **可视化图表**: 结构树形图、平面布局图

### 空状态/零数据
- **无结构节点**: 显示"暂无项目结构，点击新建根节点开始"
- **无产品清单**: 显示"暂无产品清单，点击添加产品"
- **无关联订单**: 显示"暂无关联订单"

### API接口
- **结构查询**: GET /api/projects/{id}/structure
- **节点创建**: POST /api/projects/{id}/nodes
- **节点更新**: PUT /api/projects/nodes/{id}
- **产品清单**: POST /api/projects/nodes/{id}/products
- **订单关联**: POST /api/projects/nodes/{id}/orders

## 5. 异常与边界处理 (Error & Edge Cases)

### **节点删除冲突**
- **提示信息**: "该节点下存在子节点或关联数据，无法删除"
- **用户操作**: 显示冲突详情和处理建议

### **产品不存在**
- **提示信息**: "选择的产品不存在或已停用"
- **用户操作**: 提供产品搜索和替代产品建议

### **结构层级过深**
- **提示信息**: "项目结构层级不能超过6级"
- **用户操作**: 建议调整结构设计

### **数据同步失败**
- **提示信息**: "与销售订单同步失败，请重试"
- **用户操作**: 提供重试按钮和手动同步选项

### **权限不足**
- **提示信息**: "您没有权限修改项目结构"
- **用户操作**: 显示所需权限和申请流程

## 6. 验收标准 (Acceptance Criteria)

- [ ] 项目结构创建灵活，支持不限层级
- [ ] 节点操作便捷，支持拖拽和批量操作
- [ ] 产品清单管理完整，支持导入导出
- [ ] 销售订单关联准确，数据同步及时
- [ ] 结构可视化清晰，交互体验良好
- [ ] 数据校验完善，错误提示友好
- [ ] 统计信息准确，实时更新
- [ ] 支持大规模项目结构（1000+节点）
- [ ] 所有页面元素符合全局设计规范
- [ ] 响应时间<2秒，支持并发操作
- [ ] 数据完整性保证，支持事务回滚
- [ ] 与其他模块集成正常
