# 功能模块规格说明书：项目主数据管理模块

- **模块ID**: PJS-001
- **所属子系统**: 项目管理子系统(Project System)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 项目经理, **I want to** 创建和维护项目基本信息, **so that** 建立项目管理的基础数据。
- **As a** 项目主管, **I want to** 分配项目团队和角色权限, **so that** 确保项目团队协作有序。
- **As a** 项目经理, **I want to** 管理项目状态和生命周期, **so that** 跟踪项目整体进展。
- **As a** 系统管理员, **I want to** 设置项目编码规则, **so that** 确保项目编码的唯一性和规范性。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有项目管理权限
- 客户主数据已存在
- 组织架构和人员信息已设置
- 项目编码规则已配置

### 核心流程

#### 2.1 项目立项流程
1. 填写项目基本信息（名称、编码、客户、合同金额等）
2. 系统自动校验项目编码唯一性
3. 设置项目计划起止时间和预算
4. 选择项目类型和优先级
5. 提交项目立项申请
6. 项目状态更新为"立项"

#### 2.2 项目团队分配流程
1. 指定项目经理和核心团队成员
2. 设置团队成员的项目角色和权限
3. 配置项目访问控制规则
4. 发送项目团队通知
5. 团队成员确认参与项目
6. 项目团队信息生效

#### 2.3 项目状态管理流程
1. 根据项目进展更新项目状态
2. 系统自动记录状态变更历史
3. 触发相应的业务流程（如开工、完工等）
4. 发送状态变更通知给相关人员
5. 更新项目看板和报表数据
6. 记录状态变更日志

### 后置条件
- 项目主数据创建完成
- 项目团队权限生效
- 项目状态正确更新
- 相关系统数据同步

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：项目主数据管理页面
### 页面目标：提供项目基础信息的创建、维护和管理功能

### 信息架构：
- **顶部区域**：包含 项目查询, 新建项目, 批量操作, 导入导出
- **左侧区域**：包含 项目状态筛选, 项目类型筛选, 负责人筛选
- **中间区域**：包含 项目列表, 项目详情, 编辑界面
- **右侧区域**：包含 项目统计, 快速操作, 相关信息

### 交互逻辑与状态：

#### **项目查询区域**
- **基础查询：**
  - **项目名称：** 输入框，支持模糊搜索
  - **项目编码：** 输入框，精确查询
  - **客户名称：** 下拉选择器，支持搜索
  - **项目状态：** 多选下拉，立项/进行中/已完工/已关闭
- **高级查询：**
  - **创建时间：** 日期范围选择器
  - **项目经理：** 下拉选择项目经理
  - **合同金额：** 数字范围输入
  - **项目类型：** 下拉选择项目类型

#### **项目列表区域**
- **列表表头：**
  - **项目编码：** 可排序，点击查看详情
  - **项目名称：** 显示项目全称，支持编辑
  - **客户名称：** 显示客户信息
  - **项目经理：** 显示负责人姓名
  - **合同金额：** 右对齐，千分位格式
  - **计划工期：** 显示计划起止时间
  - **项目状态：** 状态标签
  - **操作：** 编辑、查看、删除等操作
- **状态标识：**
  - **立项：** 蓝色标签，"立项"
  - **进行中：** 绿色标签，"进行中"
  - **已完工：** 橙色标签，"已完工"
  - **已关闭：** 灰色标签，"已关闭"

#### **项目创建/编辑界面**
- **基本信息：**
  - **项目编码：** 输入框，自动生成或手工输入，必填
  - **项目名称：** 输入框，必填，最大100字符
  - **客户选择：** 下拉搜索选择器，必填
  - **合同金额：** 数字输入框，必填，支持千分位
- **项目属性：**
  - **项目类型：** 下拉选择，防火窗/幕墙/其他
  - **优先级：** 单选按钮，高/中/低
  - **项目描述：** 文本域，最大500字符
  - **备注信息：** 文本域，最大200字符
- **时间计划：**
  - **计划开始时间：** 日期选择器，必填
  - **计划结束时间：** 日期选择器，必填
  - **预计工期：** 自动计算，天数显示
  - **关键里程碑：** 可添加多个里程碑节点
- **预算信息：**
  - **总预算：** 数字输入框，与合同金额关联
  - **材料预算：** 数字输入框，预算分解
  - **人工预算：** 数字输入框，预算分解
  - **其他费用：** 数字输入框，预算分解

#### **项目团队管理**
- **团队成员：**
  - **项目经理：** 下拉选择，必填，只能选择一人
  - **设计负责人：** 下拉选择，可选
  - **生产负责人：** 下拉选择，可选
  - **质量负责人：** 下拉选择，可选
- **团队列表：**
  - **成员姓名：** 显示团队成员姓名
  - **部门：** 显示所属部门
  - **项目角色：** 下拉选择角色
  - **权限级别：** 下拉选择权限
  - **操作：** 移除成员按钮
- **权限设置：**
  - **查看权限：** 复选框，查看项目信息
  - **编辑权限：** 复选框，编辑项目数据
  - **审批权限：** 复选框，审批项目事务
  - **管理权限：** 复选框，管理项目团队

#### **项目状态管理**
- **状态切换：**
  - **当前状态：** 显示当前项目状态
  - **目标状态：** 下拉选择要切换的状态
  - **变更原因：** 文本框，必填状态变更原因
  - **变更说明：** 文本域，详细说明
- **状态历史：**
  - **变更时间：** 显示状态变更时间
  - **变更人：** 显示操作人员
  - **原状态：** 显示变更前状态
  - **新状态：** 显示变更后状态
  - **变更原因：** 显示变更原因

#### **项目统计信息**
- **基础统计：**
  - **项目总数：** 显示系统中项目总数
  - **进行中项目：** 显示正在执行的项目数
  - **本月新增：** 显示本月新增项目数
  - **即将到期：** 显示即将到期的项目数
- **状态分布：**
  - **饼图：** 显示各状态项目分布
  - **柱状图：** 显示月度项目创建趋势
  - **列表：** 显示各状态项目明细
- **金额统计：**
  - **合同总金额：** 显示所有项目合同金额
  - **在执行金额：** 显示进行中项目金额
  - **已完工金额：** 显示已完工项目金额
  - **平均项目金额：** 计算平均合同金额

### 数据校验规则：

#### **项目编码**
- **校验规则：** 必须唯一，符合编码规则格式
- **错误提示文案：** "项目编码已存在，请重新输入"

#### **项目名称**
- **校验规则：** 必填，长度1-100字符，不能包含特殊字符
- **错误提示文案：** "项目名称为必填项，长度不能超过100字符"

#### **时间计划**
- **校验规则：** 结束时间必须大于开始时间
- **错误提示文案：** "项目结束时间必须晚于开始时间"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **项目基本信息**:
  - **项目编码 (project_code)**: String, 必填, 全局唯一
  - **项目名称 (project_name)**: String, 必填, 最大100字符
  - **客户ID (customer_id)**: String, 必填, 引用客户主数据
  - **合同金额 (contract_amount)**: Decimal, 必填, 大于0
- **项目团队**:
  - **项目经理ID (manager_id)**: String, 必填, 引用员工主数据
  - **团队成员 (team_members)**: Array, 可选, 团队成员列表

### 展示数据
- **项目列表**: 项目编码、名称、客户、经理、状态、金额
- **项目详情**: 完整的项目信息和团队配置
- **统计信息**: 项目数量、金额、状态分布统计
- **操作日志**: 项目创建、修改、状态变更记录

### 空状态/零数据
- **无项目数据**: 显示"暂无项目，点击新建项目开始"
- **无团队成员**: 显示"暂未分配团队成员"
- **无操作记录**: 显示"暂无操作记录"

### API接口
- **项目查询**: GET /api/projects
- **项目创建**: POST /api/projects
- **项目更新**: PUT /api/projects/{id}
- **项目删除**: DELETE /api/projects/{id}
- **团队管理**: POST /api/projects/{id}/team

## 5. 异常与边界处理 (Error & Edge Cases)

### **项目编码重复**
- **提示信息**: "项目编码已存在，请使用其他编码"
- **用户操作**: 提供编码建议或自动生成选项

### **客户信息缺失**
- **提示信息**: "客户信息不存在，请先创建客户档案"
- **用户操作**: 提供跳转到客户管理的快捷链接

### **权限不足**
- **提示信息**: "您没有权限创建/修改项目"
- **用户操作**: 显示所需权限和申请流程

### **数据保存失败**
- **提示信息**: "数据保存失败，请检查网络连接后重试"
- **用户操作**: 提供重试按钮和数据恢复选项

### **团队成员冲突**
- **提示信息**: "该成员已在其他项目中担任相同角色"
- **用户操作**: 显示冲突详情和解决建议

## 6. 验收标准 (Acceptance Criteria)

- [ ] 项目创建功能完整，信息录入便捷
- [ ] 项目编码唯一性校验有效
- [ ] 项目团队分配和权限管理正确
- [ ] 项目状态管理流程清晰
- [ ] 项目查询和筛选功能准确
- [ ] 数据校验规则完善，错误提示友好
- [ ] 项目统计信息实时准确
- [ ] 支持批量操作和数据导入导出
- [ ] 所有页面元素符合全局设计规范
- [ ] 响应时间<3秒，支持并发操作
- [ ] 操作日志完整，支持审计追踪
- [ ] 与其他子系统集成正常
