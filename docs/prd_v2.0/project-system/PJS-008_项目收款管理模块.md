# 功能模块规格说明书：项目收款管理模块

- **模块ID**: PJS-008
- **所属子系统**: 项目管理子系统(Project System)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 项目经理, **I want to** 跟踪项目收款进度, **so that** 确保项目资金回收按计划进行。
- **As a** 财务人员, **I want to** 管理项目收款计划和实际收款, **so that** 准确核算项目收入和应收账款。
- **As a** 销售人员, **I want to** 催收逾期应收款项, **so that** 降低坏账风险并改善现金流。
- **As a** 项目主管, **I want to** 分析项目收款情况, **so that** 评估项目盈利能力和资金状况。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 项目合同已签署
- 收款条款已明确
- 项目交付已完成
- 发票已开具

### 核心流程

#### 2.1 收款计划制定流程
1. 根据合同条款制定收款计划
2. 设置收款节点和收款金额
3. 关联收款条件和里程碑
4. 设置收款提醒和预警
5. 财务审核收款计划
6. 收款计划正式生效

#### 2.2 收款执行流程
1. 监控收款条件是否满足
2. 向客户发送收款通知
3. 跟踪客户付款进度
4. 记录实际收款情况
5. 更新应收账款余额
6. 生成收款确认单

#### 2.3 逾期催收流程
1. 系统自动识别逾期应收款
2. 发送逾期提醒通知
3. 安排专人进行催收
4. 记录催收过程和结果
5. 评估坏账风险
6. 必要时启动法律程序

### 后置条件
- 收款计划执行完成
- 应收账款及时回收
- 收款记录完整准确
- 坏账风险得到控制

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：项目收款管理页面
### 页面目标：提供项目收款计划、执行和催收管理功能

### 信息架构：
- **顶部区域**：包含 项目选择, 收款视图, 时间范围, 状态筛选
- **左侧区域**：包含 收款计划, 收款状态, 快速筛选
- **中间区域**：包含 收款详情, 催收记录, 分析图表
- **右侧区域**：包含 收款统计, 逾期预警, 操作历史

### 交互逻辑与状态：

#### **项目和时间选择区域**
- **项目选择：**
  - **项目下拉：** 下拉选择器，选择要管理收款的项目
  - **项目信息：** 显示项目基本信息和合同金额
  - **收款概览：** 显示收款总额、已收金额、待收金额
- **时间范围：**
  - **收款期间：** 日期范围选择器
  - **预设范围：** 本月/本季度/本年度快捷选择
  - **逾期查看：** 快捷按钮，查看逾期应收款
  - **实时刷新：** 开关，设置数据自动刷新

#### **收款计划管理区域**
- **计划列表：**
  - **收款节点：** 显示收款节点名称
  - **计划金额：** 显示计划收款金额
  - **计划时间：** 显示计划收款时间
  - **收款条件：** 显示收款触发条件
  - **收款状态：** 显示收款状态标签
- **状态标识：**
  - **未到期：** 蓝色标签，"未到期"
  - **可收款：** 绿色标签，"可收款"
  - **已收款：** 灰色标签，"已收款"
  - **已逾期：** 红色标签，"已逾期"
- **计划操作：**
  - **新建计划：** 创建新的收款计划
  - **编辑计划：** 编辑收款计划内容
  - **删除计划：** 删除未执行的收款计划
  - **批量操作：** 批量修改收款计划

#### **收款计划制定界面**
- **基本信息：**
  - **收款节点：** 输入框，收款节点名称
  - **收款类型：** 下拉选择，预付款/进度款/尾款/质保金
  - **收款金额：** 数字输入框，必填
  - **收款比例：** 数字输入框，占合同金额比例
- **时间设置：**
  - **计划收款日期：** 日期选择器，必填
  - **最晚收款日期：** 日期选择器，可选
  - **提醒提前天数：** 数字输入框，提前提醒天数
  - **逾期容忍天数：** 数字输入框，逾期容忍天数
- **收款条件：**
  - **触发条件：** 下拉选择，合同签署/交付完成/验收通过
  - **关联里程碑：** 下拉选择关联的项目里程碑
  - **附加条件：** 文本域，其他收款条件
  - **条件说明：** 文本域，条件详细说明
- **发票信息：**
  - **发票类型：** 下拉选择，增值税专票/普票
  - **发票金额：** 数字输入框，发票金额
  - **开票时间：** 日期选择器，开票时间
  - **发票状态：** 下拉选择，未开票/已开票/已寄送

#### **收款执行管理**
- **收款记录：**
  - **收款日期：** 日期选择器，实际收款日期
  - **收款金额：** 数字输入框，实际收款金额
  - **收款方式：** 下拉选择，银行转账/现金/票据
  - **收款账户：** 下拉选择收款银行账户
- **收款凭证：**
  - **银行回单：** 上传银行收款回单
  - **收款凭证：** 上传其他收款凭证
  - **备注说明：** 文本域，收款备注说明
  - **确认人员：** 下拉选择收款确认人员
- **应收更新：**
  - **应收余额：** 自动计算剩余应收金额
  - **收款进度：** 显示收款完成百分比
  - **账龄分析：** 显示应收账款账龄
  - **坏账风险：** 评估坏账风险等级

#### **催收管理界面**
- **逾期列表：**
  - **逾期天数：** 显示逾期天数
  - **逾期金额：** 显示逾期金额
  - **客户信息：** 显示客户名称和联系方式
  - **催收状态：** 显示催收状态
- **催收计划：**
  - **催收方式：** 下拉选择，电话/邮件/上门/法律
  - **催收人员：** 下拉选择催收负责人
  - **催收时间：** 日期时间选择器
  - **催收内容：** 文本域，催收内容模板
- **催收执行：**
  - **催收记录：** 记录催收过程和结果
  - **客户反馈：** 记录客户反馈意见
  - **承诺付款：** 记录客户承诺付款时间
  - **催收效果：** 评估催收效果
- **风险评估：**
  - **客户信用：** 显示客户信用等级
  - **还款能力：** 评估客户还款能力
  - **坏账概率：** 计算坏账发生概率
  - **处理建议：** 提供处理建议

#### **收款分析图表**
- **收款概览：**
  - **收款仪表盘：** 显示收款完成百分比
  - **收款趋势：** 折线图显示收款趋势
  - **计划vs实际：** 对比图显示计划与实际收款
  - **收款预测：** 基于趋势预测未来收款
- **账龄分析：**
  - **账龄分布：** 饼图显示应收账款账龄分布
  - **逾期分析：** 柱状图显示逾期金额分布
  - **客户分析：** 显示各客户应收账款情况
  - **风险分析：** 显示坏账风险分布
- **收款效率：**
  - **平均收款周期：** 计算平均收款周期
  - **收款及时率：** 统计按时收款比例
  - **催收成功率：** 统计催收成功比例
  - **坏账率：** 统计坏账发生比例

#### **收款提醒和预警**
- **提醒设置：**
  - **提醒类型：** 下拉选择提醒类型
  - **提醒时间：** 设置提醒触发时间
  - **提醒对象：** 多选设置提醒接收人
  - **提醒方式：** 复选框选择提醒方式
- **预警规则：**
  - **逾期预警：** 应收款逾期时预警
  - **金额预警：** 大额应收款预警
  - **客户预警：** 高风险客户预警
  - **账龄预警：** 长账龄应收款预警
- **预警信息：**
  - **预警级别：** 显示预警级别（严重/警告/提醒）
  - **预警内容：** 显示具体预警信息
  - **影响金额：** 显示预警涉及的金额
  - **处理建议：** 显示系统建议的处理措施
- **预警处理：**
  - **确认预警：** 确认已知晓预警信息
  - **处理记录：** 记录预警处理过程
  - **预警关闭：** 问题解决后关闭预警
  - **预警统计：** 统计预警发生频率和处理情况

#### **收款报表生成**
- **报表类型：**
  - **收款明细表：** 详细的收款记录
  - **应收账款表：** 应收账款余额表
  - **账龄分析表：** 应收账款账龄分析
  - **催收记录表：** 催收过程记录
- **报表参数：**
  - **报表期间：** 选择报表统计期间
  - **项目范围：** 选择包含的项目范围
  - **客户范围：** 选择包含的客户范围
  - **报表格式：** 选择报表输出格式
- **报表输出：**
  - **在线查看：** 在线查看报表内容
  - **PDF导出：** 导出PDF格式报表
  - **Excel导出：** 导出Excel格式报表
  - **定时发送：** 设置报表定时发送

#### **客户信用管理**
- **信用档案：**
  - **客户基本信息：** 显示客户基本信息
  - **信用等级：** 显示客户信用等级
  - **信用额度：** 显示客户信用额度
  - **历史记录：** 显示历史合作记录
- **信用评估：**
  - **付款历史：** 分析客户付款历史
  - **逾期记录：** 统计客户逾期记录
  - **还款能力：** 评估客户还款能力
  - **信用评分：** 计算客户信用评分
- **信用调整：**
  - **信用等级调整：** 调整客户信用等级
  - **信用额度调整：** 调整客户信用额度
  - **调整原因：** 记录调整原因
  - **审批流程：** 信用调整审批流程

### 数据校验规则：

#### **收款金额**
- **校验规则：** 收款金额不能超过应收余额
- **错误提示文案：** "收款金额不能超过应收余额"

#### **收款时间**
- **校验规则：** 收款时间不能早于合同签署时间
- **错误提示文案：** "收款时间不能早于合同签署时间"

#### **收款比例**
- **校验规则：** 所有收款计划比例总和不能超过100%
- **错误提示文案：** "收款计划比例总和不能超过100%"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **收款计划**:
  - **项目ID (project_id)**: String, 必填, 引用项目主数据
  - **收款节点 (payment_node)**: String, 必填, 最大50字符
  - **计划金额 (planned_amount)**: Decimal, 必填, 大于0
  - **计划时间 (planned_date)**: Date, 必填
- **收款记录**:
  - **实际金额 (actual_amount)**: Decimal, 必填, 大于0
  - **收款时间 (payment_date)**: Date, 必填

### 展示数据
- **收款计划**: 收款计划列表和详细信息
- **收款记录**: 实际收款记录和凭证
- **应收分析**: 应收账款余额和账龄分析
- **催收记录**: 催收过程和结果记录

### 空状态/零数据
- **无收款计划**: 显示"暂无收款计划，请先制定收款计划"
- **无收款记录**: 显示"暂无收款记录"
- **无逾期应收**: 显示"当前无逾期应收款"

### API接口
- **收款查询**: GET /api/projects/{id}/payments
- **计划创建**: POST /api/projects/{id}/payment-plans
- **收款记录**: POST /api/projects/{id}/payment-records
- **催收管理**: POST /api/projects/{id}/collections
- **应收分析**: GET /api/projects/{id}/receivables-analysis

## 5. 异常与边界处理 (Error & Edge Cases)

### **收款金额异常**
- **提示信息**: "收款金额异常，请核实收款凭证"
- **用户操作**: 显示异常详情和修正选项

### **重复收款**
- **提示信息**: "检测到可能的重复收款，请确认"
- **用户操作**: 显示相似收款记录和确认选项

### **客户信息缺失**
- **提示信息**: "客户联系信息缺失，无法进行催收"
- **用户操作**: 提供客户信息补充和更新功能

### **银行账户异常**
- **提示信息**: "收款银行账户异常，请检查账户设置"
- **用户操作**: 提供账户验证和修正功能

### **权限不足**
- **提示信息**: "您没有权限执行收款操作"
- **用户操作**: 显示所需权限和申请流程

## 6. 验收标准 (Acceptance Criteria)

- [ ] 收款计划制定灵活，支持多种收款方式
- [ ] 收款执行记录完整，凭证管理规范
- [ ] 催收管理功能完善，流程规范
- [ ] 应收账款分析准确，风险识别及时
- [ ] 收款提醒和预警及时有效
- [ ] 客户信用管理完整，评估准确
- [ ] 数据实时性好，延迟<5分钟
- [ ] 支持大数据量处理（万级记录）
- [ ] 所有页面元素符合全局设计规范
- [ ] 响应时间<3秒，支持并发操作
- [ ] 数据准确性高，异常处理完善
- [ ] 与财务系统集成正常
