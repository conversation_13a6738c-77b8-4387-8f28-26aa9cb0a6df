# 功能模块规格说明书：项目进度跟踪模块

- **模块ID**: PJS-004
- **所属子系统**: 项目管理子系统(Project System)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 项目经理, **I want to** 可视化跟踪项目进度, **so that** 及时发现进度偏差并采取纠正措施。
- **As a** 项目团队成员, **I want to** 填报任务实际进度, **so that** 反映真实的工作完成情况。
- **As a** 项目主管, **I want to** 查看关键路径分析, **so that** 识别影响项目工期的关键任务。
- **As a** 客户, **I want to** 查看项目进度报告, **so that** 了解项目执行状况。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- WBS任务已创建
- 任务依赖关系已设置
- 项目团队已分配
- 用户具有进度管理权限

### 核心流程

#### 2.1 进度填报流程
1. 任务负责人登录系统查看分配任务
2. 选择要更新进度的任务
3. 填写任务实际进度百分比
4. 更新任务实际开始/结束时间
5. 添加进度说明和遇到的问题
6. 提交进度更新并通知项目经理

#### 2.2 进度监控流程
1. 项目经理查看项目进度仪表盘
2. 分析任务完成情况和时间偏差
3. 识别进度滞后的任务和原因
4. 查看关键路径和风险任务
5. 制定进度纠正措施
6. 更新项目进度报告

#### 2.3 预警处理流程
1. 系统自动检测进度异常情况
2. 触发进度预警通知相关人员
3. 项目经理分析预警原因
4. 制定应对措施和调整计划
5. 执行纠正措施并跟踪效果
6. 更新预警状态和处理结果

### 后置条件
- 任务进度准确更新
- 项目整体进度清晰
- 风险任务及时识别
- 进度报告自动生成

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：项目进度跟踪页面
### 页面目标：提供项目进度可视化跟踪和管理功能

### 信息架构：
- **顶部区域**：包含 项目选择, 视图切换, 时间范围, 刷新同步
- **左侧区域**：包含 任务筛选, 进度统计, 预警信息
- **中间区域**：包含 甘特图, 进度填报, 分析图表
- **右侧区域**：包含 任务详情, 关键路径, 操作历史

### 交互逻辑与状态：

#### **项目和时间选择区域**
- **项目选择：**
  - **项目下拉：** 下拉选择器，选择要跟踪的项目
  - **项目概览：** 显示项目基本信息和整体进度
  - **快速切换：** 标签页快速切换常用项目
- **时间范围：**
  - **时间选择：** 日期范围选择器，设置显示时间范围
  - **预设范围：** 快捷按钮，本周/本月/本季度/全部
  - **自动刷新：** 开关，设置数据自动刷新间隔
  - **最后更新：** 显示数据最后更新时间

#### **甘特图展示区域**
- **甘特图组件：**
  - **任务条：** 显示任务计划时间和实际进度
  - **依赖线：** 显示任务间的依赖关系
  - **里程碑：** 显示关键里程碑节点
  - **今日线：** 红色竖线标识当前日期
- **进度显示：**
  - **计划进度：** 浅色背景显示计划进度
  - **实际进度：** 深色填充显示实际完成进度
  - **进度百分比：** 在任务条上显示完成百分比
  - **状态颜色：** 用颜色区分任务状态（正常/延期/风险）
- **交互操作：**
  - **缩放：** 鼠标滚轮或工具栏缩放时间轴
  - **拖拽：** 拖拽任务条调整计划时间
  - **点击：** 点击任务查看详情或填报进度
  - **悬停：** 悬停显示任务详细信息

#### **进度填报界面**
- **任务信息：**
  - **任务名称：** 显示任务名称和编码
  - **计划时间：** 显示任务计划开始和结束时间
  - **负责人：** 显示任务负责人信息
  - **任务描述：** 显示任务详细描述
- **进度更新：**
  - **完成百分比：** 滑块或输入框，0-100%
  - **实际开始时间：** 日期时间选择器
  - **实际结束时间：** 日期时间选择器（任务完成时）
  - **预计完成时间：** 日期时间选择器（未完成任务）
- **进度说明：**
  - **工作内容：** 文本域，描述已完成的工作内容
  - **遇到问题：** 文本域，描述遇到的问题和困难
  - **需要支持：** 文本域，描述需要的支持和资源
  - **下步计划：** 文本域，描述下一步工作计划
- **附件上传：**
  - **进度照片：** 上传工作现场照片
  - **相关文档：** 上传相关工作文档
  - **问题截图：** 上传问题相关截图
  - **其他附件：** 上传其他相关附件

#### **进度分析图表**
- **整体进度：**
  - **进度仪表盘：** 显示项目整体完成百分比
  - **计划vs实际：** 对比图显示计划进度和实际进度
  - **进度趋势：** 折线图显示进度变化趋势
  - **完成预测：** 基于当前进度预测完成时间
- **任务分析：**
  - **任务状态分布：** 饼图显示各状态任务分布
  - **部门进度对比：** 柱状图显示各部门进度
  - **人员工作量：** 显示各成员任务分配和完成情况
  - **关键路径：** 高亮显示影响工期的关键任务
- **时间分析：**
  - **里程碑进度：** 显示各里程碑完成情况
  - **延期任务：** 列表显示延期任务和延期天数
  - **风险预警：** 显示可能延期的风险任务
  - **工期预测：** 基于当前进度预测项目完成时间

#### **任务筛选和搜索**
- **状态筛选：**
  - **未开始：** 复选框，筛选未开始任务
  - **进行中：** 复选框，筛选进行中任务
  - **已完成：** 复选框，筛选已完成任务
  - **已延期：** 复选框，筛选延期任务
- **人员筛选：**
  - **负责人：** 下拉多选，按负责人筛选
  - **参与人：** 下拉多选，按参与人筛选
  - **部门：** 下拉选择，按部门筛选
  - **我的任务：** 快捷按钮，显示当前用户任务
- **时间筛选：**
  - **计划时间：** 日期范围，按计划时间筛选
  - **实际时间：** 日期范围，按实际时间筛选
  - **本周任务：** 快捷按钮，显示本周任务
  - **逾期任务：** 快捷按钮，显示逾期任务

#### **关键路径分析**
- **路径识别：**
  - **关键路径：** 红色高亮显示关键路径任务
  - **次关键路径：** 橙色显示次关键路径
  - **浮动时间：** 显示任务的总浮动时间
  - **自由浮动：** 显示任务的自由浮动时间
- **影响分析：**
  - **延期影响：** 显示任务延期对项目的影响
  - **资源冲突：** 显示资源冲突的任务
  - **风险等级：** 显示任务的风险等级
  - **优化建议：** 提供进度优化建议

#### **预警和通知**
- **预警规则：**
  - **进度预警：** 任务进度低于计划阈值时预警
  - **时间预警：** 任务即将到期时预警
  - **里程碑预警：** 里程碑可能延期时预警
  - **资源预警：** 资源冲突或不足时预警
- **预警信息：**
  - **预警级别：** 显示预警级别（高/中/低）
  - **预警内容：** 显示具体预警信息
  - **影响范围：** 显示预警影响的任务范围
  - **建议措施：** 显示系统建议的处理措施
- **通知管理：**
  - **即时通知：** 系统消息、邮件、短信通知
  - **定期报告：** 周报、月报自动发送
  - **订阅设置：** 用户自定义通知订阅
  - **通知历史：** 查看历史通知记录

#### **进度报告生成**
- **报告类型：**
  - **日报：** 每日进度简报
  - **周报：** 周度进度详报
  - **月报：** 月度进度总结
  - **专项报告：** 针对特定问题的专项报告
- **报告内容：**
  - **进度概览：** 项目整体进度情况
  - **完成任务：** 本期完成的任务列表
  - **进行任务：** 正在进行的任务状态
  - **问题风险：** 发现的问题和风险
- **报告分发：**
  - **自动发送：** 定时自动发送给相关人员
  - **手动发送：** 手动选择发送对象
  - **导出功能：** 导出PDF、Excel格式
  - **在线查看：** 在线查看历史报告

### 数据校验规则：

#### **进度百分比**
- **校验规则：** 必须在0-100之间，不能超过100%
- **错误提示文案：** "进度百分比必须在0-100之间"

#### **实际时间**
- **校验规则：** 实际开始时间不能晚于实际结束时间
- **错误提示文案：** "实际结束时间不能早于实际开始时间"

#### **进度逻辑**
- **校验规则：** 任务完成100%时必须填写实际结束时间
- **错误提示文案：** "任务完成时必须填写实际结束时间"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **进度更新**:
  - **任务ID (task_id)**: String, 必填, 引用WBS任务
  - **完成百分比 (progress_percent)**: Integer, 必填, 0-100
  - **实际开始时间 (actual_start)**: DateTime, 可选
  - **实际结束时间 (actual_end)**: DateTime, 可选
- **进度说明**:
  - **工作内容 (work_content)**: String, 可选, 最大500字符
  - **遇到问题 (issues)**: String, 可选, 最大500字符

### 展示数据
- **甘特图数据**: 任务时间线、依赖关系、进度状态
- **进度统计**: 整体进度、部门进度、人员进度
- **关键路径**: 关键任务、浮动时间、风险分析
- **预警信息**: 预警任务、风险等级、处理建议

### 空状态/零数据
- **无进度数据**: 显示"暂无进度数据，请填报任务进度"
- **无预警信息**: 显示"当前无预警信息"
- **无关键路径**: 显示"无法计算关键路径，请检查任务依赖"

### API接口
- **进度查询**: GET /api/projects/{id}/progress
- **进度更新**: POST /api/projects/tasks/{id}/progress
- **关键路径**: GET /api/projects/{id}/critical-path
- **预警查询**: GET /api/projects/{id}/alerts
- **报告生成**: GET /api/projects/{id}/reports

## 5. 异常与边界处理 (Error & Edge Cases)

### **进度数据异常**
- **提示信息**: "检测到进度数据异常，请核实后重新填报"
- **用户操作**: 显示异常数据详情和修正建议

### **关键路径计算失败**
- **提示信息**: "关键路径计算失败，请检查任务依赖关系"
- **用户操作**: 提供依赖关系检查和修复工具

### **数据同步延迟**
- **提示信息**: "数据同步中，请稍后查看最新进度"
- **用户操作**: 显示同步进度和手动刷新选项

### **权限不足**
- **提示信息**: "您没有权限更新此任务进度"
- **用户操作**: 显示所需权限和申请流程

### **网络连接问题**
- **提示信息**: "网络连接异常，进度数据可能不是最新"
- **用户操作**: 提供离线模式和数据恢复选项

## 6. 验收标准 (Acceptance Criteria)

- [ ] 甘特图显示清晰，交互体验流畅
- [ ] 进度填报便捷，支持批量更新
- [ ] 关键路径分析准确，风险识别及时
- [ ] 预警机制完善，通知及时有效
- [ ] 进度报告自动生成，内容完整
- [ ] 数据实时性好，延迟<5分钟
- [ ] 支持大规模项目（500+任务）
- [ ] 移动端适配良好，支持移动填报
- [ ] 所有页面元素符合全局设计规范
- [ ] 响应时间<2秒，支持并发操作
- [ ] 数据准确性高，异常处理完善
- [ ] 与其他模块集成正常
