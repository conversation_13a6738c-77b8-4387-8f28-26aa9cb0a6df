# 功能模块规格说明书：WBS任务管理模块

- **模块ID**: PJS-003
- **所属子系统**: 项目管理子系统(Project System)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 项目经理, **I want to** 使用WBS分解项目任务, **so that** 对项目执行进行有效的计划和控制。
- **As a** 项目经理, **I want to** 设置任务依赖关系, **so that** 合理安排任务执行顺序。
- **As a** 项目团队成员, **I want to** 查看分配给我的任务, **so that** 明确工作内容和时间要求。
- **As a** 项目主管, **I want to** 设置关键里程碑, **so that** 监控项目重要节点的完成情况。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 项目主数据已创建
- 项目结构已分解
- 用户具有任务管理权限
- 项目团队已分配

### 核心流程

#### 2.1 WBS任务创建流程
1. 选择项目并进入WBS管理界面
2. 创建第一级任务（如设计阶段、生产阶段）
3. 在父任务下分解子任务
4. 设置任务属性（名称、工期、负责人等）
5. 配置任务预算和资源需求
6. 保存WBS任务结构

#### 2.2 任务依赖设置流程
1. 选择要设置依赖的任务
2. 选择前置任务和依赖类型
3. 设置依赖关系的延迟时间
4. 系统自动调整任务时间计划
5. 验证依赖关系的合理性
6. 确认依赖关系生效

#### 2.3 里程碑管理流程
1. 在WBS中标识关键节点
2. 设置里程碑属性和验收标准
3. 配置里程碑提醒和预警
4. 关联里程碑交付物
5. 设置里程碑审批流程
6. 发布里程碑计划

### 后置条件
- WBS任务结构完整
- 任务依赖关系清晰
- 里程碑节点明确
- 任务计划可执行

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：WBS任务管理页面
### 页面目标：提供项目任务分解、依赖设置和里程碑管理功能

### 信息架构：
- **顶部区域**：包含 项目选择, 视图切换, 任务操作, 导入导出
- **左侧区域**：包含 WBS树形结构, 任务筛选, 快速操作
- **中间区域**：包含 任务详情, 编辑界面, 甘特图预览
- **右侧区域**：包含 任务属性, 依赖关系, 资源分配

### 交互逻辑与状态：

#### **项目和视图选择区域**
- **项目选择：**
  - **项目下拉：** 下拉选择器，选择要管理的项目
  - **项目信息：** 显示项目基本信息和进度概览
  - **WBS统计：** 显示任务总数、完成数、里程碑数
- **视图切换：**
  - **树形视图：** 按钮，显示WBS树形结构
  - **甘特图视图：** 按钮，显示任务时间计划
  - **列表视图：** 按钮，显示任务列表
  - **里程碑视图：** 按钮，显示里程碑计划

#### **WBS树形结构区域**
- **任务树展示：**
  - **任务图标：** 不同类型任务使用不同图标
  - **任务名称：** 显示任务名称，支持内联编辑
  - **完成进度：** 显示任务完成百分比
  - **任务状态：** 显示任务状态标签
- **树形操作：**
  - **展开/折叠：** 点击展开或折叠子任务
  - **拖拽调整：** 支持任务拖拽调整层级
  - **右键菜单：** 新增子任务、编辑、删除、复制
  - **多选操作：** 支持多选任务进行批量操作
- **任务筛选：**
  - **状态筛选：** 按任务状态筛选
  - **负责人筛选：** 按任务负责人筛选
  - **时间筛选：** 按计划时间筛选
  - **关键词搜索：** 输入框搜索任务名称

#### **任务详情编辑**
- **基本信息：**
  - **任务编码：** 输入框，自动生成或手工输入
  - **任务名称：** 输入框，必填，最大100字符
  - **任务类型：** 下拉选择，设计/生产/安装/验收
  - **任务描述：** 文本域，详细描述任务内容
- **时间计划：**
  - **计划开始时间：** 日期时间选择器
  - **计划结束时间：** 日期时间选择器
  - **计划工期：** 数字输入框，天数
  - **工作日历：** 下拉选择工作日历
- **资源分配：**
  - **任务负责人：** 下拉选择项目团队成员
  - **参与人员：** 多选下拉，选择参与人员
  - **所需技能：** 多选标签，选择所需技能
  - **资源需求：** 文本域，描述资源需求
- **预算信息：**
  - **任务预算：** 数字输入框，任务预算金额
  - **人工预算：** 数字输入框，人工费用预算
  - **材料预算：** 数字输入框，材料费用预算
  - **其他费用：** 数字输入框，其他费用预算

#### **任务依赖管理**
- **依赖关系列表：**
  - **前置任务：** 显示前置任务名称
  - **依赖类型：** 显示依赖类型（FS/SS/FF/SF）
  - **延迟时间：** 显示延迟天数
  - **操作：** 编辑、删除依赖关系
- **添加依赖：**
  - **前置任务选择：** 下拉选择前置任务
  - **依赖类型选择：** 单选按钮选择依赖类型
  - **延迟时间设置：** 数字输入框设置延迟
  - **确认添加：** 按钮确认添加依赖关系
- **依赖类型说明：**
  - **FS (完成-开始)：** 前置任务完成后，当前任务才能开始
  - **SS (开始-开始)：** 前置任务开始后，当前任务才能开始
  - **FF (完成-完成)：** 前置任务完成后，当前任务才能完成
  - **SF (开始-完成)：** 前置任务开始后，当前任务才能完成

#### **里程碑管理**
- **里程碑列表：**
  - **里程碑名称：** 显示里程碑名称
  - **计划时间：** 显示里程碑计划时间
  - **关联任务：** 显示关联的任务
  - **完成状态：** 显示里程碑完成状态
- **里程碑设置：**
  - **里程碑名称：** 输入框，必填
  - **里程碑类型：** 下拉选择，设计完成/生产完成/交付完成
  - **计划时间：** 日期时间选择器
  - **验收标准：** 文本域，描述验收标准
- **里程碑属性：**
  - **重要级别：** 下拉选择，高/中/低
  - **提醒设置：** 复选框，是否提前提醒
  - **提醒时间：** 数字输入框，提前天数
  - **审批要求：** 复选框，是否需要审批
- **交付物管理：**
  - **交付物名称：** 输入框，交付物名称
  - **交付物类型：** 下拉选择，文档/图纸/产品
  - **负责人：** 下拉选择负责人
  - **交付要求：** 文本域，交付要求说明

#### **任务模板管理**
- **模板列表：**
  - **模板名称：** 显示模板名称
  - **适用项目类型：** 显示适用的项目类型
  - **任务数量：** 显示模板包含的任务数
  - **操作：** 应用、编辑、删除模板
- **模板应用：**
  - **选择模板：** 下拉选择要应用的模板
  - **参数设置：** 设置模板参数（如项目周期）
  - **任务调整：** 预览并调整生成的任务
  - **确认应用：** 确认应用模板到当前项目
- **模板创建：**
  - **模板名称：** 输入框，模板名称
  - **项目类型：** 下拉选择适用项目类型
  - **任务结构：** 从当前WBS保存为模板
  - **参数化设置：** 设置可变参数

#### **批量操作功能**
- **批量编辑：**
  - **批量选择：** 复选框批量选择任务
  - **批量修改：** 批量修改任务属性
  - **批量分配：** 批量分配任务负责人
  - **批量调整：** 批量调整任务时间
- **导入导出：**
  - **Excel导入：** 从Excel导入WBS任务
  - **模板导入：** 从项目模板导入任务
  - **Excel导出：** 导出WBS到Excel
  - **PDF导出：** 导出WBS结构图

### 数据校验规则：

#### **任务时间**
- **校验规则：** 结束时间必须大于等于开始时间
- **错误提示文案：** "任务结束时间不能早于开始时间"

#### **任务依赖**
- **校验规则：** 不能形成循环依赖
- **错误提示文案：** "任务依赖不能形成循环，请检查依赖关系"

#### **里程碑时间**
- **校验规则：** 里程碑时间必须在项目时间范围内
- **错误提示文案：** "里程碑时间必须在项目计划时间范围内"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **任务信息**:
  - **任务编码 (task_code)**: String, 必填, 项目内唯一
  - **任务名称 (task_name)**: String, 必填, 最大100字符
  - **父任务ID (parent_id)**: String, 可选, 引用父任务
  - **负责人ID (assignee_id)**: String, 必填, 引用员工主数据
- **时间计划**:
  - **计划开始时间 (planned_start)**: DateTime, 必填
  - **计划结束时间 (planned_end)**: DateTime, 必填
  - **计划工期 (planned_duration)**: Integer, 必填, 天数

### 展示数据
- **WBS任务树**: 完整的任务层级结构
- **任务详情**: 任务属性、依赖关系、资源分配
- **里程碑计划**: 里程碑列表和时间安排
- **甘特图数据**: 任务时间线和依赖关系

### 空状态/零数据
- **无WBS任务**: 显示"暂无WBS任务，点击新建任务开始"
- **无依赖关系**: 显示"暂无任务依赖关系"
- **无里程碑**: 显示"暂无项目里程碑"

### API接口
- **WBS查询**: GET /api/projects/{id}/wbs
- **任务创建**: POST /api/projects/{id}/tasks
- **任务更新**: PUT /api/projects/tasks/{id}
- **依赖管理**: POST /api/projects/tasks/{id}/dependencies
- **里程碑管理**: POST /api/projects/{id}/milestones

## 5. 异常与边界处理 (Error & Edge Cases)

### **循环依赖检测**
- **提示信息**: "检测到任务循环依赖，请调整依赖关系"
- **用户操作**: 显示依赖路径和修改建议

### **任务删除冲突**
- **提示信息**: "该任务存在子任务或依赖关系，无法删除"
- **用户操作**: 显示冲突详情和处理选项

### **资源冲突**
- **提示信息**: "任务负责人在此时间段已有其他任务安排"
- **用户操作**: 显示冲突任务和调整建议

### **时间计划冲突**
- **提示信息**: "任务时间与依赖关系冲突"
- **用户操作**: 提供自动调整和手动调整选项

### **权限不足**
- **提示信息**: "您没有权限修改此任务"
- **用户操作**: 显示所需权限和申请流程

## 6. 验收标准 (Acceptance Criteria)

- [ ] WBS任务创建灵活，支持多层级分解
- [ ] 任务依赖设置准确，支持多种依赖类型
- [ ] 里程碑管理完整，支持提醒和审批
- [ ] 任务模板功能完善，提高创建效率
- [ ] 批量操作便捷，支持导入导出
- [ ] 数据校验完善，防止逻辑错误
- [ ] 甘特图预览清晰，交互体验良好
- [ ] 支持大规模WBS（500+任务）
- [ ] 所有页面元素符合全局设计规范
- [ ] 响应时间<2秒，支持并发操作
- [ ] 数据完整性保证，支持版本管理
- [ ] 与进度跟踪模块集成正常
