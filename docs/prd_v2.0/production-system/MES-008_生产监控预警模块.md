# 功能模块规格说明书：生产监控预警模块

- **模块ID**: MES-008
- **所属子系统**: 生产管理子系统(MES)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 车间主管, **I want to** 实时查看生产看板, **so that** 全面掌握车间生产状态。
- **As a** 生产计划员, **I want to** 监控计划执行进度, **so that** 及时调整生产安排。
- **As a** 设备工程师, **I want to** 接收设备异常预警, **so that** 快速响应设备故障。
- **As a** 质量工程师, **I want to** 监控质量指标趋势, **so that** 预防质量问题发生。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 生产数据实时采集正常
- 预警规则配置完整
- 通知渠道设置正确
- 用户权限配置合理

### 核心流程

#### 2.1 实时数据监控流程
1. 实时采集生产执行数据
2. 汇总各工位的生产状态
3. 计算关键生产指标
4. 更新生产看板显示
5. 检测异常和预警条件
6. 触发相应的预警机制

#### 2.2 预警规则触发流程
1. 监控系统检测到异常条件
2. 匹配对应的预警规则
3. 评估预警级别和紧急程度
4. 生成预警信息和建议
5. 通过多渠道发送预警通知
6. 记录预警历史和处理状态

#### 2.3 Andon报警处理流程
1. 工位操作员触发Andon报警
2. 系统记录报警时间和位置
3. 自动通知相关责任人
4. 显示报警信息和处理状态
5. 跟踪问题处理进度
6. 记录处理结果和用时

#### 2.4 生产看板展示流程
1. 实时获取生产数据
2. 按车间和工位分组展示
3. 计算和显示关键指标
4. 更新设备状态和利用率
5. 展示质量状态和趋势
6. 显示异常和预警信息

### 后置条件
- 生产状态实时可见
- 异常情况及时预警
- 问题处理过程可追踪
- 生产效率持续改进

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：生产监控预警大屏
### 页面目标：提供全面的生产状态监控和预警管理

### 信息架构：
- **顶部区域**：包含 总体状态指示, 关键指标展示, 时间和班次信息
- **中间区域**：包含 车间布局图, 工位状态监控, 设备运行状态
- **底部区域**：包含 预警信息列表, Andon报警, 趋势图表

### 交互逻辑与状态：

#### **总体状态指示器**
- **生产状态总览：**
  - **正常运行：** 绿色大圆点，"生产正常"
  - **部分异常：** 黄色大圆点，"X个工位异常"
  - **严重异常：** 红色大圆点，"生产中断"
  - **停机维护：** 蓝色大圆点，"计划停机"
- **状态统计数字：**
  - **运行工位：** 绿色数字，正常运行的工位数
  - **异常工位：** 红色数字，存在异常的工位数
  - **停机工位：** 灰色数字，停机的工位数
  - **维护工位：** 蓝色数字，维护中的工位数

#### **关键指标展示**
- **生产指标卡片：**
  - **计划完成率：** 大号百分比，绿色/红色显示
  - **设备利用率：** 大号百分比，带趋势箭头
  - **质量合格率：** 大号百分比，质量状态颜色
  - **准时交付率：** 大号百分比，交期状态颜色
- **实时产量显示：**
  - **今日产量：** 大号数字，实时更新
  - **计划产量：** 目标数字，对比显示
  - **完成进度：** 进度条，百分比显示
  - **预计完成：** 预计完成时间

#### **时间和班次信息**
- **当前时间：** 大号数字时钟，实时显示
- **当前班次：** 显示当前班次信息
- **班次进度：** 显示班次进度条
- **下班倒计时：** 显示距离下班时间

#### **车间布局图**
- **3D车间布局：**
  - **工位节点：** 3D立体显示各工位
  - **设备模型：** 简化的设备3D模型
  - **连接线：** 显示物料流向
  - **状态颜色：** 绿色/黄色/红色表示状态
- **交互功能：**
  - **缩放旋转：** 支持3D视图缩放和旋转
  - **工位点击：** 点击工位查看详细信息
  - **状态筛选：** 筛选显示特定状态的工位
  - **全屏查看：** 全屏显示车间布局

#### **工位状态监控**
- **工位状态卡片：**
  - **工位编号：** 大号字体显示
  - **当前任务：** 显示正在执行的任务
  - **进度条：** 显示任务完成进度
  - **操作员：** 显示当前操作员
  - **状态指示：** 彩色圆点表示工位状态
- **工位状态类型：**
  - **生产中：** 绿色，正常生产状态
  - **等待中：** 黄色，等待物料或指令
  - **故障中：** 红色，设备故障或异常
  - **维护中：** 蓝色，计划维护状态
  - **空闲中：** 灰色，无任务分配

#### **设备运行状态**
- **设备状态矩阵：**
  - **设备网格：** 网格布局显示所有设备
  - **状态颜色：** 绿色/黄色/红色表示设备状态
  - **运行参数：** 显示关键运行参数
  - **利用率：** 显示设备利用率
- **设备详情弹窗：**
  - **基本信息：** 设备名称、型号、位置
  - **运行状态：** 当前运行状态和参数
  - **故障信息：** 当前故障和历史故障
  - **维护计划：** 下次维护时间和内容

#### **预警信息列表**
- **预警级别标识：**
  - **紧急：** 红色背景，闪烁效果，"紧急"
  - **重要：** 橙色背景，"重要"
  - **一般：** 黄色背景，"一般"
  - **提示：** 蓝色背景，"提示"
- **预警信息内容：**
  - **预警时间：** 显示预警发生时间
  - **预警类型：** 显示预警类型图标
  - **预警内容：** 简要描述预警内容
  - **影响范围：** 显示受影响的工位或设备
  - **处理状态：** 显示预警处理状态
- **预警操作：**
  - **查看详情：** 查看预警详细信息
  - **确认预警：** 确认已知晓预警
  - **处理预警：** 标记预警已处理
  - **忽略预警：** 忽略误报预警

#### **Andon报警系统**
- **报警触发区域：**
  - **紧急停机：** 红色大按钮，紧急情况使用
  - **质量问题：** 黄色按钮，质量异常报警
  - **设备故障：** 橙色按钮，设备故障报警
  - **物料短缺：** 蓝色按钮，物料供应问题
  - **技术支援：** 绿色按钮，需要技术支援
- **报警状态显示：**
  - **报警工位：** 高亮显示报警的工位
  - **报警类型：** 显示报警类型和图标
  - **报警时长：** 显示报警持续时间
  - **响应状态：** 显示是否有人响应
- **报警处理：**
  - **响应报警：** 技术人员响应报警
  - **到达现场：** 标记已到达现场
  - **问题解决：** 标记问题已解决
  - **关闭报警：** 关闭报警状态

#### **趋势图表区域**
- **生产趋势图：**
  - **产量趋势：** 显示产量随时间变化
  - **效率趋势：** 显示生产效率趋势
  - **质量趋势：** 显示质量指标趋势
  - **设备利用率：** 显示设备利用率变化
- **对比分析：**
  - **计划vs实际：** 对比计划和实际数据
  - **同期对比：** 与历史同期数据对比
  - **班次对比：** 不同班次数据对比
  - **工位对比：** 不同工位效率对比

#### **预警规则配置**
- **规则类型设置：**
  - **产量预警：** 产量低于目标值预警
  - **质量预警：** 质量指标异常预警
  - **设备预警：** 设备故障或异常预警
  - **进度预警：** 生产进度延迟预警
- **预警条件设置：**
  - **阈值设置：** 设置预警触发阈值
  - **持续时间：** 设置异常持续时间
  - **预警级别：** 设置预警严重程度
  - **通知方式：** 设置通知渠道和人员
- **预警动作配置：**
  - **自动处理：** 设置自动处理动作
  - **通知人员：** 设置通知的责任人
  - **升级规则：** 设置预警升级规则
  - **记录日志：** 设置日志记录规则

#### **大屏显示控制**
- **显示模式切换：**
  - **总览模式：** 显示整体生产状态
  - **详细模式：** 显示详细生产数据
  - **预警模式：** 重点显示预警信息
  - **趋势模式：** 重点显示趋势图表
- **刷新控制：**
  - **自动刷新：** 设置自动刷新间隔
  - **手动刷新：** 手动刷新数据
  - **暂停刷新：** 暂停数据更新
- **显示设置：**
  - **亮度调节：** 调节屏幕亮度
  - **字体大小：** 调节字体大小
  - **颜色主题：** 选择显示主题

### 数据校验规则：

#### **预警阈值**
- **校验规则：** 阈值必须在合理范围内
- **错误提示文案：** "预警阈值设置超出合理范围"

#### **通知设置**
- **校验规则：** 通知人员和方式必须有效
- **错误提示文案：** "通知设置无效，请检查联系方式"

#### **时间设置**
- **校验规则：** 时间间隔必须大于0
- **错误提示文案：** "时间间隔设置错误"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **监控配置**:
  - **刷新间隔 (refresh_interval)**: Number, 必填, 秒为单位
  - **预警规则 (alert_rules)**: Array, 预警规则配置
  - **显示设置 (display_settings)**: Object, 显示参数
- **实时数据**:
  - **生产数据 (production_data)**: Object, 实时生产数据
  - **设备状态 (equipment_status)**: Object, 设备状态信息
  - **质量数据 (quality_data)**: Object, 质量检验数据

### 展示数据
- **生产状态**: 工位状态、任务进度、产量统计
- **设备状态**: 运行状态、利用率、故障信息
- **预警信息**: 预警列表、处理状态、历史记录
- **趋势数据**: 生产趋势、效率趋势、质量趋势

### 空状态/零数据
- **无生产数据**: 显示"暂无生产数据，请检查数据采集"
- **无预警信息**: 显示"当前无预警信息，生产状态正常"
- **设备离线**: 显示"设备离线，无法获取状态信息"

### API接口
- **获取生产状态**: GET /api/production/status
- **获取预警信息**: GET /api/alerts
- **触发Andon报警**: POST /api/andon/alert
- **更新预警规则**: PUT /api/alert-rules

## 5. 异常与边界处理 (Error & Edge Cases)

### **数据采集中断**
- **提示信息**: "数据采集中断，显示数据可能不是最新"
- **用户操作**: 显示最后更新时间，提供手动刷新

### **预警系统故障**
- **提示信息**: "预警系统异常，可能无法及时发送预警"
- **用户操作**: 提供系统检测功能，通知管理员

### **网络连接异常**
- **提示信息**: "网络连接异常，正在尝试重新连接"
- **用户操作**: 显示连接状态，提供离线模式

### **大屏显示异常**
- **提示信息**: "显示设备异常，请检查硬件连接"
- **用户操作**: 提供显示测试功能，调整显示参数

### **预警风暴**
- **提示信息**: "检测到大量预警，可能存在系统性问题"
- **用户操作**: 提供预警聚合功能，防止预警风暴

### **权限不足**
- **提示信息**: "您没有权限查看部分监控数据"
- **用户操作**: 显示可查看的数据范围，提供权限申请

## 6. 验收标准 (Acceptance Criteria)

- [ ] 实时显示生产状态和关键指标
- [ ] 支持3D车间布局可视化展示
- [ ] 预警响应时间<10秒
- [ ] 支持多级预警和自动通知
- [ ] Andon报警系统响应及时
- [ ] 大屏显示适配不同分辨率
- [ ] 支持预警规则灵活配置
- [ ] 历史数据趋势分析准确
- [ ] 支持多种显示模式切换
- [ ] 界面美观，符合工业大屏设计规范
- [ ] 数据刷新频率可配置（1-60秒）
- [ ] 支持7×24小时稳定运行
- [ ] 所有交互响应时间<3秒
- [ ] 支持移动设备访问和控制
