# 功能模块规格说明书：设备档案管理模块

- **模块ID**: MES-009
- **所属子系统**: 生产管理子系统(MES)
- **最后更新**: 2025-07-31
- **功能整合说明**: 本模块整合了原设备管理子系统(EMS-001)的功能，专注于生产设备的档案管理和基础信息维护

## 1. 用户故事 (User Stories)

- **As a** 设备管理员, **I want to** 建立和维护生产设备档案信息, **so that** 为生产排程和设备管理提供基础数据。
- **As a** 生产计划员, **I want to** 查看设备的技术参数和状态信息, **so that** 合理安排生产计划和设备使用。
- **As a** 维护工程师, **I want to** 获取设备的详细技术资料, **so that** 高效执行设备维护和故障排除。
- **As a** 车间主管, **I want to** 实时了解设备状态, **so that** 及时调整生产安排和资源配置。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有设备管理权限
- 供应商主数据已在系统中建立
- 设备分类体系已定义
- 设备编码规则已配置

### 核心流程

#### 2.1 设备档案创建流程
1. 选择设备分类和设备类型
2. 系统自动生成设备编码或手工输入
3. 填写设备基本信息（名称、型号、规格等）
4. 录入技术参数和性能指标
5. 设置设备供应商和采购信息
6. 上传设备相关文档和图片
7. 设置设备初始状态和位置信息
8. 关联设备到生产线和工位
9. 提交设备档案审核并生效

#### 2.2 设备状态管理流程
1. 根据设备实际情况更新状态
2. 记录状态变更时间和原因
3. 自动触发生产排程调整
4. 更新设备可用性和生产计划
5. 发送状态变更通知给相关人员
6. 记录状态变更日志

### 后置条件
- 设备档案信息完整准确
- 设备编码唯一且符合规范
- 设备状态实时更新
- 与生产系统数据同步

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：设备档案管理页面
### 页面目标：提供生产设备的档案管理和状态监控功能

### 信息架构：
- **顶部区域**：包含 设备搜索, 新建设备, 批量操作, 状态监控
- **左侧区域**：包含 设备分类树, 状态筛选, 生产线筛选
- **中间区域**：包含 设备列表, 设备详情, 编辑界面
- **右侧区域**：包含 设备统计, 快速操作, 生产关联信息

### 交互逻辑与状态：

#### **设备查询区域**
- **基础查询：**
  - **设备名称：** 输入框，支持模糊搜索
  - **设备编码：** 输入框，精确查询
  - **设备型号：** 输入框，支持模糊搜索
  - **设备状态：** 多选下拉，运行中/停机/维修中/报废
- **高级查询：**
  - **设备分类：** 树形选择器，支持多选
  - **生产线：** 下拉选择器，筛选所属生产线
  - **工位：** 下拉选择器，筛选所属工位
  - **采购时间：** 日期范围选择器

#### **设备列表区域**
- **列表表头：**
  - **设备编码：** 可排序，点击查看详情
  - **设备名称：** 显示设备名称和型号
  - **设备分类：** 显示分类路径
  - **设备状态：** 状态标签，实时更新
  - **所属生产线：** 显示生产线和工位
  - **当前任务：** 显示正在执行的生产任务
  - **操作：** 编辑、查看、维护等操作
- **状态标识：**
  - **运行中：** 绿色标签，"运行中"
  - **空闲：** 蓝色标签，"空闲"
  - **停机：** 橙色标签，"停机"
  - **维修中：** 红色标签，"维修中"
  - **报废：** 灰色标签，"报废"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **设备基本信息**:
  - **设备编码 (equipment_code)**: String, 必填, 全局唯一
  - **设备名称 (equipment_name)**: String, 必填, 最大100字符
  - **设备型号 (model)**: String, 必填, 最大50字符
  - **设备分类ID (category_id)**: String, 必填, 引用设备分类
- **生产关联信息**:
  - **生产线ID (production_line_id)**: String, 必填, 引用生产线
  - **工位ID (workstation_id)**: String, 可选, 引用工位
  - **设备能力 (capacity)**: Decimal, 可选, 设备产能
  - **工艺适用性 (process_capability)**: Array, 可选, 适用工艺列表

### 展示数据
- **设备列表**: 设备编码、名称、分类、状态、生产线、当前任务
- **设备详情**: 完整的设备信息和技术参数
- **生产关联**: 设备与生产线、工位、任务的关联关系
- **统计信息**: 设备数量、状态分布、利用率统计

### API接口
- **设备查询**: GET /api/mes/equipment
- **设备创建**: POST /api/mes/equipment
- **设备更新**: PUT /api/mes/equipment/{id}
- **状态变更**: POST /api/mes/equipment/{id}/status
- **生产关联**: PUT /api/mes/equipment/{id}/production-link

## 5. 异常与边界处理 (Error & Edge Cases)

### **设备编码重复**
- **提示信息**: "设备编码已存在，请使用其他编码"
- **用户操作**: 提供编码建议或自动生成选项

### **生产线关联冲突**
- **提示信息**: "设备已关联到其他生产线，请先解除关联"
- **用户操作**: 提供关联管理和调整选项

### **设备状态变更限制**
- **提示信息**: "设备正在执行生产任务，无法变更状态"
- **用户操作**: 显示当前任务信息，提供任务完成后变更选项

## 6. 验收标准 (Acceptance Criteria)

- [ ] 设备档案创建功能完整，信息录入便捷
- [ ] 设备编码唯一性校验有效
- [ ] 设备状态管理流程清晰
- [ ] 与生产排程系统集成正常
- [ ] 设备查询和筛选功能准确
- [ ] 生产关联管理功能完善
- [ ] 数据校验规则完善，错误提示友好
- [ ] 设备统计信息实时准确
- [ ] 支持批量操作和数据导入导出
- [ ] 所有页面元素符合全局设计规范
- [ ] 响应时间<3秒，支持并发操作
- [ ] 与MES其他模块数据同步正常
