# 功能模块规格说明书：设备维护管理模块

- **模块ID**: MES-010
- **所属子系统**: 生产管理子系统(MES)
- **最后更新**: 2025-07-31
- **功能整合说明**: 本模块整合了原设备管理子系统(EMS-002)的功能，专注于生产设备的维护管理和故障处理

## 1. 用户故事 (User Stories)

- **As a** 维护工程师, **I want to** 制定和执行设备维护计划, **so that** 确保生产设备正常运行和延长使用寿命。
- **As a** 生产计划员, **I want to** 查看设备维护计划和状态, **so that** 合理安排生产计划避免冲突。
- **As a** 车间主管, **I want to** 监控设备维护执行进度, **so that** 确保维护工作按时完成不影响生产。
- **As a** 设备管理员, **I want to** 分析设备维护成本和效果, **so that** 优化维护策略和控制维护成本。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 设备档案已在MES-009中建立
- 维护人员信息已录入系统
- 维护计划模板已配置
- 用户具有维护管理权限

### 核心流程

#### 2.1 维护计划制定流程
1. 选择需要制定计划的生产设备
2. 根据设备类型选择维护计划模板
3. 设置维护周期和维护内容
4. 分配维护责任人和执行团队
5. 考虑生产计划安排维护时间
6. 审核维护计划的合理性
7. 提交维护计划审批
8. 审批通过后计划生效
9. 系统自动生成维护任务

#### 2.2 维护任务执行流程
1. 系统自动生成维护任务单
2. 检查生产计划确认维护时机
3. 发送维护任务通知给责任人
4. 维护人员接收任务并开始执行
5. 记录维护过程和发现的问题
6. 填写维护结果和设备状态
7. 更新设备状态和下次维护时间
8. 通知生产计划员设备可用状态
9. 维护主管审核确认

#### 2.3 故障维修处理流程
1. 接收生产现场设备故障报告
2. 评估故障严重程度和生产影响
3. 紧急调整生产计划和设备安排
4. 分配维修任务给合适的工程师
5. 诊断故障原因和制定维修方案
6. 申请维修备件和外部支持
7. 执行维修作业和测试验证
8. 更新设备状态和维修记录
9. 恢复生产计划和设备使用

### 后置条件
- 维护计划按时执行
- 维护记录完整准确
- 设备状态实时更新
- 生产计划及时调整

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：设备维护管理页面
### 页面目标：提供设备维护计划制定、任务执行和记录管理功能

### 信息架构：
- **顶部区域**：包含 任务搜索, 新建计划, 批量操作, 生产协调
- **左侧区域**：包含 设备分类, 维护类型, 状态筛选, 时间筛选
- **中间区域**：包含 维护任务列表, 任务详情, 执行界面
- **右侧区域**：包含 维护统计, 生产影响, 快速操作

### 交互逻辑与状态：

#### **维护任务筛选区域**
- **基础筛选：**
  - **设备名称：** 搜索选择器，支持多选
  - **维护类型：** 下拉选择，预防性/故障性/改进性
  - **任务状态：** 多选下拉，待执行/执行中/已完成/已延期
  - **责任人：** 搜索选择器，维护人员
- **时间筛选：**
  - **计划时间：** 日期范围选择器
  - **执行时间：** 日期范围选择器
  - **完成时间：** 日期范围选择器
- **生产协调：**
  - **生产线：** 下拉选择器，筛选生产线
  - **生产影响：** 复选框，高/中/低影响
  - **紧急程度：** 单选按钮，紧急/一般/计划

#### **维护任务列表**
- **列表表头：**
  - **任务编号：** 可排序，点击查看详情
  - **设备名称：** 显示设备信息和位置
  - **维护类型：** 显示维护类型标签
  - **计划时间：** 显示计划执行时间
  - **任务状态：** 状态标签，实时更新
  - **责任人：** 显示维护负责人
  - **生产影响：** 显示对生产的影响程度
  - **操作：** 执行、编辑、查看等操作
- **状态标识：**
  - **待执行：** 蓝色标签，"待执行"
  - **执行中：** 绿色标签，"执行中"
  - **已完成：** 灰色标签，"已完成"
  - **已延期：** 红色标签，"已延期"

#### **维护执行界面**
- **任务信息：**
  - **任务编号：** 显示任务唯一编号
  - **设备信息：** 显示设备名称、型号、位置
  - **维护内容：** 显示详细维护项目清单
  - **技术要求：** 显示维护技术标准和要求
- **执行记录：**
  - **开始时间：** 时间选择器，记录开始时间
  - **执行人员：** 多选框，选择参与人员
  - **维护过程：** 文本域，记录维护过程
  - **发现问题：** 文本域，记录发现的问题
  - **解决方案：** 文本域，记录解决措施
  - **完成时间：** 时间选择器，记录完成时间
- **结果确认：**
  - **维护结果：** 单选按钮，成功/部分成功/失败
  - **设备状态：** 下拉选择，更新设备状态
  - **下次维护：** 日期选择器，设置下次维护时间
  - **备注说明：** 文本域，补充说明信息

## 4. 数据规格 (Data Requirements)

### 输入数据
- **维护计划**:
  - **设备ID (equipment_id)**: String, 必填, 引用设备档案
  - **维护类型 (maintenance_type)**: String, 必填, 预防性/故障性/改进性
  - **维护周期 (cycle_days)**: Integer, 必填, 维护周期天数
  - **计划时间 (planned_time)**: DateTime, 必填, 计划执行时间
- **执行记录**:
  - **实际开始时间 (actual_start_time)**: DateTime, 必填
  - **实际完成时间 (actual_end_time)**: DateTime, 必填
  - **执行人员 (executors)**: Array, 必填, 执行人员列表
  - **维护结果 (result)**: String, 必填, 成功/部分成功/失败

### 展示数据
- **维护任务列表**: 任务编号、设备信息、维护类型、状态、责任人
- **维护历史**: 历史维护记录、成本统计、趋势分析
- **生产协调**: 维护对生产的影响分析和协调建议
- **统计报表**: 维护成本、工时统计、效果评估

### API接口
- **维护计划**: GET/POST/PUT /api/mes/maintenance/plans
- **维护任务**: GET/POST/PUT /api/mes/maintenance/tasks
- **执行记录**: POST /api/mes/maintenance/records
- **生产协调**: GET /api/mes/maintenance/production-impact

## 5. 异常与边界处理 (Error & Edge Cases)

### **生产计划冲突**
- **提示信息**: "维护时间与生产计划冲突，请协调安排"
- **用户操作**: 提供生产计划查看和时间调整选项

### **维护人员冲突**
- **提示信息**: "维护人员在该时间段已有其他任务安排"
- **用户操作**: 提供人员调度建议和时间调整选项

### **设备状态异常**
- **提示信息**: "设备当前状态不允许执行维护，请检查设备状态"
- **用户操作**: 提供设备状态查看和状态变更选项

## 6. 验收标准 (Acceptance Criteria)

- [ ] 维护计划制定功能完整，考虑生产协调
- [ ] 维护任务执行流程清晰，记录完整
- [ ] 故障维修响应及时，处理高效
- [ ] 与生产计划系统集成正常
- [ ] 维护成本统计准确，分析有效
- [ ] 设备状态更新及时，数据同步
- [ ] 维护提醒和预警功能正常
- [ ] 维护历史查询和分析功能完善
- [ ] 所有页面元素符合全局设计规范
- [ ] 响应时间<3秒，支持并发操作
- [ ] 与MES其他模块数据同步正常
