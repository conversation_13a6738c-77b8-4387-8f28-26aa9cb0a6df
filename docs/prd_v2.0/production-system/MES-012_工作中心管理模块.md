# 功能模块规格说明书：工作中心管理模块

- **模块ID**: MES-012
- **所属子系统**: 生产管理子系统(MES)
- **最后更新**: 2025-07-31

## 1. 用户故事 (User Stories)

- **As a** 生产经理, **I want to** 建立和维护工作中心主数据, **so that** 为生产调度和资源分配提供基础数据。
- **As a** 工艺工程师, **I want to** 配置工作中心的设备和人员资源, **so that** 支持工艺路线的资源分配。
- **As a** 生产计划员, **I want to** 查看工作中心的产能信息, **so that** 制定合理的生产计划和排程。
- **As a** 车间主任, **I want to** 管理工作中心的状态和可用性, **so that** 确保生产资源的有效利用。
- **As a** 成本会计, **I want to** 按工作中心归集成本, **so that** 进行精确的成本核算和分析。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有工作中心管理权限
- 设备档案已在系统中建立
- 组织架构和人员信息已配置
- 车间布局和区域划分已规划

### 核心流程

#### 2.1 工作中心创建流程
1. 选择工作中心类型和所属车间
2. 填写工作中心基本信息（编码、名称、位置）
3. 配置工作中心的物理属性和布局
4. 分配设备资源到工作中心
5. 配置人员资源和技能要求
6. 设置工作中心的产能参数
7. 定义工作中心的成本中心
8. 提交工作中心审核
9. 审核通过后工作中心生效

#### 2.2 资源配置管理流程
1. 查询需要配置的工作中心
2. 添加或移除设备资源
3. 配置人员岗位和技能要求
4. 设置资源的可用时间和班次
5. 计算和更新工作中心产能
6. 验证资源配置的合理性
7. 保存资源配置信息
8. 通知相关人员配置变更

#### 2.3 产能管理流程
1. 设置工作中心的理论产能
2. 配置产能计算参数和公式
3. 监控实际产能和利用率
4. 分析产能瓶颈和改进机会
5. 调整产能参数和资源配置
6. 生成产能分析报告
7. 为生产计划提供产能数据

#### 2.4 状态管理流程
1. 监控工作中心的实时状态
2. 处理状态变更请求
3. 记录状态变更原因和时间
4. 通知相关人员状态变更
5. 更新生产计划和调度
6. 记录状态变更历史
7. 分析状态变更对生产的影响

### 后置条件
- 工作中心信息完整准确
- 资源配置已生效
- 产能数据已更新
- 状态变更已通知相关人员

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：工作中心管理页面
### 页面目标：提供工作中心的创建、配置和监控功能

### 信息架构：
- **顶部区域**：包含 工作中心搜索, 新建工作中心, 批量操作, 状态监控
- **左侧区域**：包含 车间分类树, 工作中心类型筛选, 状态筛选
- **中间区域**：包含 工作中心列表, 工作中心详情, 配置界面
- **右侧区域**：包含 产能统计, 资源状态, 操作历史

### 交互逻辑与状态：

#### **车间分类树**
- **默认状态：** 展示车间层级，工作中心折叠显示
- **展开状态：** 点击车间前的展开图标，显示下属工作中心
- **选中状态：** 蓝色背景(#E6F7FF)，蓝色左边框
- **悬停状态：** 浅灰背景(#FAFAFA)，显示操作图标
- **交互行为：** 点击选中车间，中间区域显示对应工作中心列表

#### **工作中心列表**
- **列表项：**
  - **工作中心编码：** 显示唯一编码，点击进入详情
  - **工作中心名称：** 显示名称和位置信息
  - **工作中心类型：** 彩色标签显示类型
  - **当前状态：** 状态指示器（可用/维护/故障）
  - **产能利用率：** 进度条显示当前利用率
  - **设备数量：** 显示关联的设备数量
- **状态指示器：**
  - **可用状态：** 绿色圆点，表示正常可用
  - **维护状态：** 黄色圆点，表示计划维护
  - **故障状态：** 红色圆点，表示设备故障
  - **停用状态：** 灰色圆点，表示暂时停用

#### **工作中心详情/配置界面**
- **基本信息：**
  - **工作中心编码：** 输入框，自动生成或手工输入，必填
  - **工作中心名称：** 输入框，必填，最大50字符
  - **工作中心类型：** 下拉选择，加工中心/检验中心/包装中心
  - **所属车间：** 树形选择器，选择所属车间
  - **物理位置：** 输入框，详细位置描述
- **资源配置：**
  - **设备资源：** 多选表格，选择关联设备
  - **人员配置：** 数字输入框，标准作业人员数量
  - **技能要求：** 多选框，选择所需技能等级
  - **班次设置：** 时间选择器，设置工作班次
- **产能参数：**
  - **理论产能：** 数字输入框，单位件/小时
  - **实际产能：** 显示字段，系统计算
  - **产能利用率：** 显示字段，实时计算
  - **瓶颈分析：** 文本显示，系统分析结果

#### **资源监控面板**
- **设备状态：**
  - **设备列表：** 显示关联设备及其状态
  - **故障设备：** 高亮显示故障设备
  - **维护计划：** 显示即将到期的维护任务
- **人员状态：**
  - **在岗人员：** 显示当前在岗人员数量
  - **技能匹配：** 显示技能匹配度
  - **班次安排：** 显示当前班次安排
- **产能监控：**
  - **实时产能：** 显示当前产能利用率
  - **产能趋势：** 图表显示产能变化趋势
  - **瓶颈预警：** 显示产能瓶颈预警信息

### 数据校验规则：

#### **工作中心编码**
- **校验规则：** 编码必须全局唯一，符合编码规则格式
- **错误提示文案：** "工作中心编码已存在或格式不正确"

#### **资源配置**
- **校验规则：** 至少配置一台设备或一个人员岗位
- **错误提示文案：** "工作中心必须至少配置一种资源"

#### **产能参数**
- **校验规则：** 理论产能必须大于0
- **错误提示文案：** "理论产能必须大于0"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **工作中心基本信息**:
  - **工作中心编码 (workcenter_code)**: String, 必填, 全局唯一
  - **工作中心名称 (workcenter_name)**: String, 必填, 最大50字符
  - **工作中心类型 (workcenter_type)**: Enum, 必填, 加工中心/检验中心/包装中心
  - **所属车间 (workshop_id)**: String, 必填, 关联车间ID
- **资源配置**:
  - **设备资源 (equipment_list)**: Array, 关联设备ID列表
  - **人员配置 (staff_count)**: Integer, 必填, 标准作业人员数量
  - **技能要求 (skill_requirements)**: Array, 技能等级要求
- **产能参数**:
  - **理论产能 (theoretical_capacity)**: Decimal, 必填, 单位件/小时
  - **工作班次 (work_shifts)**: Array, 工作班次时间配置

### 展示数据
- **工作中心列表**: 工作中心的基本信息和状态
- **资源状态**: 设备和人员的实时状态信息
- **产能数据**: 产能利用率和效率统计
- **监控数据**: 实时运行状态和预警信息

### 空状态/零数据
- **无工作中心**: 显示"暂无工作中心，请先创建工作中心"
- **无设备资源**: 显示"暂未配置设备资源"
- **无产能数据**: 显示"暂无产能数据，请先配置产能参数"

### API接口
- **工作中心查询**: GET /api/mes/workcenters
- **工作中心创建**: POST /api/mes/workcenters
- **工作中心更新**: PUT /api/mes/workcenters/{id}
- **资源配置**: POST /api/mes/workcenters/{id}/resources
- **状态更新**: PUT /api/mes/workcenters/{id}/status
- **产能监控**: GET /api/mes/workcenters/{id}/capacity

## 5. 异常与边界处理 (Error & Edge Cases)

### **工作中心编码重复**
- **提示信息**: "工作中心编码已存在，请使用其他编码"
- **用户操作**: 编码字段标红，聚焦到编码输入框

### **删除被引用的工作中心**
- **提示信息**: "该工作中心正在被工艺路线使用，不能直接删除"
- **用户操作**: 显示引用详情，提供"停用工作中心"选项

### **资源配置冲突**
- **提示信息**: "设备资源已被其他工作中心使用，请重新选择"
- **用户操作**: 高亮冲突设备，提供可用设备列表

### **产能计算异常**
- **提示信息**: "产能计算参数异常，请检查配置"
- **用户操作**: 显示异常参数，提供修复建议

### **状态变更失败**
- **提示信息**: "工作中心状态变更失败，请稍后重试"
- **用户操作**: 提供重试按钮和技术支持联系方式

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持工作中心的创建、编辑、查询、删除操作
- [ ] 工作中心编码全局唯一，支持自定义编码规则
- [ ] 完整的资源配置管理，包含设备和人员资源
- [ ] 支持工作中心分类管理和层级结构
- [ ] 实时的状态监控和产能分析
- [ ] 与工艺路线设计模块的无缝集成
- [ ] 支持产能计算和瓶颈分析
- [ ] 提供工作中心利用率统计和报告
- [ ] 数据准确性≥99%，系统可用性≥99.5%
- [ ] 页面响应时间<2秒，状态更新实时
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持工作中心数据的导入导出功能
