# 功能模块规格说明书：变体生产优化管理模块

- **模块ID**: MES-013
- **所属子系统**: 生产管理子系统(MES)
- **最后更新**: 2025-07-31

## 1. 用户故事 (User Stories)

- **As a** 生产计划员, **I want to** 分析生产任务的变体需求, **so that** 为变体切割优化提供准确的规格要求。
- **As a** 工艺工程师, **I want to** 执行变体切割优化算法, **so that** 最大化原片利用率，最小化废料。
- **As a** 车间操作员, **I want to** 获取变体物料选择建议, **so that** 选择最优的变体规格进行生产。
- **As a** 仓库管理员, **I want to** 生成变体领料出库单, **so that** 准确出库对应的变体物料。
- **As a** 切割工, **I want to** 获取变体切割通知单和优化结果, **so that** 按照最优方案执行切割操作。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 物料变体管理功能已启用
- 生产任务已完成分解
- 变体库存数据准确
- 切割优化算法已配置

### 核心流程

#### 2.1 生产任务变体需求分析流程
1. 接收来自MES-001的生产任务分解结果
2. 提取任务中的基础物料需求（如：5mm白玻、5mm绿玻、8mm白玻）
3. 分析具体的生产规格要求：
   - 产品尺寸要求（如：1800mm*2400mm、1200mm*1500mm）
   - 质量等级要求（如：建筑级、汽车级、光伏级）
   - 特殊工艺要求（如：钢化、夹胶、中空）
4. 按基础物料类型分组需求
5. 计算每组的总需求量和规格分布
6. 生成变体需求分析报告

#### 2.2 变体切割优化分析流程
1. 获取当前库存中可用的变体规格：
   - 查询基础物料的所有变体库存
   - 获取原片规格（如：2440mm*3660mm、2440mm*1830mm）
   - 确认库存数量和质量状态
2. 执行切割优化算法：
   - 输入：需求规格列表、可用原片规格列表
   - 算法目标：最大化原片利用率，最小化废料
   - 约束条件：切割设备能力、最小边料尺寸
   - 输出：最优切割方案和变体选择建议
3. 计算优化效果指标：
   - 原片利用率（%）
   - 废料率（%）
   - 成本节约金额
4. 生成多个备选方案供选择
5. 推荐最优的变体选择方案

#### 2.3 变体物料选择与确认流程
1. 展示变体切割优化结果
2. 生产计划员审核优化方案：
   - 检查原片利用率是否满足要求
   - 确认变体库存是否充足
   - 评估切割复杂度和可操作性
3. 选择最终的变体选择方案
4. 系统自动分配具体的变体物料：
   - 为每个生产任务指定具体变体规格
   - 预留对应的变体库存
   - 生成变体物料清单
5. 确认变体选择并锁定库存

#### 2.4 变体领料出库单生成流程
1. 基于确认的变体选择方案
2. 生成详细的变体领料出库单：
   - 基础物料信息
   - 具体变体规格（长×宽×厚）
   - 领料数量和单位
   - 生产任务关联信息
   - 质量要求和检验标准
3. 关联到具体的生产工序和工位
4. 设置领料时间和优先级
5. 推送到WMS系统执行出库
6. 生成领料单号和追溯码

#### 2.5 变体切割通知单生成流程
1. 基于切割优化结果生成切割通知单
2. 包含详细的切割指令：
   - 原片规格和批次信息
   - 切割图纸和尺寸标注
   - 切割顺序和工艺要求
   - 成品规格和数量
   - 废料处理要求
3. 生成切割优化数据文件：
   - 切割机可读的数据格式
   - 包含切割路径和参数
   - 质量控制点设置
4. 推送到切割设备控制系统
5. 记录切割通知单状态

### 后置条件
- 变体需求分析完成
- 切割优化方案确定
- 变体物料成功预留
- 领料出库单已生成
- 切割通知单已下发

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：变体生产优化管理页面
### 页面目标：提供全面的变体生产优化和管理功能

### 信息架构：
- **左侧区域**：包含 功能导航, 任务筛选, 状态统计
- **中间区域**：包含 需求分析, 优化结果, 操作面板
- **右侧区域**：包含 变体详情, 库存状态, 优化指标

### 交互逻辑与状态：

#### **变体优化导航**
- **需求分析：** 分析生产任务的变体需求
- **切割优化：** 执行变体切割优化算法
- **物料选择：** 确认变体物料选择方案
- **出库管理：** 管理变体领料出库
- **切割通知：** 管理变体切割通知单

#### **生产任务变体需求分析**
- **任务概览：**
  - **待分析任务：** 显示需要进行变体分析的生产任务
  - **基础物料分组：** 按基础物料类型分组显示
  - **规格需求分布：** 显示各规格的需求数量
  - **紧急程度：** 按交期紧急程度排序
- **需求详情：**
  - **产品规格：** 具体的尺寸、厚度、质量要求
  - **数量需求：** 每个规格的需求数量
  - **质量标准：** 产品质量等级和检验要求
  - **工艺要求：** 特殊工艺处理要求

#### **变体切割优化分析**
- **库存状态：**
  - **可用原片：** 显示当前库存的原片规格和数量
  - **库存分布：** 按规格和质量等级分布
  - **库存预警：** 库存不足的变体规格提醒
- **优化算法：**
  - **算法选择：** 选择不同的优化算法
  - **参数设置：** 设置优化目标和约束条件
  - **计算进度：** 显示优化计算进度
  - **结果预览：** 实时显示优化结果

#### **优化结果展示**
- **切割方案：**
  - **切割图纸：** 可视化的切割布局图
  - **利用率指标：** 原片利用率、废料率
  - **成本分析：** 成本节约金额和比例
- **方案对比：**
  - **多方案比较：** 并排显示多个优化方案
  - **指标对比：** 利用率、成本、复杂度对比
  - **推荐方案：** 系统推荐的最优方案

#### **变体物料选择确认**
- **选择界面：**
  - **方案选择：** 选择最终的优化方案
  - **物料分配：** 为每个任务分配具体变体
  - **库存确认：** 确认变体库存充足性
  - **预留操作：** 预留对应的变体库存
- **确认结果：**
  - **物料清单：** 详细的变体物料清单
  - **分配结果：** 任务与变体的对应关系
  - **库存状态：** 更新后的库存状态

#### **变体领料出库管理**
- **出库单列表：**
  - **出库单信息：** 出库单号、生成时间、状态
  - **变体信息：** 基础物料、变体规格、数量
  - **任务关联：** 关联的生产任务和工序
  - **执行状态：** 待出库、已出库、已领料
- **出库单详情：**
  - **物料详情：** 详细的变体规格和质量要求
  - **领料信息：** 领料人、领料时间、工位信息
  - **追溯信息：** 批次号、供应商、质检报告

#### **变体切割通知管理**
- **通知单列表：**
  - **通知单信息：** 通知单号、生成时间、状态
  - **切割信息：** 原片规格、切割方案、成品规格
  - **设备信息：** 目标切割设备、操作员
  - **执行状态：** 待切割、切割中、已完成
- **切割指令：**
  - **切割图纸：** 详细的切割布局和尺寸
  - **工艺参数：** 切割速度、压力、温度等
  - **质量要求：** 切割精度、边缘质量要求
  - **数据文件：** 切割机可读的数据文件

## 4. 数据规格 (Data Requirements)

### 输入数据
- **生产任务需求**:
  - **任务ID (task_id)**: String, 必填, 生产任务唯一标识
  - **基础物料ID (base_material_id)**: String, 必填, 基础物料标识
  - **产品规格 (product_spec)**: Object, 必填, 产品尺寸和质量要求
  - **需求数量 (demand_quantity)**: Number, 必填, 生产需求数量
  - **交期要求 (delivery_date)**: Date, 必填, 要求完成日期
- **变体库存信息**:
  - **变体ID (variant_id)**: String, 必填, 变体唯一标识
  - **变体规格 (variant_spec)**: Object, 必填, 变体尺寸规格
  - **库存数量 (stock_quantity)**: Number, 必填, 当前库存数量
  - **质量等级 (quality_grade)**: String, 必填, 质量等级标识

### 展示数据
- **需求分析结果**: 基础物料分组、规格分布、数量统计
- **切割优化方案**: 切割图纸、利用率指标、成本分析
- **变体选择结果**: 物料分配、库存预留、执行计划
- **出库单信息**: 领料清单、追溯信息、执行状态

### 空状态/零数据
- **无生产任务**: 显示"当前无待优化的生产任务"
- **无可用变体**: 显示"该基础物料暂无可用变体库存"
- **优化失败**: 显示"切割优化失败，请检查约束条件"

### API接口
- **获取生产任务需求**: GET /api/variant-production/demands
- **执行切割优化**: POST /api/variant-production/optimize
- **确认变体选择**: POST /api/variant-production/confirm
- **生成出库单**: POST /api/variant-production/delivery-order

## 5. 异常与边界处理 (Error & Edge Cases)

### **变体库存不足**
- **提示信息**: "变体库存不足，无法满足生产需求，建议调整方案或紧急采购"
- **用户操作**: 提供库存预警详情，支持采购建议生成

### **切割优化无解**
- **提示信息**: "当前约束条件下无可行的切割方案，建议调整规格要求"
- **用户操作**: 提供约束条件调整建议，支持手动方案设计

### **原片利用率过低**
- **提示信息**: "原片利用率低于设定阈值，建议重新优化或调整需求"
- **用户操作**: 提供利用率分析报告，支持参数调整

### **切割设备故障**
- **提示信息**: "目标切割设备故障，已自动调整到备用设备"
- **用户操作**: 提供设备状态信息，支持手动设备选择

## 6. 验收标准 (Acceptance Criteria)

- [ ] 生产任务变体需求分析准确率100%
- [ ] 切割优化算法计算成功率≥95%
- [ ] 原片利用率提升≥20%（相比人工方案）
- [ ] 变体物料选择准确性100%
- [ ] 领料出库单生成及时性≤5分钟
- [ ] 切割通知单数据完整性100%
- [ ] 优化计算响应时间≤2分钟（100个任务）
- [ ] 界面操作响应时间≤3秒
- [ ] 支持并发优化操作，数据一致性保证
- [ ] 所有操作记录到审计日志
