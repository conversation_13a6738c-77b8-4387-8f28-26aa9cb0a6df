# 功能模块规格说明书：设备性能分析模块

- **模块ID**: MES-011
- **所属子系统**: 生产管理子系统(MES)
- **最后更新**: 2025-07-31
- **功能整合说明**: 本模块整合了原设备管理子系统(EMS-004)的功能，专注于生产设备的性能分析和优化
- **数据分析架构说明**: 本模块专注于设备性能的基础分析和监控，复杂的设备预测性分析、跨系统设备效能分析通过DC数据中心系统提供

## 1. 用户故事 (User Stories)

- **As a** 设备工程师, **I want to** 分析设备运行性能和效率, **so that** 识别设备问题和优化机会。
- **As a** 生产经理, **I want to** 监控设备OEE指标, **so that** 提升生产效率和设备利用率。
- **As a** 维护主管, **I want to** 分析设备故障模式和趋势, **so that** 制定预防性维护策略。
- **As a** 车间主管, **I want to** 实时监控设备健康状态, **so that** 及时发现异常并采取措施。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 设备档案已在MES-009中建立
- 设备运行数据已通过MES-006采集
- 维护记录已在MES-010中记录
- 用户具有性能分析权限

### 核心流程

#### 2.1 设备性能数据采集流程
1. 从MES-006获取设备运行数据
2. 从MES-010获取维护记录
3. 从生产系统获取产量数据
4. 数据清洗和标准化处理
5. 计算基础性能指标
6. 存储到性能分析数据库
7. 触发异常数据预警
8. 更新实时性能仪表板

#### 2.2 OEE分析计算流程
1. 获取设备计划运行时间
2. 计算设备实际运行时间
3. 统计设备停机时间和原因
4. 计算设备可用率(Availability)
5. 统计产品合格率数据
6. 计算设备质量率(Quality)
7. 分析设备运行速度效率
8. 计算设备效率率(Performance)
9. 综合计算OEE指标

#### 2.3 设备异常预警流程
1. 实时监控设备性能指标
2. 对比设备性能基准值
3. 识别异常指标和趋势
4. 评估异常严重程度
5. 触发相应级别的预警
6. 发送预警通知给相关人员
7. 记录预警处理过程
8. 跟踪预警处理结果

### 后置条件
- 设备性能指标实时更新
- OEE分析报告准确生成
- 异常预警及时发送
- 性能优化建议可行

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：设备性能分析页面
### 页面目标：提供设备性能监控、OEE分析和异常预警功能

### 信息架构：
- **顶部区域**：包含 时间筛选, 设备筛选, 分析类型, 报表导出
- **左侧区域**：包含 设备分类, 性能指标, 分析维度, 对比选项
- **中间区域**：包含 性能仪表板, 趋势图表, 分析报告
- **右侧区域**：包含 关键指标, 预警信息, 改进建议

### 交互逻辑与状态：

#### **性能分析筛选区域**
- **时间筛选：**
  - **分析周期：** 单选按钮，实时/小时/日/周/月
  - **时间范围：** 日期范围选择器
  - **班次筛选：** 复选框，早班/中班/晚班
- **设备筛选：**
  - **设备分类：** 树形选择器，支持多选
  - **设备名称：** 搜索选择器，支持多选
  - **生产线：** 下拉选择生产线
- **分析维度：**
  - **分析类型：** 单选按钮，OEE分析/故障分析/效率分析
  - **对比维度：** 下拉选择，同期对比/环比/设备对比

#### **OEE性能仪表板**
- **核心指标：**
  - **OEE总体：** 仪表盘显示，目标值和实际值
  - **可用率：** 仪表盘显示，百分比格式
  - **效率率：** 仪表盘显示，百分比格式
  - **质量率：** 仪表盘显示，百分比格式
- **趋势图表：**
  - **OEE趋势：** 折线图，显示OEE变化趋势
  - **三率对比：** 柱状图，对比可用率、效率率、质量率
  - **损失分析：** 饼图，显示各类损失占比
- **详细数据：**
  - **计划时间：** 显示设备计划运行时间
  - **运行时间：** 显示设备实际运行时间
  - **停机时间：** 显示设备停机时间
  - **产量数据：** 显示实际产量和计划产量

#### **设备效率分析界面**
- **效率指标：**
  - **设备利用率：** 百分比显示，运行时间/计划时间
  - **生产效率：** 百分比显示，实际产量/理论产量
  - **能耗效率：** 显示单位产品能耗
- **效率趋势：**
  - **日效率趋势：** 折线图显示日效率变化
  - **班次效率：** 柱状图显示各班次效率
  - **设备效率排名：** 表格显示设备效率排名
- **影响因素：**
  - **停机原因：** 饼图显示停机原因分布
  - **速度损失：** 柱状图显示速度损失原因
  - **改进机会：** 列表显示效率改进机会

#### **异常预警管理**
- **预警设置：**
  - **预警指标：** 复选框，选择监控指标
  - **预警阈值：** 数值输入，设置预警阈值
  - **预警级别：** 下拉选择，高/中/低
  - **通知方式：** 复选框，邮件/短信/系统通知
- **预警列表：**
  - **预警时间：** 显示预警发生时间
  - **设备名称：** 显示异常设备
  - **预警类型：** 显示预警指标类型
  - **预警级别：** 显示预警严重程度
  - **处理状态：** 显示处理状态
  - **操作：** 查看详情、标记处理等

## 4. 数据规格 (Data Requirements)

### 输入数据
- **运行数据**:
  - **设备ID (equipment_id)**: String, 必填, 引用设备档案
  - **数据时间 (data_time)**: DateTime, 必填, 数据采集时间
  - **运行状态 (status)**: String, 必填, 运行/停机/故障
  - **产量数据 (output)**: Integer, 可选, 产量数量
- **性能指标**:
  - **OEE值 (oee_value)**: Decimal, 计算得出, OEE综合指标
  - **可用率 (availability)**: Decimal, 计算得出, 设备可用率
  - **效率率 (performance)**: Decimal, 计算得出, 设备效率率
  - **质量率 (quality)**: Decimal, 计算得出, 产品质量率

### 展示数据
- **OEE指标**: 可用率、效率率、质量率、综合OEE
- **性能趋势**: 各项指标的时间趋势图表
- **异常预警**: 预警列表、处理状态、统计信息
- **对比分析**: 设备对比、时间对比、基准对比

### API接口
- **性能数据**: GET /api/mes/equipment/performance
- **OEE计算**: GET /api/mes/equipment/oee
- **异常预警**: GET/POST /api/mes/equipment/alerts
- **报告生成**: POST /api/mes/equipment/reports

## 5. 异常与边界处理 (Error & Edge Cases)

### **数据缺失**
- **提示信息**: "设备运行数据不完整，分析结果可能不准确"
- **用户操作**: 提供数据补充建议和替代分析方法

### **计算异常**
- **提示信息**: "OEE计算出现异常，请检查基础数据"
- **用户操作**: 提供数据检查工具和手动修正选项

### **预警失效**
- **提示信息**: "设备预警功能异常，请检查预警配置"
- **用户操作**: 提供预警配置检查和重置选项

## 6. 验收标准 (Acceptance Criteria)

- [ ] OEE计算准确，三率分解清晰
- [ ] 设备性能趋势分析功能完整
- [ ] 异常预警功能及时有效
- [ ] 性能对比分析功能准确
- [ ] 实时性能监控仪表板响应及时
- [ ] 与设备档案系统(MES-009)集成正常
- [ ] 与维护管理系统(MES-010)集成正常
- [ ] 与设备数据采集系统(MES-006)集成正常
- [ ] 性能报告生成功能完善
- [ ] 数据可视化图表清晰美观
- [ ] 所有页面元素符合全局设计规范
- [ ] 响应时间<5秒，支持大数据量分析
- [ ] 分析结果准确可靠，支持决策制定
