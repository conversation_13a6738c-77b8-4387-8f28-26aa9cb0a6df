# 功能模块规格说明书：生产计划下发模块

- **模块ID**: MES-003
- **所属子系统**: 生产管理子系统(MES)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 生产计划员, **I want to** 一键下发排程计划到各工位, **so that** 快速启动生产执行。
- **As a** 生产计划员, **I want to** 实时跟踪计划下发状态, **so that** 确保所有工位都收到最新计划。
- **As a** 车间主管, **I want to** 确认接收生产计划, **so that** 保证计划执行的准确性。
- **As a** 车间操作员, **I want to** 在工位终端查看最新任务, **so that** 按计划执行生产作业。
- **As a** 生产计划员, **I want to** 下发计划时包含变体选择信息, **so that** 工位能够获取准确的变体物料要求。
- **As a** 仓库管理员, **I want to** 接收变体物料需求计划, **so that** 提前准备对应的变体库存。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- APS排程计划已生成并确认
- 工位终端设备正常连接
- 工序任务信息完整准确
- 相关人员具有计划执行权限

### 核心流程

#### 2.1 计划下发准备流程
1. 选择已确认的排程方案
2. 验证计划完整性和可执行性
3. 检查工位终端连接状态
4. 确认物料和资源可用性
5. 生成下发任务清单
6. 设置下发优先级和时间

#### 2.2 计划下发执行流程
1. 启动计划下发操作
2. 按工位分组推送任务信息
3. 包含完整的工艺参数和SOP
4. 实时监控下发进度和状态
5. 处理下发失败的任务
6. 记录下发日志和时间戳

#### 2.3 工位接收确认流程
1. 工位终端接收任务推送
2. 显示新任务通知和数量
3. 车间主管或操作员确认接收
4. 验证任务信息的完整性
5. 反馈接收状态给计划系统
6. 更新任务状态为"已下发"

#### 2.4 计划变更同步流程
1. 检测排程计划的变更
2. 识别受影响的工位和任务
3. 生成变更通知和说明
4. 推送变更信息到相关工位
5. 工位确认变更并更新任务
6. 记录变更历史和原因

### 后置条件
- 所有工位接收到最新计划
- 任务信息完整准确传达
- 下发状态可追踪查询
- 生产执行准备就绪

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：生产计划下发页面
### 页面目标：提供高效的计划下发和状态监控界面

### 信息架构：
- **顶部区域**：包含 下发控制面板, 连接状态监控, 快捷操作按钮
- **中间区域**：包含 工位状态总览, 任务下发列表, 下发进度监控
- **底部区域**：包含 下发日志, 异常处理, 统计报表

### 交互逻辑与状态：

#### **下发控制面板**
- **方案选择器：**
  - **下拉选择：** 显示可下发的排程方案
  - **方案信息：** 显示方案创建时间和关键指标
  - **预览按钮：** 查看方案详细内容
- **下发模式设置：**
  - **全量下发：** 单选按钮，下发所有任务
  - **增量下发：** 单选按钮，仅下发变更任务
  - **指定工位：** 复选框组，选择特定工位
- **下发时机控制：**
  - **立即下发：** 单选按钮，立即执行下发
  - **定时下发：** 单选按钮，设置下发时间
  - **手动触发：** 单选按钮，手动确认后下发

#### **连接状态监控**
- **总体状态指示：**
  - **正常：** 绿色指示灯，显示"所有工位连接正常"
  - **部分异常：** 黄色指示灯，显示"X个工位连接异常"
  - **严重异常：** 红色指示灯，显示"多个工位离线"
- **工位连接列表：**
  - **工位名称：** 显示工位编号和名称
  - **连接状态：** 绿色/红色圆点表示在线/离线
  - **最后通信：** 显示最后通信时间
  - **信号强度：** 显示网络信号强度

#### **快捷操作按钮组**
- **开始下发按钮：**
  - **默认状态：** 绿色背景(#52C41A)，白色文字"开始下发"
  - **下发中状态：** 蓝色背景，显示"下发中..."和进度
  - **完成状态：** 绿色背景，显示"重新下发"
- **停止下发按钮：** 红色边框按钮，中断下发过程
- **刷新状态按钮：** 蓝色边框按钮，刷新工位连接状态
- **导出报告按钮：** 灰色边框按钮，导出下发报告

#### **工位状态总览**
- **工位卡片布局：** 网格布局显示各工位状态
- **工位卡片内容：**
  - **工位名称：** 大号字体显示工位编号
  - **连接状态：** 彩色圆点和文字说明
  - **任务数量：** 显示待执行任务数量
  - **最后更新：** 显示最后任务更新时间
  - **操作按钮：** 查看详情、重新下发、测试连接

#### **工位状态标识**
- **在线正常：** 绿色卡片边框，绿色状态点
- **在线异常：** 黄色卡片边框，黄色状态点
- **离线：** 红色卡片边框，红色状态点
- **下发中：** 蓝色卡片边框，蓝色状态点，带动画效果

#### **任务下发列表**
- **表格样式：** 固定表头，实时更新，支持排序
- **列定义：**
  - 任务编号：链接，查看任务详情
  - 工序名称：显示工序类型
  - 目标工位：显示工位名称
  - 产品信息：显示产品名称和数量
  - 计划时间：显示开始和结束时间
  - 下发状态：状态标签
  - 下发时间：时间戳显示
  - 操作：重新下发、查看详情

#### **下发状态标识**
- **待下发：** 灰色标签(#8C8C8C)，"待下发"
- **下发中：** 蓝色标签(#1890FF)，"下发中"
- **已下发：** 绿色标签(#52C41A)，"已下发"
- **下发失败：** 红色标签(#F5222D)，"下发失败"
- **已确认：** 深绿色标签(#389E0D)，"已确认"

#### **下发进度监控**
- **总体进度条：** 显示整体下发进度百分比
- **分工位进度：** 显示各工位的下发进度
- **实时统计：**
  - **总任务数：** 显示需要下发的总任务数
  - **已下发：** 显示已成功下发的任务数
  - **下发中：** 显示正在下发的任务数
  - **失败数：** 显示下发失败的任务数
- **预计完成时间：** 显示预计下发完成时间

#### **任务详情对话框**
- **任务基本信息：**
  - 任务编号：大号字体显示
  - 工序名称：突出显示
  - 产品信息：显示产品名称、规格、图片
  - 加工数量：数值和单位
- **时间安排：**
  - 计划开始时间：日期时间显示
  - 计划结束时间：日期时间显示
  - 预计工时：小时数显示
- **工艺信息：**
  - 工艺参数：表格显示关键参数
  - 质量要求：显示质量标准
  - 安全注意事项：显示安全要求
- **资源分配：**
  - 分配设备：显示设备信息
  - 分配人员：显示操作员信息
  - 所需物料：列表显示物料需求

#### **下发设置对话框**
- **下发范围选择：**
  - **全部工位：** 单选按钮，下发到所有工位
  - **指定工位：** 复选框组，选择特定工位
  - **工位组：** 下拉选择，按工位组下发
- **下发内容设置：**
  - **任务信息：** 复选框，包含任务基本信息
  - **工艺参数：** 复选框，包含工艺参数
  - **SOP文档：** 复选框，包含作业指导书
  - **质量标准：** 复选框，包含质量要求
- **下发时机设置：**
  - **立即下发：** 单选按钮
  - **定时下发：** 日期时间选择器
  - **条件触发：** 设置触发条件

#### **下发日志面板**
- **日志列表：** 时间倒序显示下发记录
- **日志内容：**
  - **时间戳：** 精确到秒的时间显示
  - **操作类型：** 下发/重发/取消等操作
  - **目标工位：** 显示工位名称
  - **任务数量：** 显示下发的任务数量
  - **执行结果：** 成功/失败状态
  - **错误信息：** 失败时显示错误原因
- **日志筛选：** 按时间、工位、状态筛选日志
- **日志导出：** 导出日志到Excel文件

#### **异常处理面板**
- **异常任务列表：** 显示下发失败的任务
- **异常原因分析：** 显示失败原因和解决建议
- **批量重试：** 批量重新下发失败任务
- **手动处理：** 手动标记任务状态
- **异常通知：** 发送异常通知给相关人员

### 数据校验规则：

#### **下发方案**
- **校验规则：** 必须选择有效的排程方案
- **错误提示文案：** "请选择要下发的排程方案"

#### **工位连接**
- **校验规则：** 目标工位必须在线且连接正常
- **错误提示文案：** "目标工位离线，无法下发任务"

#### **任务完整性**
- **校验规则：** 任务信息必须完整，包含必要的工艺参数
- **错误提示文案：** "任务信息不完整，请检查工艺参数"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **排程方案**:
  - **方案ID (schedule_id)**: String, 必填
  - **下发模式 (delivery_mode)**: Enum, [全量/增量/指定]
  - **目标工位 (target_stations)**: Array, 工位列表
  - **下发时间 (delivery_time)**: DateTime, 可选
- **任务信息**:
  - **任务列表 (tasks)**: Array, 待下发任务
  - **工艺参数 (process_params)**: Object, 工艺参数
  - **SOP文档 (sop_documents)**: Array, 作业指导书

### 展示数据
- **工位状态**: 连接状态、任务数量、最后更新时间
- **下发进度**: 总进度、分工位进度、实时统计
- **任务列表**: 任务信息、下发状态、时间戳
- **下发日志**: 操作记录、结果状态、错误信息

### 空状态/零数据
- **无可下发方案**: 显示"暂无可下发的排程方案"
- **无连接工位**: 显示"暂无在线工位，请检查网络连接"
- **下发队列为空**: 显示"暂无待下发任务"

### API接口
- **获取排程方案**: GET /api/schedules/deliverable
- **执行计划下发**: POST /api/production/deliver
- **查询下发状态**: GET /api/production/delivery-status
- **获取工位状态**: GET /api/stations/status

## 5. 异常与边界处理 (Error & Edge Cases)

### **网络连接异常**
- **提示信息**: "网络连接异常，部分工位无法接收任务"
- **用户操作**: 显示离线工位列表，提供重试功能

### **工位终端故障**
- **提示信息**: "工位终端响应异常，任务下发失败"
- **用户操作**: 提供手动重发选项，记录故障信息

### **任务信息不完整**
- **提示信息**: "任务信息不完整，无法下发到工位"
- **用户操作**: 高亮缺失信息，提供补充入口

### **下发过程中断**
- **提示信息**: "下发过程被中断，部分任务可能未成功下发"
- **用户操作**: 显示下发状态，提供续传功能

### **工位存储空间不足**
- **提示信息**: "工位存储空间不足，无法接收新任务"
- **用户操作**: 提示清理工位数据，或分批下发

### **版本冲突**
- **提示信息**: "检测到计划版本冲突，请确认是否覆盖"
- **用户操作**: 提供版本对比，用户选择处理方式

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持一键下发排程计划到各工位
- [ ] 实时监控工位连接状态和下发进度
- [ ] 任务信息完整传达，包含工艺参数和SOP
- [ ] 支持全量、增量和指定工位下发模式
- [ ] 下发失败任务自动重试和手动处理
- [ ] 工位接收确认和状态反馈机制
- [ ] 计划变更的实时同步和通知
- [ ] 完整的下发日志和异常记录
- [ ] 支持定时下发和条件触发下发
- [ ] 界面支持响应式设计，适配不同屏幕
- [ ] 下发响应时间：100个任务<30秒
- [ ] 工位状态更新延迟<5秒
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持键盘导航和无障碍访问
