# 功能模块规格说明书：质量标准管理模块

- **模块ID**: QMS-001
- **所属子系统**: 质量管理子系统(QMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 质量工程师, **I want to** 创建和管理检验项目库, **so that** 建立标准化的质量检验体系。
- **As a** 质量工程师, **I want to** 设计检验方案并关联到物料和工序, **so that** 确保质量标准的正确执行。
- **As a** 质量主管, **I want to** 审批质量标准的变更, **so that** 控制质量标准的一致性和权威性。
- **As a** 质检员, **I want to** 查看清晰的检验标准和要求, **so that** 准确执行质量检验工作。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有质量管理权限
- 物料主数据已建立
- 工艺路线已定义
- 检验仪器设备已配置

### 核心流程

#### 2.1 检验项目管理流程
1. 质量工程师创建检验项目
2. 设置检验项目属性（定量/定性、标准值、公差等）
3. 关联检验仪器和检验方法
4. 设置检验项目状态和版本
5. 提交审核或直接生效
6. 检验项目入库并可用于方案配置

#### 2.2 检验方案设计流程
1. 质量工程师创建检验方案
2. 从检验项目库中选择相关项目
3. 设置抽样规则和判定标准
4. 关联到特定物料或工序
5. 配置检验频次和触发条件
6. 提交审批并生效执行

#### 2.3 标准审批流程
1. 质量工程师提交标准变更申请
2. 质量主管审核标准的合理性
3. 评估变更对现有业务的影响
4. 审批通过后更新标准版本
5. 通知相关人员标准变更
6. 新标准正式生效执行

### 后置条件
- 质量标准体系完整建立
- 检验方案正确关联业务
- 标准变更记录完整
- 相关人员及时获得通知

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：质量标准管理页面
### 页面目标：提供检验项目和检验方案的创建、管理和审批功能

### 信息架构：
- **顶部区域**：包含 标准类型切换, 搜索筛选, 新建按钮, 批量操作
- **左侧区域**：包含 分类树, 状态筛选, 快速导航
- **中间区域**：包含 标准列表, 详情展示, 编辑界面
- **右侧区域**：包含 操作历史, 关联信息, 审批状态

### 交互逻辑与状态：

#### **标准类型切换区域**
- **检验项目：**
  - **项目标签：** 选中状态显示蓝色背景，显示检验项目列表
  - **方案标签：** 未选中状态显示灰色背景，点击切换到检验方案
  - **统计信息：** 显示当前类型的总数、启用数、停用数
- **检验方案：**
  - **方案标签：** 选中状态显示蓝色背景，显示检验方案列表
  - **项目标签：** 未选中状态显示灰色背景，点击切换到检验项目
  - **关联统计：** 显示方案关联的物料数、工序数

#### **检验项目管理界面**
- **项目列表：**
  - **项目编码：** 显示唯一的检验项目编码
  - **项目名称：** 显示检验项目名称，支持点击查看详情
  - **检验类型：** 标签显示定量/定性检验类型
  - **标准值：** 显示检验标准值和公差范围
  - **状态标识：** 启用(绿色)、停用(灰色)、草稿(橙色)
  - **操作按钮：** 编辑、复制、启用/停用、删除
- **项目创建：**
  - **基本信息：**
    - **项目编码：** 输入框，自动生成或手工输入，必填
    - **项目名称：** 输入框，必填，最大50字符
    - **检验类型：** 单选按钮，定量检验/定性检验
    - **项目描述：** 文本域，可选，最大200字符
  - **检验标准：**
    - **标准值：** 数字输入框，定量检验必填
    - **上限公差：** 数字输入框，可选
    - **下限公差：** 数字输入框，可选
    - **计量单位：** 下拉选择，mm/MPa/℃等
    - **判定标准：** 文本域，定性检验必填
  - **检验方法：**
    - **检验仪器：** 下拉选择关联的检验仪器
    - **检验方法：** 下拉选择标准检验方法
    - **操作说明：** 文本域，详细操作步骤
    - **注意事项：** 文本域，检验注意事项

#### **检验方案管理界面**
- **方案列表：**
  - **方案编码：** 显示唯一的检验方案编码
  - **方案名称：** 显示检验方案名称
  - **适用范围：** 显示关联的物料或工序
  - **检验项目数：** 显示包含的检验项目数量
  - **状态标识：** 生效(绿色)、草稿(橙色)、停用(灰色)
  - **操作按钮：** 编辑、复制、生效/停用、删除
- **方案创建：**
  - **基本信息：**
    - **方案编码：** 输入框，自动生成或手工输入
    - **方案名称：** 输入框，必填，最大50字符
    - **方案类型：** 下拉选择，IQC/IPQC/FQC
    - **适用说明：** 文本域，方案适用说明
  - **关联设置：**
    - **关联类型：** 单选按钮，物料关联/工序关联
    - **关联对象：** 下拉多选，选择具体物料或工序
    - **生效时间：** 日期选择器，方案生效时间
    - **失效时间：** 日期选择器，可选
  - **检验项目配置：**
    - **项目选择：** 从检验项目库中多选检验项目
    - **项目列表：** 显示已选择的检验项目
    - **检验顺序：** 拖拽调整检验项目顺序
    - **必检标识：** 复选框，标识必检项目
  - **抽样规则：**
    - **抽样方式：** 下拉选择，全检/抽检/免检
    - **抽样数量：** 数字输入框，抽检数量
    - **抽样比例：** 数字输入框，抽检比例
    - **判定规则：** 下拉选择，AQL标准等

#### **标准审批界面**
- **审批列表：**
  - **申请类型：** 显示新建/修改/停用申请
  - **申请内容：** 显示申请的具体内容
  - **申请人：** 显示申请人姓名和时间
  - **审批状态：** 待审批(橙色)、已通过(绿色)、已拒绝(红色)
  - **操作按钮：** 查看详情、审批、撤回
- **审批详情：**
  - **变更对比：** 对比显示变更前后的内容
  - **变更原因：** 显示申请人填写的变更原因
  - **影响评估：** 显示变更对现有业务的影响
  - **审批意见：** 文本域，填写审批意见
  - **审批结果：** 单选按钮，通过/拒绝
  - **审批操作：** 提交审批、保存草稿、返回修改

#### **版本管理界面**
- **版本历史：**
  - **版本号：** 显示标准版本号
  - **生效时间：** 显示版本生效时间
  - **变更内容：** 显示主要变更内容
  - **变更人：** 显示变更操作人
  - **操作按钮：** 查看详情、版本对比、回滚
- **版本对比：**
  - **对比选择：** 下拉选择要对比的两个版本
  - **差异展示：** 高亮显示版本间的差异
  - **变更统计：** 统计新增、修改、删除的项目数
  - **导出功能：** 导出版本对比报告

#### **关联信息展示**
- **物料关联：**
  - **关联物料：** 显示使用此标准的物料列表
  - **关联数量：** 显示关联的物料数量
  - **最近使用：** 显示最近使用此标准的时间
  - **使用频次：** 显示标准的使用频次统计
- **工序关联：**
  - **关联工序：** 显示使用此标准的工序列表
  - **工序路线：** 显示相关的工艺路线
  - **检验频次：** 显示工序检验的频次设置
  - **异常统计：** 显示工序检验的异常统计

### 数据校验规则：

#### **检验项目编码**
- **校验规则：** 项目编码必须全局唯一
- **错误提示文案：** "检验项目编码已存在，请使用其他编码"

#### **标准值和公差**
- **校验规则：** 上限公差必须大于下限公差
- **错误提示文案：** "上限公差必须大于下限公差"

#### **方案关联对象**
- **校验规则：** 每个方案必须至少关联一个物料或工序
- **错误提示文案：** "请至少选择一个关联的物料或工序"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **检验项目**:
  - **项目编码 (item_code)**: String, 必填, 全局唯一
  - **项目名称 (item_name)**: String, 必填, 最大50字符
  - **检验类型 (inspection_type)**: Enum, 必填, 定量/定性
  - **标准值 (standard_value)**: Decimal, 定量检验必填
- **检验方案**:
  - **方案编码 (plan_code)**: String, 必填, 全局唯一
  - **方案名称 (plan_name)**: String, 必填, 最大50字符
  - **方案类型 (plan_type)**: Enum, 必填, IQC/IPQC/FQC

### 展示数据
- **标准列表**: 检验项目和方案的列表信息
- **关联信息**: 标准与物料、工序的关联关系
- **版本历史**: 标准的版本变更记录
- **审批状态**: 标准变更的审批流程状态

### 空状态/零数据
- **无检验项目**: 显示"暂无检验项目，请先创建检验项目"
- **无检验方案**: 显示"暂无检验方案，请先创建检验方案"
- **无关联信息**: 显示"暂无关联的物料或工序"

### API接口
- **项目查询**: GET /api/quality/inspection-items
- **项目创建**: POST /api/quality/inspection-items
- **方案查询**: GET /api/quality/inspection-plans
- **方案创建**: POST /api/quality/inspection-plans
- **标准审批**: POST /api/quality/standards/{id}/approve

## 5. 异常与边界处理 (Error & Edge Cases)

### **标准冲突**
- **提示信息**: "检测到标准冲突，同一物料存在多个生效的检验方案"
- **用户操作**: 显示冲突详情和解决建议

### **关联数据变更**
- **提示信息**: "关联的物料信息已变更，请确认是否更新检验标准"
- **用户操作**: 提供标准更新和确认选项

### **版本回滚风险**
- **提示信息**: "回滚到此版本可能影响正在执行的检验任务"
- **用户操作**: 显示影响范围和确认操作

### **审批超时**
- **提示信息**: "标准审批超时，请联系审批人员"
- **用户操作**: 显示审批流程和催办功能

### **权限不足**
- **提示信息**: "您没有权限修改此质量标准"
- **用户操作**: 显示所需权限和申请流程

## 6. 验收标准 (Acceptance Criteria)

- [ ] 检验项目创建功能完整，支持定量和定性检验
- [ ] 检验方案配置灵活，支持多种抽样规则
- [ ] 标准审批流程规范，权限控制严格
- [ ] 版本管理功能完善，支持版本对比和回滚
- [ ] 关联管理准确，支持物料和工序关联
- [ ] 数据校验完整，防止标准冲突
- [ ] 支持大数据量处理（千级标准项目）
- [ ] 所有页面元素符合全局设计规范
- [ ] 响应时间<3秒，支持并发操作
- [ ] 数据完整性保证，异常处理完善
- [ ] 与物料和工艺系统集成正常
