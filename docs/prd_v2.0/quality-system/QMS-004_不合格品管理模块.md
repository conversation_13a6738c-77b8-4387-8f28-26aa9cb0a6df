# 功能模块规格说明书：不合格品管理模块

- **模块ID**: QMS-004
- **所属子系统**: 质量管理子系统(QMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 质检员, **I want to** 快速标识和隔离不合格品, **so that** 防止不合格品流入下一环节。
- **As a** 质量主管, **I want to** 评审和决策不合格品的处置方式, **so that** 最小化质量损失和成本。
- **As a** 生产主管, **I want to** 了解不合格品的处理进度, **so that** 及时调整生产计划。
- **As a** 财务人员, **I want to** 统计不合格品的处理成本, **so that** 准确核算质量成本。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 检验结果已确定
- 不合格品已识别
- 处理权限已分配
- 隔离区域已准备

### 核心流程

#### 2.1 不合格品识别流程
1. 检验系统自动识别不合格检验结果
2. 系统自动生成不合格品处理单
3. 不合格品自动标记和隔离
4. 通知相关责任人和管理者
5. 阻止不合格品的后续流转
6. 记录不合格品的详细信息

#### 2.2 不合格品评审流程
1. 质量主管接收不合格品处理单
2. 分析不合格的原因和影响
3. 评估可能的处置方式
4. 确定最优的处置决策
5. 填写评审意见和处置指令
6. 提交处置方案等待执行

#### 2.3 不合格品处置流程
1. 执行人员接收处置指令
2. 按照处置方案执行操作
3. 记录处置过程和结果
4. 更新库存和财务数据
5. 完成处置并关闭处理单
6. 统计处置成本和效果

### 后置条件
- 不合格品得到妥善处理
- 质量风险得到控制
- 处理成本准确统计
- 改进措施得到实施

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：不合格品管理页面
### 页面目标：提供不合格品的识别、评审、处置和统计管理功能

### 信息架构：
- **顶部区域**：包含 处理状态切换, 时间筛选, 搜索功能, 统计概览
- **左侧区域**：包含 处理状态筛选, 不合格类型, 责任部门
- **中间区域**：包含 不合格品列表, 处理详情, 处置操作
- **右侧区域**：包含 处理进度, 成本统计, 改进建议

### 交互逻辑与状态：

#### **处理状态切换区域**
- **待处理：**
  - **状态标签：** 选中状态显示橙色背景，显示待处理的不合格品
  - **数量统计：** 显示待处理不合格品数量
  - **紧急标识：** 红色标识紧急处理的不合格品
- **处理中：**
  - **状态标签：** 选中状态显示蓝色背景，显示正在处理的不合格品
  - **进度显示：** 显示处理进度百分比
  - **责任人：** 显示当前处理责任人
- **已完成：**
  - **状态标签：** 选中状态显示绿色背景，显示已完成处理的不合格品
  - **完成统计：** 显示完成处理的数量和比例
  - **成本汇总：** 显示处理成本汇总

#### **不合格品列表区域**
- **基本信息：**
  - **处理单号：** 显示不合格品处理单编号
  - **物料信息：** 显示不合格物料编码和名称
  - **批次号：** 显示物料批次号
  - **不合格数量：** 显示不合格品数量和单位
  - **发现时间：** 显示不合格品发现时间
  - **发现环节：** 标签显示IQC/IPQC/FQC
- **不合格信息：**
  - **不合格项目：** 显示具体的不合格检验项目
  - **不合格描述：** 显示不合格的具体描述
  - **严重程度：** 严重(红色)、一般(橙色)、轻微(黄色)
  - **影响范围：** 显示不合格品的影响范围
- **处理状态：**
  - **当前状态：** 待评审、评审中、待处置、处置中、已完成
  - **处理进度：** 进度条显示处理完成百分比
  - **责任人：** 显示当前处理责任人
  - **预计完成时间：** 显示预计处理完成时间
- **操作按钮：**
  - **查看详情：** 查看不合格品详细信息
  - **开始评审：** 开始不合格品评审
  - **执行处置：** 执行处置方案
  - **关闭处理：** 完成处理并关闭

#### **不合格品评审界面**
- **不合格分析：**
  - **不合格原因：** 下拉多选，选择可能的不合格原因
  - **原因分析：** 文本域，详细分析不合格原因
  - **责任部门：** 下拉选择责任部门
  - **责任人员：** 下拉选择具体责任人
- **影响评估：**
  - **质量影响：** 评估对产品质量的影响程度
  - **成本影响：** 评估处理成本和损失
  - **进度影响：** 评估对生产进度的影响
  - **客户影响：** 评估对客户的潜在影响
- **处置方案：**
  - **处置方式：** 单选按钮，报废/返工/降级/退货/特采
  - **处置说明：** 文本域，详细的处置说明
  - **处置成本：** 数字输入框，预估处置成本
  - **处置时限：** 日期选择器，要求完成时间
- **评审决策：**
  - **评审意见：** 文本域，填写评审意见
  - **审批级别：** 根据影响程度确定审批级别
  - **评审结果：** 单选按钮，通过/退回/升级处理
  - **后续措施：** 文本域，制定后续改进措施

#### **处置执行界面**
- **处置信息：**
  - **处置方式：** 显示评审确定的处置方式
  - **处置说明：** 显示详细的处置要求
  - **处置时限：** 显示要求的完成时间
  - **执行人员：** 下拉选择处置执行人员
- **报废处理：**
  - **报废确认：** 确认报废的物料和数量
  - **报废原因：** 记录报废的具体原因
  - **报废成本：** 计算报废造成的损失
  - **报废凭证：** 上传报废处理的凭证
- **返工处理：**
  - **返工方案：** 制定详细的返工方案
  - **返工工序：** 指定需要返工的工序
  - **返工成本：** 计算返工的人工和材料成本
  - **返工进度：** 跟踪返工的执行进度
- **降级处理：**
  - **降级等级：** 确定降级后的产品等级
  - **降级价格：** 设定降级产品的销售价格
  - **降级用途：** 说明降级产品的适用范围
  - **降级标识：** 对降级产品进行特殊标识
- **退货处理：**
  - **退货联系：** 联系供应商安排退货
  - **退货运输：** 安排退货的物流运输
  - **退货成本：** 计算退货的运输和处理成本
  - **退货跟踪：** 跟踪退货的处理进度

#### **处置跟踪监控**
- **执行进度：**
  - **进度概览：** 显示所有处置任务的执行进度
  - **进度详情：** 显示单个处置任务的详细进度
  - **里程碑：** 标识处置过程的关键里程碑
  - **延期预警：** 预警可能延期的处置任务
- **成本跟踪：**
  - **实际成本：** 记录处置的实际发生成本
  - **成本对比：** 对比预估成本与实际成本
  - **成本分类：** 按人工、材料、运输等分类统计
  - **成本趋势：** 分析处置成本的变化趋势
- **效果评估：**
  - **处置效果：** 评估处置方案的执行效果
  - **客户满意度：** 收集客户对处置结果的反馈
  - **改进效果：** 评估改进措施的实施效果
  - **经验总结：** 总结处置过程的经验教训

#### **不合格品统计分析**
- **发生统计：**
  - **发生频次：** 统计不合格品的发生频次
  - **发生趋势：** 折线图显示不合格品发生趋势
  - **分类统计：** 按物料、工序、原因等分类统计
  - **严重程度分布：** 饼图显示不同严重程度的分布
- **处理统计：**
  - **处理效率：** 统计不合格品的处理效率
  - **处理方式分布：** 统计各种处理方式的使用情况
  - **处理成本：** 统计不合格品的处理成本
  - **处理时间：** 统计平均处理时间
- **原因分析：**
  - **原因排名：** 柏拉图显示不合格原因排名
  - **责任部门分析：** 分析各部门的不合格品情况
  - **供应商分析：** 分析供应商的质量表现
  - **改进效果分析：** 分析改进措施的效果

#### **预防改进管理**
- **改进措施：**
  - **措施制定：** 根据不合格品分析制定改进措施
  - **措施分类：** 按预防、纠正、改进等分类管理
  - **责任分配：** 分配改进措施的责任部门和人员
  - **实施计划：** 制定改进措施的实施计划
- **措施跟踪：**
  - **实施进度：** 跟踪改进措施的实施进度
  - **效果评估：** 评估改进措施的实施效果
  - **持续改进：** 基于效果评估持续优化措施
  - **经验分享：** 分享成功的改进经验
- **知识库：**
  - **案例库：** 建立不合格品处理案例库
  - **经验库：** 积累处理经验和最佳实践
  - **标准库：** 建立处理标准和规范
  - **培训库：** 提供相关的培训资料

#### **移动端处置支持**
- **现场处置：**
  - **移动查看：** 移动端查看不合格品信息
  - **现场拍照：** 拍摄不合格品现场照片
  - **处置确认：** 现场确认处置执行结果
  - **进度更新：** 实时更新处置执行进度

### 数据校验规则：

#### **处置成本**
- **校验规则：** 处置成本必须大于0
- **错误提示文案：** "处置成本必须大于0"

#### **处置时限**
- **校验规则：** 处置时限不能早于当前时间
- **错误提示文案：** "处置时限不能早于当前时间"

#### **处置方式**
- **校验规则：** 必须选择一种处置方式
- **错误提示文案：** "请选择处置方式"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **不合格品信息**:
  - **处理单号 (ncr_code)**: String, 必填, 全局唯一
  - **物料编码 (material_code)**: String, 必填, 引用物料主数据
  - **不合格数量 (nonconform_quantity)**: Decimal, 必填, 大于0
  - **不合格描述 (nonconform_description)**: String, 必填, 最大500字符
- **处置信息**:
  - **处置方式 (disposal_method)**: Enum, 必填, 报废/返工/降级/退货/特采
  - **处置成本 (disposal_cost)**: Decimal, 必填, 大于等于0

### 展示数据
- **不合格品列表**: 不合格品的基本信息和处理状态
- **处理进度**: 不合格品处理的实时进度
- **成本统计**: 不合格品处理的成本分析
- **改进措施**: 基于不合格品分析的改进建议

### 空状态/零数据
- **无不合格品**: 显示"暂无不合格品记录"
- **无处理记录**: 显示"暂无处理记录"
- **无改进措施**: 显示"暂无改进措施"

### API接口
- **不合格品查询**: GET /api/quality/nonconforming-products
- **处理单创建**: POST /api/quality/ncr
- **评审提交**: POST /api/quality/ncr/{id}/review
- **处置执行**: POST /api/quality/ncr/{id}/disposal
- **统计分析**: GET /api/quality/ncr/statistics

## 5. 异常与边界处理 (Error & Edge Cases)

### **处置冲突**
- **提示信息**: "该批次物料正在其他流程中使用，无法执行处置"
- **用户操作**: 显示冲突详情和协调建议

### **成本超限**
- **提示信息**: "处置成本超过预算限额，需要上级审批"
- **用户操作**: 启动特殊审批流程

### **处置失败**
- **提示信息**: "处置执行失败，请检查执行条件"
- **用户操作**: 显示失败原因和重试选项

### **数据同步异常**
- **提示信息**: "与库存系统同步异常，数据可能不一致"
- **用户操作**: 提供手动同步和数据校验选项

### **权限不足**
- **提示信息**: "您没有权限执行此处置操作"
- **用户操作**: 显示所需权限和申请流程

## 6. 验收标准 (Acceptance Criteria)

- [ ] 不合格品自动识别准确率≥99%
- [ ] 处理流程规范完整，权限控制严格
- [ ] 处置方式多样化，成本核算准确
- [ ] 统计分析功能完善，支持决策
- [ ] 改进措施跟踪有效，持续改进
- [ ] 与检验和库存系统集成正常
- [ ] 支持大数据量处理（万级记录）
- [ ] 所有页面元素符合全局设计规范
- [ ] 响应时间<3秒，支持并发操作
- [ ] 数据完整性保证，异常处理完善
- [ ] 移动端支持现场处置操作
