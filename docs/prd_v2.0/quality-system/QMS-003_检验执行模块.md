# 功能模块规格说明书：检验执行模块

- **模块ID**: QMS-003
- **所属子系统**: 质量管理子系统(QMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 质检员, **I want to** 在移动设备上执行检验任务, **so that** 在现场便捷地完成质量检验工作。
- **As a** 质检员, **I want to** 查看清晰的检验标准和要求, **so that** 准确执行每个检验项目。
- **As a** 质检员, **I want to** 快速录入检验结果, **so that** 提高检验工作效率。
- **As a** 质量主管, **I want to** 实时查看检验执行进度, **so that** 及时了解质量控制状况。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 检验任务已分配
- 质检员已登录系统
- 检验标准已配置
- 检验设备已准备

### 核心流程

#### 2.1 检验任务接收流程
1. 质检员登录移动端查看待执行任务
2. 选择要执行的检验任务
3. 确认检验对象和检验要求
4. 开始执行检验任务
5. 任务状态更新为执行中
6. 记录检验开始时间

#### 2.2 检验执行流程
1. 系统显示检验项目列表和标准
2. 质检员按顺序执行各检验项目
3. 录入测量值或选择判定结果
4. 系统实时校验检验数据
5. 拍照记录检验过程和结果
6. 填写检验备注和异常说明

#### 2.3 检验结果提交流程
1. 完成所有检验项目
2. 系统自动计算检验结果
3. 质检员确认检验结论
4. 提交检验报告
5. 系统生成检验记录
6. 通知相关人员检验完成

### 后置条件
- 检验数据完整记录
- 检验结果准确判定
- 相关业务流程触发
- 质量记录归档保存

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：检验执行页面
### 页面目标：提供移动端检验作业的完整执行界面

### 信息架构：
- **顶部区域**：包含 任务信息, 进度指示, 操作按钮
- **主体区域**：包含 检验项目, 标准展示, 结果录入
- **底部区域**：包含 导航按钮, 提交按钮, 帮助信息

### 交互逻辑与状态：

#### **任务信息区域**
- **任务概览：**
  - **任务编号：** 显示检验任务编号
  - **检验类型：** 标签显示IQC/IPQC/FQC
  - **物料信息：** 显示物料编码和名称
  - **批次信息：** 显示批次号和数量
- **进度指示：**
  - **进度条：** 显示检验项目完成进度
  - **项目计数：** 显示"3/8"格式的项目进度
  - **时间显示：** 显示检验开始时间和已用时间
  - **状态标识：** 显示当前检验状态

#### **检验项目执行界面**
- **项目信息：**
  - **项目名称：** 显示当前检验项目名称
  - **项目编码：** 显示检验项目编码
  - **检验类型：** 显示定量/定性检验类型
  - **必检标识：** 红色星号标识必检项目
- **检验标准：**
  - **标准值：** 大字体显示检验标准值
  - **公差范围：** 显示上下限公差
  - **计量单位：** 显示测量单位
  - **判定标准：** 显示定性检验的判定标准
- **检验方法：**
  - **检验仪器：** 显示推荐使用的检验仪器
  - **操作说明：** 可展开查看详细操作步骤
  - **注意事项：** 高亮显示检验注意事项
  - **参考图片：** 显示检验方法的参考图片

#### **结果录入界面**
- **定量检验录入：**
  - **数值输入：** 大号数字输入框，支持小数点
  - **单位显示：** 输入框右侧显示计量单位
  - **快捷按钮：** 提供常用数值的快捷输入
  - **计算器：** 内置计算器辅助计算
- **定性检验录入：**
  - **判定选择：** 大按钮选择合格/不合格
  - **等级选择：** 多级评分的等级选择
  - **选项列表：** 多选或单选的判定选项
  - **自定义输入：** 支持自定义判定结果
- **结果校验：**
  - **实时校验：** 输入时实时显示是否合格
  - **超限提醒：** 超出公差范围时红色高亮提醒
  - **必填校验：** 必检项目未填写时阻止继续
  - **格式校验：** 检验数据格式的实时校验

#### **检验记录功能**
- **拍照记录：**
  - **拍照按钮：** 大按钮支持拍摄检验现场照片
  - **照片预览：** 拍摄后立即预览照片
  - **照片标注：** 支持在照片上添加文字标注
  - **照片管理：** 支持删除和重新拍摄
- **语音记录：**
  - **语音输入：** 支持语音录入检验备注
  - **语音转文字：** 自动将语音转换为文字
  - **语音播放：** 支持播放录制的语音
  - **语音保存：** 保存语音文件作为检验记录
- **备注输入：**
  - **文本输入：** 文本域输入检验备注
  - **模板选择：** 提供常用备注模板
  - **异常说明：** 专门的异常情况说明输入
  - **建议输入：** 输入改进建议和意见

#### **检验导航功能**
- **项目导航：**
  - **上一项：** 返回上一个检验项目
  - **下一项：** 进入下一个检验项目
  - **项目列表：** 显示所有检验项目的完成状态
  - **快速跳转：** 点击项目列表快速跳转
- **进度控制：**
  - **暂停检验：** 暂停当前检验任务
  - **继续检验：** 恢复暂停的检验任务
  - **保存草稿：** 保存当前检验进度
  - **退出检验：** 退出检验并保存进度

#### **检验结果汇总**
- **结果概览：**
  - **总体结论：** 显示整体检验结论（合格/不合格）
  - **项目统计：** 显示合格项目数/总项目数
  - **关键项目：** 高亮显示不合格的关键项目
  - **风险评估：** 显示质量风险等级
- **详细结果：**
  - **项目列表：** 列表显示所有检验项目和结果
  - **数据对比：** 对比实测值与标准值
  - **趋势分析：** 显示与历史检验结果的对比
  - **异常标识：** 标识异常和超限的检验项目
- **结果确认：**
  - **结果复核：** 质检员确认检验结果无误
  - **签名确认：** 电子签名确认检验结果
  - **时间戳：** 记录结果确认的时间
  - **责任人：** 记录检验责任人信息

#### **离线作业支持**
- **离线模式：**
  - **数据缓存：** 缓存检验任务和标准数据
  - **离线录入：** 支持离线状态下录入检验结果
  - **数据同步：** 网络恢复后自动同步数据
  - **冲突处理：** 处理离线数据与服务器数据的冲突
- **数据备份：**
  - **本地存储：** 检验数据本地备份存储
  - **定期清理：** 定期清理过期的本地数据
  - **数据恢复：** 支持从本地备份恢复数据
  - **存储管理：** 管理本地存储空间使用

#### **质量控制功能**
- **数据校验：**
  - **范围校验：** 检验数据是否在合理范围内
  - **逻辑校验：** 检验数据的逻辑一致性
  - **完整性校验：** 检验数据的完整性
  - **准确性校验：** 检验数据的准确性
- **异常处理：**
  - **异常标识：** 自动标识异常的检验数据
  - **异常原因：** 记录异常的可能原因
  - **处理建议：** 提供异常处理建议
  - **上报机制：** 重大异常自动上报

#### **帮助和指导**
- **操作指导：**
  - **新手引导：** 首次使用的操作引导
  - **操作提示：** 关键操作的提示信息
  - **帮助文档：** 详细的操作帮助文档
  - **视频教程：** 检验操作的视频教程
- **标准查询：**
  - **标准库：** 查询相关的质量标准
  - **历史数据：** 查询历史检验数据
  - **参考案例：** 查询类似的检验案例
  - **专家建议：** 获取专家的检验建议

### 数据校验规则：

#### **检验数值**
- **校验规则：** 数值必须在合理范围内
- **错误提示文案：** "检验数值超出合理范围，请重新测量"

#### **必检项目**
- **校验规则：** 必检项目必须完成检验
- **错误提示文案：** "必检项目未完成，无法提交检验结果"

#### **照片质量**
- **校验规则：** 照片必须清晰可见
- **错误提示文案：** "照片不够清晰，请重新拍摄"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **检验结果**:
  - **项目编码 (item_code)**: String, 必填, 引用检验项目
  - **测量值 (measured_value)**: Decimal, 定量检验必填
  - **判定结果 (judgment_result)**: Enum, 必填, 合格/不合格
  - **检验备注 (inspection_note)**: String, 可选, 最大500字符
- **检验记录**:
  - **检验照片 (inspection_photos)**: Array, 可选, 图片文件
  - **检验时间 (inspection_time)**: DateTime, 必填

### 展示数据
- **检验任务**: 待执行的检验任务信息
- **检验标准**: 检验项目的标准和要求
- **历史数据**: 相关的历史检验数据
- **进度信息**: 检验执行的实时进度

### 空状态/零数据
- **无检验任务**: 显示"暂无待执行的检验任务"
- **无检验项目**: 显示"当前任务无检验项目"
- **无历史数据**: 显示"暂无历史检验数据"

### API接口
- **任务获取**: GET /api/quality/inspection-tasks/{id}
- **结果提交**: POST /api/quality/inspection-results
- **照片上传**: POST /api/quality/inspection-photos
- **进度更新**: PUT /api/quality/inspection-tasks/{id}/progress
- **离线同步**: POST /api/quality/inspection-results/sync

## 5. 异常与边界处理 (Error & Edge Cases)

### **网络中断**
- **提示信息**: "网络连接中断，已切换到离线模式"
- **用户操作**: 继续离线作业，网络恢复后自动同步

### **设备故障**
- **提示信息**: "检验设备异常，请检查设备状态"
- **用户操作**: 提供设备检查指导和替代方案

### **数据异常**
- **提示信息**: "检验数据异常，请确认测量结果"
- **用户操作**: 提供重新测量和异常上报选项

### **权限过期**
- **提示信息**: "登录已过期，请重新登录"
- **用户操作**: 保存当前进度，引导重新登录

### **存储空间不足**
- **提示信息**: "设备存储空间不足，请清理空间"
- **用户操作**: 提供存储清理和数据同步选项

## 6. 验收标准 (Acceptance Criteria)

- [ ] 移动端检验界面友好，操作便捷
- [ ] 检验标准显示清晰，易于理解
- [ ] 结果录入快速准确，校验完整
- [ ] 离线作业功能稳定，数据同步可靠
- [ ] 拍照和语音功能正常，记录完整
- [ ] 检验进度实时更新，状态准确
- [ ] 支持多种移动设备（手机/平板）
- [ ] 所有页面元素符合移动端设计规范
- [ ] 响应时间<2秒，操作流畅
- [ ] 数据准确性高，异常处理完善
- [ ] 与检验任务系统集成正常
