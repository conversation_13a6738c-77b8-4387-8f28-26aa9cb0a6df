# 功能模块规格说明书：检验任务管理模块

- **模块ID**: QMS-002
- **所属子系统**: 质量管理子系统(QMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 质检员, **I want to** 接收自动生成的检验任务, **so that** 及时响应各种检验需求。
- **As a** 质量主管, **I want to** 管理检验任务的分配和优先级, **so that** 优化检验资源配置。
- **As a** 生产主管, **I want to** 查看检验任务的执行状态, **so that** 了解生产流程的质量控制情况。
- **As a** 系统管理员, **I want to** 配置检验任务的自动触发规则, **so that** 确保检验任务的及时生成。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 质量标准已建立
- 检验方案已配置
- 业务单据已生成
- 质检员已分配权限

### 核心流程

#### 2.1 检验任务自动生成流程
1. 业务系统触发检验事件（收货/报工/完工）
2. 系统根据物料或工序匹配检验方案
3. 自动生成检验单并填充基本信息
4. 根据规则分配给合适的质检员
5. 发送任务通知给相关人员
6. 检验任务进入待执行状态

#### 2.2 检验任务分配流程
1. 系统根据质检员工作负荷自动分配
2. 质量主管可手动调整任务分配
3. 考虑质检员专业领域和权限
4. 设置任务优先级和完成时限
5. 通知质检员接收新任务
6. 任务状态更新为已分配

#### 2.3 检验任务监控流程
1. 实时监控检验任务执行状态
2. 识别超时和异常的检验任务
3. 自动发送提醒和预警通知
4. 质量主管可进行任务干预
5. 记录任务执行过程和结果
6. 生成检验任务统计报告

### 后置条件
- 检验任务及时生成
- 任务分配合理有效
- 执行状态实时可见
- 异常情况及时处理

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：检验任务管理页面
### 页面目标：提供检验任务的生成、分配、监控和管理功能

### 信息架构：
- **顶部区域**：包含 任务视图切换, 时间筛选, 状态筛选, 搜索功能
- **左侧区域**：包含 任务分类, 优先级筛选, 质检员筛选
- **中间区域**：包含 任务列表, 任务详情, 批量操作
- **右侧区域**：包含 任务统计, 执行进度, 异常预警

### 交互逻辑与状态：

#### **任务视图切换区域**
- **我的任务：**
  - **任务标签：** 选中状态显示蓝色背景，显示当前用户的检验任务
  - **任务统计：** 显示待执行、执行中、已完成任务数量
  - **刷新按钮：** 手动刷新任务列表
- **全部任务：**
  - **全部标签：** 选中状态显示蓝色背景，显示所有检验任务
  - **权限控制：** 根据用户权限显示可见的任务范围
  - **筛选器：** 提供更多筛选条件

#### **检验任务列表区域**
- **任务展示：**
  - **任务编号：** 显示唯一的检验任务编号
  - **检验类型：** 标签显示IQC/IPQC/FQC类型
  - **物料信息：** 显示被检验的物料编码和名称
  - **批次信息：** 显示物料批次号和数量
  - **任务状态：** 待执行(橙色)、执行中(蓝色)、已完成(绿色)、已超时(红色)
  - **优先级：** 高(红色)、中(橙色)、低(灰色)优先级标识
  - **分配人员：** 显示负责的质检员姓名
  - **计划时间：** 显示计划完成时间
  - **操作按钮：** 查看详情、开始检验、重新分配、取消任务
- **任务筛选：**
  - **状态筛选：** 多选复选框，按任务状态筛选
  - **类型筛选：** 下拉选择，按检验类型筛选
  - **时间筛选：** 日期范围选择器
  - **人员筛选：** 下拉多选，按质检员筛选
  - **优先级筛选：** 复选框，按优先级筛选
- **批量操作：**
  - **批量分配：** 选择多个任务批量分配给质检员
  - **批量调整优先级：** 批量调整任务优先级
  - **批量导出：** 导出选中任务的详细信息
  - **批量取消：** 批量取消未开始的任务

#### **任务详情界面**
- **基本信息：**
  - **任务编号：** 显示检验任务唯一编号
  - **检验类型：** 显示IQC/IPQC/FQC类型
  - **创建时间：** 显示任务创建时间
  - **计划完成时间：** 显示计划完成时间
  - **任务状态：** 显示当前任务状态
  - **优先级：** 显示任务优先级
- **检验对象：**
  - **物料编码：** 显示被检验物料编码
  - **物料名称：** 显示物料名称和规格
  - **批次号：** 显示物料批次号
  - **数量：** 显示检验数量和单位
  - **供应商：** 显示物料供应商信息（IQC）
  - **生产订单：** 显示关联的生产订单（IPQC/FQC）
- **检验方案：**
  - **方案名称：** 显示适用的检验方案
  - **检验项目：** 列表显示所有检验项目
  - **抽样规则：** 显示抽样数量和规则
  - **判定标准：** 显示检验判定标准
- **任务分配：**
  - **分配人员：** 下拉选择质检员
  - **分配时间：** 显示任务分配时间
  - **分配原因：** 文本域，填写分配原因
  - **完成时限：** 日期时间选择器

#### **任务自动生成配置**
- **触发规则：**
  - **IQC触发：** 采购收货完成自动生成IQC任务
  - **IPQC触发：** 工序报工完成自动生成IPQC任务
  - **FQC触发：** 生产完工自动生成FQC任务
  - **手动触发：** 支持手动创建检验任务
- **生成条件：**
  - **物料匹配：** 根据物料编码匹配检验方案
  - **工序匹配：** 根据工序编码匹配检验方案
  - **批次规则：** 设置批次检验的触发条件
  - **免检规则：** 设置免检物料和条件
- **任务属性：**
  - **优先级规则：** 根据物料重要性设置优先级
  - **时限规则：** 根据检验类型设置完成时限
  - **分配规则：** 根据专业领域自动分配质检员
  - **通知规则：** 设置任务通知的方式和对象

#### **任务执行监控**
- **执行状态：**
  - **待执行任务：** 显示待执行任务数量和列表
  - **执行中任务：** 显示正在执行的任务和进度
  - **已完成任务：** 显示已完成任务的统计
  - **超时任务：** 高亮显示超时的检验任务
- **进度跟踪：**
  - **任务进度：** 显示各任务的执行进度百分比
  - **检验项目进度：** 显示单个任务内检验项目的完成情况
  - **时间进度：** 显示任务执行的时间进度
  - **质量进度：** 显示检验结果的质量状况
- **异常监控：**
  - **超时预警：** 任务接近超时时发出预警
  - **异常任务：** 显示执行异常的任务
  - **质量异常：** 显示检验发现质量问题的任务
  - **人员异常：** 显示质检员工作负荷异常

#### **任务统计分析**
- **执行统计：**
  - **任务完成率：** 统计任务按时完成比例
  - **检验效率：** 统计平均检验时间
  - **质量合格率：** 统计检验合格比例
  - **人员效率：** 统计各质检员的工作效率
- **趋势分析：**
  - **任务量趋势：** 折线图显示任务量变化趋势
  - **完成率趋势：** 显示任务完成率变化趋势
  - **质量趋势：** 显示质量合格率变化趋势
  - **效率趋势：** 显示检验效率变化趋势
- **对比分析：**
  - **类型对比：** 对比不同检验类型的执行情况
  - **人员对比：** 对比不同质检员的工作表现
  - **时间对比：** 对比不同时间段的执行情况
  - **物料对比：** 对比不同物料的检验情况

#### **任务通知管理**
- **通知设置：**
  - **通知类型：** 任务分配、任务提醒、任务超时、任务完成
  - **通知方式：** 系统消息、邮件、短信、微信
  - **通知对象：** 质检员、质量主管、生产主管
  - **通知时机：** 立即通知、定时通知、条件触发
- **通知模板：**
  - **模板管理：** 创建和编辑通知消息模板
  - **变量设置：** 设置模板中的动态变量
  - **格式设置：** 设置不同通知方式的消息格式
  - **多语言支持：** 支持中英文通知模板
- **通知记录：**
  - **发送记录：** 记录所有通知的发送情况
  - **接收确认：** 记录通知的接收和确认状态
  - **失败重试：** 自动重试发送失败的通知
  - **统计分析：** 统计通知的发送成功率

### 数据校验规则：

#### **任务分配**
- **校验规则：** 质检员必须具有相应的检验权限
- **错误提示文案：** "所选质检员没有此类检验的权限"

#### **完成时限**
- **校验规则：** 完成时限不能早于当前时间
- **错误提示文案：** "完成时限不能早于当前时间"

#### **批量操作**
- **校验规则：** 批量操作的任务状态必须一致
- **错误提示文案：** "只能对相同状态的任务进行批量操作"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **检验任务**:
  - **任务编号 (task_code)**: String, 必填, 全局唯一
  - **检验类型 (inspection_type)**: Enum, 必填, IQC/IPQC/FQC
  - **物料编码 (material_code)**: String, 必填, 引用物料主数据
  - **批次号 (batch_number)**: String, 必填
- **任务分配**:
  - **质检员ID (inspector_id)**: String, 必填, 引用用户主数据
  - **完成时限 (deadline)**: DateTime, 必填

### 展示数据
- **任务列表**: 检验任务的列表信息和状态
- **执行进度**: 任务执行的实时进度
- **统计数据**: 任务执行的统计分析数据
- **通知记录**: 任务相关的通知发送记录

### 空状态/零数据
- **无检验任务**: 显示"暂无检验任务"
- **无分配人员**: 显示"暂无可分配的质检员"
- **无统计数据**: 显示"暂无统计数据"

### API接口
- **任务查询**: GET /api/quality/inspection-tasks
- **任务创建**: POST /api/quality/inspection-tasks
- **任务分配**: PUT /api/quality/inspection-tasks/{id}/assign
- **任务统计**: GET /api/quality/inspection-tasks/statistics
- **通知发送**: POST /api/quality/inspection-tasks/{id}/notify

## 5. 异常与边界处理 (Error & Edge Cases)

### **自动生成失败**
- **提示信息**: "检验任务自动生成失败，请检查检验方案配置"
- **用户操作**: 显示失败原因和手动创建选项

### **分配冲突**
- **提示信息**: "质检员工作负荷过重，建议重新分配"
- **用户操作**: 显示负荷情况和其他可选人员

### **任务超时**
- **提示信息**: "检验任务已超时，请及时处理"
- **用户操作**: 提供延期、重新分配、取消等选项

### **系统集成异常**
- **提示信息**: "与业务系统集成异常，任务信息可能不完整"
- **用户操作**: 提供手动补充信息和重新同步选项

### **权限不足**
- **提示信息**: "您没有权限管理此检验任务"
- **用户操作**: 显示所需权限和申请流程

## 6. 验收标准 (Acceptance Criteria)

- [ ] 检验任务自动生成准确率100%
- [ ] 任务分配算法合理，负荷均衡
- [ ] 任务状态实时更新，监控有效
- [ ] 通知机制完善，及时准确
- [ ] 批量操作功能完整，效率高
- [ ] 统计分析准确，支持决策
- [ ] 支持大并发任务处理（百级并发）
- [ ] 所有页面元素符合全局设计规范
- [ ] 响应时间<3秒，支持实时刷新
- [ ] 数据完整性保证，异常处理完善
- [ ] 与业务系统集成稳定可靠
