# 功能模块规格说明书：外协成本管理模块

- **模块ID**: PMS-010
- **所属子系统**: 采购管理子系统(PMS)
- **最后更新**: 2025-07-31
- **功能说明**: 管理外协业务的成本核算、预算控制、成本分析和优化，确保外协成本的透明化和可控性

## 1. 用户故事 (User Stories)

- **As a** 成本会计, **I want to** 准确核算外协成本, **so that** 为产品定价和盈利分析提供准确数据。
- **As a** 采购经理, **I want to** 控制外协预算执行, **so that** 确保外协成本在预算范围内。
- **As a** 财务分析师, **I want to** 分析外协成本趋势, **so that** 识别成本优化机会和风险。
- **As a** 项目经理, **I want to** 查看项目外协成本, **so that** 控制项目成本和预算执行。
- **As a** 管理层, **I want to** 获得外协成本报告, **so that** 做出外协策略和供应商选择决策。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 外协订单已创建并确认
- 成本科目和成本中心已配置
- 外协供应商价格信息已维护
- 相关人员具有成本管理权限

### 核心流程

#### 2.1 外协成本预算制定流程
1. 根据年度生产计划制定外协预算
2. 按产品线和供应商分解预算
3. 设置预算控制规则和预警阈值
4. 审批并发布外协成本预算
5. 分配预算到各责任中心
6. 建立预算执行监控机制

#### 2.2 外协成本核算流程
1. 外协订单执行时记录实际成本
2. 收集外协相关的直接和间接成本
3. 按成本对象分配外协成本
4. 计算外协成本差异和变动
5. 生成外协成本核算报告
6. 更新产品成本和库存成本

#### 2.3 外协成本控制流程
1. 实时监控外协成本执行情况
2. 对比实际成本与预算成本
3. 识别成本超支和异常情况
4. 触发预警和审批流程
5. 分析成本差异原因
6. 制定成本控制措施

#### 2.4 外协成本分析流程
1. 定期收集外协成本数据
2. 分析成本结构和变动趋势
3. 对比不同供应商成本水平
4. 识别成本优化机会
5. 生成成本分析报告
6. 提出成本改进建议

### 系统集成说明
- **与PMS-005集成**: 获取外协订单成本数据
- **与FMS-009集成**: 向财务系统传递成本数据
- **与PJS-005集成**: 支持项目外协成本归集
- **与预算系统集成**: 获取预算数据和执行控制

### 后置条件
- 外协成本准确核算和记录
- 成本数据及时传递到财务系统
- 预算执行情况实时监控
- 成本分析报告定期生成

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：外协成本管理仪表板
### 页面目标：提供外协成本的全面监控和分析界面

### 信息架构：
- **顶部区域**：包含 成本概览, 预算执行, 预警信息
- **中间区域**：包含 成本分析, 供应商对比, 趋势图表
- **底部区域**：包含 详细数据, 操作记录, 报告下载

### 交互逻辑与状态：

#### **成本概览仪表板**
- **关键指标：**
  - **总外协成本：** 显示当期总外协成本金额
  - **预算执行率：** 显示预算执行百分比
  - **成本节约：** 显示相比预算的节约金额
  - **平均成本：** 显示单位外协成本
- **状态指示：**
  - **正常：** 绿色指示，成本在预算范围内
  - **预警：** 橙色指示，成本接近预算上限
  - **超支：** 红色指示，成本超出预算
  - **节约：** 蓝色指示，成本低于预算

#### **预算执行监控**
- **预算对比：**
  - **预算金额：** 显示各项目的预算金额
  - **实际金额：** 显示实际发生的成本金额
  - **差异金额：** 显示预算与实际的差异
  - **执行进度：** 显示预算执行的进度条
- **预警设置：**
  - **预警阈值：** 设置成本预警的百分比
  - **预警方式：** 设置预警通知方式
  - **预警对象：** 设置预警通知的人员
  - **预警频率：** 设置预警检查的频率

#### **成本分析图表**
- **成本趋势：**
  - **月度趋势：** 显示外协成本的月度变化
  - **季度对比：** 显示不同季度的成本对比
  - **年度趋势：** 显示外协成本的年度变化
  - **预测分析：** 基于历史数据预测未来成本
- **成本结构：**
  - **按供应商：** 显示不同供应商的成本占比
  - **按产品：** 显示不同产品的外协成本
  - **按工序：** 显示不同工序的外协成本
  - **按项目：** 显示不同项目的外协成本

#### **供应商成本对比**
- **成本排名：**
  - **单价排名：** 按外协单价排序供应商
  - **总成本排名：** 按总外协成本排序
  - **成本效率：** 按成本效率指标排序
  - **性价比：** 综合质量和成本的性价比
- **对比分析：**
  - **价格对比：** 同类产品不同供应商价格对比
  - **成本变动：** 供应商成本的历史变动
  - **市场价格：** 与市场平均价格的对比
  - **谈判空间：** 分析价格谈判的空间

## 4. 数据需求 (Data Requirements)

### 核心数据实体

#### **外协成本预算 (OutsourcingCostBudget)**
```sql
CREATE TABLE outsourcing_cost_budget (
    budget_id VARCHAR(32) PRIMARY KEY COMMENT '预算ID',
    budget_year INT NOT NULL COMMENT '预算年度',
    budget_period VARCHAR(20) NOT NULL COMMENT '预算期间',
    cost_center VARCHAR(50) COMMENT '成本中心',
    product_line VARCHAR(100) COMMENT '产品线',
    supplier_id VARCHAR(32) COMMENT '供应商ID',
    budget_amount DECIMAL(15,2) NOT NULL COMMENT '预算金额',
    used_amount DECIMAL(15,2) DEFAULT 0 COMMENT '已用金额',
    remaining_amount DECIMAL(15,2) COMMENT '剩余金额',
    status VARCHAR(20) NOT NULL COMMENT '预算状态',
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### **外协成本明细 (OutsourcingCostDetail)**
```sql
CREATE TABLE outsourcing_cost_detail (
    cost_id VARCHAR(32) PRIMARY KEY COMMENT '成本ID',
    order_id VARCHAR(32) NOT NULL COMMENT '外协订单ID',
    supplier_id VARCHAR(32) NOT NULL COMMENT '供应商ID',
    product_id VARCHAR(32) NOT NULL COMMENT '产品ID',
    cost_type VARCHAR(50) NOT NULL COMMENT '成本类型',
    cost_amount DECIMAL(15,2) NOT NULL COMMENT '成本金额',
    quantity DECIMAL(15,3) NOT NULL COMMENT '数量',
    unit_cost DECIMAL(15,4) NOT NULL COMMENT '单位成本',
    cost_date DATE NOT NULL COMMENT '成本发生日期',
    cost_center VARCHAR(50) COMMENT '成本中心',
    project_id VARCHAR(32) COMMENT '项目ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### **成本分析报告 (CostAnalysisReport)**
```sql
CREATE TABLE cost_analysis_report (
    report_id VARCHAR(32) PRIMARY KEY COMMENT '报告ID',
    report_name VARCHAR(200) NOT NULL COMMENT '报告名称',
    report_type VARCHAR(50) NOT NULL COMMENT '报告类型',
    analysis_period VARCHAR(20) NOT NULL COMMENT '分析期间',
    total_cost DECIMAL(15,2) COMMENT '总成本',
    budget_variance DECIMAL(15,2) COMMENT '预算差异',
    cost_saving DECIMAL(15,2) COMMENT '成本节约',
    report_content TEXT COMMENT '报告内容',
    generated_by VARCHAR(32) NOT NULL COMMENT '生成人',
    generated_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 数据字典

#### **成本类型 (cost_type)**
- MATERIAL_COST: 材料成本
- PROCESSING_COST: 加工成本
- TRANSPORTATION_COST: 运输成本
- QUALITY_COST: 质量成本
- MANAGEMENT_COST: 管理成本

#### **预算状态 (status)**
- DRAFT: 草稿
- APPROVED: 已批准
- ACTIVE: 执行中
- COMPLETED: 已完成
- CANCELLED: 已取消

#### **报告类型 (report_type)**
- MONTHLY: 月度报告
- QUARTERLY: 季度报告
- ANNUAL: 年度报告
- SUPPLIER: 供应商分析
- PRODUCT: 产品分析

### 数据关系
- 外协成本预算 ←→ 成本明细 (1:N)
- 外协订单 ←→ 成本明细 (1:N)
- 供应商 ←→ 成本明细 (1:N)

## 5. 错误处理与边界情况 (Error Handling & Edge Cases)

### 业务规则验证

#### **预算控制验证**
- **预算额度检查：** 验证成本是否超出预算限制
- **权限验证：** 检查用户是否有权限执行成本操作
- **期间验证：** 确保成本记录在正确的会计期间
- **科目验证：** 验证成本科目的正确性和有效性

#### **成本核算验证**
- **数据完整性：** 检查成本数据的完整性和准确性
- **计算逻辑：** 验证成本计算公式和逻辑
- **汇率处理：** 处理外币成本的汇率转换
- **税费处理：** 正确处理相关税费和附加费用

### 异常情况处理

#### **系统异常**
- **数据同步失败：** 与财务系统数据同步失败
  - 错误提示：成本数据同步失败，请联系系统管理员
  - 处理方式：记录失败日志，支持手动重新同步
- **计算错误：** 成本计算过程中出现错误
  - 错误提示：成本计算异常，请检查基础数据
  - 处理方式：提供数据校验和修复功能

#### **业务异常**
- **预算超支：** 成本超出预算限制
  - 错误提示：成本超出预算，需要审批
  - 处理方式：启动超支审批流程
- **成本异常：** 成本数据异常波动
  - 错误提示：成本数据异常，请核实
  - 处理方式：提供成本异常分析工具

### 边界情况

#### **极限成本**
- **零成本：** 免费外协服务的处理
- **负成本：** 退货或补偿导致的负成本
- **巨额成本：** 大额外协成本的特殊处理

#### **特殊情况**
- **汇率波动：** 外币成本的汇率风险
- **价格调整：** 外协价格的临时调整
- **成本分摊：** 共享外协成本的分摊

## 6. 验收标准 (Acceptance Criteria)

### 功能验收标准

#### **成本预算管理**
- [ ] 能够制定和维护外协成本预算
- [ ] 支持预算的分解和分配
- [ ] 预算执行能够实时监控
- [ ] 预算超支能够及时预警

#### **成本核算功能**
- [ ] 能够准确核算外协成本
- [ ] 支持多维度的成本分析
- [ ] 成本数据能够及时更新
- [ ] 成本报告能够自动生成

#### **成本控制功能**
- [ ] 能够设置成本控制规则
- [ ] 支持成本审批流程
- [ ] 成本异常能够及时发现
- [ ] 成本优化建议能够提供

### 性能验收标准

#### **响应时间要求**
- [ ] 成本查询响应时间 ≤ 3秒
- [ ] 成本计算完成时间 ≤ 5秒
- [ ] 报告生成时间 ≤ 10秒
- [ ] 数据同步时间 ≤ 30秒

#### **数据处理能力**
- [ ] 支持100万条成本记录
- [ ] 支持1000个并发用户查询
- [ ] 系统在高负载下保持稳定

### 集成验收标准

#### **系统集成**
- [ ] 与采购管理系统集成正常
- [ ] 与财务管理系统集成正常
- [ ] 与项目管理系统集成正常
- [ ] 数据同步准确及时

#### **用户体验**
- [ ] 界面设计符合财务管理习惯
- [ ] 操作流程简洁高效
- [ ] 成本数据展示清晰直观
- [ ] 帮助文档完整易懂

### 安全验收标准

#### **权限控制**
- [ ] 成本数据访问权限严格控制
- [ ] 预算管理权限分级有效
- [ ] 成本审批权限正确配置
- [ ] 操作日志记录完整

#### **数据安全**
- [ ] 成本数据加密存储
- [ ] 敏感信息脱敏处理
- [ ] 数据备份恢复机制完善
- [ ] 审计跟踪功能完整
