# 功能模块规格说明书：供应商档案管理模块

- **模块ID**: PMS-001
- **所属子系统**: 采购管理子系统(PMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 采购经理, **I want to** 维护完整的供应商信息库, **so that** 进行有效的供应商关系管理。
- **As a** 采购员, **I want to** 快速查找和选择合适的供应商, **so that** 高效完成采购任务。
- **As a** 采购经理, **I want to** 管理供应商的准入和退出流程, **so that** 确保供应商质量和合规性。
- **As a** 采购员, **I want to** 查看供应商的历史交易记录, **so that** 了解合作情况做出更好决策。
- **As a** 采购经理, **I want to** 管理供应商的变体供应能力, **so that** 了解供应商可提供的物料变体规格。
- **As a** 采购员, **I want to** 查询供应商的变体价格和供应能力, **so that** 为变体采购选择最优供应商。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有供应商管理权限
- 组织架构和权限体系已建立
- 供应商分类体系已配置

### 核心流程

#### 2.1 供应商新增流程
1. 采购经理或采购员点击"新增供应商"
2. 填写供应商基本信息（名称、类型、联系方式等）
3. 上传供应商资质文件（营业执照、税务登记证等）
4. 填写财务信息（开户银行、账号、信用等级等）
5. 设置供应商分类和评级
6. 提交审核（如需要）
7. 审核通过后供应商状态变为"正常"

#### 2.2 供应商信息维护流程
1. 在供应商列表中搜索定位目标供应商
2. 点击进入供应商详情页面
3. 编辑需要修改的信息字段
4. 上传或更新相关资质文件
5. 保存修改并记录变更日志
6. 重要信息变更需要审核确认

#### 2.3 供应商评级管理流程
1. 采购经理进入供应商评级管理页面
2. 选择需要评级的供应商
3. 根据评级标准进行评分：
   - 质量能力（产品质量、质量体系）
   - 交付能力（交期准确性、供应稳定性）
   - 服务能力（响应速度、技术支持）
   - 财务状况（资金实力、信用记录）
4. 系统自动计算综合评级（A/B/C类）
5. 保存评级结果并设置评级有效期

#### 2.4 供应商变体供应能力管理流程
1. 采购经理进入供应商变体管理页面
2. 选择需要配置变体供应能力的供应商
3. 添加供应商可供应的基础物料
4. 为每个基础物料配置可供应的变体规格
5. 设置变体供应价格和最小订购量
6. 配置变体供应的交期和质量标准
7. 保存变体供应能力配置并生效

#### 2.5 供应商准入审核流程
1. 新供应商提交准入申请
2. 采购部门进行初步审核
3. 质量部门进行质量体系审核
4. 财务部门进行财务状况审核
5. 采购经理进行综合评估
6. 审核通过后供应商正式准入
7. 建立供应商档案并分配供应商编码

### 后置条件
- 供应商信息完整准确
- 供应商状态正确维护
- 变更记录完整可追溯
- 相关人员收到变更通知

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：供应商档案管理页面
### 页面目标：提供全面的供应商信息管理界面

### 信息架构：
- **顶部区域**：包含 搜索筛选器, 新增供应商按钮, 批量操作工具
- **中间区域**：包含 供应商列表表格, 供应商详情面板, 快捷操作按钮
- **底部区域**：包含 分页控件, 统计信息, 导出功能

### 交互逻辑与状态：

#### **搜索筛选器**
- **供应商名称搜索：** 文本输入框，支持模糊搜索
- **供应商类型筛选：** 下拉多选（原材料/设备/服务等）
- **供应商状态筛选：** 下拉选择（正常/暂停/黑名单）
- **评级筛选：** 下拉选择（A类/B类/C类/未评级）
- **地区筛选：** 级联选择器（省市区）
- **重置按钮：** 清空所有筛选条件

#### **新增供应商按钮**
- **默认状态：** 蓝色背景(#1890FF)，白色文字"新增供应商"
- **悬停状态：** 背景色加深至#096DD9
- **权限不足状态：** 灰色背景，禁用状态
- **交互行为：** 点击打开供应商信息录入对话框

#### **供应商列表表格**
- **表格样式：** 固定表头，斑马纹行，支持排序
- **列定义：**
  - 供应商编码：系统自动生成，不可编辑
  - 供应商名称：链接，点击查看详情
  - 供应商类型：标签显示
  - 联系人：显示主要联系人姓名
  - 联系电话：可点击拨打
  - 评级：彩色标签（A类绿色/B类橙色/C类红色）
  - 状态：状态标签（正常绿色/暂停橙色/黑名单红色）
  - 创建时间：相对时间显示
  - 操作：编辑、查看、禁用按钮

#### **供应商状态标识**
- **正常状态：** 绿色标签(#52C41A)，"正常"
- **暂停合作：** 橙色标签(#FAAD14)，"暂停"
- **黑名单：** 红色标签(#F5222D)，"黑名单"
- **待审核：** 蓝色标签(#1890FF)，"待审核"

#### **供应商详情面板**
- **基本信息卡片：**
  - 供应商名称：大号字体显示
  - 统一社会信用代码：重要信息突出显示
  - 成立时间、注册资本：基础信息
  - 经营范围：文本区域显示
- **联系信息卡片：**
  - 联系地址：完整地址信息
  - 联系人列表：支持多个联系人
  - 联系电话、邮箱：可点击操作
- **财务信息卡片：**
  - 开户银行、账号：敏感信息脱敏显示
  - 信用等级：等级标签显示
  - 付款条件：文本显示

#### **资质文件管理**
- **文件列表：** 显示已上传的资质文件
- **文件类型：** 营业执照、税务登记证、质量认证等
- **文件状态：** 有效/过期/即将过期
- **上传按钮：** 支持拖拽上传和点击上传
- **预览功能：** 点击文件名预览文件内容
- **到期提醒：** 文件即将过期时红色提醒

#### **供应商评级组件**
- **评级显示：** A/B/C类用不同颜色标签显示
- **评级详情：** 显示各维度评分和总分
- **评级历史：** 显示历史评级变化趋势
- **重新评级按钮：** 触发评级流程

#### **批量操作工具栏**
- **全选复选框：** 控制列表项的全选/取消全选
- **批量启用：** 绿色按钮，批量启用选中供应商
- **批量禁用：** 红色边框按钮，批量禁用供应商
- **批量导出：** 蓝色边框按钮，导出选中供应商信息
- **批量评级：** 橙色边框按钮，批量进行供应商评级

### 数据校验规则：

#### **供应商名称**
- **校验规则：** 必填，2-100字符，不能重复
- **错误提示文案：** "供应商名称不能为空" / "供应商名称已存在"

#### **统一社会信用代码**
- **校验规则：** 必填，18位字符，格式校验，唯一性校验
- **错误提示文案：** "请输入正确的统一社会信用代码"

#### **联系电话**
- **校验规则：** 必填，手机号或固话格式
- **错误提示文案：** "请输入正确的联系电话"

#### **银行账号**
- **校验规则：** 数字格式，长度校验
- **错误提示文案：** "请输入正确的银行账号"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **基本信息**:
  - **供应商名称 (supplier_name)**: String, 必填, 2-100字符
  - **供应商类型 (supplier_type)**: Enum, 必填, [原材料/设备/服务/外协]
  - **统一社会信用代码 (credit_code)**: String, 必填, 18字符
  - **联系地址 (address)**: String, 必填, 最大200字符
- **联系信息**:
  - **联系人姓名 (contact_name)**: String, 必填
  - **联系电话 (contact_phone)**: String, 必填
  - **联系邮箱 (contact_email)**: String, 可选
- **财务信息**:
  - **开户银行 (bank_name)**: String, 可选
  - **银行账号 (bank_account)**: String, 可选
  - **信用等级 (credit_level)**: Enum, [AAA/AA/A/BBB/BB/B]

### 展示数据
- **供应商概览**: 供应商数量、类型分布、评级分布
- **供应商列表**: 编码、名称、类型、联系人、评级、状态
- **供应商详情**: 完整的供应商信息和历史记录
- **资质文件**: 文件列表、状态、到期时间
- **变更历史**: 修改时间、修改人、修改内容

### 空状态/零数据
- **无供应商**: 显示"暂无供应商，点击新增开始添加"
- **搜索无结果**: 显示"未找到匹配的供应商，请调整搜索条件"
- **无资质文件**: 显示"暂无资质文件，请上传相关证件"

### API接口
- **创建供应商**: POST /api/suppliers
- **获取供应商列表**: GET /api/suppliers
- **更新供应商信息**: PUT /api/suppliers/{id}
- **上传资质文件**: POST /api/suppliers/{id}/documents
- **供应商评级**: POST /api/suppliers/{id}/rating

## 5. 异常与边界处理 (Error & Edge Cases)

### **供应商信息重复**
- **提示信息**: "供应商名称或统一社会信用代码已存在"
- **用户操作**: 显示重复的供应商信息，提供查看选项

### **资质文件过期**
- **提示信息**: "供应商资质文件即将过期，请及时更新"
- **用户操作**: 高亮显示过期文件，提供上传新文件功能

### **供应商状态异常**
- **提示信息**: "供应商已被列入黑名单，无法进行采购"
- **用户操作**: 显示黑名单原因，提供申诉流程

### **批量操作失败**
- **提示信息**: "部分供应商操作失败，请查看详细结果"
- **用户操作**: 显示操作结果列表，标明成功和失败项

### **权限不足**
- **提示信息**: "您没有权限执行此操作"
- **用户操作**: 隐藏无权限操作按钮，显示权限说明

### **数据同步异常**
- **提示信息**: "供应商信息同步异常，请稍后重试"
- **用户操作**: 提供手动刷新按钮，显示同步状态

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持供应商的创建、编辑、查询、禁用操作
- [ ] 供应商信息包含基本信息、财务信息、资质文件
- [ ] 支持供应商评级（A/B/C类）和分类管理
- [ ] 关键信息（名称、统一社会信用代码）唯一性校验
- [ ] 供应商变更记录完整的审计轨迹
- [ ] 资质文件上传、预览、到期提醒功能正常
- [ ] 支持多维度搜索和筛选功能
- [ ] 批量操作功能正常（启用、禁用、导出、评级）
- [ ] 供应商状态管理正确，状态变更有权限控制
- [ ] 供应商准入审核流程正确执行
- [ ] 界面支持响应式设计，移动端可查看
- [ ] 供应商查询响应时间小于1秒
- [ ] 供应商信息保存响应时间小于3秒
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持键盘导航和无障碍访问
