# 功能模块规格说明书：供应商绩效管理模块

- **模块ID**: PMS-002
- **所属子系统**: 采购管理子系统(PMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 采购经理, **I want to** 评估供应商绩效, **so that** 优化供应商结构和提升采购质量。
- **As a** 采购经理, **I want to** 监控供应商交付及时率, **so that** 识别和解决交付风险。
- **As a** 采购经理, **I want to** 分析供应商价格竞争力, **so that** 制定更好的采购策略。
- **As a** 采购员, **I want to** 查看供应商历史绩效, **so that** 选择最优供应商进行采购。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 供应商档案已建立
- 采购订单数据已存在
- 收货和质检数据已录入
- 绩效评估规则已配置

### 核心流程

#### 2.1 绩效数据自动收集流程
1. 系统定期扫描已完成的采购订单
2. 自动计算交付及时率：
   - 按时交付订单数 / 总订单数 × 100%
3. 自动计算质量合格率：
   - 质检合格订单数 / 总订单数 × 100%
4. 自动计算价格竞争力：
   - 对比同类产品的价格水平
5. 更新供应商绩效数据库

#### 2.2 绩效评估执行流程
1. 采购经理选择评估周期（月度/季度/年度）
2. 选择需要评估的供应商范围
3. 系统自动计算各项绩效指标：
   - 交付绩效（及时率、准确率）
   - 质量绩效（合格率、退货率）
   - 价格绩效（价格竞争力、成本节约）
   - 服务绩效（响应速度、配合度）
4. 生成绩效评估报告
5. 根据评估结果调整供应商等级

#### 2.3 供应商排名分析流程
1. 设置排名维度和权重
2. 系统计算各供应商综合得分
3. 生成供应商排名列表
4. 识别优秀供应商和问题供应商
5. 制定供应商优化建议

#### 2.4 风险预警处理流程
1. 系统监控供应商关键指标
2. 当指标超出预警阈值时触发预警
3. 发送预警通知给相关人员
4. 采购经理查看预警详情
5. 制定风险应对措施
6. 跟踪风险处理结果

### 后置条件
- 供应商绩效数据准确更新
- 绩效评估报告生成完成
- 风险预警及时处理
- 供应商等级调整生效

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：供应商绩效管理页面
### 页面目标：提供全面的供应商绩效分析和管理界面

### 信息架构：
- **顶部区域**：包含 绩效概览仪表板, 时间筛选器, 导出按钮
- **中间区域**：包含 绩效指标卡片, 供应商排名表, 趋势分析图表
- **底部区域**：包含 风险预警面板, 绩效详情表格, 操作按钮组

### 交互逻辑与状态：

#### **绩效概览仪表板**
- **关键指标卡片组：** 4个指标卡片并排显示
  - **平均及时率：** 绿色数字，显示百分比和趋势箭头
  - **平均合格率：** 蓝色数字，显示百分比和对比
  - **活跃供应商数：** 橙色数字，显示总数和新增
  - **风险供应商数：** 红色数字，显示数量和占比
- **环形图表：** 显示供应商等级分布（A/B/C类）

#### **时间筛选器**
- **预设时间：** 标签页形式（本月/本季度/本年度）
- **自定义时间：** 日期范围选择器
- **对比模式：** 开关控件，启用同比/环比对比
- **刷新按钮：** 手动刷新数据

#### **绩效指标卡片**
- **交付绩效卡片：**
  - 及时率：大号百分比显示
  - 趋势图：小型折线图显示变化趋势
  - 对比数据：与上期对比的增减情况
- **质量绩效卡片：**
  - 合格率：大号百分比显示
  - 退货率：小号百分比显示
  - 质量趋势：颜色编码显示改善/恶化
- **价格绩效卡片：**
  - 价格指数：相对数值显示
  - 成本节约：金额和百分比显示
  - 价格趋势：上升/下降趋势指示

#### **供应商排名表格**
- **表格样式：** 排名列表，支持多维度排序
- **排名显示：** 数字排名，前三名特殊标识
- **列定义：**
  - 排名：数字显示，前三名金银铜标识
  - 供应商名称：链接，点击查看详情
  - 综合得分：进度条显示，满分100
  - 及时率：百分比，颜色编码
  - 合格率：百分比，颜色编码
  - 价格指数：数值显示
  - 等级变化：箭头显示升降
  - 操作：查看详情、发送反馈

#### **趋势分析图表**
- **多线图表：** 显示多个供应商的绩效趋势
- **图例控制：** 可选择显示/隐藏特定供应商
- **时间轴：** 支持缩放和平移
- **数据点提示：** 悬停显示具体数值
- **图表类型切换：** 线图/柱图/面积图

#### **风险预警面板**
- **预警级别标识：**
  - 高风险：红色背景(#FFF2F0)，红色图标
  - 中风险：橙色背景(#FFF7E6)，橙色图标
  - 低风险：黄色背景(#FFFBE6)，黄色图标
- **预警内容：** 供应商名称、风险类型、风险描述
- **处理状态：** 未处理/处理中/已处理
- **操作按钮：** 查看详情、标记处理、忽略预警

#### **绩效详情表格**
- **供应商筛选：** 下拉多选，支持搜索
- **指标筛选：** 复选框组，选择显示的指标
- **数据导出：** 支持Excel和PDF格式导出
- **详情展开：** 点击行展开显示详细数据

#### **绩效评估对话框**
- **评估周期选择：** 单选按钮（月度/季度/年度）
- **供应商范围：** 多选列表，支持全选
- **评估维度权重：** 滑块控件设置各维度权重
- **开始评估按钮：** 绿色按钮，触发评估计算

#### **供应商对比分析**
- **对比选择器：** 最多选择5个供应商进行对比
- **雷达图：** 多维度绩效对比
- **数据表格：** 详细数据对比
- **优劣势分析：** 文字描述各供应商特点

### 数据校验规则：

#### **评估周期**
- **校验规则：** 必须选择有效的时间范围
- **错误提示文案：** "请选择评估周期"

#### **供应商选择**
- **校验规则：** 至少选择一个供应商
- **错误提示文案：** "请至少选择一个供应商"

#### **权重设置**
- **校验规则：** 各维度权重总和必须等于100%
- **错误提示文案：** "权重总和必须等于100%"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **评估参数**:
  - **评估周期 (evaluation_period)**: Enum, 必填, [月度/季度/年度]
  - **供应商范围 (supplier_scope)**: Array, 必填, 供应商ID列表
  - **维度权重 (dimension_weights)**: Object, 各维度权重配置
- **预警设置**:
  - **及时率阈值 (delivery_threshold)**: Number, 默认90%
  - **合格率阈值 (quality_threshold)**: Number, 默认95%
  - **价格波动阈值 (price_threshold)**: Number, 默认10%

### 展示数据
- **绩效概览**: 平均指标、供应商分布、趋势对比
- **供应商排名**: 排名、得分、各维度指标
- **绩效趋势**: 时间序列数据、变化趋势
- **风险预警**: 预警级别、风险描述、处理状态
- **对比分析**: 多供应商对比数据、优劣势分析

### 空状态/零数据
- **无绩效数据**: 显示"暂无绩效数据，请先完成采购订单"
- **无风险预警**: 显示"当前无风险预警，供应商表现良好"
- **评估无结果**: 显示"选择的时间范围内无数据"

### API接口
- **获取绩效概览**: GET /api/supplier-performance/overview
- **执行绩效评估**: POST /api/supplier-performance/evaluate
- **获取供应商排名**: GET /api/supplier-performance/ranking
- **获取风险预警**: GET /api/supplier-performance/alerts
- **导出绩效报告**: GET /api/supplier-performance/export

## 5. 异常与边界处理 (Error & Edge Cases)

### **数据不足导致评估失败**
- **提示信息**: "数据不足，无法进行绩效评估，建议选择更长的时间范围"
- **用户操作**: 提供时间范围调整建议，显示数据要求

### **绩效计算异常**
- **提示信息**: "绩效计算出现异常，请检查基础数据完整性"
- **用户操作**: 提供数据检查工具，显示缺失数据项

### **预警阈值设置不合理**
- **提示信息**: "预警阈值设置过于严格，可能产生过多预警"
- **用户操作**: 显示建议阈值范围，提供历史数据参考

### **供应商绩效数据异常**
- **提示信息**: "检测到异常绩效数据，请核实数据准确性"
- **用户操作**: 高亮异常数据，提供数据修正入口

### **报告生成失败**
- **提示信息**: "绩效报告生成失败，请稍后重试"
- **用户操作**: 提供重试按钮，显示生成进度

### **权限不足**
- **提示信息**: "您没有权限查看此供应商的绩效数据"
- **用户操作**: 隐藏无权限数据，显示权限申请流程

## 6. 验收标准 (Acceptance Criteria)

- [ ] 自动统计供应商交付及时率和质量合格率
- [ ] 支持供应商价格对比分析
- [ ] 提供供应商风险预警机制
- [ ] 生成供应商绩效评估报告
- [ ] 支持多维度供应商排名和对比
- [ ] 绩效趋势分析图表显示正确
- [ ] 风险预警及时触发，通知准确发送
- [ ] 绩效数据计算准确，误差小于1%
- [ ] 支持绩效报告的导出功能
- [ ] 绩效评估周期可配置（月度/季度/年度）
- [ ] 界面支持响应式设计，移动端可查看
- [ ] 绩效数据查询响应时间小于2秒
- [ ] 绩效评估计算时间小于30秒
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持键盘导航和无障碍访问
