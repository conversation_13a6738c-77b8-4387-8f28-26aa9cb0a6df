# 功能模块规格说明书：外协质量管理模块

- **模块ID**: PMS-009
- **所属子系统**: 采购管理子系统(PMS)
- **最后更新**: 2025-07-31
- **功能说明**: 管理外协供应商的质量标准、检验要求、质量评估和改进，确保外协产品质量符合要求

## 1. 用户故事 (User Stories)

- **As a** 质量工程师, **I want to** 制定外协供应商的质量标准, **so that** 确保外协产品质量符合公司要求。
- **As a** 采购员, **I want to** 查看供应商的质量评级, **so that** 选择质量可靠的外协供应商。
- **As a** 质检员, **I want to** 执行外协产品的入厂检验, **so that** 及时发现和处理质量问题。
- **As a** 供应商质量工程师, **I want to** 分析外协质量数据, **so that** 持续改进外协供应商质量管理。
- **As a** 外协供应商, **I want to** 了解质量要求和反馈, **so that** 改进生产工艺和质量控制。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 外协供应商已注册并通过资质审核
- 外协产品质量标准已制定
- 质检人员已配置相应权限
- 检验设备和工具已准备就绪

### 核心流程

#### 2.1 外协质量标准制定流程
1. 质量工程师分析外协产品技术要求
2. 制定外协产品质量标准和检验规范
3. 确定关键质量控制点和检验项目
4. 设置质量标准的检验方法和工具
5. 审核并发布外协质量标准
6. 向外协供应商传达质量要求

#### 2.2 外协产品检验流程
1. 外协产品到货后创建检验任务
2. 质检员根据质量标准执行检验
3. 记录检验数据和结果判定
4. 对不合格品进行标识和隔离
5. 生成检验报告并反馈供应商
6. 更新供应商质量档案

#### 2.3 质量问题处理流程
1. 发现质量问题后立即记录
2. 分析问题原因和影响范围
3. 通知供应商并要求整改
4. 跟踪供应商整改措施执行
5. 验证整改效果和预防措施
6. 更新供应商质量评级

#### 2.4 供应商质量评估流程
1. 定期收集供应商质量数据
2. 计算质量指标和评分
3. 进行供应商质量等级评定
4. 生成质量评估报告
5. 与供应商沟通评估结果
6. 制定质量改进计划

### 系统集成说明
- **与PMS-001集成**: 获取供应商基础信息
- **与PMS-007集成**: 关联外协订单信息
- **与QMS系统集成**: 共享质量标准和检验数据
- **与WMS-004集成**: 协调入库质量确认

### 后置条件
- 外协产品质量得到有效控制
- 供应商质量档案准确更新
- 质量问题得到及时处理
- 质量数据完整记录和分析

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：外协质量管理主页面
### 页面目标：提供外协质量管理的统一操作界面

### 信息架构：
- **顶部区域**：包含 质量概览, 快捷操作, 预警信息
- **中间区域**：包含 检验任务, 质量标准, 供应商评级
- **底部区域**：包含 质量分析, 改进计划, 操作记录

### 交互逻辑与状态：

#### **质量概览仪表板**
- **质量指标：**
  - **合格率：** 显示外协产品整体合格率
  - **及时交付率：** 显示按时交付的外协产品比例
  - **供应商数量：** 显示活跃的外协供应商数量
  - **质量问题数：** 显示当前未解决的质量问题
- **趋势图表：**
  - **质量趋势：** 显示近期质量指标变化趋势
  - **供应商排名：** 显示供应商质量排名
  - **问题分布：** 显示质量问题类型分布

#### **检验任务管理**
- **任务列表：**
  - **待检验：** 橙色标识，显示待执行的检验任务
  - **检验中：** 蓝色标识，显示正在执行的检验
  - **已完成：** 绿色标识，显示已完成的检验
  - **已逾期：** 红色标识，显示逾期的检验任务
- **任务操作：**
  - **开始检验：** 启动检验任务执行
  - **暂停检验：** 暂停当前检验任务
  - **完成检验：** 提交检验结果
  - **查看详情：** 查看检验任务详细信息

#### **质量标准管理**
- **标准分类：**
  - **产品标准：** 按产品类型分类的质量标准
  - **工艺标准：** 按工艺流程分类的质量要求
  - **检验标准：** 检验方法和判定标准
  - **包装标准：** 包装和标识要求
- **标准操作：**
  - **新建标准：** 创建新的质量标准
  - **修订标准：** 修订现有质量标准
  - **发布标准：** 发布质量标准给供应商
  - **废止标准：** 废止过期的质量标准

#### **供应商质量评级**
- **评级等级：**
  - **A级：** 绿色标识，"优秀供应商"
  - **B级：** 蓝色标识，"合格供应商"
  - **C级：** 橙色标识，"待改进供应商"
  - **D级：** 红色标识，"不合格供应商"
- **评级指标：**
  - **质量得分：** 基于质量数据计算的综合得分
  - **交付得分：** 基于交付表现的得分
  - **服务得分：** 基于服务质量的得分
  - **综合评级：** 基于各项指标的综合评级

## 4. 数据需求 (Data Requirements)

### 核心数据实体

#### **外协质量标准 (OutsourcingQualityStandard)**
```sql
CREATE TABLE outsourcing_quality_standard (
    standard_id VARCHAR(32) PRIMARY KEY COMMENT '质量标准ID',
    standard_code VARCHAR(50) NOT NULL COMMENT '标准编码',
    standard_name VARCHAR(200) NOT NULL COMMENT '标准名称',
    product_category VARCHAR(100) COMMENT '适用产品类别',
    version VARCHAR(20) NOT NULL COMMENT '版本号',
    status VARCHAR(20) NOT NULL COMMENT '标准状态',
    effective_date DATE NOT NULL COMMENT '生效日期',
    expiry_date DATE COMMENT '失效日期',
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### **外协检验任务 (OutsourcingInspectionTask)**
```sql
CREATE TABLE outsourcing_inspection_task (
    task_id VARCHAR(32) PRIMARY KEY COMMENT '检验任务ID',
    order_id VARCHAR(32) NOT NULL COMMENT '外协订单ID',
    supplier_id VARCHAR(32) NOT NULL COMMENT '供应商ID',
    product_id VARCHAR(32) NOT NULL COMMENT '产品ID',
    batch_no VARCHAR(50) NOT NULL COMMENT '批次号',
    quantity DECIMAL(15,3) NOT NULL COMMENT '检验数量',
    inspector_id VARCHAR(32) COMMENT '检验员ID',
    inspection_date DATE COMMENT '检验日期',
    status VARCHAR(20) NOT NULL COMMENT '任务状态',
    result VARCHAR(20) COMMENT '检验结果',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### **供应商质量评级 (SupplierQualityRating)**
```sql
CREATE TABLE supplier_quality_rating (
    rating_id VARCHAR(32) PRIMARY KEY COMMENT '评级ID',
    supplier_id VARCHAR(32) NOT NULL COMMENT '供应商ID',
    rating_period VARCHAR(20) NOT NULL COMMENT '评级周期',
    quality_score DECIMAL(5,2) COMMENT '质量得分',
    delivery_score DECIMAL(5,2) COMMENT '交付得分',
    service_score DECIMAL(5,2) COMMENT '服务得分',
    total_score DECIMAL(5,2) COMMENT '总得分',
    rating_level VARCHAR(10) COMMENT '评级等级',
    rating_date DATE NOT NULL COMMENT '评级日期',
    rater_id VARCHAR(32) NOT NULL COMMENT '评级人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 数据字典

#### **标准状态 (status)**
- DRAFT: 草稿
- EFFECTIVE: 生效
- EXPIRED: 失效
- CANCELLED: 取消

#### **任务状态 (status)**
- PENDING: 待检验
- IN_PROGRESS: 检验中
- COMPLETED: 已完成
- OVERDUE: 已逾期

#### **检验结果 (result)**
- QUALIFIED: 合格
- UNQUALIFIED: 不合格
- CONDITIONAL: 有条件合格

#### **评级等级 (rating_level)**
- A: 优秀
- B: 合格
- C: 待改进
- D: 不合格

### 数据关系
- 外协质量标准 ←→ 检验任务 (1:N)
- 供应商 ←→ 质量评级 (1:N)
- 外协订单 ←→ 检验任务 (1:N)

## 5. 错误处理与边界情况 (Error Handling & Edge Cases)

### 业务规则验证

#### **质量标准验证**
- **标准完整性：** 检查质量标准的完整性和有效性
- **版本控制：** 确保质量标准版本管理的正确性
- **生效日期：** 验证标准生效和失效日期的合理性
- **适用范围：** 确认标准适用的产品和供应商范围

#### **检验任务验证**
- **任务分配：** 验证检验员的资质和可用性
- **检验时限：** 检查检验任务的时间要求
- **样品数量：** 验证检验样品数量的合理性
- **检验设备：** 确认检验设备的可用性和校准状态

### 异常情况处理

#### **系统异常**
- **数据同步失败：** 与其他系统数据同步失败
  - 错误提示：数据同步异常，请稍后重试
  - 处理方式：记录失败日志，支持手动重新同步
- **检验设备故障：** 检验设备无法正常使用
  - 错误提示：检验设备故障，请联系设备管理员
  - 处理方式：提供备用设备或延期检验

#### **业务异常**
- **供应商资质过期：** 供应商资质证书过期
  - 错误提示：供应商资质已过期，请更新资质信息
  - 处理方式：暂停该供应商的新订单，要求更新资质
- **质量标准冲突：** 多个质量标准存在冲突
  - 错误提示：质量标准存在冲突，请联系质量工程师
  - 处理方式：提供标准优先级设置功能

### 边界情况

#### **极限检验**
- **零缺陷要求：** 某些产品要求零缺陷
- **大批量检验：** 大批量产品的抽样检验
- **紧急检验：** 紧急订单的快速检验

#### **特殊情况**
- **新供应商：** 新供应商的首次质量评估
- **新产品：** 新产品的质量标准制定
- **质量事故：** 严重质量问题的应急处理

## 6. 验收标准 (Acceptance Criteria)

### 功能验收标准

#### **质量标准管理**
- [ ] 能够创建和维护外协质量标准
- [ ] 支持质量标准的版本控制
- [ ] 质量标准能够关联到具体产品和供应商
- [ ] 支持质量标准的发布和废止

#### **检验任务管理**
- [ ] 能够自动创建外协产品检验任务
- [ ] 支持检验任务的分配和执行
- [ ] 检验结果能够准确记录和判定
- [ ] 不合格品能够正确标识和处理

#### **供应商质量评级**
- [ ] 能够基于质量数据自动计算评级
- [ ] 支持多维度的质量评估指标
- [ ] 评级结果能够及时更新和通知
- [ ] 评级历史能够完整追溯

### 性能验收标准

#### **响应时间要求**
- [ ] 质量标准查询响应时间 ≤ 2秒
- [ ] 检验任务创建时间 ≤ 3秒
- [ ] 质量评级计算时间 ≤ 5秒
- [ ] 质量报告生成时间 ≤ 10秒

#### **数据处理能力**
- [ ] 支持1000个并发检验任务
- [ ] 支持10万条质量数据的分析
- [ ] 系统在高负载下保持稳定

### 集成验收标准

#### **系统集成**
- [ ] 与采购管理系统集成正常
- [ ] 与质量管理系统集成正常
- [ ] 与仓储管理系统集成正常
- [ ] 数据同步准确及时

#### **用户体验**
- [ ] 界面设计符合质量管理习惯
- [ ] 操作流程简洁高效
- [ ] 质量数据展示清晰直观
- [ ] 帮助文档完整易懂

### 安全验收标准

#### **权限控制**
- [ ] 质量标准管理权限控制正确
- [ ] 检验任务权限分级有效
- [ ] 质量数据访问权限严格
- [ ] 操作日志记录完整

#### **数据安全**
- [ ] 质量数据加密存储
- [ ] 敏感信息脱敏处理
- [ ] 数据备份恢复机制完善
- [ ] 审计跟踪功能完整
