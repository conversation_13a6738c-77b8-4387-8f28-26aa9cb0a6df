【用户需求说明书】

我是一家软件开发公司的负责人，现在想开发一套细分领域的管理系统，主要包含：
- **ERP系统**：物料管理、产品管理、订单管理、采购管理、财务管理
- **MES系统**：生产计划、工艺管理、质量控制、设备管理
- **WMS系统**：库存管理、出入库管理、包装配送
- **CRM系统**：客户管理、销售管理、服务管理
- **PLM系统**：产品设计、工艺管理、技术文档
- **财务一体化**：可根据生产中心独立核算与合并报表

目标客户是：
- **玻璃深加工企业**
*   **建筑玻璃**：广泛应用于住宅、商业、工业等建筑领域，产品包括普通平板玻璃、钢化玻璃、中空玻璃、夹层玻璃、低辐射玻璃等，主要用于建筑物的门窗、幕墙等。
*   **家具玻璃**：应用于家具制造，如餐桌、茶几、衣柜等家具的玻璃面板，产品包括钢化玻璃、磨砂玻璃、彩色玻璃等，除了满足基本的使用功能外，还注重美观性和装饰性。
*   **装饰玻璃**：用于室内装饰，可增加空间的美感和艺术氛围，产品种类繁多，如喷砂或磨砂玻璃、喷花玻璃、雕刻玻璃、彩绘玻璃、镭射玻璃等。
*   **特种玻璃**：包括一些具有特殊性能和用途的玻璃，如航空航天耐高温玻璃、半导体用高纯石英玻璃、防弹玻璃、防火玻璃、杀菌玻璃、自洁净玻璃等，这些玻璃通常需要采用特殊的加工工艺和技术，以满足特定领域的高端需求。
- **相关企业**
*   **酒店隔断**：【工艺玻璃+铝合金型材+不锈钢型材】
*   **防火窗**：【防火玻璃+铝合金型材】
*   **幕墙**：【钢化玻璃+铝合金/不锈钢型材】

我希望打造一套以上行业通用的管理软件。

目前的痛点是：
1. 企业信息化程度低，管理效率低下：目前使用 EXCEL 管理，数据容易错误、遗漏
2. 建筑玻璃下单需求量大，规格复杂：单个订单可包含数百种不同规格产品，规格录入复杂度高，尺寸(宽×高)、玻璃类型、厚度、工艺要求、特殊加工要求多等
3. 酒店隔断、防火窗、幕墙等需要采用项目制管理，各有不同的管理流程

### 核心业务线分析
1. **玻璃制品制造**（复杂规格定制订单）
   - 产品类型：建筑玻璃、家具玻璃、装饰玻璃、特种玻璃等
   - 核心挑战：单个订单可包含数百种不同规格产品，规格录入复杂度高
   - 规格要素：尺寸(宽×高)、玻璃类型、厚度、工艺要求、特殊加工
   - 外协管理：部分工序可选择外协

2. **防火窗**（项目制管理）
   - 项目层级：项目→楼栋→楼层→房间→防火窗产品
   - 业务流程：设计→分构件生产→分阶段交付→现场安装
   - 生产构件：窗框（铝型材）、窗扇(含铝型材+玻璃)、固定玻璃
   - 外协管理：部分工序和玻璃生产可选择内部生产或外协
   - 交付模式：三阶段交付(窗框→固定玻璃→窗扇)

3. **酒店隔断**（大型项目管理）
   - 项目层级：酒店→楼栋→楼层→房间→户型→淋浴房产品
   - 设计要求：按户型和产品进行标准化设计
   - 生产确认：按房间和产品进行现场复尺
   - 材料来源：玻璃制品和不锈钢制品可选内部生产或外协
   - 服务范围：设计、生产、安装、维护、售后全流程

## 设计要求
1. **聚焦行业特色**：重点分析玻璃深加工行业的特殊需求和解决方案
2. **突出企业特点**：防火窗、酒店隔断等企业有的自建玻璃生产线，有的外协玻璃
3. **用户体验优先**：从实际操作角度设计友好的交互界面和业务流程
4. **逻辑完整性**：确保业务逻辑闭环，数据流转合理
5. **问题识别**：主动识别需求描述中的潜在问题或不明确之处
6. **针对性提问**：当信息不足时，提出具体的业务场景问题以完善需求

## 子系统
*   [ ] **基础管理子系统** - 用户权限、组织架构、基础数据字典、系统配置
*   [ ] **CRM子系统** - 客户档案、销售机会、客户跟进、售后服务管理
*   [ ] **销售管理子系统** - 专注玻璃产品订单管理，包含复杂规格处理、批量订单、价格计算
*   [ ] **项目制管理子系统** - 防火窗、酒店隔断、幕墙工程等大型项目的全生命周期管理
*   [ ] **采购管理子系统** - MRP需求计算、采购计划、采购申请、采购订单、供应商管理、外协管理
*   [ ] **工艺管理子系统** - 产品结构设计、工艺路线、工艺BOM、技术文档管理
*   [ ] **生产管理子系统** - 符合工业4.0标准的智能制造，包含切割优化、生产排程、质量控制
*   [ ] **人事管理子系统** - 重点突出玻璃深加工行业的计件工资计算和绩效管理
*   [ ] **仓储管理子系统** - 智能仓储管理，支持传统玻璃铁架和智能玻璃架的存储管理
*   [ ] **质量管理子系统** - 智能质检，支持传统玻璃铁架和智能玻璃架的质检管理
*   [ ] **财务管理子系统** - 业财一体化，为其他子系统提供应收应付和收/付款、成本核算、财务分析能力
*   [ ] **数据中心** - 数据集成、报表分析、决策支持、数据可视化


## 输出要求：
1. 明确定义各子系统的功能边界，避免功能重叠和遗漏
2. 子系统间的数据流转和业务协同关系：定义数据流转路径，确保订单、项目、采购、生产等业务流程的顺畅执行
3. 突出玻璃深加工行业的特殊业务需求和技术要求：如定制订单的复杂规格处理、项目制管理的复杂业务流程、采购管理的复杂订单处理等
