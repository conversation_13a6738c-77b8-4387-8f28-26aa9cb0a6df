请开始：{ 子系统名称  }子系统的交互设计与功能规格说明书制作。

具体要求：
1. 首先处理**{ 子系统名称  }系统**，请读取并分析 `{ 子系统PRD文件路径 }` 文件内容
2. 按照既定流程执行：
   - 进行思维链分析，识别核心用户角色、用户目标、关键场景和主干用户流程
   - 将宏观需求拆解为具体的、正交的、可独立设计的功能模块
   - 为每个功能模块创建独立的Markdown规格文档，严格遵循模板格式（包含用户故事、业务规则、页面元素、数据规格、异常处理、验收标准等6个部分）
3. 在 `{ 子系统PRD文件路径 }` 同一目录下创建相应的功能模块规格文档
4. 完成后更新 `TODOLIST.md` 中的任务状态，并报告完成情况
5. 所有设计必须符合已建立的 `@/docs/prd_v2.0/Frontend_Design_Guidelines.md` 全局设计规范
6. 引用 `@/docs/prd_v2.0/_Business_Rules.md` 和 `@/docs/prd_v2.0/_Glossary.md` 中的相关规则和术语定义

7. 输出要求：
  **功能模块拆解**: 将宏观需求拆解为一系列具体的、正交的、可独立设计的【功能模块】。
  **生成规格文档**: 为每一个拆解出的【功能模块】，在对应的子系统目录下，创建一个独立的Markdown文档。该文档必须严格遵循以下模板：

    ```markdown
    # 功能模块规格说明书：【功能模块名称】

    - **模块ID**: [子系统缩写-模块编号，如: UMS-001]
    - **所属子系统**: [子系统名称]
    - **最后更新**: [YYYY-MM-DD]

    ## 1. 用户故事 (User Stories)
    - **As a** [用户角色], **I want to** [执行某个动作], **so that** [我能获得某种价值].
    - (可列出多个相关的用户故事)

    ## 2. 业务规则与流程 (Business Logic & Flow)
    - **前置条件**: 用户进入此模块前必须满足的状态或权限。
    - **核心流程**: 以步骤列表形式，详细描述从开始到完成的每一步操作，说明系统应如何响应。
    - **后置条件**: 成功完成流程后，系统和用户状态的变化。

    ## 3. 页面元素与交互说明 (Page Elements & Interactions)
    - **页面/组件名称：** [例如：用户登录页]
    - **页面目标：** [此页面的核心目的是什么]
    - **信息架构：**
      - **[区域一：如导航栏]**：包含 [元素A], [元素B]...
      - **[区域二：如内容区]**：包含 [字段1], [字段2], [图片], [列表]...
    - **交互逻辑与状态：**
      - **[元素名称，如“获取验证码”按钮]**
        - **默认状态：** [样式描述，如“蓝色背景，白色文字”]
        - **交互行为：** [点击后，向后端API发送请求，并进入“禁用”状态]
        - **禁用状态：** [样式描述，如“灰色背景，灰色文字”，显示“60s后重试”倒计时，期间不可点击]
        - **其他状态：** [如Hover状态、加载中状态等]
    - **数据校验规则：**
      - **[字段名称，如“手机号输入框”]**
        - **校验规则：** [必须为11位数字，以1开头]
        - **错误提示文案：** [“请输入正确的手机号码”]

    ## 4. 数据规格 (Data Requirements)
    - **输入数据**: 用户需要输入或选择的数据项及其格式、校验规则。
    - **展示数据**: 页面需要从后端获取并展示的数据字段。
    - **空状态/零数据**: 当列表为空或无数据时，页面的显示方式。

    ## 5. 异常与边界处理 (Error & Edge Cases)
    - **[场景一：如网络请求失败]**: [具体的提示信息和用户可进行的操作]。
    - **[场景二：如输入格式错误]**: [具体的、友好的错误提示]。
    - **[场景三：如权限不足]**: [系统的处理方式]。

    ## 6. 验收标准 (Acceptance Criteria)
    - [ ] 条件一：用户可以成功完成...
    - [ ] 条件二：当...发生时，系统正确显示...
    - [ ] 条件三：所有页面元素符合全局设计规范。
    ```

请开始执行设计工作。在工作过程中，你可以使用 sequentialthinking 和 serena 等工具来帮助你完成任务，并保持更新 serena 记忆，确保其与项目结构的一致。