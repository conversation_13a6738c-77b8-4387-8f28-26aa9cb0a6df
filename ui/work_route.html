<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工艺流程管理 - 玻璃深加工ERP系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');
        
        :root {
            --primary: #1890ff;
            --success: #52c41a;
            --warning: #faad14;
            --error: #f5222d;
            --title-gray: #262626;
            --text-gray: #595959;
            --border-gray: #d9d9d9;
            --bg-gray: #f5f5f5;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
        }
        
        .font-chinese {
            font-family: 'Noto Sans SC', sans-serif;
        }
        
        .text-primary {
            color: var(--primary);
        }
        
        .bg-primary {
            background-color: var(--primary);
        }
        
        .border-primary {
            border-color: var(--primary);
        }
        
        .text-success {
            color: var(--success);
        }
        
        .bg-success {
            background-color: var(--success);
        }
        
        .text-warning {
            color: var(--warning);
        }
        
        .bg-warning {
            background-color: var(--warning);
        }
        
        .text-error {
            color: var(--error);
        }
        
        .bg-error {
            background-color: var(--error);
        }
        
        .text-title-gray {
            color: var(--title-gray);
        }
        
        .text-text-gray {
            color: var(--text-gray);
        }
        
        .border-border-gray {
            border-color: var(--border-gray);
        }
        
        .bg-bg-gray {
            background-color: var(--bg-gray);
        }
        
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        
        /* 工序节点样式 */
        .process-node {
            position: absolute;
            width: 160px;
            height: 80px;
            border-radius: 8px;
            background: white;
            border: 2px solid #d9d9d9;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            cursor: move;
            user-select: none;
            transition: all 0.2s;
        }
        
        .process-node:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .process-node.selected {
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.2);
        }
        
        .process-node.start {
            border-radius: 50%;
            width: 80px;
            height: 80px;
            background-color: var(--success);
            color: white;
            border: none;
        }
        
        .process-node.end {
            border-radius: 50%;
            width: 80px;
            height: 80px;
            background-color: var(--error);
            color: white;
            border: none;
        }
        
        .process-node.parallel {
            transform: rotate(45deg);
            width: 100px;
            height: 100px;
            border-color: var(--warning);
        }
        
        .process-node.parallel > div {
            transform: rotate(-45deg);
        }
        
        /* 连接线样式 */
        .connection-line {
            position: absolute;
            background-color: #d9d9d9;
            transform-origin: left center;
            z-index: 1;
        }
        
        .connection-line.selected {
            background-color: var(--primary);
        }
        
        /* 工艺路线画布样式 */
        .process-canvas {
            position: relative;
            background-image: 
                linear-gradient(rgba(0, 0, 0, 0.05) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
            background-size: 20px 20px;
            overflow: auto;
        }
        
        /* 工序模板样式 */
        .process-template {
            cursor: grab;
            transition: all 0.2s;
        }
        
        .process-template:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .process-template:active {
            cursor: grabbing;
        }
        
        /* 参数化配置样式 */
        .param-config-item {
            border-left: 3px solid transparent;
            transition: all 0.2s;
        }
        
        .param-config-item:hover {
            border-left-color: var(--primary);
            background-color: rgba(24, 144, 255, 0.05);
        }
        
        /* 工艺路线对比样式 */
        .route-comparison {
            display: flex;
            gap: 20px;
            overflow-x: auto;
        }
        
        .route-column {
            min-width: 300px;
            flex: 1;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .process-node {
                width: 120px;
                height: 60px;
            }
            
            .process-node.start, .process-node.end {
                width: 60px;
                height: 60px;
            }
            
            .process-node.parallel {
                width: 80px;
                height: 80px;
            }
        }
    </style>
</head>
<body class="bg-bg-gray font-chinese" x-data="processRouteApp()">
    <!-- 顶部导航栏 -->
    <nav class="bg-white shadow-sm border-b border-border-gray">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="ri-dashboard-3-line text-2xl text-primary mr-2"></i>
                        <span class="text-xl font-semibold text-title-gray">玻璃深加工ERP系统</span>
                    </div>
                    <div class="hidden md:ml-10 md:flex md:space-x-8">
                        <a href="#" class="text-primary border-b-2 border-primary px-1 pt-1 text-sm font-medium">工艺管理</a>
                        <a href="#" class="text-text-gray hover:text-primary border-transparent hover:border-gray-300 border-b-2 px-1 pt-1 text-sm font-medium">生产管理</a>
                        <a href="#" class="text-text-gray hover:text-primary border-transparent hover:border-gray-300 border-b-2 px-1 pt-1 text-sm font-medium">质量管理</a>
                        <a href="#" class="text-text-gray hover:text-primary border-transparent hover:border-gray-300 border-b-2 px-1 pt-1 text-sm font-medium">设备管理</a>
                    </div>
                </div>
                <div class="flex items-center">
                    <button class="p-1 rounded-full text-text-gray hover:text-primary focus:outline-none">
                        <i class="ri-notification-3-line text-xl"></i>
                    </button>
                    <div class="ml-3 relative">
                        <div>
                            <button class="flex text-sm rounded-full focus:outline-none" @click="userMenuOpen = !userMenuOpen">
                                <img class="h-8 w-8 rounded-full" src="https://picsum.photos/seed/user123/40/40.jpg" alt="用户头像">
                            </button>
                        </div>
                        <div x-show="userMenuOpen" @click.away="userMenuOpen = false" 
                             x-transition:enter="transition ease-out duration-100"
                             x-transition:enter-start="transform opacity-0 scale-95"
                             x-transition:enter-end="transform opacity-100 scale-100"
                             x-transition:leave="transition ease-in duration-75"
                             x-transition:leave-start="transform opacity-100 scale-100"
                             x-transition:leave-end="transform opacity-0 scale-95"
                             class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
                            <div class="py-1">
                                <a href="#" class="block px-4 py-2 text-sm text-text-gray hover:bg-bg-gray">个人中心</a>
                                <a href="#" class="block px-4 py-2 text-sm text-text-gray hover:bg-bg-gray">系统设置</a>
                                <a href="#" class="block px-4 py-2 text-sm text-text-gray hover:bg-bg-gray">退出登录</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <!-- 页面标题和操作按钮 -->
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <div>
                <h1 class="text-2xl font-bold text-title-gray">工艺流程管理</h1>
                <p class="mt-1 text-sm text-text-gray">PDM-005 统一管理工序标准、工艺路线设计和外协工序，提供端到端的工艺流程解决方案</p>
            </div>
            <div class="mt-4 md:mt-0 flex space-x-3">
                <button @click="showImportModal = true" class="inline-flex items-center px-4 py-2 border border-border-gray rounded-md shadow-sm text-sm font-medium text-text-gray bg-white hover:bg-bg-gray">
                    <i class="ri-upload-2-line mr-2"></i>
                    导入
                </button>
                <button @click="exportRoutes()" class="inline-flex items-center px-4 py-2 border border-border-gray rounded-md shadow-sm text-sm font-medium text-text-gray bg-white hover:bg-bg-gray">
                    <i class="ri-download-2-line mr-2"></i>
                    导出
                </button>
                <button @click="createNewRoute()" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-blue-700">
                    <i class="ri-add-line mr-2"></i>
                    新建工艺路线
                </button>
            </div>
        </div>

        <!-- 模块信息说明 -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i class="ri-information-line text-blue-400 text-xl"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">PDM-005 工艺流程管理模块 (v2.1 整合版)</h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <p>本模块整合了原PDM-005工序管理、PDM-006工艺路线设计、PDM-007外协工序管理，提供端到端的工艺流程管理解决方案。</p>
                        <div class="mt-2 flex flex-wrap gap-4 text-xs">
                            <span class="inline-flex items-center px-2 py-1 rounded-full bg-blue-100 text-blue-800">
                                <i class="ri-settings-3-line mr-1"></i>基础层：工序标准化
                            </span>
                            <span class="inline-flex items-center px-2 py-1 rounded-full bg-blue-100 text-blue-800">
                                <i class="ri-flow-chart mr-1"></i>设计层：工艺路线设计
                            </span>
                            <span class="inline-flex items-center px-2 py-1 rounded-full bg-blue-100 text-blue-800">
                                <i class="ri-building-line mr-1"></i>执行层：外协工序管理
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 三层架构导航标签 -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="border-b border-border-gray">
                <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                    <button @click="currentView = 'list'"
                            :class="currentView === 'list' ? 'border-primary text-primary' : 'border-transparent text-text-gray hover:text-title-gray hover:border-gray-300'"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                        <i class="ri-list-check-2 mr-2"></i>
                        工艺路线列表
                    </button>
                    <button @click="currentView = 'operations'"
                            :class="currentView === 'operations' ? 'border-primary text-primary' : 'border-transparent text-text-gray hover:text-title-gray hover:border-gray-300'"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                        <i class="ri-settings-3-line mr-2"></i>
                        基础层：工序标准管理
                    </button>
                    <button @click="currentView = 'designer'"
                            :class="currentView === 'designer' ? 'border-primary text-primary' : 'border-transparent text-text-gray hover:text-title-gray hover:border-gray-300'"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                        <i class="ri-flow-chart mr-2"></i>
                        设计层：工艺路线设计
                    </button>
                    <button @click="currentView = 'outsourcing'"
                            :class="currentView === 'outsourcing' ? 'border-primary text-primary' : 'border-transparent text-text-gray hover:text-title-gray hover:border-gray-300'"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                        <i class="ri-building-line mr-2"></i>
                        执行层：外协工序管理
                    </button>
                </nav>
            </div>
        </div>

        <!-- 工艺路线列表视图 -->
        <div x-show="currentView === 'list'" class="bg-white shadow rounded-lg">
            <!-- 搜索和筛选区域 -->
            <div class="p-4 border-b border-border-gray">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div class="md:col-span-2">
                        <div class="relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="ri-search-line text-text-gray"></i>
                            </div>
                            <input type="text" x-model="searchQuery" @input="filterRoutes()" class="focus:ring-primary focus:border-primary block w-full pl-10 sm:text-sm border-border-gray rounded-md" placeholder="搜索工艺路线名称、编码...">
                        </div>
                    </div>
                    <div>
                        <select x-model="filterType" @change="filterRoutes()" class="block w-full pl-3 pr-10 py-2 text-base border-border-gray focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                            <option value="">全部类型</option>
                            <option value="standard">标准路线</option>
                            <option value="parametric">参数化路线</option>
                            <option value="variant">变体路线</option>
                        </select>
                    </div>
                    <div>
                        <select x-model="filterStatus" @change="filterRoutes()" class="block w-full pl-3 pr-10 py-2 text-base border-border-gray focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                            <option value="">全部状态</option>
                            <option value="draft">草稿</option>
                            <option value="active">生效</option>
                            <option value="inactive">停用</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 工艺路线列表 -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-border-gray">
                    <thead class="bg-bg-gray">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-gray uppercase tracking-wider">
                                工艺路线信息
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-gray uppercase tracking-wider">
                                类型
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-gray uppercase tracking-wider">
                                适用物料
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-gray uppercase tracking-wider">
                                工序数量
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-gray uppercase tracking-wider">
                                状态
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-gray uppercase tracking-wider">
                                更新时间
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-text-gray uppercase tracking-wider">
                                操作
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-border-gray">
                        <template x-for="route in filteredRoutes" :key="route.id">
                            <tr class="hover:bg-bg-gray">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-md bg-primary bg-opacity-10">
                                            <i class="ri-flow-chart text-primary text-xl"></i>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-title-gray" x-text="route.name"></div>
                                            <div class="text-sm text-text-gray" x-text="route.code"></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                                          :class="{
                                              'bg-blue-100 text-blue-800': route.type === 'standard',
                                              'bg-purple-100 text-purple-800': route.type === 'parametric',
                                              'bg-green-100 text-green-800': route.type === 'variant'
                                          }"
                                          x-text="getTypeLabel(route.type)">
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-gray" x-text="route.material"></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-gray" x-text="route.operationCount"></td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                                          :class="{
                                              'bg-gray-100 text-gray-800': route.status === 'draft',
                                              'bg-green-100 text-green-800': route.status === 'active',
                                              'bg-red-100 text-red-800': route.status === 'inactive'
                                          }"
                                          x-text="getStatusLabel(route.status)">
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-gray" x-text="route.updatedAt"></td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <button @click="editRoute(route)" class="text-primary hover:text-blue-700 mr-3">编辑</button>
                                    <button @click="duplicateRoute(route)" class="text-text-gray hover:text-title-gray mr-3">复制</button>
                                    <button @click="deleteRoute(route)" class="text-error hover:text-red-700">删除</button>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>

            <!-- 分页控件 -->
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-border-gray sm:px-6">
                <div class="flex-1 flex justify-between sm:hidden">
                    <button @click="prevPage()" :disabled="currentPage === 1" class="relative inline-flex items-center px-4 py-2 border border-border-gray text-sm font-medium rounded-md text-text-gray bg-white hover:bg-bg-gray">
                        上一页
                    </button>
                    <button @click="nextPage()" :disabled="currentPage >= totalPages" class="ml-3 relative inline-flex items-center px-4 py-2 border border-border-gray text-sm font-medium rounded-md text-text-gray bg-white hover:bg-bg-gray">
                        下一页
                    </button>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-text-gray">
                            显示第 <span class="font-medium" x-text="(currentPage - 1) * pageSize + 1"></span> 至 
                            <span class="font-medium" x-text="Math.min(currentPage * pageSize, totalItems)"></span> 项，
                            共 <span class="font-medium" x-text="totalItems"></span> 项结果
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <button @click="prevPage()" :disabled="currentPage === 1" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-border-gray bg-white text-sm font-medium text-text-gray hover:bg-bg-gray">
                                <i class="ri-arrow-left-s-line"></i>
                            </button>
                            <template x-for="page in pageNumbers" :key="page">
                                <button @click="goToPage(page)" 
                                        :class="{'bg-primary border-primary text-white': page === currentPage, 'bg-white border-border-gray text-text-gray hover:bg-bg-gray': page !== currentPage}"
                                        class="relative inline-flex items-center px-4 py-2 border text-sm font-medium"
                                        x-text="page">
                                </button>
                            </template>
                            <button @click="nextPage()" :disabled="currentPage >= totalPages" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-border-gray bg-white text-sm font-medium text-text-gray hover:bg-bg-gray">
                                <i class="ri-arrow-right-s-line"></i>
                            </button>
                        </nav>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设计层：工艺路线设计视图 -->
        <div x-show="currentView === 'designer'" class="bg-white shadow rounded-lg overflow-hidden">
            <!-- 工艺路线设计器工具栏 -->
            <div class="bg-bg-gray px-4 py-3 border-b border-border-gray flex flex-wrap items-center justify-between">
                <div class="flex items-center space-x-2 mb-2 md:mb-0">
                    <div class="flex items-center">
                        <i class="ri-flow-chart text-primary mr-2"></i>
                        <span class="text-lg font-medium text-title-gray">设计层：工艺路线设计</span>
                    </div>
                    <div class="h-6 w-px bg-border-gray mx-2"></div>
                    <span class="text-sm text-text-gray">可视化设计参数化工艺路线，支持多路线选择和智能配置</span>
                    <div class="h-6 w-px bg-border-gray mx-2"></div>
                    <span class="text-sm font-medium text-title-gray" x-text="currentRoute ? currentRoute.name : '新建工艺路线'"></span>
                    <span class="text-xs text-text-gray ml-2" x-text="currentRoute ? `(${currentRoute.code})` : ''"></span>
                </div>
                <div class="flex items-center space-x-2">
                    <button @click="undo()" class="p-1.5 rounded text-text-gray hover:text-primary hover:bg-white" title="撤销">
                        <i class="ri-arrow-go-back-line text-lg"></i>
                    </button>
                    <button @click="redo()" class="p-1.5 rounded text-text-gray hover:text-primary hover:bg-white" title="重做">
                        <i class="ri-arrow-go-forward-line text-lg"></i>
                    </button>
                    <div class="h-6 w-px bg-border-gray mx-1"></div>
                    <button @click="zoomIn()" class="p-1.5 rounded text-text-gray hover:text-primary hover:bg-white" title="放大">
                        <i class="ri-zoom-in-line text-lg"></i>
                    </button>
                    <button @click="zoomOut()" class="p-1.5 rounded text-text-gray hover:text-primary hover:bg-white" title="缩小">
                        <i class="ri-zoom-out-line text-lg"></i>
                    </button>
                    <button @click="resetZoom()" class="p-1.5 rounded text-text-gray hover:text-primary hover:bg-white" title="重置缩放">
                        <i class="ri-fullscreen-line text-lg"></i>
                    </button>
                    <div class="h-6 w-px bg-border-gray mx-1"></div>
                    <button @click="validateRoute()" class="inline-flex items-center px-3 py-1.5 border border-border-gray rounded-md shadow-sm text-sm font-medium text-text-gray bg-white hover:bg-bg-gray">
                        <i class="ri-check-line mr-1"></i>
                        验证
                    </button>
                    <button @click="saveRoute()" class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-blue-700">
                        <i class="ri-save-line mr-1"></i>
                        保存
                    </button>
                </div>
            </div>

            <!-- 设计器主体区域 -->
            <div class="flex flex-col md:flex-row h-[calc(100vh-220px)]">
                <!-- 左侧工具栏 -->
                <div class="w-full md:w-64 bg-bg-gray border-r border-border-gray flex flex-col">
                    <!-- 工序模板库 -->
                    <div class="p-3 border-b border-border-gray">
                        <h3 class="text-sm font-medium text-title-gray mb-2">工序模板库</h3>
                        <div class="space-y-2 max-h-48 overflow-y-auto">
                            <template x-for="template in operationTemplates" :key="template.id">
                                <div class="process-template bg-white p-2 rounded border border-border-gray cursor-grab"
                                     draggable="true"
                                     @dragstart="dragStart($event, template)"
                                     @dragend="dragEnd($event)">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-8 w-8 flex items-center justify-center rounded-md bg-primary bg-opacity-10">
                                            <i class="ri-tools-line text-primary"></i>
                                        </div>
                                        <div class="ml-2">
                                            <div class="text-xs font-medium text-title-gray" x-text="template.name"></div>
                                            <div class="text-xs text-text-gray" x-text="template.type"></div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>

                    <!-- 工艺参数库 -->
                    <div class="p-3 border-b border-border-gray">
                        <h3 class="text-sm font-medium text-title-gray mb-2">工艺参数库</h3>
                        <div class="space-y-1 max-h-32 overflow-y-auto">
                            <template x-for="param in processParameters" :key="param.id">
                                <div class="text-xs p-1.5 rounded hover:bg-white cursor-pointer" @click="addParameter(param)">
                                    <span class="text-title-gray" x-text="param.name"></span>
                                    <span class="text-text-gray ml-1" x-text="`(${param.unit})`"></span>
                                </div>
                            </template>
                        </div>
                    </div>

                    <!-- 工装夹具库 -->
                    <div class="p-3 border-b border-border-gray">
                        <h3 class="text-sm font-medium text-title-gray mb-2">工装夹具库</h3>
                        <div class="space-y-1 max-h-32 overflow-y-auto">
                            <template x-for="tool in toolingLibrary" :key="tool.id">
                                <div class="text-xs p-1.5 rounded hover:bg-white cursor-pointer" @click="addTooling(tool)">
                                    <span class="text-title-gray" x-text="tool.name"></span>
                                </div>
                            </template>
                        </div>
                    </div>

                    <!-- 工作中心列表 -->
                    <div class="p-3 flex-grow">
                        <h3 class="text-sm font-medium text-title-gray mb-2">工作中心</h3>
                        <div class="space-y-1 max-h-32 overflow-y-auto">
                            <template x-for="center in workCenters" :key="center.id">
                                <div class="text-xs p-1.5 rounded hover:bg-white cursor-pointer" @click="selectWorkCenter(center)">
                                    <span class="text-title-gray" x-text="center.name"></span>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>

                <!-- 中间画布区域 -->
                <div class="flex-grow relative overflow-hidden">
                    <div class="process-canvas w-full h-full"
                         @dragover.prevent
                         @drop="dropOnCanvas($event)"
                         @click="clearSelection()">
                        <!-- 工艺路线画布内容将通过JavaScript动态生成 -->
                        <template x-for="node in processNodes" :key="node.id">
                            <div class="process-node"
                                 :class="{
                                     'selected': selectedNode === node.id,
                                     'start': node.type === 'start',
                                     'end': node.type === 'end',
                                     'parallel': node.type === 'parallel'
                                 }"
                                 :style="`left: ${node.x}px; top: ${node.y}px;`"
                                 @click.stop="selectNode(node.id)"
                                 @mousedown="startDrag($event, node.id)">
                                <div x-show="node.type !== 'start' && node.type !== 'end'">
                                    <div class="text-xs font-medium text-title-gray" x-text="node.operationNumber"></div>
                                    <div class="text-xs text-text-gray" x-text="node.name"></div>
                                </div>
                                <div x-show="node.type === 'start'">
                                    <i class="ri-play-fill text-2xl"></i>
                                </div>
                                <div x-show="node.type === 'end'">
                                    <i class="ri-stop-fill text-2xl"></i>
                                </div>
                                <div x-show="node.type === 'parallel'">
                                    <div class="text-xs font-medium text-title-gray">并行</div>
                                </div>
                            </div>
                        </template>

                        <!-- 连接线 -->
                        <template x-for="connection in connections" :key="connection.id">
                            <div class="connection-line"
                                 :class="{'selected': selectedConnection === connection.id}"
                                 :style="`left: ${connection.x1}px; top: ${connection.y1}px; width: ${connection.length}px; height: 2px; transform: rotate(${connection.angle}deg);`"
                                 @click.stop="selectConnection(connection.id)">
                            </div>
                        </template>
                    </div>

                    <!-- 缩放控制 -->
                    <div class="absolute bottom-4 right-4 bg-white rounded-md shadow-md p-1 flex items-center">
                        <button @click="zoomOut()" class="p-1 rounded text-text-gray hover:text-primary">
                            <i class="ri-zoom-out-line"></i>
                        </button>
                        <span class="px-2 text-xs text-text-gray" x-text="`${Math.round(zoomLevel * 100)}%`"></span>
                        <button @click="zoomIn()" class="p-1 rounded text-text-gray hover:text-primary">
                            <i class="ri-zoom-in-line"></i>
                        </button>
                    </div>
                </div>

                <!-- 右侧属性面板 -->
                <div class="w-full md:w-80 bg-bg-gray border-l border-border-gray flex flex-col">
                    <!-- 工序属性面板 -->
                    <div x-show="selectedNodeData" class="p-4 border-b border-border-gray">
                        <h3 class="text-sm font-medium text-title-gray mb-3">工序属性</h3>
                        <div class="space-y-3">
                            <div>
                                <label class="block text-xs font-medium text-text-gray mb-1">工序号</label>
                                <input type="text" x-model="selectedNodeData.operationNumber" class="block w-full rounded-md border-border-gray shadow-sm focus:border-primary focus:ring-primary sm:text-sm">
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-text-gray mb-1">工序名称</label>
                                <input type="text" x-model="selectedNodeData.name" class="block w-full rounded-md border-border-gray shadow-sm focus:border-primary focus:ring-primary sm:text-sm">
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-text-gray mb-1">工作中心</label>
                                <select x-model="selectedNodeData.workCenterId" class="block w-full rounded-md border-border-gray shadow-sm focus:border-primary focus:ring-primary sm:text-sm">
                                    <template x-for="center in workCenters" :key="center.id">
                                        <option :value="center.id" x-text="center.name"></option>
                                    </template>
                                </select>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-text-gray mb-1">工时配置</label>
                                <div class="grid grid-cols-2 gap-2">
                                    <div>
                                        <input type="number" x-model="selectedNodeData.setupTime" placeholder="准备工时" class="block w-full rounded-md border-border-gray shadow-sm focus:border-primary focus:ring-primary sm:text-sm">
                                    </div>
                                    <div>
                                        <input type="number" x-model="selectedNodeData.cycleTime" placeholder="单件工时" class="block w-full rounded-md border-border-gray shadow-sm focus:border-primary focus:ring-primary sm:text-sm">
                                    </div>
                                </div>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-text-gray mb-1">外协标识</label>
                                <div class="flex items-center">
                                    <input type="checkbox" x-model="selectedNodeData.isOutsourced" class="h-4 w-4 text-primary focus:ring-primary border-border-gray rounded">
                                    <span class="ml-2 text-sm text-text-gray">外协工序</span>
                                </div>
                            </div>
                            <div x-show="selectedNodeData.isOutsourced">
                                <label class="block text-xs font-medium text-text-gray mb-1">外协供应商</label>
                                <select x-model="selectedNodeData.supplierId" class="block w-full rounded-md border-border-gray shadow-sm focus:border-primary focus:ring-primary sm:text-sm">
                                    <template x-for="supplier in suppliers" :key="supplier.id">
                                        <option :value="supplier.id" x-text="supplier.name"></option>
                                    </template>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 参数化配置面板 -->
                    <div x-show="currentRoute && currentRoute.type === 'parametric'" class="p-4 border-b border-border-gray flex-grow overflow-y-auto">
                        <h3 class="text-sm font-medium text-title-gray mb-3">参数化配置</h3>
                        <div class="space-y-3">
                            <div>
                                <label class="block text-xs font-medium text-text-gray mb-1">产品参数</label>
                                <div class="space-y-2">
                                    <template x-for="param in currentRoute.productParameters" :key="param.id">
                                        <div class="param-config-item p-2 rounded bg-white">
                                            <div class="flex justify-between items-center">
                                                <span class="text-sm text-title-gray" x-text="param.name"></span>
                                                <span class="text-xs text-text-gray" x-text="param.unit"></span>
                                            </div>
                                            <input type="text" x-model="param.defaultValue" placeholder="默认值" class="mt-1 block w-full rounded-md border-border-gray shadow-sm focus:border-primary focus:ring-primary sm:text-sm">
                                        </div>
                                    </template>
                                </div>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-text-gray mb-1">工时计算公式</label>
                                <textarea x-model="currentRoute.timeFormula" rows="3" class="block w-full rounded-md border-border-gray shadow-sm focus:border-primary focus:ring-primary sm:text-sm" placeholder="例如: 准备工时 + 单件工时 * 面积"></textarea>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-text-gray mb-1">路线选择规则</label>
                                <select x-model="currentRoute.selectionRule" class="block w-full rounded-md border-border-gray shadow-sm focus:border-primary focus:ring-primary sm:text-sm">
                                    <option value="cost">成本优先</option>
                                    <option value="quality">质量优先</option>
                                    <option value="delivery">交期优先</option>
                                    <option value="custom">自定义规则</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 多路线管理面板 -->
                    <div x-show="currentRoute && currentRoute.type === 'variant'" class="p-4 flex-grow overflow-y-auto">
                        <h3 class="text-sm font-medium text-title-gray mb-3">多路线管理</h3>
                        <div class="space-y-3">
                            <div>
                                <label class="block text-xs font-medium text-text-gray mb-1">路线变体</label>
                                <div class="space-y-2">
                                    <template x-for="variant in currentRoute.variants" :key="variant.id">
                                        <div class="p-2 rounded bg-white border border-border-gray">
                                            <div class="flex justify-between items-center">
                                                <span class="text-sm text-title-gray" x-text="variant.name"></span>
                                                <button @click="deleteVariant(variant.id)" class="text-error hover:text-red-700">
                                                    <i class="ri-delete-bin-line"></i>
                                                </button>
                                            </div>
                                            <div class="mt-1 text-xs text-text-gray" x-text="variant.description"></div>
                                        </div>
                                    </template>
                                </div>
                                <button @click="addVariant()" class="mt-2 w-full inline-flex justify-center items-center px-3 py-1.5 border border-dashed border-border-gray rounded-md shadow-sm text-sm font-medium text-text-gray hover:bg-bg-gray">
                                    <i class="ri-add-line mr-1"></i>
                                    添加路线变体
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 统计信息面板 -->
                    <div class="p-4 border-t border-border-gray">
                        <h3 class="text-sm font-medium text-title-gray mb-3">统计信息</h3>
                        <div class="space-y-2">
                            <div class="flex justify-between text-sm">
                                <span class="text-text-gray">工序总数</span>
                                <span class="text-title-gray font-medium" x-text="processNodes.length"></span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-text-gray">总工时</span>
                                <span class="text-title-gray font-medium" x-text="calculateTotalTime() + ' 分钟'"></span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-text-gray">外协工序</span>
                                <span class="text-title-gray font-medium" x-text="countOutsourcedOperations()"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 基础层：工序标准管理视图 -->
        <div x-show="currentView === 'operations'" class="bg-white shadow rounded-lg">
            <!-- 工序标准管理工具栏 -->
            <div class="p-4 border-b border-border-gray flex flex-wrap items-center justify-between">
                <div class="flex items-center space-x-2 mb-2 md:mb-0">
                    <div class="flex items-center">
                        <i class="ri-settings-3-line text-primary mr-2"></i>
                        <span class="text-lg font-medium text-title-gray">基础层：工序标准管理</span>
                    </div>
                    <div class="h-6 w-px bg-border-gray mx-2"></div>
                    <span class="text-sm text-text-gray">建立标准化的工序主数据库，为工艺路线设计提供标准化工序信息</span>
                </div>
                <div class="flex items-center space-x-2">
                    <button @click="showImportOperationModal = true" class="inline-flex items-center px-3 py-1.5 border border-border-gray rounded-md shadow-sm text-sm font-medium text-text-gray bg-white hover:bg-bg-gray">
                        <i class="ri-upload-2-line mr-1"></i>
                        导入
                    </button>
                    <button @click="exportOperations()" class="inline-flex items-center px-3 py-1.5 border border-border-gray rounded-md shadow-sm text-sm font-medium text-text-gray bg-white hover:bg-bg-gray">
                        <i class="ri-download-2-line mr-1"></i>
                        导出
                    </button>
                    <button @click="createNewOperation()" class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-blue-700">
                        <i class="ri-add-line mr-1"></i>
                        新建工序
                    </button>
                </div>
            </div>

            <!-- 工序管理主体区域 -->
            <div class="flex flex-col md:flex-row">
                <!-- 左侧分类树 -->
                <div class="w-full md:w-64 bg-bg-gray border-r border-border-gray p-4">
                    <h3 class="text-sm font-medium text-title-gray mb-3">工序分类</h3>
                    <div class="space-y-1">
                        <template x-for="category in operationCategories" :key="category.id">
                            <div>
                                <div class="flex items-center p-2 rounded hover:bg-white cursor-pointer"
                                     :class="{'bg-white': selectedCategory === category.id}"
                                     @click="selectCategory(category.id)">
                                    <i class="ri-folder-line mr-2 text-text-gray"></i>
                                    <span class="text-sm text-title-gray" x-text="category.name"></span>
                                    <i class="ri-arrow-down-s-line ml-auto text-text-gray" x-show="category.expanded"></i>
                                    <i class="ri-arrow-right-s-line ml-auto text-text-gray" x-show="!category.expanded"></i>
                                </div>
                                <div x-show="category.expanded" class="ml-6 mt-1 space-y-1">
                                    <template x-for="subCategory in category.children" :key="subCategory.id">
                                        <div class="flex items-center p-2 rounded hover:bg-white cursor-pointer"
                                             :class="{'bg-white': selectedCategory === subCategory.id}"
                                             @click="selectCategory(subCategory.id)">
                                            <i class="ri-folder-line mr-2 text-text-gray"></i>
                                            <span class="text-sm text-title-gray" x-text="subCategory.name"></span>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>

                <!-- 右侧工序列表 -->
                <div class="flex-grow p-4">
                    <!-- 工序搜索和筛选 -->
                    <div class="mb-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="md:col-span-2">
                            <div class="relative rounded-md shadow-sm">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="ri-search-line text-text-gray"></i>
                                </div>
                                <input type="text" x-model="operationSearchQuery" @input="filterOperations()" class="focus:ring-primary focus:border-primary block w-full pl-10 sm:text-sm border-border-gray rounded-md" placeholder="搜索工序编码、名称...">
                            </div>
                        </div>
                        <div>
                            <select x-model="operationFilterType" @change="filterOperations()" class="block w-full pl-3 pr-10 py-2 text-base border-border-gray focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                                <option value="">全部类型</option>
                                <option value="cutting">切割</option>
                                <option value="grinding">磨边</option>
                                <option value="tempering">钢化</option>
                                <option value="laminating">合片</option>
                            </select>
                        </div>
                    </div>

                    <!-- 工序列表 -->
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-border-gray">
                            <thead class="bg-bg-gray">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-gray uppercase tracking-wider">
                                        工序信息
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-gray uppercase tracking-wider">
                                        类型
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-gray uppercase tracking-wider">
                                        标准工时
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-gray uppercase tracking-wider">
                                        状态
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-gray uppercase tracking-wider">
                                        更新时间
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-text-gray uppercase tracking-wider">
                                        操作
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-border-gray">
                                <template x-for="operation in filteredOperations" :key="operation.id">
                                    <tr class="hover:bg-bg-gray">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-md bg-primary bg-opacity-10">
                                                    <i class="ri-tools-line text-primary"></i>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-title-gray" x-text="operation.name"></div>
                                                    <div class="text-sm text-text-gray" x-text="operation.code"></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                                                  :class="{
                                                      'bg-blue-100 text-blue-800': operation.type === 'cutting',
                                                      'bg-green-100 text-green-800': operation.type === 'grinding',
                                                      'bg-yellow-100 text-yellow-800': operation.type === 'tempering',
                                                      'bg-purple-100 text-purple-800': operation.type === 'laminating'
                                                  }"
                                                  x-text="getOperationTypeLabel(operation.type)">
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-text-gray">
                                            <div>准备: <span x-text="operation.setupTime"></span> 分钟</div>
                                            <div>单件: <span x-text="operation.cycleTime"></span> 分钟</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                                                  :class="{
                                                      'bg-gray-100 text-gray-800': operation.status === 'draft',
                                                      'bg-green-100 text-green-800': operation.status === 'active',
                                                      'bg-red-100 text-red-800': operation.status === 'inactive'
                                                  }"
                                                  x-text="getStatusLabel(operation.status)">
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-text-gray" x-text="operation.updatedAt"></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <button @click="editOperation(operation)" class="text-primary hover:text-blue-700 mr-3">编辑</button>
                                            <button @click="deleteOperation(operation)" class="text-error hover:text-red-700">删除</button>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 执行层：外协工序管理视图 -->
        <div x-show="currentView === 'outsourcing'" class="bg-white shadow rounded-lg">
            <!-- 外协工序管理工具栏 -->
            <div class="p-4 border-b border-border-gray flex flex-wrap items-center justify-between">
                <div class="flex items-center space-x-2 mb-2 md:mb-0">
                    <div class="flex items-center">
                        <i class="ri-building-line text-primary mr-2"></i>
                        <span class="text-lg font-medium text-title-gray">执行层：外协工序管理</span>
                    </div>
                    <div class="h-6 w-px bg-border-gray mx-2"></div>
                    <span class="text-sm text-text-gray">管理外协工序配置、供应商选择和成本分析，支持采购系统集成</span>
                </div>
                <div class="flex items-center space-x-2">
                    <button @click="syncToProcurement()" class="inline-flex items-center px-3 py-1.5 border border-border-gray rounded-md shadow-sm text-sm font-medium text-text-gray bg-white hover:bg-bg-gray">
                        <i class="ri-refresh-line mr-1"></i>
                        同步到采购系统
                    </button>
                </div>
            </div>

            <!-- 外协工序管理主体区域 -->
            <div class="flex flex-col md:flex-row">
                <!-- 左侧工艺路线树 -->
                <div class="w-full md:w-64 bg-bg-gray border-r border-border-gray p-4">
                    <h3 class="text-sm font-medium text-title-gray mb-3">工艺路线</h3>
                    <div class="space-y-1">
                        <template x-for="route in routes" :key="route.id">
                            <div>
                                <div class="flex items-center p-2 rounded hover:bg-white cursor-pointer"
                                     :class="{'bg-white': selectedOutsourcingRoute === route.id}"
                                     @click="selectOutsourcingRoute(route.id)">
                                    <i class="ri-flow-chart mr-2 text-text-gray"></i>
                                    <span class="text-sm text-title-gray" x-text="route.name"></span>
                                    <span class="ml-auto text-xs bg-warning bg-opacity-20 text-warning px-1.5 py-0.5 rounded" x-text="getOutsourcingCount(route.id)"></span>
                                </div>
                                <div x-show="selectedOutsourcingRoute === route.id" class="ml-6 mt-1 space-y-1">
                                    <template x-for="operation in getRouteOperations(route.id)" :key="operation.id">
                                        <div class="flex items-center p-2 rounded hover:bg-white cursor-pointer"
                                             :class="{'bg-white': selectedOutsourcingOperation === operation.id}"
                                             @click="selectOutsourcingOperation(operation.id)">
                                            <i class="ri-tools-line mr-2 text-text-gray"></i>
                                            <span class="text-sm text-title-gray" x-text="operation.name"></span>
                                            <span x-show="operation.isOutsourced" class="ml-auto text-xs bg-orange-100 text-orange-800 px-1.5 py-0.5 rounded">外协</span>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>

                <!-- 右侧外协配置区域 -->
                <div class="flex-grow p-4">
                    <div x-show="selectedOutsourcingOperationData">
                        <!-- 外协标识 -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-title-gray mb-4">外协配置</h3>
                            <div class="bg-bg-gray p-4 rounded-lg">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="text-sm font-medium text-title-gray" x-text="selectedOutsourcingOperationData.name"></div>
                                        <div class="text-sm text-text-gray" x-text="selectedOutsourcingOperationData.code"></div>
                                    </div>
                                    <div class="flex items-center">
                                        <span class="text-sm text-text-gray mr-3">外协工序</span>
                                        <label class="relative inline-flex items-center cursor-pointer">
                                            <input type="checkbox" x-model="selectedOutsourcingOperationData.isOutsourced" class="sr-only peer">
                                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 外协类型选择 -->
                        <div x-show="selectedOutsourcingOperationData.isOutsourced" class="mb-6">
                            <h4 class="text-md font-medium text-title-gray mb-3">外协类型</h4>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div class="border border-border-gray rounded-lg p-4 cursor-pointer hover:border-primary"
                                     :class="{'border-primary bg-primary bg-opacity-5': selectedOutsourcingOperationData.outsourcingType === 'full'}"
                                     @click="selectedOutsourcingOperationData.outsourcingType = 'full'">
                                    <div class="flex items-center">
                                        <i class="ri-checkbox-circle-fill text-primary mr-2" x-show="selectedOutsourcingOperationData.outsourcingType === 'full'"></i>
                                        <i class="ri-checkbox-blank-circle-line text-text-gray mr-2" x-show="selectedOutsourcingOperationData.outsourcingType !== 'full'"></i>
                                        <div>
                                            <div class="text-sm font-medium text-title-gray">完全外协</div>
                                            <div class="text-xs text-text-gray">整个工序委托外部完成</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="border border-border-gray rounded-lg p-4 cursor-pointer hover:border-primary"
                                     :class="{'border-primary bg-primary bg-opacity-5': selectedOutsourcingOperationData.outsourcingType === 'partial'}"
                                     @click="selectedOutsourcingOperationData.outsourcingType = 'partial'">
                                    <div class="flex items-center">
                                        <i class="ri-checkbox-circle-fill text-primary mr-2" x-show="selectedOutsourcingOperationData.outsourcingType === 'partial'"></i>
                                        <i class="ri-checkbox-blank-circle-line text-text-gray mr-2" x-show="selectedOutsourcingOperationData.outsourcingType !== 'partial'"></i>
                                        <div>
                                            <div class="text-sm font-medium text-title-gray">部分外协</div>
                                            <div class="text-xs text-text-gray">工序的某些环节外协</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="border border-border-gray rounded-lg p-4 cursor-pointer hover:border-primary"
                                     :class="{'border-primary bg-primary bg-opacity-5': selectedOutsourcingOperationData.outsourcingType === 'temporary'}"
                                     @click="selectedOutsourcingOperationData.outsourcingType = 'temporary'">
                                    <div class="flex items-center">
                                        <i class="ri-checkbox-circle-fill text-primary mr-2" x-show="selectedOutsourcingOperationData.outsourcingType === 'temporary'"></i>
                                        <i class="ri-checkbox-blank-circle-line text-text-gray mr-2" x-show="selectedOutsourcingOperationData.outsourcingType !== 'temporary'"></i>
                                        <div>
                                            <div class="text-sm font-medium text-title-gray">临时外协</div>
                                            <div class="text-xs text-text-gray">根据产能情况临时外协</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 供应商配置 -->
                        <div x-show="selectedOutsourcingOperationData.isOutsourced" class="mb-6">
                            <h4 class="text-md font-medium text-title-gray mb-3">供应商配置</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-text-gray mb-1">主供应商</label>
                                    <select x-model="selectedOutsourcingOperationData.primarySupplierId" class="block w-full rounded-md border-border-gray shadow-sm focus:border-primary focus:ring-primary sm:text-sm">
                                        <option value="">请选择主供应商</option>
                                        <template x-for="supplier in suppliers" :key="supplier.id">
                                            <option :value="supplier.id" x-text="supplier.name"></option>
                                        </template>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-text-gray mb-1">备选供应商</label>
                                    <div class="space-y-2">
                                        <template x-for="supplierId in selectedOutsourcingOperationData.backupSupplierIds" :key="supplierId">
                                            <div class="flex items-center justify-between bg-bg-gray p-2 rounded">
                                                <span class="text-sm text-title-gray" x-text="getSupplierName(supplierId)"></span>
                                                <button @click="removeBackupSupplier(supplierId)" class="text-error hover:text-red-700">
                                                    <i class="ri-close-line"></i>
                                                </button>
                                            </div>
                                        </template>
                                        <button @click="showAddBackupSupplierModal = true" class="w-full inline-flex justify-center items-center px-3 py-1.5 border border-dashed border-border-gray rounded-md shadow-sm text-sm font-medium text-text-gray hover:bg-bg-gray">
                                            <i class="ri-add-line mr-1"></i>
                                            添加备选供应商
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 价格配置 -->
                        <div x-show="selectedOutsourcingOperationData.isOutsourced" class="mb-6">
                            <h4 class="text-md font-medium text-title-gray mb-3">价格配置</h4>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-text-gray mb-1">单价</label>
                                    <div class="relative rounded-md shadow-sm">
                                        <input type="number" x-model="selectedOutsourcingOperationData.unitPrice" class="focus:ring-primary focus:border-primary block w-full pl-3 pr-12 sm:text-sm border-border-gray rounded-md" placeholder="0.00">
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                            <span class="text-text-gray sm:text-sm">元</span>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-text-gray mb-1">计价方式</label>
                                    <select x-model="selectedOutsourcingOperationData.pricingMethod" class="block w-full rounded-md border-border-gray shadow-sm focus:border-primary focus:ring-primary sm:text-sm">
                                        <option value="piece">按件</option>
                                        <option value="hour">按时</option>
                                        <option value="area">按面积</option>
                                        <option value="weight">按重量</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-text-gray mb-1">价格有效期</label>
                                    <input type="date" x-model="selectedOutsourcingOperationData.priceValidUntil" class="block w-full rounded-md border-border-gray shadow-sm focus:border-primary focus:ring-primary sm:text-sm">
                                </div>
                            </div>
                        </div>

                        <!-- 交期配置 -->
                        <div x-show="selectedOutsourcingOperationData.isOutsourced" class="mb-6">
                            <h4 class="text-md font-medium text-title-gray mb-3">交期配置</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-text-gray mb-1">标准交期</label>
                                    <div class="relative rounded-md shadow-sm">
                                        <input type="number" x-model="selectedOutsourcingOperationData.standardLeadTime" class="focus:ring-primary focus:border-primary block w-full pl-3 pr-12 sm:text-sm border-border-gray rounded-md" placeholder="0">
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                            <span class="text-text-gray sm:text-sm">天</span>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-text-gray mb-1">加急交期</label>
                                    <div class="relative rounded-md shadow-sm">
                                        <input type="number" x-model="selectedOutsourcingOperationData.urgentLeadTime" class="focus:ring-primary focus:border-primary block w-full pl-3 pr-12 sm:text-sm border-border-gray rounded-md" placeholder="0">
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                            <span class="text-text-gray sm:text-sm">天</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 质量要求 -->
                        <div x-show="selectedOutsourcingOperationData.isOutsourced" class="mb-6">
                            <h4 class="text-md font-medium text-title-gray mb-3">质量要求</h4>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-text-gray mb-1">技术标准</label>
                                    <textarea x-model="selectedOutsourcingOperationData.technicalStandard" rows="3" class="block w-full rounded-md border-border-gray shadow-sm focus:border-primary focus:ring-primary sm:text-sm" placeholder="请输入技术标准要求..."></textarea>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-text-gray mb-1">质量标准</label>
                                    <select x-model="selectedOutsourcingOperationData.qualityStandard" class="block w-full rounded-md border-border-gray shadow-sm focus:border-primary focus:ring-primary sm:text-sm">
                                        <option value="">请选择质量标准</option>
                                        <option value="A">A级</option>
                                        <option value="B">B级</option>
                                        <option value="C">C级</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-text-gray mb-1">检验要求</label>
                                    <div class="space-y-2">
                                        <template x-for="inspection in inspectionItems" :key="inspection.id">
                                            <div class="flex items-center">
                                                <input type="checkbox" :id="'inspection-' + inspection.id" :value="inspection.id" x-model="selectedOutsourcingOperationData.inspectionItems" class="h-4 w-4 text-primary focus:ring-primary border-border-gray rounded">
                                                <label :for="'inspection-' + inspection.id" class="ml-2 block text-sm text-text-gray" x-text="inspection.name"></label>
                                            </div>
                                        </template>
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-text-gray mb-1">合格率要求</label>
                                    <div class="relative rounded-md shadow-sm">
                                        <input type="number" x-model="selectedOutsourcingOperationData.acceptanceRate" min="0" max="100" class="focus:ring-primary focus:border-primary block w-full pl-3 pr-12 sm:text-sm border-border-gray rounded-md" placeholder="0">
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                            <span class="text-text-gray sm:text-sm">%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 成本分析 -->
                        <div x-show="selectedOutsourcingOperationData.isOutsourced" class="mb-6">
                            <h4 class="text-md font-medium text-title-gray mb-3">成本分析</h4>
                            <div class="bg-bg-gray p-4 rounded-lg">
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-title-gray" x-text="calculateOutsourcingCost()"></div>
                                        <div class="text-sm text-text-gray">外协成本</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-title-gray" x-text="calculateInternalCost()"></div>
                                        <div class="text-sm text-text-gray">内制成本</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-2xl font-bold" :class="calculateCostDifference() > 0 ? 'text-error' : 'text-success'" x-text="calculateCostDifference()"></div>
                                        <div class="text-sm text-text-gray">成本差异</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 保存按钮 -->
                        <div x-show="selectedOutsourcingOperationData.isOutsourced" class="flex justify-end">
                            <button @click="saveOutsourcingConfig()" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-blue-700">
                                <i class="ri-save-line mr-2"></i>
                                保存外协配置
                            </button>
                        </div>
                    </div>

                    <!-- 空状态 -->
                    <div x-show="!selectedOutsourcingOperationData" class="flex flex-col items-center justify-center py-12">
                        <i class="ri-file-text-line text-6xl text-text-gray opacity-20 mb-4"></i>
                        <h3 class="text-lg font-medium text-title-gray mb-2">请选择工序</h3>
                        <p class="text-sm text-text-gray text-center max-w-md">从左侧工艺路线树中选择一个工序，查看和配置外协信息</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新建/编辑工艺路线模态框 -->
    <div x-show="showRouteModal" class="fixed z-50 inset-0 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" @click="showRouteModal = false"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <form @submit.prevent="saveRouteModal()">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-primary bg-opacity-10 sm:mx-0 sm:h-10 sm:w-10">
                                <i class="ri-flow-chart text-primary"></i>
                            </div>
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                <h3 class="text-lg leading-6 font-medium text-title-gray" id="modal-title">
                                    <span x-text="editingRoute ? '编辑工艺路线' : '新建工艺路线'"></span>
                                </h3>
                                <div class="mt-4 space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-text-gray mb-1">路线名称</label>
                                        <input type="text" x-model="routeForm.name" required class="block w-full rounded-md border-border-gray shadow-sm focus:border-primary focus:ring-primary sm:text-sm">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-text-gray mb-1">路线编码</label>
                                        <input type="text" x-model="routeForm.code" required class="block w-full rounded-md border-border-gray shadow-sm focus:border-primary focus:ring-primary sm:text-sm">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-text-gray mb-1">路线类型</label>
                                        <select x-model="routeForm.type" required class="block w-full rounded-md border-border-gray shadow-sm focus:border-primary focus:ring-primary sm:text-sm">
                                            <option value="standard">标准路线</option>
                                            <option value="parametric">参数化路线</option>
                                            <option value="variant">变体路线</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-text-gray mb-1">适用物料</label>
                                        <select x-model="routeForm.materialId" required class="block w-full rounded-md border-border-gray shadow-sm focus:border-primary focus:ring-primary sm:text-sm">
                                            <option value="">请选择物料</option>
                                            <template x-for="material in materials" :key="material.id">
                                                <option :value="material.id" x-text="material.name"></option>
                                            </template>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-text-gray mb-1">描述</label>
                                        <textarea x-model="routeForm.description" rows="3" class="block w-full rounded-md border-border-gray shadow-sm focus:border-primary focus:ring-primary sm:text-sm"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-bg-gray px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-blue-700 focus:outline-none sm:ml-3 sm:w-auto sm:text-sm">
                            保存
                        </button>
                        <button type="button" @click="showRouteModal = false" class="mt-3 w-full inline-flex justify-center rounded-md border border-border-gray shadow-sm px-4 py-2 bg-white text-base font-medium text-text-gray hover:bg-bg-gray focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                            取消
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 新建/编辑工序模态框 -->
    <div x-show="showOperationModal && operationForm" class="fixed z-50 inset-0 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" @click="showOperationModal = false"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
                <form @submit.prevent="saveOperationModal()">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-primary bg-opacity-10 sm:mx-0 sm:h-10 sm:w-10">
                                <i class="ri-tools-line text-primary"></i>
                            </div>
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                <h3 class="text-lg leading-6 font-medium text-title-gray" id="modal-title">
                                    <span x-text="editingOperation ? '编辑工序' : '新建工序'"></span>
                                </h3>
                                <div x-show="operationForm" class="mt-4 space-y-4">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-text-gray mb-1">工序编码</label>
                                            <input type="text" x-model="operationForm.code" required class="block w-full rounded-md border-border-gray shadow-sm focus:border-primary focus:ring-primary sm:text-sm">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-text-gray mb-1">工序名称</label>
                                            <input type="text" x-model="operationForm.name" required class="block w-full rounded-md border-border-gray shadow-sm focus:border-primary focus:ring-primary sm:text-sm">
                                        </div>
                                    </div>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-text-gray mb-1">工序类型</label>
                                            <select x-model="operationForm.type" required class="block w-full rounded-md border-border-gray shadow-sm focus:border-primary focus:ring-primary sm:text-sm">
                                                <option value="cutting">切割</option>
                                                <option value="grinding">磨边</option>
                                                <option value="tempering">钢化</option>
                                                <option value="laminating">合片</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-text-gray mb-1">工序分类</label>
                                            <select x-model="operationForm.categoryId" required class="block w-full rounded-md border-border-gray shadow-sm focus:border-primary focus:ring-primary sm:text-sm">
                                                <option value="">请选择分类</option>
                                                <template x-for="category in flattenOperationCategories()" :key="category.id">
                                                    <option :value="category.id" x-text="category.name"></option>
                                                </template>
                                            </select>
                                        </div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-text-gray mb-1">工序描述</label>
                                        <textarea x-model="operationForm.description" rows="2" class="block w-full rounded-md border-border-gray shadow-sm focus:border-primary focus:ring-primary sm:text-sm"></textarea>
                                    </div>
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-text-gray mb-1">准备工时(分钟)</label>
                                            <input type="number" x-model="operationForm.setupTime" required min="0" class="block w-full rounded-md border-border-gray shadow-sm focus:border-primary focus:ring-primary sm:text-sm">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-text-gray mb-1">单件工时(分钟)</label>
                                            <input type="number" x-model="operationForm.cycleTime" required min="0" class="block w-full rounded-md border-border-gray shadow-sm focus:border-primary focus:ring-primary sm:text-sm">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-text-gray mb-1">拆卸工时(分钟)</label>
                                            <input type="number" x-model="operationForm.teardownTime" min="0" class="block w-full rounded-md border-border-gray shadow-sm focus:border-primary focus:ring-primary sm:text-sm">
                                        </div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-text-gray mb-1">质量标准</label>
                                        <textarea x-model="operationForm.qualityStandard" rows="3" class="block w-full rounded-md border-border-gray shadow-sm focus:border-primary focus:ring-primary sm:text-sm"></textarea>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-text-gray mb-1">作业指导</label>
                                        <textarea x-model="operationForm.workInstructions" rows="3" class="block w-full rounded-md border-border-gray shadow-sm focus:border-primary focus:ring-primary sm:text-sm"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-bg-gray px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-blue-700 focus:outline-none sm:ml-3 sm:w-auto sm:text-sm">
                            保存
                        </button>
                        <button type="button" @click="showOperationModal = false" class="mt-3 w-full inline-flex justify-center rounded-md border border-border-gray shadow-sm px-4 py-2 bg-white text-base font-medium text-text-gray hover:bg-bg-gray focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                            取消
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 添加备选供应商模态框 -->
    <div x-show="showAddBackupSupplierModal" class="fixed z-50 inset-0 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" @click="showAddBackupSupplierModal = false"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-primary bg-opacity-10 sm:mx-0 sm:h-10 sm:w-10">
                            <i class="ri-user-add-line text-primary"></i>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-title-gray" id="modal-title">
                                添加备选供应商
                            </h3>
                            <div class="mt-4">
                                <label class="block text-sm font-medium text-text-gray mb-1">选择供应商</label>
                                <select x-model="newBackupSupplierId" class="block w-full rounded-md border-border-gray shadow-sm focus:border-primary focus:ring-primary sm:text-sm">
                                    <option value="">请选择供应商</option>
                                    <template x-for="supplier in suppliers" :key="supplier.id">
                                        <option :value="supplier.id" x-text="supplier.name" :disabled="supplier.id === selectedOutsourcingOperationData.primarySupplierId || selectedOutsourcingOperationData.backupSupplierIds.includes(supplier.id)"></option>
                                    </template>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-bg-gray px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="button" @click="addBackupSupplier()" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-blue-700 focus:outline-none sm:ml-3 sm:w-auto sm:text-sm">
                        添加
                    </button>
                    <button type="button" @click="showAddBackupSupplierModal = false" class="mt-3 w-full inline-flex justify-center rounded-md border border-border-gray shadow-sm px-4 py-2 bg-white text-base font-medium text-text-gray hover:bg-bg-gray focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        取消
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 导入工艺路线模态框 -->
    <div x-show="showImportModal" class="fixed z-50 inset-0 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" @click="showImportModal = false"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-primary bg-opacity-10 sm:mx-0 sm:h-10 sm:w-10">
                            <i class="ri-upload-2-line text-primary"></i>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-title-gray" id="modal-title">
                                导入工艺路线
                            </h3>
                            <div class="mt-4">
                                <div class="border-2 border-dashed border-border-gray rounded-lg p-6 text-center">
                                    <i class="ri-file-upload-line text-4xl text-text-gray mb-2"></i>
                                    <p class="text-sm text-text-gray mb-2">拖放文件到此处，或点击上传</p>
                                    <p class="text-xs text-text-gray mb-4">支持 Excel、CSV 格式</p>
                                    <label class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-blue-700 cursor-pointer">
                                        <span>选择文件</span>
                                        <input type="file" class="sr-only" @change="handleFileUpload($event)">
                                    </label>
                                </div>
                                <div class="mt-4">
                                    <a href="#" class="text-primary text-sm hover:text-blue-700">
                                        <i class="ri-download-line mr-1"></i>
                                        下载导入模板
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-bg-gray px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="button" @click="showImportModal = false" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-blue-700 focus:outline-none sm:ml-3 sm:w-auto sm:text-sm">
                        完成
                    </button>
                    <button type="button" @click="showImportModal = false" class="mt-3 w-full inline-flex justify-center rounded-md border border-border-gray shadow-sm px-4 py-2 bg-white text-base font-medium text-text-gray hover:bg-bg-gray focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        取消
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 导入工序模态框 -->
    <div x-show="showImportOperationModal" class="fixed z-50 inset-0 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" @click="showImportOperationModal = false"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-primary bg-opacity-10 sm:mx-0 sm:h-10 sm:w-10">
                            <i class="ri-upload-2-line text-primary"></i>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-title-gray" id="modal-title">
                                导入工序
                            </h3>
                            <div class="mt-4">
                                <div class="border-2 border-dashed border-border-gray rounded-lg p-6 text-center">
                                    <i class="ri-file-upload-line text-4xl text-text-gray mb-2"></i>
                                    <p class="text-sm text-text-gray mb-2">拖放文件到此处，或点击上传</p>
                                    <p class="text-xs text-text-gray mb-4">支持 Excel、CSV 格式</p>
                                    <label class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-blue-700 cursor-pointer">
                                        <span>选择文件</span>
                                        <input type="file" class="sr-only" @change="handleOperationFileUpload($event)">
                                    </label>
                                </div>
                                <div class="mt-4">
                                    <a href="#" class="text-primary text-sm hover:text-blue-700">
                                        <i class="ri-download-line mr-1"></i>
                                        下载导入模板
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-bg-gray px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="button" @click="showImportOperationModal = false" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-blue-700 focus:outline-none sm:ml-3 sm:w-auto sm:text-sm">
                        完成
                    </button>
                    <button type="button" @click="showImportOperationModal = false" class="mt-3 w-full inline-flex justify-center rounded-md border border-border-gray shadow-sm px-4 py-2 bg-white text-base font-medium text-text-gray hover:bg-bg-gray focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        取消
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 通知提示 -->
    <div x-show="notification.show" 
         x-transition:enter="transform ease-out duration-300 transition"
         x-transition:enter-start="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
         x-transition:enter-end="translate-y-0 opacity-100 sm:translate-x-0"
         x-transition:leave="transition ease-in duration-100"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed bottom-4 right-4 max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden z-50">
        <div class="p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i x-show="notification.type === 'success'" class="ri-checkbox-circle-fill text-success text-xl"></i>
                    <i x-show="notification.type === 'error'" class="ri-error-warning-fill text-error text-xl"></i>
                    <i x-show="notification.type === 'warning'" class="ri-alert-fill text-warning text-xl"></i>
                    <i x-show="notification.type === 'info'" class="ri-information-fill text-primary text-xl"></i>
                </div>
                <div class="ml-3 w-0 flex-1 pt-0.5">
                    <p class="text-sm font-medium text-title-gray" x-text="notification.title"></p>
                    <p class="mt-1 text-sm text-text-gray" x-text="notification.message"></p>
                </div>
                <div class="ml-4 flex-shrink-0 flex">
                    <button @click="notification.show = false" class="bg-white rounded-md inline-flex text-text-gray hover:text-title-gray focus:outline-none">
                        <i class="ri-close-line text-lg"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function processRouteApp() {
            return {
                // 视图状态 - PDM-005工艺流程管理模块三层架构
                currentView: 'list', // list: 工艺路线列表, operations: 基础层-工序标准管理, designer: 设计层-工艺路线设计, outsourcing: 执行层-外协工序管理
                userMenuOpen: false,
                
                // 工艺路线相关
                routes: [
                    {
                        id: 1,
                        code: 'RT001',
                        name: '建筑玻璃标准工艺路线',
                        type: 'standard',
                        material: '建筑玻璃',
                        operationCount: 5,
                        status: 'active',
                        updatedAt: '2025-07-30 14:30',
                        description: '标准建筑玻璃加工工艺路线'
                    },
                    {
                        id: 2,
                        code: 'RT002',
                        name: '钢化玻璃参数化工艺路线',
                        type: 'parametric',
                        material: '钢化玻璃',
                        operationCount: 6,
                        status: 'active',
                        updatedAt: '2025-07-29 10:15',
                        description: '根据玻璃尺寸和厚度参数自动生成工艺路线',
                        productParameters: [
                            { id: 1, name: '长度', unit: 'mm', defaultValue: '1000' },
                            { id: 2, name: '宽度', unit: 'mm', defaultValue: '800' },
                            { id: 3, name: '厚度', unit: 'mm', defaultValue: '5' }
                        ],
                        timeFormula: '准备工时 + 单件工时 * (长度 * 宽度 / 1000000)',
                        selectionRule: 'cost'
                    },
                    {
                        id: 3,
                        code: 'RT003',
                        name: '防火窗工艺路线',
                        type: 'variant',
                        material: '防火窗',
                        operationCount: 8,
                        status: 'draft',
                        updatedAt: '2025-07-28 16:45',
                        description: '防火窗生产多路线工艺',
                        variants: [
                            { id: 1, name: '标准型', description: '标准防火窗生产工艺' },
                            { id: 2, name: '增强型', description: '增强防火性能生产工艺' }
                        ]
                    }
                ],
                filteredRoutes: [],
                searchQuery: '',
                filterType: '',
                filterStatus: '',
                currentPage: 1,
                pageSize: 10,
                totalItems: 0,
                totalPages: 0,
                
                // 工艺路线设计器相关
                currentRoute: null,
                processNodes: [
                    { id: 1, type: 'start', x: 50, y: 200, name: '开始' },
                    { id: 2, type: 'operation', x: 200, y: 200, operationNumber: '10', name: '切割', workCenterId: 'WC001', setupTime: 30, cycleTime: 5, isOutsourced: false },
                    { id: 3, type: 'operation', x: 400, y: 200, operationNumber: '20', name: '磨边', workCenterId: 'WC002', setupTime: 20, cycleTime: 3, isOutsourced: false },
                    { id: 4, type: 'operation', x: 600, y: 200, operationNumber: '30', name: '钢化', workCenterId: 'WC003', setupTime: 60, cycleTime: 10, isOutsourced: true, supplierId: 'S001' },
                    { id: 5, type: 'operation', x: 800, y: 200, operationNumber: '40', name: '合片', workCenterId: 'WC004', setupTime: 40, cycleTime: 8, isOutsourced: false },
                    { id: 6, type: 'end', x: 950, y: 200, name: '结束' }
                ],
                connections: [
                    { id: 1, from: 1, to: 2, x1: 130, y1: 200, x2: 200, y2: 200, length: 70, angle: 0 },
                    { id: 2, from: 2, to: 3, x1: 280, y1: 200, x2: 400, y2: 200, length: 120, angle: 0 },
                    { id: 3, from: 3, to: 4, x1: 480, y1: 200, x2: 600, y2: 200, length: 120, angle: 0 },
                    { id: 4, from: 4, to: 5, x1: 680, y1: 200, x2: 800, y2: 200, length: 120, angle: 0 },
                    { id: 5, from: 5, to: 6, x1: 880, y1: 200, x2: 950, y2: 200, length: 70, angle: 0 }
                ],
                selectedNode: null,
                selectedConnection: null,
                selectedNodeData: null,
                zoomLevel: 1,
                
                // 工序相关
                operations: [
                    {
                        id: 1,
                        code: 'OP001',
                        name: '玻璃切割',
                        type: 'cutting',
                        categoryId: 'cat1',
                        setupTime: 30,
                        cycleTime: 5,
                        teardownTime: 10,
                        qualityStandard: '切割尺寸误差±0.5mm',
                        workInstructions: '1. 检查玻璃原片质量\n2. 设置切割参数\n3. 进行切割操作\n4. 检查切割质量',
                        status: 'active',
                        updatedAt: '2025-07-25 09:15'
                    },
                    {
                        id: 2,
                        code: 'OP002',
                        name: '玻璃磨边',
                        type: 'grinding',
                        categoryId: 'cat1',
                        setupTime: 20,
                        cycleTime: 3,
                        teardownTime: 5,
                        qualityStandard: '边缘光滑无毛刺',
                        workInstructions: '1. 设置磨边参数\n2. 进行磨边操作\n3. 检查磨边质量',
                        status: 'active',
                        updatedAt: '2025-07-24 14:30'
                    },
                    {
                        id: 3,
                        code: 'OP003',
                        name: '玻璃钢化',
                        type: 'tempering',
                        categoryId: 'cat2',
                        setupTime: 60,
                        cycleTime: 10,
                        teardownTime: 15,
                        qualityStandard: '钢化应力符合标准',
                        workInstructions: '1. 预热玻璃\n2. 钢化处理\n3. 急冷处理\n4. 质量检验',
                        status: 'active',
                        updatedAt: '2025-07-23 11:20'
                    },
                    {
                        id: 4,
                        code: 'OP004',
                        name: '玻璃合片',
                        type: 'laminating',
                        categoryId: 'cat2',
                        setupTime: 40,
                        cycleTime: 8,
                        teardownTime: 10,
                        qualityStandard: '合片无气泡、无杂质',
                        workInstructions: '1. 清洁玻璃表面\n2. 涂布胶片\n3. 合片操作\n4. 预压处理',
                        status: 'active',
                        updatedAt: '2025-07-22 16:45'
                    }
                ],
                filteredOperations: [],
                operationSearchQuery: '',
                operationFilterType: '',
                selectedCategory: null,
                operationCategories: [
                    {
                        id: 'cat1',
                        name: '预处理工序',
                        expanded: true,
                        children: [
                            { id: 'cat1-1', name: '切割工序', expanded: false, children: [] },
                            { id: 'cat1-2', name: '磨边工序', expanded: false, children: [] }
                        ]
                    },
                    {
                        id: 'cat2',
                        name: '深加工工序',
                        expanded: true,
                        children: [
                            { id: 'cat2-1', name: '钢化工序', expanded: false, children: [] },
                            { id: 'cat2-2', name: '合片工序', expanded: false, children: [] }
                        ]
                    }
                ],
                
                // 外协工序相关
                selectedOutsourcingRoute: null,
                selectedOutsourcingOperation: null,
                selectedOutsourcingOperationData: null,
                
                // 模态框状态
                showRouteModal: false,
                showOperationModal: false,
                showImportModal: false,
                showImportOperationModal: false,
                showAddBackupSupplierModal: false,
                
                // 表单数据
                routeForm: {
                    name: '',
                    code: '',
                    type: 'standard',
                    materialId: '',
                    description: ''
                },
                operationForm: {
                    code: '',
                    name: '',
                    type: 'cutting',
                    categoryId: '',
                    description: '',
                    setupTime: 0,
                    cycleTime: 0,
                    teardownTime: 0,
                    qualityStandard: '',
                    workInstructions: ''
                },
                editingRoute: null,
                editingOperation: null,
                newBackupSupplierId: '',
                
                // 通知
                notification: {
                    show: false,
                    type: 'info',
                    title: '',
                    message: '',
                    timeout: null
                },
                
                // 模板和库数据
                operationTemplates: [
                    { id: 1, name: '切割', type: '预处理' },
                    { id: 2, name: '磨边', type: '预处理' },
                    { id: 3, name: '钢化', type: '深加工' },
                    { id: 4, name: '合片', type: '深加工' }
                ],
                processParameters: [
                    { id: 1, name: '长度', unit: 'mm' },
                    { id: 2, name: '宽度', unit: 'mm' },
                    { id: 3, name: '厚度', unit: 'mm' },
                    { id: 4, name: '温度', unit: '℃' },
                    { id: 5, name: '压力', unit: 'MPa' }
                ],
                toolingLibrary: [
                    { id: 1, name: '切割夹具' },
                    { id: 2, name: '磨边夹具' },
                    { id: 3, name: '钢化模具' },
                    { id: 4, name: '合片夹具' }
                ],
                workCenters: [
                    { id: 'WC001', name: '切割工作中心' },
                    { id: 'WC002', name: '磨边工作中心' },
                    { id: 'WC003', name: '钢化工作中心' },
                    { id: 'WC004', name: '合片工作中心' }
                ],
                suppliers: [
                    { id: 'S001', name: '华联玻璃加工厂' },
                    { id: 'S002', name: '新世纪玻璃有限公司' },
                    { id: 'S003', name: '恒信玻璃制品厂' }
                ],
                materials: [
                    { id: 'M001', name: '建筑玻璃' },
                    { id: 'M002', name: '钢化玻璃' },
                    { id: 'M003', name: '防火窗' }
                ],
                inspectionItems: [
                    { id: 1, name: '尺寸检验' },
                    { id: 2, name: '外观检验' },
                    { id: 3, name: '性能检验' },
                    { id: 4, name: '安全检验' }
                ],

                // 表单重置方法
                resetOperationForm() {
                    this.operationForm = {
                        code: '',
                        name: '',
                        type: 'cutting',
                        categoryId: '',
                        description: '',
                        setupTime: 0,
                        cycleTime: 0,
                        teardownTime: 0,
                        qualityStandard: '',
                        workInstructions: ''
                    };
                },

                // 初始化
                init() {
                    // 确保表单对象正确初始化
                    this.resetOperationForm();

                    this.filteredRoutes = [...this.routes];
                    this.filteredOperations = [...this.operations];
                    this.totalItems = this.routes.length;
                    this.totalPages = Math.ceil(this.totalItems / this.pageSize);
                    
                    // 初始化外协工序数据
                    this.processNodes.forEach(node => {
                        if (node.type === 'operation' && node.isOutsourced) {
                            if (!node.outsourcingType) node.outsourcingType = 'full';
                            if (!node.primarySupplierId) node.primarySupplierId = 'S001';
                            if (!node.backupSupplierIds) node.backupSupplierIds = ['S002'];
                            if (!node.unitPrice) node.unitPrice = 50;
                            if (!node.pricingMethod) node.pricingMethod = 'piece';
                            if (!node.priceValidUntil) node.priceValidUntil = '2025-12-31';
                            if (!node.standardLeadTime) node.standardLeadTime = 3;
                            if (!node.urgentLeadTime) node.urgentLeadTime = 1;
                            if (!node.technicalStandard) node.technicalStandard = '符合国家相关标准';
                            if (!node.qualityStandard) node.qualityStandard = 'A';
                            if (!node.inspectionItems) node.inspectionItems = [1, 2];
                            if (!node.acceptanceRate) node.acceptanceRate = 98;
                        }
                    });
                },
                
                // 工艺路线列表相关方法
                filterRoutes() {
                    this.filteredRoutes = this.routes.filter(route => {
                        const matchSearch = !this.searchQuery || 
                            route.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
                            route.code.toLowerCase().includes(this.searchQuery.toLowerCase());
                        
                        const matchType = !this.filterType || route.type === this.filterType;
                        
                        const matchStatus = !this.filterStatus || route.status === this.filterStatus;
                        
                        return matchSearch && matchType && matchStatus;
                    });
                    
                    this.totalItems = this.filteredRoutes.length;
                    this.totalPages = Math.ceil(this.totalItems / this.pageSize);
                    this.currentPage = 1;
                },
                
                prevPage() {
                    if (this.currentPage > 1) {
                        this.currentPage--;
                    }
                },
                
                nextPage() {
                    if (this.currentPage < this.totalPages) {
                        this.currentPage++;
                    }
                },
                
                goToPage(page) {
                    this.currentPage = page;
                },
                
                get pageNumbers() {
                    const pages = [];
                    const maxVisible = 5;
                    let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));
                    let end = Math.min(this.totalPages, start + maxVisible - 1);
                    
                    if (end - start + 1 < maxVisible) {
                        start = Math.max(1, end - maxVisible + 1);
                    }
                    
                    for (let i = start; i <= end; i++) {
                        pages.push(i);
                    }
                    
                    return pages;
                },
                
                createNewRoute() {
                    this.editingRoute = null;
                    this.routeForm = {
                        name: '',
                        code: '',
                        type: 'standard',
                        materialId: '',
                        description: ''
                    };
                    this.showRouteModal = true;
                },
                
                editRoute(route) {
                    this.editingRoute = route;
                    this.routeForm = {
                        name: route.name,
                        code: route.code,
                        type: route.type,
                        materialId: route.materialId || '',
                        description: route.description || ''
                    };
                    this.showRouteModal = true;
                },
                
                saveRouteModal() {
                    if (this.editingRoute) {
                        // 更新现有工艺路线
                        const index = this.routes.findIndex(r => r.id === this.editingRoute.id);
                        if (index !== -1) {
                            this.routes[index] = {
                                ...this.routes[index],
                                name: this.routeForm.name,
                                code: this.routeForm.code,
                                type: this.routeForm.type,
                                materialId: this.routeForm.materialId,
                                description: this.routeForm.description,
                                updatedAt: new Date().toLocaleDateString('zh-CN')
                            };
                        }
                        this.showNotification('success', '更新成功', '工艺路线已成功更新');
                    } else {
                        // 创建新工艺路线
                        const newRoute = {
                            id: this.routes.length + 1,
                            code: this.routeForm.code,
                            name: this.routeForm.name,
                            type: this.routeForm.type,
                            material: this.materials.find(m => m.id === this.routeForm.materialId)?.name || '',
                            operationCount: 0,
                            status: 'draft',
                            updatedAt: new Date().toLocaleDateString('zh-CN'),
                            description: this.routeForm.description
                        };
                        
                        if (this.routeForm.type === 'parametric') {
                            newRoute.productParameters = [
                                { id: 1, name: '长度', unit: 'mm', defaultValue: '1000' },
                                { id: 2, name: '宽度', unit: 'mm', defaultValue: '800' },
                                { id: 3, name: '厚度', unit: 'mm', defaultValue: '5' }
                            ];
                            newRoute.timeFormula = '准备工时 + 单件工时 * (长度 * 宽度 / 1000000)';
                            newRoute.selectionRule = 'cost';
                        }
                        
                        if (this.routeForm.type === 'variant') {
                            newRoute.variants = [
                                { id: 1, name: '标准型', description: '标准生产工艺' }
                            ];
                        }
                        
                        this.routes.push(newRoute);
                        this.showNotification('success', '创建成功', '新工艺路线已成功创建');
                    }
                    
                    this.filterRoutes();
                    this.showRouteModal = false;
                },
                
                duplicateRoute(route) {
                    const newRoute = {
                        ...route,
                        id: this.routes.length + 1,
                        code: route.code + '_COPY',
                        name: route.name + ' (副本)',
                        status: 'draft',
                        updatedAt: new Date().toLocaleDateString('zh-CN')
                    };
                    
                    this.routes.push(newRoute);
                    this.filterRoutes();
                    this.showNotification('success', '复制成功', '工艺路线已成功复制');
                },
                
                deleteRoute(route) {
                    if (confirm(`确定要删除工艺路线 "${route.name}" 吗？`)) {
                        const index = this.routes.findIndex(r => r.id === route.id);
                        if (index !== -1) {
                            this.routes.splice(index, 1);
                            this.filterRoutes();
                            this.showNotification('success', '删除成功', '工艺路线已成功删除');
                        }
                    }
                },
                
                exportRoutes() {
                    this.showNotification('info', '导出中', '正在导出工艺路线数据...');
                    // 模拟导出操作
                    setTimeout(() => {
                        this.showNotification('success', '导出成功', '工艺路线数据已成功导出');
                    }, 1500);
                },
                
                // 设计层：工艺路线设计相关方法
                openDesigner(route) {
                    this.currentRoute = route;
                    this.currentView = 'designer';
                    
                    // 如果是新建工艺路线，初始化节点和连接
                    if (!route || route.operationCount === 0) {
                        this.processNodes = [
                            { id: 1, type: 'start', x: 50, y: 200, name: '开始' },
                            { id: 6, type: 'end', x: 950, y: 200, name: '结束' }
                        ];
                        this.connections = [];
                    }
                },
                
                dragStart(event, template) {
                    event.dataTransfer.setData('template', JSON.stringify(template));
                },
                
                dragEnd(event) {
                    // 拖拽结束处理
                },
                
                dropOnCanvas(event) {
                    event.preventDefault();
                    const templateData = event.dataTransfer.getData('template');
                    if (templateData) {
                        const template = JSON.parse(templateData);
                        const rect = event.currentTarget.getBoundingClientRect();
                        const x = event.clientX - rect.left - 80; // 节点宽度的一半
                        const y = event.clientY - rect.top - 40; // 节点高度的一半
                        
                        const newNode = {
                            id: this.processNodes.length + 1,
                            type: 'operation',
                            x: x,
                            y: y,
                            operationNumber: (this.processNodes.filter(n => n.type === 'operation').length + 1) * 10,
                            name: template.name,
                            workCenterId: this.workCenters[0]?.id || '',
                            setupTime: 30,
                            cycleTime: 5,
                            isOutsourced: false
                        };
                        
                        this.processNodes.push(newNode);
                        this.showNotification('success', '添加成功', `已添加工序 "${template.name}"`);
                    }
                },
                
                selectNode(nodeId) {
                    this.selectedNode = nodeId;
                    this.selectedConnection = null;
                    this.selectedNodeData = this.processNodes.find(n => n.id === nodeId);
                },
                
                selectConnection(connectionId) {
                    this.selectedConnection = connectionId;
                    this.selectedNode = null;
                    this.selectedNodeData = null;
                },
                
                clearSelection() {
                    this.selectedNode = null;
                    this.selectedConnection = null;
                    this.selectedNodeData = null;
                },
                
                startDrag(event, nodeId) {
                    // 开始拖拽节点
                    const node = this.processNodes.find(n => n.id === nodeId);
                    if (!node) return;
                    
                    const startX = event.clientX;
                    const startY = event.clientY;
                    const startNodeX = node.x;
                    const startNodeY = node.y;
                    
                    const handleMouseMove = (e) => {
                        const dx = e.clientX - startX;
                        const dy = e.clientY - startY;
                        node.x = startNodeX + dx;
                        node.y = startNodeY + dy;
                        
                        // 更新相关连接线
                        this.updateConnections();
                    };
                    
                    const handleMouseUp = () => {
                        document.removeEventListener('mousemove', handleMouseMove);
                        document.removeEventListener('mouseup', handleMouseUp);
                    };
                    
                    document.addEventListener('mousemove', handleMouseMove);
                    document.addEventListener('mouseup', handleMouseUp);
                },
                
                updateConnections() {
                    // 更新连接线的位置和角度
                    this.connections.forEach(conn => {
                        const fromNode = this.processNodes.find(n => n.id === conn.from);
                        const toNode = this.processNodes.find(n => n.id === conn.to);
                        
                        if (fromNode && toNode) {
                            // 计算节点中心点
                            const fromX = fromNode.type === 'start' || fromNode.type === 'end' ? 
                                fromNode.x + 40 : fromNode.x + 80;
                            const fromY = fromNode.y + 40;
                            
                            const toX = toNode.type === 'start' || toNode.type === 'end' ? 
                                toNode.x + 40 : toNode.x + 80;
                            const toY = toNode.y + 40;
                            
                            // 计算距离和角度
                            const dx = toX - fromX;
                            const dy = toY - fromY;
                            const length = Math.sqrt(dx * dx + dy * dy);
                            const angle = Math.atan2(dy, dx) * 180 / Math.PI;
                            
                            // 更新连接线数据
                            conn.x1 = fromX;
                            conn.y1 = fromY;
                            conn.x2 = toX;
                            conn.y2 = toY;
                            conn.length = length;
                            conn.angle = angle;
                        }
                    });
                },
                
                zoomIn() {
                    this.zoomLevel = Math.min(this.zoomLevel + 0.1, 2);
                },
                
                zoomOut() {
                    this.zoomLevel = Math.max(this.zoomLevel - 0.1, 0.5);
                },
                
                resetZoom() {
                    this.zoomLevel = 1;
                },
                
                undo() {
                    this.showNotification('info', '撤销', '撤销操作');
                },
                
                redo() {
                    this.showNotification('info', '重做', '重做操作');
                },
                
                validateRoute() {
                    // 验证工艺路线
                    const errors = [];
                    
                    // 检查是否有开始和结束节点
                    const hasStart = this.processNodes.some(n => n.type === 'start');
                    const hasEnd = this.processNodes.some(n => n.type === 'end');
                    
                    if (!hasStart) errors.push('缺少开始节点');
                    if (!hasEnd) errors.push('缺少结束节点');
                    
                    // 检查工序节点配置
                    this.processNodes.forEach(node => {
                        if (node.type === 'operation') {
                            if (!node.operationNumber) errors.push(`工序 "${node.name}" 缺少工序号`);
                            if (!node.name) errors.push('存在未命名的工序');
                            if (!node.workCenterId) errors.push(`工序 "${node.name}" 未选择工作中心`);
                        }
                    });
                    
                    if (errors.length > 0) {
                        this.showNotification('error', '验证失败', errors.join('; '));
                    } else {
                        this.showNotification('success', '验证通过', '工艺路线验证通过，可以保存');
                    }
                },
                
                saveRoute() {
                    // 保存工艺路线
                    if (this.currentRoute) {
                        this.currentRoute.operationCount = this.processNodes.filter(n => n.type === 'operation').length;
                        this.currentRoute.updatedAt = new Date().toLocaleDateString('zh-CN');
                        this.showNotification('success', '保存成功', '工艺路线已成功保存');
                    } else {
                        this.showNotification('error', '保存失败', '请先选择或创建工艺路线');
                    }
                },
                
                addParameter(param) {
                    this.showNotification('info', '添加参数', `已添加参数 "${param.name}"`);
                },
                
                addTooling(tool) {
                    this.showNotification('info', '添加工装', `已添加工装 "${tool.name}"`);
                },
                
                selectWorkCenter(center) {
                    if (this.selectedNodeData) {
                        this.selectedNodeData.workCenterId = center.id;
                        this.showNotification('info', '选择工作中心', `已选择工作中心 "${center.name}"`);
                    }
                },
                
                calculateTotalTime() {
                    let totalTime = 0;
                    this.processNodes.forEach(node => {
                        if (node.type === 'operation') {
                            totalTime += (node.setupTime || 0) + (node.cycleTime || 0);
                        }
                    });
                    return totalTime;
                },
                
                countOutsourcedOperations() {
                    return this.processNodes.filter(n => n.type === 'operation' && n.isOutsourced).length;
                },
                
                addVariant() {
                    if (this.currentRoute && this.currentRoute.type === 'variant') {
                        if (!this.currentRoute.variants) {
                            this.currentRoute.variants = [];
                        }
                        
                        const newVariant = {
                            id: this.currentRoute.variants.length + 1,
                            name: `变体${this.currentRoute.variants.length + 1}`,
                            description: '新变体描述'
                        };
                        
                        this.currentRoute.variants.push(newVariant);
                        this.showNotification('success', '添加成功', '已添加新的路线变体');
                    }
                },
                
                deleteVariant(variantId) {
                    if (this.currentRoute && this.currentRoute.variants) {
                        const index = this.currentRoute.variants.findIndex(v => v.id === variantId);
                        if (index !== -1) {
                            this.currentRoute.variants.splice(index, 1);
                            this.showNotification('success', '删除成功', '已删除路线变体');
                        }
                    }
                },
                
                // 基础层：工序标准管理相关方法
                openOperations() {
                    this.currentView = 'operations';
                },
                
                filterOperations() {
                    this.filteredOperations = this.operations.filter(operation => {
                        const matchSearch = !this.operationSearchQuery || 
                            operation.name.toLowerCase().includes(this.operationSearchQuery.toLowerCase()) ||
                            operation.code.toLowerCase().includes(this.operationSearchQuery.toLowerCase());
                        
                        const matchType = !this.operationFilterType || operation.type === this.operationFilterType;
                        
                        const matchCategory = !this.selectedCategory || operation.categoryId === this.selectedCategory;
                        
                        return matchSearch && matchType && matchCategory;
                    });
                },
                
                selectCategory(categoryId) {
                    // 切换分类展开状态
                    const toggleExpand = (categories) => {
                        categories.forEach(cat => {
                            if (cat.id === categoryId) {
                                cat.expanded = !cat.expanded;
                            } else if (cat.children) {
                                toggleExpand(cat.children);
                            }
                        });
                    };
                    
                    toggleExpand(this.operationCategories);
                    
                    // 设置选中的分类
                    this.selectedCategory = categoryId;
                    this.filterOperations();
                },
                
                createNewOperation() {
                    this.editingOperation = null;
                    // 重置表单数据
                    this.resetOperationForm();
                    this.showOperationModal = true;
                },
                
                editOperation(operation) {
                    this.editingOperation = operation;
                    this.operationForm = {
                        code: operation.code,
                        name: operation.name,
                        type: operation.type,
                        categoryId: operation.categoryId,
                        description: operation.description,
                        setupTime: operation.setupTime,
                        cycleTime: operation.cycleTime,
                        teardownTime: operation.teardownTime,
                        qualityStandard: operation.qualityStandard,
                        workInstructions: operation.workInstructions
                    };
                    this.showOperationModal = true;
                },
                
                saveOperationModal() {
                    if (this.editingOperation) {
                        // 更新现有工序
                        const index = this.operations.findIndex(o => o.id === this.editingOperation.id);
                        if (index !== -1) {
                            this.operations[index] = {
                                ...this.operations[index],
                                code: this.operationForm.code,
                                name: this.operationForm.name,
                                type: this.operationForm.type,
                                categoryId: this.operationForm.categoryId,
                                description: this.operationForm.description,
                                setupTime: this.operationForm.setupTime,
                                cycleTime: this.operationForm.cycleTime,
                                teardownTime: this.operationForm.teardownTime,
                                qualityStandard: this.operationForm.qualityStandard,
                                workInstructions: this.operationForm.workInstructions,
                                updatedAt: new Date().toLocaleDateString('zh-CN')
                            };
                        }
                        this.showNotification('success', '更新成功', '工序已成功更新');
                    } else {
                        // 创建新工序
                        const newOperation = {
                            id: this.operations.length + 1,
                            code: this.operationForm.code,
                            name: this.operationForm.name,
                            type: this.operationForm.type,
                            categoryId: this.operationForm.categoryId,
                            description: this.operationForm.description,
                            setupTime: this.operationForm.setupTime,
                            cycleTime: this.operationForm.cycleTime,
                            teardownTime: this.operationForm.teardownTime,
                            qualityStandard: this.operationForm.qualityStandard,
                            workInstructions: this.operationForm.workInstructions,
                            status: 'draft',
                            updatedAt: new Date().toLocaleDateString('zh-CN')
                        };
                        
                        this.operations.push(newOperation);
                        this.showNotification('success', '创建成功', '新工序已成功创建');
                    }
                    
                    this.filterOperations();
                    this.showOperationModal = false;
                },
                
                deleteOperation(operation) {
                    if (confirm(`确定要删除工序 "${operation.name}" 吗？`)) {
                        const index = this.operations.findIndex(o => o.id === operation.id);
                        if (index !== -1) {
                            this.operations.splice(index, 1);
                            this.filterOperations();
                            this.showNotification('success', '删除成功', '工序已成功删除');
                        }
                    }
                },
                
                exportOperations() {
                    this.showNotification('info', '导出中', '正在导出工序数据...');
                    // 模拟导出操作
                    setTimeout(() => {
                        this.showNotification('success', '导出成功', '工序数据已成功导出');
                    }, 1500);
                },
                
                flattenOperationCategories() {
                    const flatten = (categories, result = []) => {
                        categories.forEach(cat => {
                            result.push(cat);
                            if (cat.children && cat.children.length > 0) {
                                flatten(cat.children, result);
                            }
                        });
                        return result;
                    };
                    
                    return flatten(this.operationCategories);
                },
                
                // 执行层：外协工序管理相关方法
                openOutsourcing() {
                    this.currentView = 'outsourcing';
                },
                
                selectOutsourcingRoute(routeId) {
                    this.selectedOutsourcingRoute = routeId;
                    this.selectedOutsourcingOperation = null;
                    this.selectedOutsourcingOperationData = null;
                },
                
                selectOutsourcingOperation(operationId) {
                    this.selectedOutsourcingOperation = operationId;
                    this.selectedOutsourcingOperationData = this.processNodes.find(n => n.id === operationId);
                },
                
                getRouteOperations(routeId) {
                    // 根据工艺路线ID获取工序列表
                    // 这里简化处理，实际应用中需要根据工艺路线ID获取对应的工序
                    return this.processNodes.filter(n => n.type === 'operation');
                },
                
                getOutsourcingCount(routeId) {
                    const operations = this.getRouteOperations(routeId);
                    return operations.filter(o => o.isOutsourced).length;
                },
                
                saveOutsourcingConfig() {
                    if (this.selectedOutsourcingOperationData) {
                        this.showNotification('success', '保存成功', '外协配置已成功保存');
                    } else {
                        this.showNotification('error', '保存失败', '请先选择工序');
                    }
                },
                
                addBackupSupplier() {
                    if (this.newBackupSupplierId && this.selectedOutsourcingOperationData) {
                        if (!this.selectedOutsourcingOperationData.backupSupplierIds) {
                            this.selectedOutsourcingOperationData.backupSupplierIds = [];
                        }
                        
                        if (!this.selectedOutsourcingOperationData.backupSupplierIds.includes(this.newBackupSupplierId)) {
                            this.selectedOutsourcingOperationData.backupSupplierIds.push(this.newBackupSupplierId);
                            this.showNotification('success', '添加成功', '备选供应商已成功添加');
                        }
                        
                        this.newBackupSupplierId = '';
                        this.showAddBackupSupplierModal = false;
                    }
                },
                
                removeBackupSupplier(supplierId) {
                    if (this.selectedOutsourcingOperationData && this.selectedOutsourcingOperationData.backupSupplierIds) {
                        const index = this.selectedOutsourcingOperationData.backupSupplierIds.indexOf(supplierId);
                        if (index !== -1) {
                            this.selectedOutsourcingOperationData.backupSupplierIds.splice(index, 1);
                            this.showNotification('success', '删除成功', '备选供应商已成功删除');
                        }
                    }
                },
                
                getSupplierName(supplierId) {
                    const supplier = this.suppliers.find(s => s.id === supplierId);
                    return supplier ? supplier.name : '';
                },
                
                calculateOutsourcingCost() {
                    if (!this.selectedOutsourcingOperationData || !this.selectedOutsourcingOperationData.isOutsourced) {
                        return 0;
                    }
                    
                    // 简化计算，实际应用中需要根据计价方式和数量计算
                    return this.selectedOutsourcingOperationData.unitPrice || 0;
                },
                
                calculateInternalCost() {
                    if (!this.selectedOutsourcingOperationData) {
                        return 0;
                    }
                    
                    // 简化计算，实际应用中需要根据工时和费率计算
                    const setupTime = this.selectedOutsourcingOperationData.setupTime || 0;
                    const cycleTime = this.selectedOutsourcingOperationData.cycleTime || 0;
                    const hourlyRate = 100; // 假设每小时费率为100元
                    
                    return ((setupTime + cycleTime) / 60) * hourlyRate;
                },
                
                calculateCostDifference() {
                    const outsourcingCost = this.calculateOutsourcingCost();
                    const internalCost = this.calculateInternalCost();
                    
                    return (outsourcingCost - internalCost).toFixed(2);
                },
                
                syncToProcurement() {
                    this.showNotification('info', '同步中', '正在同步外协数据到采购系统...');
                    // 模拟同步操作
                    setTimeout(() => {
                        this.showNotification('success', '同步成功', '外协数据已成功同步到采购系统');
                    }, 2000);
                },
                
                // 文件上传处理
                handleFileUpload(event) {
                    const file = event.target.files[0];
                    if (file) {
                        this.showNotification('info', '上传中', `正在上传文件 "${file.name}"...`);
                        // 模拟上传操作
                        setTimeout(() => {
                            this.showNotification('success', '上传成功', '文件已成功上传并处理');
                            this.showImportModal = false;
                        }, 1500);
                    }
                },
                
                handleOperationFileUpload(event) {
                    const file = event.target.files[0];
                    if (file) {
                        this.showNotification('info', '上传中', `正在上传文件 "${file.name}"...`);
                        // 模拟上传操作
                        setTimeout(() => {
                            this.showNotification('success', '上传成功', '文件已成功上传并处理');
                            this.showImportOperationModal = false;
                        }, 1500);
                    }
                },
                
                // 通知相关方法
                showNotification(type, title, message) {
                    // 清除之前的定时器
                    if (this.notification.timeout) {
                        clearTimeout(this.notification.timeout);
                    }
                    
                    // 设置新通知
                    this.notification.type = type;
                    this.notification.title = title;
                    this.notification.message = message;
                    this.notification.show = true;
                    
                    // 设置自动关闭定时器
                    this.notification.timeout = setTimeout(() => {
                        this.notification.show = false;
                    }, 3000);
                },
                
                // 辅助方法
                getTypeLabel(type) {
                    const labels = {
                        'standard': '标准路线',
                        'parametric': '参数化路线',
                        'variant': '变体路线'
                    };
                    return labels[type] || type;
                },
                
                getStatusLabel(status) {
                    const labels = {
                        'draft': '草稿',
                        'active': '生效',
                        'inactive': '停用'
                    };
                    return labels[status] || status;
                },
                
                getOperationTypeLabel(type) {
                    const labels = {
                        'cutting': '切割',
                        'grinding': '磨边',
                        'tempering': '钢化',
                        'laminating': '合片'
                    };
                    return labels[type] || type;
                }
            };
        }
    </script>
</body>
</html>
