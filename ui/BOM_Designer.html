<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>参数化BOM设计器 - 工艺管理子系统</title>
    
    <!-- TailwindCSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom Styles -->
    <style>
        [x-cloak] { display: none !important; }
        
        .bom-node {
            transition: all 0.2s ease;
        }
        
        .bom-node:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .component-node {
            border-left: 4px solid #3b82f6;
        }
        
        .material-node {
            border-left: 4px solid #10b981;
        }
        
        .sub-material-node {
            border-left: 4px solid #f59e0b;
        }
        
        .connection-line {
            stroke: #9ca3af;
            stroke-width: 2;
            fill: none;
        }
        
        .dragging {
            opacity: 0.5;
        }
        
        .drag-over {
            background-color: #eff6ff;
            border: 2px dashed #3b82f6;
        }
        
        .formula-editor {
            font-family: 'Courier New', monospace;
            background-color: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 0.375rem;
            padding: 0.5rem;
            min-height: 100px;
        }
        
        .param-tag {
            display: inline-block;
            background-color: #dbeafe;
            color: #1d4ed8;
            padding: 0.125rem 0.5rem;
            border-radius: 0.25rem;
            margin: 0.125rem;
            cursor: pointer;
            font-size: 0.875rem;
        }
        
        .param-tag:hover {
            background-color: #bfdbfe;
        }
        
        .version-tab {
            transition: all 0.2s ease;
        }
        
        .version-tab.active {
            background-color: #3b82f6;
            color: white;
        }
        
        .canvas-container {
            background-image: 
                linear-gradient(rgba(0, 0, 0, 0.05) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
            background-size: 20px 20px;
        }
    </style>
</head>
<body class="bg-gray-50" x-data="bomDesigner()">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-800">参数化BOM设计器</h1>
                    <span class="ml-3 px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">工艺管理子系统</span>
                </div>
                <div class="flex items-center space-x-4">
                    <button @click="showHelp = true" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-question-circle"></i>
                    </button>
                    <button @click="saveBom" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        <i class="fas fa-save mr-2"></i>保存BOM
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex flex-col h-screen pt-1">
        <!-- Product Selection Bar -->
        <div class="bg-white border-b border-gray-200 px-4 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div>
                        <label class="text-sm font-medium text-gray-700">产品类型</label>
                        <select x-model="selectedProductType" @change="loadProductTemplate" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                            <option value="fireproof-window">防火窗</option>
                            <option value="shower-room">淋浴房</option>
                            <option value="glass-product">玻璃制品</option>
                        </select>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-700">产品名称</label>
                        <input type="text" x-model="productName" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-700">产品编码</label>
                        <input type="text" x-model="productCode" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <button @click="validateBom" class="px-3 py-1.5 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                        <i class="fas fa-check-circle mr-1"></i>验证
                    </button>
                    <button @click="calculateBom" class="px-3 py-1.5 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                        <i class="fas fa-calculator mr-1"></i>计算
                    </button>
                </div>
            </div>
        </div>

        <!-- Version Tabs -->
        <div class="bg-white border-b border-gray-200 px-4 py-2">
            <div class="flex items-center space-x-2">
                <template x-for="(version, index) in bomVersions" :key="index">
                    <button 
                        @click="activeVersion = index" 
                        :class="{'active': activeVersion === index}"
                        class="version-tab px-4 py-2 text-sm font-medium rounded-md focus:outline-none"
                        x-text="`版本 ${version.version}`">
                    </button>
                </template>
                <button @click="addNewVersion" class="ml-2 px-3 py-1.5 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                    <i class="fas fa-plus mr-1"></i>新增版本
                </button>
                <button @click="compareVersions" class="ml-2 px-3 py-1.5 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                    <i class="fas fa-columns mr-1"></i>版本对比
                </button>
            </div>
        </div>

        <!-- Main Layout -->
        <div class="flex flex-1 overflow-hidden">
            <!-- Left Panel - Material Library -->
            <div class="w-64 bg-white border-r border-gray-200 flex flex-col">
                <div class="p-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-800">物料库</h2>
                    <div class="mt-2">
                        <input type="text" x-model="materialSearch" placeholder="搜索物料..." class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    </div>
                </div>
                
                <div class="flex-1 overflow-y-auto">
                    <div class="p-2">
                        <template x-for="category in materialCategories" :key="category.id">
                            <div class="mb-4">
                                <h3 class="px-2 py-1 text-sm font-medium text-gray-700 bg-gray-100 rounded-md" x-text="category.name"></h3>
                                <div class="mt-1 space-y-1">
                                    <template x-for="material in category.materials.filter(m => m.name.toLowerCase().includes(materialSearch.toLowerCase()))" :key="material.id">
                                        <div 
                                            draggable="true"
                                            @dragstart="dragStart($event, material)"
                                            @dragend="dragEnd"
                                            class="p-2 bg-white border border-gray-200 rounded-md cursor-move hover:bg-gray-50"
                                        >
                                            <div class="flex items-center">
                                                <i :class="material.icon" class="text-gray-500 mr-2"></i>
                                                <span class="text-sm" x-text="material.name"></span>
                                            </div>
                                            <div class="text-xs text-gray-500 mt-1" x-text="material.code"></div>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>

            <!-- Center Panel - BOM Structure Canvas -->
            <div class="flex-1 flex flex-col overflow-hidden">
                <div class="bg-white border-b border-gray-200 p-2 flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        <button @click="zoomIn" class="p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded">
                            <i class="fas fa-search-plus"></i>
                        </button>
                        <button @click="zoomOut" class="p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded">
                            <i class="fas fa-search-minus"></i>
                        </button>
                        <button @click="resetZoom" class="p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded">
                            <i class="fas fa-compress"></i>
                        </button>
                        <span class="text-sm text-gray-500" x-text="`缩放: ${Math.round(zoomLevel * 100)}%`"></span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button @click="expandAll" class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">
                            <i class="fas fa-expand-alt mr-1"></i>全部展开
                        </button>
                        <button @click="collapseAll" class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">
                            <i class="fas fa-compress-alt mr-1"></i>全部折叠
                        </button>
                    </div>
                </div>
                
                <div class="flex-1 overflow-auto canvas-container p-4" 
                     :style="`transform: scale(${zoomLevel}); transform-origin: top left;`"
                     @dragover.prevent
                     @drop="dropOnCanvas($event)">
                    <div class="min-w-full min-h-full">
                        <!-- BOM Structure Tree -->
                        <div class="bg-white rounded-lg shadow-sm p-4 max-w-4xl mx-auto">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-medium text-gray-800">BOM结构树</h3>
                                <div class="text-sm text-gray-500">
                                    <span x-text="`构件: ${componentCount} | 物料: ${materialCount} | 辅料: ${subMaterialCount}`"></span>
                                </div>
                            </div>
                            
                            <div class="space-y-2">
                                <!-- Product Node -->
                                <div class="bom-node bg-blue-50 border border-blue-200 rounded-lg p-3">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <i class="fas fa-cube text-blue-600 mr-2"></i>
                                            <span class="font-medium" x-text="productName || '未命名产品'"></span>
                                            <span class="ml-2 text-sm text-gray-500" x-text="productCode"></span>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <button @click="addComponent" class="text-blue-600 hover:text-blue-800">
                                                <i class="fas fa-plus-circle"></i>
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <!-- Components -->
                                    <div class="mt-3 ml-6 space-y-3">
                                        <template x-for="(component, index) in currentBom.components" :key="component.id">
                                            <div class="component-node bg-white border border-gray-200 rounded-lg p-3">
                                                <div class="flex items-center justify-between">
                                                    <div class="flex items-center">
                                                        <button @click="component.expanded = !component.expanded" class="mr-2 text-gray-500">
                                                            <i :class="component.expanded ? 'fas fa-chevron-down' : 'fas fa-chevron-right'"></i>
                                                        </button>
                                                        <i class="fas fa-puzzle-piece text-blue-500 mr-2"></i>
                                                        <span class="font-medium" x-text="component.name"></span>
                                                    </div>
                                                    <div class="flex items-center space-x-2">
                                                        <button @click="editComponent(index)" class="text-gray-500 hover:text-gray-700">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button @click="removeComponent(index)" class="text-red-500 hover:text-red-700">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                        <button @click="addMaterial(index)" class="text-green-600 hover:text-green-800">
                                                            <i class="fas fa-plus-circle"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                                
                                                <!-- Component Parameters -->
                                                <div class="mt-2 ml-6 flex flex-wrap gap-1">
                                                    <template x-for="param in component.parameters" :key="param.name">
                                                        <div class="param-tag" x-text="`${param.name}: ${param.value}`"></div>
                                                    </template>
                                                </div>
                                                
                                                <!-- Materials -->
                                                <div x-show="component.expanded" class="mt-3 ml-6 space-y-2">
                                                    <template x-for="(material, mIndex) in component.materials" :key="material.id">
                                                        <div class="material-node bg-gray-50 border border-gray-200 rounded-lg p-2">
                                                            <div class="flex items-center justify-between">
                                                                <div class="flex items-center">
                                                                    <button @click="material.expanded = !material.expanded" class="mr-2 text-gray-500">
                                                                        <i :class="material.expanded ? 'fas fa-chevron-down' : 'fas fa-chevron-right'"></i>
                                                                    </button>
                                                                    <i class="fas fa-box text-green-500 mr-2"></i>
                                                                    <span x-text="material.name"></span>
                                                                    <span class="ml-2 text-sm text-gray-500" x-text="material.code"></span>
                                                                </div>
                                                                <div class="flex items-center space-x-2">
                                                                    <button @click="editMaterial(index, mIndex)" class="text-gray-500 hover:text-gray-700">
                                                                        <i class="fas fa-edit"></i>
                                                                    </button>
                                                                    <button @click="removeMaterial(index, mIndex)" class="text-red-500 hover:text-red-700">
                                                                        <i class="fas fa-trash"></i>
                                                                    </button>
                                                                    <button @click="addSubMaterial(index, mIndex)" class="text-green-600 hover:text-green-800">
                                                                        <i class="fas fa-plus-circle"></i>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                            
                                                            <!-- Material Formula -->
                                                            <div class="mt-1 ml-6 text-sm">
                                                                <span class="text-gray-600">用量: </span>
                                                                <span class="font-mono" x-text="material.formula"></span>
                                                                <span class="ml-2 text-gray-500" x-text="`=${material.calculatedQuantity}`"></span>
                                                            </div>
                                                            
                                                            <!-- Sub-materials -->
                                                            <div x-show="material.expanded" class="mt-2 ml-6 space-y-1">
                                                                <template x-for="(subMaterial, sIndex) in material.subMaterials" :key="subMaterial.id">
                                                                    <div class="sub-material-node bg-white border border-gray-200 rounded-lg p-2">
                                                                        <div class="flex items-center justify-between">
                                                                            <div class="flex items-center">
                                                                                <i class="fas fa-cog text-yellow-500 mr-2"></i>
                                                                                <span x-text="subMaterial.name"></span>
                                                                                <span class="ml-2 text-sm text-gray-500" x-text="subMaterial.code"></span>
                                                                            </div>
                                                                            <div class="flex items-center space-x-2">
                                                                                <button @click="editSubMaterial(index, mIndex, sIndex)" class="text-gray-500 hover:text-gray-700">
                                                                                    <i class="fas fa-edit"></i>
                                                                                </button>
                                                                                <button @click="removeSubMaterial(index, mIndex, sIndex)" class="text-red-500 hover:text-red-700">
                                                                                    <i class="fas fa-trash"></i>
                                                                                </button>
                                                                            </div>
                                                                        </div>
                                                                        
                                                                        <!-- Sub-material Formula -->
                                                                        <div class="mt-1 ml-6 text-sm">
                                                                            <span class="text-gray-600">用量: </span>
                                                                            <span class="font-mono" x-text="subMaterial.formula"></span>
                                                                            <span class="ml-2 text-gray-500" x-text="`=${subMaterial.calculatedQuantity}`"></span>
                                                                        </div>
                                                                    </div>
                                                                </template>
                                                            </div>
                                                        </div>
                                                    </template>
                                                </div>
                                            </div>
                                        </template>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Properties -->
            <div class="w-80 bg-white border-l border-gray-200 flex flex-col">
                <div class="p-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-800">属性面板</h2>
                </div>
                
                <div class="flex-1 overflow-y-auto p-4">
                    <div x-show="!selectedNode" class="text-center text-gray-500 py-8">
                        <i class="fas fa-mouse-pointer text-3xl mb-2"></i>
                        <p>请选择一个节点查看属性</p>
                    </div>
                    
                    <!-- Product Properties -->
                    <div x-show="selectedNodeType === 'product'" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">产品参数</label>
                            <div class="space-y-2">
                                <template x-for="param in productParameters" :key="param.name">
                                    <div class="flex items-center">
                                        <span class="w-24 text-sm text-gray-600" x-text="param.name + ':'"></span>
                                        <input type="text" x-model="param.value" @input="updateCalculations" class="flex-1 px-2 py-1 border border-gray-300 rounded-md text-sm">
                                        <span class="ml-2 text-sm text-gray-500" x-text="param.unit"></span>
                                    </div>
                                </template>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">适用条件</label>
                            <textarea x-model="applicableConditions" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"></textarea>
                        </div>
                    </div>
                    
                    <!-- Component Properties -->
                    <div x-show="selectedNodeType === 'component'" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">构件名称</label>
                            <input type="text" x-model="selectedNode.name" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">构件参数</label>
                            <div class="space-y-2">
                                <template x-for="(param, index) in selectedNode.parameters" :key="index">
                                    <div class="flex items-center">
                                        <input type="text" x-model="param.name" placeholder="参数名" class="w-24 px-2 py-1 border border-gray-300 rounded-md text-sm">
                                        <input type="text" x-model="param.value" placeholder="值" @input="updateCalculations" class="flex-1 mx-2 px-2 py-1 border border-gray-300 rounded-md text-sm">
                                        <input type="text" x-model="param.unit" placeholder="单位" class="w-16 px-2 py-1 border border-gray-300 rounded-md text-sm">
                                        <button @click="selectedNode.parameters.splice(index, 1)" class="ml-2 text-red-500 hover:text-red-700">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </template>
                                <button @click="selectedNode.parameters.push({name: '', value: '', unit: ''})" class="text-sm text-blue-600 hover:text-blue-800">
                                    <i class="fas fa-plus mr-1"></i>添加参数
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Material Properties -->
                    <div x-show="selectedNodeType === 'material'" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">物料名称</label>
                            <input type="text" x-model="selectedNode.name" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">物料编码</label>
                            <input type="text" x-model="selectedNode.code" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">用量公式</label>
                            <div class="formula-editor" contenteditable="true" 
                                 @blur="selectedNode.formula = $event.textContent; updateCalculations()"
                                 x-text="selectedNode.formula"></div>
                            <div class="mt-2">
                                <p class="text-sm text-gray-600 mb-1">可用参数:</p>
                                <div class="flex flex-wrap">
                                    <template x-for="param in availableParameters" :key="param.name">
                                        <span @click="insertParameter(param.name)" class="param-tag cursor-pointer" x-text="param.name"></span>
                                    </template>
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">计算结果</label>
                            <div class="px-3 py-2 bg-gray-100 rounded-md">
                                <span class="font-mono" x-text="selectedNode.calculatedQuantity"></span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Sub-material Properties -->
                    <div x-show="selectedNodeType === 'subMaterial'" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">辅料名称</label>
                            <input type="text" x-model="selectedNode.name" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">辅料编码</label>
                            <input type="text" x-model="selectedNode.code" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">用量公式</label>
                            <div class="formula-editor" contenteditable="true" 
                                 @blur="selectedNode.formula = $event.textContent; updateCalculations()"
                                 x-text="selectedNode.formula"></div>
                            <div class="mt-2">
                                <p class="text-sm text-gray-600 mb-1">可用参数:</p>
                                <div class="flex flex-wrap">
                                    <template x-for="param in availableParameters" :key="param.name">
                                        <span @click="insertParameter(param.name)" class="param-tag cursor-pointer" x-text="param.name"></span>
                                    </template>
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">计算结果</label>
                            <div class="px-3 py-2 bg-gray-100 rounded-md">
                                <span class="font-mono" x-text="selectedNode.calculatedQuantity"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Help Modal -->
    <div x-show="showHelp" x-cloak class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click.self="showHelp = false">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-semibold text-gray-800">参数化BOM设计器使用指南</h3>
                    <button @click="showHelp = false" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="space-y-4">
                    <div>
                        <h4 class="font-medium text-gray-800 mb-2">基本概念</h4>
                        <p class="text-gray-600">参数化BOM设计器支持按照构件方式组织物料清单，结构为：产品 → 构件 → 原料/半成品 → 原料/辅料。</p>
                    </div>
                    
                    <div>
                        <h4 class="font-medium text-gray-800 mb-2">操作步骤</h4>
                        <ol class="list-decimal pl-5 space-y-1 text-gray-600">
                            <li>选择产品类型并填写产品信息</li>
                            <li>添加构件（如窗框、窗扇、固定玻璃）</li>
                            <li>为每个构件添加原料/半成品</li>
                            <li>为原料/半成品添加辅料</li>
                            <li>设置参数和用量公式</li>
                            <li>验证和计算BOM</li>
                            <li>保存BOM设计</li>
                        </ol>
                    </div>
                    
                    <div>
                        <h4 class="font-medium text-gray-800 mb-2">参数化公式</h4>
                        <p class="text-gray-600">用量公式支持基本数学运算和参数引用，例如：</p>
                        <ul class="list-disc pl-5 space-y-1 text-gray-600 mt-2">
                            <li><code class="bg-gray-100 px-1 rounded">L * W * T</code> - 使用长度、宽度、厚度参数计算体积</li>
                            <li><code class="bg-gray-100 px-1 rounded">(L + W) * 2</code> - 计算周长</li>
                            <li><code class="bg-gray-100 px-1 rounded">count * 0.1</code> - 使用数量参数计算</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4 class="font-medium text-gray-800 mb-2">版本管理</h4>
                        <p class="text-gray-600">支持为同一产品创建多个BOM版本，可以设置适用条件，系统会根据条件自动选择合适的版本。</p>
                    </div>
                </div>
                
                <div class="mt-6 flex justify-end">
                    <button @click="showHelp = false" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Version Comparison Modal -->
    <div x-show="showVersionComparison" x-cloak class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click.self="showVersionComparison = false">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-semibold text-gray-800">BOM版本对比</h3>
                    <button @click="showVersionComparison = false" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="grid grid-cols-2 gap-4">
                    <template x-for="(version, index) in bomVersions" :key="index">
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h4 class="font-medium text-gray-800 mb-2" x-text="`版本 ${version.version}`"></h4>
                            <div class="space-y-2">
                                <div class="text-sm">
                                    <span class="text-gray-600">构件数量: </span>
                                    <span x-text="version.components.length"></span>
                                </div>
                                <div class="text-sm">
                                    <span class="text-gray-600">物料数量: </span>
                                    <span x-text="version.components.reduce((sum, comp) => sum + comp.materials.length, 0)"></span>
                                </div>
                                <div class="text-sm">
                                    <span class="text-gray-600">辅料数量: </span>
                                    <span x-text="version.components.reduce((sum, comp) => sum + comp.materials.reduce((subSum, mat) => subSum + mat.subMaterials.length, 0), 0)"></span>
                                </div>
                                <div class="text-sm">
                                    <span class="text-gray-600">适用条件: </span>
                                    <span x-text="version.conditions || '无'"></span>
                                </div>
                            </div>
                        </div>
                    </template>
                </div>
                
                <div class="mt-6">
                    <h4 class="font-medium text-gray-800 mb-2">差异分析</h4>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-gray-600">版本对比功能将帮助您识别不同BOM版本之间的差异，包括构件、物料和辅料的变化。</p>
                    </div>
                </div>
                
                <div class="mt-6 flex justify-end">
                    <button @click="showVersionComparison = false" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Calculation Result Modal -->
    <div x-show="showCalculationResult" x-cloak class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click.self="showCalculationResult = false">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-semibold text-gray-800">BOM计算结果</h3>
                    <button @click="showCalculationResult = false" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="space-y-4">
                    <div>
                        <h4 class="font-medium text-gray-800 mb-2">物料清单汇总</h4>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">物料编码</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">物料名称</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单位</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用量</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <template x-for="item in calculationResults" :key="item.code">
                                        <tr>
                                            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500" x-text="item.code"></td>
                                            <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900" x-text="item.name"></td>
                                            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500" x-text="item.unit"></td>
                                            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900" x-text="item.quantity"></td>
                                        </tr>
                                    </template>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="font-medium text-gray-800 mb-2">计算日志</h4>
                        <div class="bg-gray-50 rounded-lg p-4 max-h-40 overflow-y-auto">
                            <template x-for="log in calculationLogs" :key="log">
                                <div class="text-sm text-gray-600 mb-1" x-text="log"></div>
                            </template>
                        </div>
                    </div>
                </div>
                
                <div class="mt-6 flex justify-end">
                    <button @click="exportCalculationResult" class="mr-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                        <i class="fas fa-download mr-2"></i>导出结果
                    </button>
                    <button @click="showCalculationResult = false" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function bomDesigner() {
            return {
                // Product info
                selectedProductType: 'fireproof-window',
                productName: '防火窗',
                productCode: 'FW-2023-001',
                productParameters: [
                    { name: 'L', value: '1200', unit: 'mm' },
                    { name: 'W', value: '800', unit: 'mm' },
                    { name: 'T', value: '50', unit: 'mm' }
                ],
                applicableConditions: '标准防火窗，适用于一般建筑',
                
                // BOM versions
                bomVersions: [
                    {
                        version: '1.0',
                        conditions: '标准版本',
                        components: [
                            {
                                id: 'comp-1',
                                name: '窗框',
                                expanded: true,
                                parameters: [
                                    { name: '材质', value: '钢质', unit: '' },
                                    { name: '厚度', value: '1.5', unit: 'mm' }
                                ],
                                materials: [
                                    {
                                        id: 'mat-1',
                                        name: '窗框型材',
                                        code: 'XF-001',
                                        expanded: true,
                                        formula: '(L + W) * 2 / 1000',
                                        calculatedQuantity: '4.00',
                                        unit: 'm',
                                        subMaterials: [
                                            {
                                                id: 'sub-1',
                                                name: '型材切割',
                                                code: 'XF-001-CUT',
                                                formula: '1',
                                                calculatedQuantity: '1',
                                                unit: '次'
                                            },
                                            {
                                                id: 'sub-2',
                                                name: '型材焊接',
                                                code: 'XF-001-WELD',
                                                formula: '4',
                                                calculatedQuantity: '4',
                                                unit: '个'
                                            }
                                        ]
                                    },
                                    {
                                        id: 'mat-2',
                                        name: '窗框角码',
                                        code: 'XF-002',
                                        expanded: true,
                                        formula: '4',
                                        calculatedQuantity: '4',
                                        unit: '个',
                                        subMaterials: [
                                            {
                                                id: 'sub-3',
                                                name: '角码连接',
                                                code: 'XF-002-CONN',
                                                formula: '1',
                                                calculatedQuantity: '1',
                                                unit: '次'
                                            }
                                        ]
                                    }
                                ]
                            },
                            {
                                id: 'comp-2',
                                name: '窗扇',
                                expanded: true,
                                parameters: [
                                    { name: '材质', value: '钢质', unit: '' },
                                    { name: '厚度', value: '1.2', unit: 'mm' }
                                ],
                                materials: [
                                    {
                                        id: 'mat-3',
                                        name: '窗扇型材',
                                        code: 'CS-001',
                                        expanded: true,
                                        formula: '((L - 20) + (W - 20)) * 2 / 1000',
                                        calculatedQuantity: '3.92',
                                        unit: 'm',
                                        subMaterials: [
                                            {
                                                id: 'sub-4',
                                                name: '型材切割',
                                                code: 'CS-001-CUT',
                                                formula: '1',
                                                calculatedQuantity: '1',
                                                unit: '次'
                                            }
                                        ]
                                    },
                                    {
                                        id: 'mat-4',
                                        name: '窗扇玻璃',
                                        code: 'CS-002',
                                        expanded: true,
                                        formula: '(L - 40) * (W - 40) * T / 1000000',
                                        calculatedQuantity: '0.046',
                                        unit: 'm³',
                                        subMaterials: [
                                            {
                                                id: 'sub-5',
                                                name: '玻璃切割',
                                                code: 'CS-002-CUT',
                                                formula: '1',
                                                calculatedQuantity: '1',
                                                unit: '次'
                                            }
                                        ]
                                    },
                                    {
                                        id: 'mat-5',
                                        name: '窗扇五金',
                                        code: 'CS-003',
                                        expanded: true,
                                        formula: '1',
                                        calculatedQuantity: '1',
                                        unit: '套',
                                        subMaterials: [
                                            {
                                                id: 'sub-6',
                                                name: '铰链',
                                                code: 'CS-003-HINGE',
                                                formula: '2',
                                                calculatedQuantity: '2',
                                                unit: '个'
                                            },
                                            {
                                                id: 'sub-7',
                                                name: '把手',
                                                code: 'CS-003-HANDLE',
                                                formula: '1',
                                                calculatedQuantity: '1',
                                                unit: '个'
                                            }
                                        ]
                                    }
                                ]
                            },
                            {
                                id: 'comp-3',
                                name: '固定玻璃',
                                expanded: true,
                                parameters: [
                                    { name: '玻璃类型', value: '防火玻璃', unit: '' },
                                    { name: '厚度', value: '12', unit: 'mm' }
                                ],
                                materials: [
                                    {
                                        id: 'mat-6',
                                        name: '固定玻璃板',
                                        code: 'GD-001',
                                        expanded: true,
                                        formula: '(L - 100) * (W - 100) * 12 / 1000000',
                                        calculatedQuantity: '0.011',
                                        unit: 'm³',
                                        subMaterials: [
                                            {
                                                id: 'sub-8',
                                                name: '玻璃切割',
                                                code: 'GD-001-CUT',
                                                formula: '1',
                                                calculatedQuantity: '1',
                                                unit: '次'
                                            }
                                        ]
                                    },
                                    {
                                        id: 'mat-7',
                                        name: '玻璃密封胶',
                                        code: 'GD-002',
                                        expanded: true,
                                        formula: '((L - 100) + (W - 100)) * 2 / 1000 * 0.01',
                                        calculatedQuantity: '0.038',
                                        unit: 'L',
                                        subMaterials: [
                                            {
                                                id: 'sub-9',
                                                name: '胶条安装',
                                                code: 'GD-002-INST',
                                                formula: '1',
                                                calculatedQuantity: '1',
                                                unit: '次'
                                            }
                                        ]
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        version: '2.0',
                        conditions: '高等级防火版本',
                        components: [
                            {
                                id: 'comp-1',
                                name: '窗框',
                                expanded: true,
                                parameters: [
                                    { name: '材质', value: '不锈钢', unit: '' },
                                    { name: '厚度', value: '2.0', unit: 'mm' }
                                ],
                                materials: [
                                    {
                                        id: 'mat-1',
                                        name: '窗框型材',
                                        code: 'XF-001',
                                        expanded: true,
                                        formula: '(L + W) * 2 / 1000',
                                        calculatedQuantity: '4.00',
                                        unit: 'm',
                                        subMaterials: [
                                            {
                                                id: 'sub-1',
                                                name: '型材切割',
                                                code: 'XF-001-CUT',
                                                formula: '1',
                                                calculatedQuantity: '1',
                                                unit: '次'
                                            },
                                            {
                                                id: 'sub-2',
                                                name: '型材焊接',
                                                code: 'XF-001-WELD',
                                                formula: '4',
                                                calculatedQuantity: '4',
                                                unit: '个'
                                            }
                                        ]
                                    }
                                ]
                            },
                            {
                                id: 'comp-2',
                                name: '窗扇',
                                expanded: true,
                                parameters: [
                                    { name: '材质', value: '不锈钢', unit: '' },
                                    { name: '厚度', value: '1.5', unit: 'mm' }
                                ],
                                materials: [
                                    {
                                        id: 'mat-3',
                                        name: '窗扇型材',
                                        code: 'CS-001',
                                        expanded: true,
                                        formula: '((L - 20) + (W - 20)) * 2 / 1000',
                                        calculatedQuantity: '3.92',
                                        unit: 'm',
                                        subMaterials: [
                                            {
                                                id: 'sub-4',
                                                name: '型材切割',
                                                code: 'CS-001-CUT',
                                                formula: '1',
                                                calculatedQuantity: '1',
                                                unit: '次'
                                            }
                                        ]
                                    },
                                    {
                                        id: 'mat-4',
                                        name: '窗扇玻璃',
                                        code: 'CS-002',
                                        expanded: true,
                                        formula: '(L - 40) * (W - 40) * T / 1000000',
                                        calculatedQuantity: '0.046',
                                        unit: 'm³',
                                        subMaterials: [
                                            {
                                                id: 'sub-5',
                                                name: '玻璃切割',
                                                code: 'CS-002-CUT',
                                                formula: '1',
                                                calculatedQuantity: '1',
                                                unit: '次'
                                            }
                                        ]
                                    }
                                ]
                            },
                            {
                                id: 'comp-3',
                                name: '固定玻璃',
                                expanded: true,
                                parameters: [
                                    { name: '玻璃类型', value: '高等级防火玻璃', unit: '' },
                                    { name: '厚度', value: '15', unit: 'mm' }
                                ],
                                materials: [
                                    {
                                        id: 'mat-6',
                                        name: '固定玻璃板',
                                        code: 'GD-001',
                                        expanded: true,
                                        formula: '(L - 100) * (W - 100) * 15 / 1000000',
                                        calculatedQuantity: '0.014',
                                        unit: 'm³',
                                        subMaterials: [
                                            {
                                                id: 'sub-8',
                                                name: '玻璃切割',
                                                code: 'GD-001-CUT',
                                                formula: '1',
                                                calculatedQuantity: '1',
                                                unit: '次'
                                            }
                                        ]
                                    }
                                ]
                            }
                        ]
                    }
                ],
                activeVersion: 0,
                
                // Material library
                materialSearch: '',
                materialCategories: [
                    {
                        id: 'frame',
                        name: '窗框构件',
                        materials: [
                            { id: 'm1', name: '钢质窗框型材', code: 'XF-001', icon: 'fas fa-ruler-combined' },
                            { id: 'm2', name: '不锈钢窗框型材', code: 'XF-002', icon: 'fas fa-ruler-combined' },
                            { id: 'm3', name: '窗框角码', code: 'XF-003', icon: 'fas fa-cube' },
                            { id: 'm4', name: '窗框密封条', code: 'XF-004', icon: 'fas fa-grip-lines' }
                        ]
                    },
                    {
                        id: 'sash',
                        name: '窗扇构件',
                        materials: [
                            { id: 'm5', name: '钢质窗扇型材', code: 'CS-001', icon: 'fas fa-ruler-combined' },
                            { id: 'm6', name: '不锈钢窗扇型材', code: 'CS-002', icon: 'fas fa-ruler-combined' },
                            { id: 'm7', name: '防火玻璃', code: 'CS-003', icon: 'fas fa-th' },
                            { id: 'm8', name: '窗扇五金件', code: 'CS-004', icon: 'fas fa-tools' },
                            { id: 'm9', name: '铰链', code: 'CS-005', icon: 'fas fa-circle-notch' },
                            { id: 'm10', name: '把手', code: 'CS-006', icon: 'fas fa-hand-paper' }
                        ]
                    },
                    {
                        id: 'glass',
                        name: '固定玻璃构件',
                        materials: [
                            { id: 'm11', name: '防火玻璃板', code: 'GD-001', icon: 'fas fa-th' },
                            { id: 'm12', name: '玻璃密封胶', code: 'GD-002', icon: 'fas fa-tint' },
                            { id: 'm13', name: '玻璃压条', code: 'GD-003', icon: 'fas fa-grip-lines' }
                        ]
                    },
                    {
                        id: 'accessories',
                        name: '辅料',
                        materials: [
                            { id: 'm14', name: '螺丝', code: 'AC-001', icon: 'fas fa-screw' },
                            { id: 'm15', name: '密封胶', code: 'AC-002', icon: 'fas fa-tint' },
                            { id: 'm16', name: '防火填充料', code: 'AC-003', icon: 'fas fa-fire-extinguisher' }
                        ]
                    }
                ],
                
                // UI state
                selectedNode: null,
                selectedNodeType: null,
                selectedNodePath: [],
                zoomLevel: 1.0,
                showHelp: false,
                showVersionComparison: false,
                showCalculationResult: false,
                calculationResults: [],
                calculationLogs: [],
                
                // Drag and drop
                draggedMaterial: null,
                
                // Computed properties
                get currentBom() {
                    return this.bomVersions[this.activeVersion];
                },
                
                get componentCount() {
                    return this.currentBom.components.length;
                },
                
                get materialCount() {
                    return this.currentBom.components.reduce((sum, comp) => sum + comp.materials.length, 0);
                },
                
                get subMaterialCount() {
                    return this.currentBom.components.reduce((sum, comp) => 
                        sum + comp.materials.reduce((subSum, mat) => subSum + mat.subMaterials.length, 0), 0);
                },
                
                get availableParameters() {
                    const params = [...this.productParameters];
                    
                    if (this.selectedNodeType === 'material' || this.selectedNodeType === 'subMaterial') {
                        // Add component parameters
                        if (this.selectedNodePath.length > 0) {
                            const componentIndex = this.selectedNodePath[0];
                            const component = this.currentBom.components[componentIndex];
                            if (component) {
                                params.push(...component.parameters);
                            }
                        }
                    }
                    
                    return params;
                },
                
                // Methods
                loadProductTemplate() {
                    // Reset BOM when product type changes
                    this.bomVersions = [{
                        version: '1.0',
                        conditions: '',
                        components: []
                    }];
                    this.activeVersion = 0;
                    
                    // Load default template based on product type
                    if (this.selectedProductType === 'fireproof-window') {
                        this.productName = '防火窗';
                        this.productCode = 'FW-2023-001';
                        this.loadFireproofWindowTemplate();
                    } else if (this.selectedProductType === 'shower-room') {
                        this.productName = '淋浴房';
                        this.productCode = 'SR-2023-001';
                        this.loadShowerRoomTemplate();
                    } else {
                        this.productName = '玻璃制品';
                        this.productCode = 'GP-2023-001';
                        this.loadGlassProductTemplate();
                    }
                },
                
                loadFireproofWindowTemplate() {
                    this.currentBom.components = [
                        {
                            id: 'comp-1',
                            name: '窗框',
                            expanded: true,
                            parameters: [
                                { name: '材质', value: '钢质', unit: '' },
                                { name: '厚度', value: '1.5', unit: 'mm' }
                            ],
                            materials: []
                        },
                        {
                            id: 'comp-2',
                            name: '窗扇',
                            expanded: true,
                            parameters: [
                                { name: '材质', value: '钢质', unit: '' },
                                { name: '厚度', value: '1.2', unit: 'mm' }
                            ],
                            materials: []
                        },
                        {
                            id: 'comp-3',
                            name: '固定玻璃',
                            expanded: true,
                            parameters: [
                                { name: '玻璃类型', value: '防火玻璃', unit: '' },
                                { name: '厚度', value: '12', unit: 'mm' }
                            ],
                            materials: []
                        }
                    ];
                },
                
                loadShowerRoomTemplate() {
                    this.currentBom.components = [
                        {
                            id: 'comp-1',
                            name: '框架',
                            expanded: true,
                            parameters: [
                                { name: '材质', value: '铝合金', unit: '' },
                                { name: '厚度', value: '1.2', unit: 'mm' }
                            ],
                            materials: []
                        },
                        {
                            id: 'comp-2',
                            name: '玻璃门',
                            expanded: true,
                            parameters: [
                                { name: '玻璃类型', value: '钢化玻璃', unit: '' },
                                { name: '厚度', value: '8', unit: 'mm' }
                            ],
                            materials: []
                        },
                        {
                            id: 'comp-3',
                            name: '固定墙板',
                            expanded: true,
                            parameters: [
                                { name: '玻璃类型', value: '钢化玻璃', unit: '' },
                                { name: '厚度', value: '6', unit: 'mm' }
                            ],
                            materials: []
                        },
                        {
                            id: 'comp-4',
                            name: '底盘',
                            expanded: true,
                            parameters: [
                                { name: '材质', value: '亚克力', unit: '' },
                                { name: '厚度', value: '5', unit: 'mm' }
                            ],
                            materials: []
                        }
                    ];
                },
                
                loadGlassProductTemplate() {
                    this.currentBom.components = [
                        {
                            id: 'comp-1',
                            name: '玻璃主体',
                            expanded: true,
                            parameters: [
                                { name: '玻璃类型', value: '浮法玻璃', unit: '' },
                                { name: '厚度', value: '5', unit: 'mm' }
                            ],
                            materials: []
                        },
                        {
                            id: 'comp-2',
                            name: '边框',
                            expanded: true,
                            parameters: [
                                { name: '材质', value: '铝合金', unit: '' },
                                { name: '厚度', value: '1.0', unit: 'mm' }
                            ],
                            materials: []
                        }
                    ];
                },
                
                addComponent() {
                    const newComponent = {
                        id: 'comp-' + Date.now(),
                        name: '新构件',
                        expanded: true,
                        parameters: [],
                        materials: []
                    };
                    this.currentBom.components.push(newComponent);
                },
                
                editComponent(index) {
                    this.selectedNode = this.currentBom.components[index];
                    this.selectedNodeType = 'component';
                    this.selectedNodePath = [index];
                },
                
                removeComponent(index) {
                    if (confirm('确定要删除此构件吗？')) {
                        this.currentBom.components.splice(index, 1);
                        if (this.selectedNodePath.length > 0 && this.selectedNodePath[0] === index) {
                            this.selectedNode = null;
                            this.selectedNodeType = null;
                            this.selectedNodePath = [];
                        }
                    }
                },
                
                addMaterial(componentIndex) {
                    const newMaterial = {
                        id: 'mat-' + Date.now(),
                        name: '新物料',
                        code: '',
                        expanded: true,
                        formula: '1',
                        calculatedQuantity: '1',
                        unit: '个',
                        subMaterials: []
                    };
                    this.currentBom.components[componentIndex].materials.push(newMaterial);
                },
                
                editMaterial(componentIndex, materialIndex) {
                    this.selectedNode = this.currentBom.components[componentIndex].materials[materialIndex];
                    this.selectedNodeType = 'material';
                    this.selectedNodePath = [componentIndex, materialIndex];
                },
                
                removeMaterial(componentIndex, materialIndex) {
                    if (confirm('确定要删除此物料吗？')) {
                        this.currentBom.components[componentIndex].materials.splice(materialIndex, 1);
                        if (this.selectedNodePath.length > 1 && 
                            this.selectedNodePath[0] === componentIndex && 
                            this.selectedNodePath[1] === materialIndex) {
                            this.selectedNode = null;
                            this.selectedNodeType = null;
                            this.selectedNodePath = [];
                        }
                    }
                },
                
                addSubMaterial(componentIndex, materialIndex) {
                    const newSubMaterial = {
                        id: 'sub-' + Date.now(),
                        name: '新辅料',
                        code: '',
                        formula: '1',
                        calculatedQuantity: '1',
                        unit: '个'
                    };
                    this.currentBom.components[componentIndex].materials[materialIndex].subMaterials.push(newSubMaterial);
                },
                
                editSubMaterial(componentIndex, materialIndex, subMaterialIndex) {
                    this.selectedNode = this.currentBom.components[componentIndex].materials[materialIndex].subMaterials[subMaterialIndex];
                    this.selectedNodeType = 'subMaterial';
                    this.selectedNodePath = [componentIndex, materialIndex, subMaterialIndex];
                },
                
                removeSubMaterial(componentIndex, materialIndex, subMaterialIndex) {
                    if (confirm('确定要删除此辅料吗？')) {
                        this.currentBom.components[componentIndex].materials[materialIndex].subMaterials.splice(subMaterialIndex, 1);
                        if (this.selectedNodePath.length > 2 && 
                            this.selectedNodePath[0] === componentIndex && 
                            this.selectedNodePath[1] === materialIndex && 
                            this.selectedNodePath[2] === subMaterialIndex) {
                            this.selectedNode = null;
                            this.selectedNodeType = null;
                            this.selectedNodePath = [];
                        }
                    }
                },
                
                insertParameter(paramName) {
                    if (this.selectedNode && (this.selectedNodeType === 'material' || this.selectedNodeType === 'subMaterial')) {
                        this.selectedNode.formula += paramName;
                        this.updateCalculations();
                    }
                },
                
                updateCalculations() {
                    // Update all calculations
                    this.currentBom.components.forEach(component => {
                        component.materials.forEach(material => {
                            try {
                                // Create a safe evaluation context
                                const context = {
                                    L: parseFloat(this.productParameters.find(p => p.name === 'L')?.value || 0),
                                    W: parseFloat(this.productParameters.find(p => p.name === 'W')?.value || 0),
                                    T: parseFloat(this.productParameters.find(p => p.name === 'T')?.value || 0)
                                };
                                
                                // Add component parameters to context
                                component.parameters.forEach(param => {
                                    if (param.name && param.value) {
                                        context[param.name] = parseFloat(param.value) || 0;
                                    }
                                });
                                
                                // Evaluate formula
                                const formula = material.formula.replace(/[^0-9+\-*/().\sLWTa-zA-Z]/g, '');
                                // Simple evaluation - in a real app, use a proper expression parser
                                let result = 0;
                                try {
                                    // This is a simplified evaluation - not safe for production
                                    result = eval(formula.replace(/L|W|T|[a-zA-Z]+/g, match => {
                                        return context[match] || 0;
                                    }));
                                } catch (e) {
                                    result = 0;
                                }
                                
                                material.calculatedQuantity = result.toFixed(2);
                            } catch (e) {
                                material.calculatedQuantity = '0.00';
                            }
                            
                            // Update sub-materials
                            material.subMaterials.forEach(subMaterial => {
                                try {
                                    const context = {
                                        L: parseFloat(this.productParameters.find(p => p.name === 'L')?.value || 0),
                                        W: parseFloat(this.productParameters.find(p => p.name === 'W')?.value || 0),
                                        T: parseFloat(this.productParameters.find(p => p.name === 'T')?.value || 0)
                                    };
                                    
                                    // Add component parameters to context
                                    component.parameters.forEach(param => {
                                        if (param.name && param.value) {
                                            context[param.name] = parseFloat(param.value) || 0;
                                        }
                                    });
                                    
                                    // Evaluate formula
                                    const formula = subMaterial.formula.replace(/[^0-9+\-*/().\sLWTa-zA-Z]/g, '');
                                    let result = 0;
                                    try {
                                        result = eval(formula.replace(/L|W|T|[a-zA-Z]+/g, match => {
                                            return context[match] || 0;
                                        }));
                                    } catch (e) {
                                        result = 0;
                                    }
                                    
                                    subMaterial.calculatedQuantity = result.toFixed(2);
                                } catch (e) {
                                    subMaterial.calculatedQuantity = '0.00';
                                }
                            });
                        });
                    });
                },
                
                validateBom() {
                    // Simple validation - in a real app, this would be more comprehensive
                    let isValid = true;
                    let errors = [];
                    
                    if (!this.productName) {
                        isValid = false;
                        errors.push('产品名称不能为空');
                    }
                    
                    if (!this.productCode) {
                        isValid = false;
                        errors.push('产品编码不能为空');
                    }
                    
                    if (this.currentBom.components.length === 0) {
                        isValid = false;
                        errors.push('至少需要添加一个构件');
                    }
                    
                    this.currentBom.components.forEach((component, compIndex) => {
                        if (!component.name) {
                            isValid = false;
                            errors.push(`构件 ${compIndex + 1} 名称不能为空`);
                        }
                        
                        component.materials.forEach((material, matIndex) => {
                            if (!material.name) {
                                isValid = false;
                                errors.push(`构件 ${component.name} 的物料 ${matIndex + 1} 名称不能为空`);
                            }
                            
                            if (!material.formula) {
                                isValid = false;
                                errors.push(`构件 ${component.name} 的物料 ${material.name} 用量公式不能为空`);
                            }
                            
                            material.subMaterials.forEach((subMaterial, subIndex) => {
                                if (!subMaterial.name) {
                                    isValid = false;
                                    errors.push(`构件 ${component.name} 的物料 ${material.name} 的辅料 ${subIndex + 1} 名称不能为空`);
                                }
                                
                                if (!subMaterial.formula) {
                                    isValid = false;
                                    errors.push(`构件 ${component.name} 的物料 ${material.name} 的辅料 ${subMaterial.name} 用量公式不能为空`);
                                }
                            });
                        });
                    });
                    
                    if (isValid) {
                        alert('BOM验证通过！');
                    } else {
                        alert('BOM验证失败：\n' + errors.join('\n'));
                    }
                },
                
                calculateBom() {
                    this.updateCalculations();
                    
                    // Generate calculation results
                    this.calculationResults = [];
                    this.calculationLogs = [];
                    
                    this.calculationLogs.push('开始计算BOM...');
                    
                    this.currentBom.components.forEach(component => {
                        this.calculationLogs.push(`计算构件: ${component.name}`);
                        
                        component.materials.forEach(material => {
                            this.calculationResults.push({
                                code: material.code,
                                name: material.name,
                                unit: material.unit || '个',
                                quantity: material.calculatedQuantity
                            });
                            
                            this.calculationLogs.push(`  物料: ${material.name} = ${material.calculatedQuantity} ${material.unit || '个'}`);
                            
                            material.subMaterials.forEach(subMaterial => {
                                this.calculationResults.push({
                                    code: subMaterial.code,
                                    name: subMaterial.name,
                                    unit: subMaterial.unit || '个',
                                    quantity: subMaterial.calculatedQuantity
                                });
                                
                                this.calculationLogs.push(`    辅料: ${subMaterial.name} = ${subMaterial.calculatedQuantity} ${subMaterial.unit || '个'}`);
                            });
                        });
                    });
                    
                    this.calculationLogs.push('BOM计算完成');
                    this.showCalculationResult = true;
                },
                
                exportCalculationResult() {
                    // In a real app, this would generate and download a file
                    alert('导出功能将在实际应用中实现');
                },
                
                saveBom() {
                    // In a real app, this would save to the backend
                    alert('BOM已保存！');
                },
                
                addNewVersion() {
                    const newVersion = {
                        version: (this.bomVersions.length + 1).toFixed(1),
                        conditions: '',
                        components: JSON.parse(JSON.stringify(this.currentBom.components))
                    };
                    this.bomVersions.push(newVersion);
                    this.activeVersion = this.bomVersions.length - 1;
                },
                
                compareVersions() {
                    this.showVersionComparison = true;
                },
                
                zoomIn() {
                    this.zoomLevel = Math.min(this.zoomLevel + 0.1, 2.0);
                },
                
                zoomOut() {
                    this.zoomLevel = Math.max(this.zoomLevel - 0.1, 0.5);
                },
                
                resetZoom() {
                    this.zoomLevel = 1.0;
                },
                
                expandAll() {
                    this.currentBom.components.forEach(component => {
                        component.expanded = true;
                        component.materials.forEach(material => {
                            material.expanded = true;
                        });
                    });
                },
                
                collapseAll() {
                    this.currentBom.components.forEach(component => {
                        component.expanded = false;
                        component.materials.forEach(material => {
                            material.expanded = false;
                        });
                    });
                },
                
                // Drag and drop methods
                dragStart(event, material) {
                    this.draggedMaterial = material;
                    event.dataTransfer.effectAllowed = 'copy';
                    event.target.classList.add('dragging');
                },
                
                dragEnd(event) {
                    event.target.classList.remove('dragging');
                },
                
                dropOnCanvas(event) {
                    event.preventDefault();
                    if (this.draggedMaterial) {
                        // In a real app, this would add the material to the BOM
                        alert(`添加物料: ${this.draggedMaterial.name}`);
                        this.draggedMaterial = null;
                    }
                }
            };
        }
    </script>
</body>
</html>
