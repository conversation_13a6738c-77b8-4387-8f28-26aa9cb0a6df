<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工装夹具管理 - PDM系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        [x-cloak] { display: none !important; }
        .tree-node {
            transition: all 0.2s ease;
        }
        .tree-node:hover {
            background-color: #f5f5f5;
        }
        .tree-node.selected {
            background-color: #e6f7ff;
            border-left: 3px solid #1890ff;
        }
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 500;
        }
        .status-available {
            background-color: #f6ffed;
            color: #52c41a;
        }
        .status-in-use {
            background-color: #e6f7ff;
            color: #1890ff;
        }
        .status-maintenance {
            background-color: #fff7e6;
            color: #faad14;
        }
        .status-disabled {
            background-color: #fff1f0;
            color: #ff4d4f;
        }
        .tooling-card {
            transition: all 0.3s ease;
        }
        .tooling-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .tab-active {
            border-bottom: 2px solid #1890ff;
            color: #1890ff;
        }
        .chart-bar {
            transition: height 0.5s ease;
        }
    </style>
</head>
<body class="bg-gray-50" x-data="toolingManagement()">
    <!-- 顶部导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="ri-tools-line text-blue-600 text-2xl mr-2"></i>
                        <span class="text-xl font-semibold text-gray-800">PDM系统</span>
                    </div>
                    <div class="hidden md:ml-10 md:flex md:space-x-8">
                        <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">首页</a>
                        <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">工序管理</a>
                        <a href="#" class="text-blue-600 border-b-2 border-blue-600 px-3 py-2 text-sm font-medium">工装夹具管理</a>
                        <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">工艺路线</a>
                    </div>
                </div>
                <div class="flex items-center">
                    <button class="p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none">
                        <i class="ri-notification-3-line text-xl"></i>
                    </button>
                    <div class="ml-3 relative">
                        <div class="flex items-center">
                            <img class="h-8 w-8 rounded-full" src="https://picsum.photos/seed/user1/40/40.jpg" alt="用户头像">
                            <span class="ml-2 text-sm font-medium text-gray-700">张工程师</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主内容区 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex flex-col md:flex-row gap-6">
            <!-- 左侧区域：工装分类树和筛选器 -->
            <div class="w-full md:w-64 flex-shrink-0">
                <div class="bg-white rounded-lg shadow p-4">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">工装分类</h3>
                    <div class="space-y-1">
                        <template x-for="category in categories" :key="category.id">
                            <div class="tree-node p-2 rounded cursor-pointer" 
                                 :class="selectedCategory === category.id ? 'selected' : ''"
                                 @click="selectCategory(category.id)">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <i :class="category.icon" class="mr-2"></i>
                                        <span x-text="category.name"></span>
                                    </div>
                                    <span class="text-xs text-gray-500" x-text="category.count"></span>
                                </div>
                            </div>
                        </template>
                    </div>
                    
                    <div class="mt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">状态筛选</h3>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded text-blue-600" x-model="filters.status" value="available">
                                <span class="ml-2 text-sm text-gray-700">可用</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded text-blue-600" x-model="filters.status" value="in-use">
                                <span class="ml-2 text-sm text-gray-700">使用中</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded text-blue-600" x-model="filters.status" value="maintenance">
                                <span class="ml-2 text-sm text-gray-700">维护中</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded text-blue-600" x-model="filters.status" value="disabled">
                                <span class="ml-2 text-sm text-gray-700">停用</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="mt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">工序关联</h3>
                        <select class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm" x-model="filters.operation">
                            <option value="">全部工序</option>
                            <template x-for="op in operations" :key="op.id">
                                <option :value="op.id" x-text="op.name"></option>
                            </template>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 中间区域：工装列表和详情 -->
            <div class="flex-1">
                <div class="bg-white rounded-lg shadow">
                    <!-- 工具栏 -->
                    <div class="px-4 py-3 border-b border-gray-200 flex justify-between items-center">
                        <div class="flex items-center">
                            <h2 class="text-lg font-medium text-gray-900">工装夹具列表</h2>
                            <span class="ml-2 text-sm text-gray-500" x-text="`共 ${filteredToolings.length} 项`"></span>
                        </div>
                        <div class="flex space-x-2">
                            <button @click="showCreateModal = true" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700 focus:outline-none">
                                <i class="ri-add-line mr-1"></i> 新建工装
                            </button>
                            <button class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none">
                                <i class="ri-download-line mr-1"></i> 导出
                            </button>
                        </div>
                    </div>
                    
                    <!-- 工装列表 -->
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">工装编码</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">工装名称</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">适用工序</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">库存</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">下次维护</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <template x-for="tooling in filteredToolings" :key="tooling.id">
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900" x-text="tooling.code"></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="tooling.name"></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800" x-text="tooling.type"></span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="tooling.operations.join(', ')"></td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="status-badge" :class="getStatusClass(tooling.status)" x-text="getStatusText(tooling.status)"></span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <span x-text="`${tooling.availableStock}/${tooling.totalStock}`"></span>
                                            <span x-show="tooling.availableStock < tooling.minStock" class="text-red-500 ml-1">
                                                <i class="ri-alert-line"></i>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="tooling.nextMaintenance"></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button @click="viewTooling(tooling)" class="text-blue-600 hover:text-blue-900 mr-3">查看</button>
                                            <button @click="editTooling(tooling)" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 空状态 -->
                    <div x-show="filteredToolings.length === 0" class="text-center py-12">
                        <i class="ri-inbox-line text-gray-400 text-4xl"></i>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">暂无工装夹具数据</h3>
                        <p class="mt-1 text-sm text-gray-500">请先创建工装夹具信息</p>
                        <div class="mt-6">
                            <button @click="showCreateModal = true" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none">
                                <i class="ri-add-line mr-2"></i> 新建工装
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧区域：统计和库存信息 -->
            <div class="w-full md:w-80 flex-shrink-0">
                <div class="bg-white rounded-lg shadow p-4 mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">使用统计</h3>
                    <div class="space-y-4">
                        <div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">使用频次</span>
                                <span class="font-medium" x-text="`${selectedTooling?.usageFrequency || 0} 次/月`"></span>
                            </div>
                            <div class="mt-1 w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full" :style="`width: ${selectedTooling?.usageFrequency || 0}%`"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">使用效率</span>
                                <span class="font-medium" x-text="`${selectedTooling?.efficiency || 0}%`"></span>
                            </div>
                            <div class="mt-1 w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-green-600 h-2 rounded-full" :style="`width: ${selectedTooling?.efficiency || 0}%`"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">故障率</span>
                                <span class="font-medium" x-text="`${selectedTooling?.failureRate || 0}%`"></span>
                            </div>
                            <div class="mt-1 w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-red-600 h-2 rounded-full" :style="`width: ${selectedTooling?.failureRate || 0}%`"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-6">
                        <h4 class="text-sm font-medium text-gray-900 mb-2">月度使用趋势</h4>
                        <div class="flex items-end h-24 space-x-1">
                            <div class="flex-1 flex flex-col items-center">
                                <div class="w-full bg-blue-500 rounded-t chart-bar" style="height: 40%"></div>
                                <span class="text-xs text-gray-500 mt-1">1月</span>
                            </div>
                            <div class="flex-1 flex flex-col items-center">
                                <div class="w-full bg-blue-500 rounded-t chart-bar" style="height: 60%"></div>
                                <span class="text-xs text-gray-500 mt-1">2月</span>
                            </div>
                            <div class="flex-1 flex flex-col items-center">
                                <div class="w-full bg-blue-500 rounded-t chart-bar" style="height: 45%"></div>
                                <span class="text-xs text-gray-500 mt-1">3月</span>
                            </div>
                            <div class="flex-1 flex flex-col items-center">
                                <div class="w-full bg-blue-500 rounded-t chart-bar" style="height: 80%"></div>
                                <span class="text-xs text-gray-500 mt-1">4月</span>
                            </div>
                            <div class="flex-1 flex flex-col items-center">
                                <div class="w-full bg-blue-500 rounded-t chart-bar" style="height: 70%"></div>
                                <span class="text-xs text-gray-500 mt-1">5月</span>
                            </div>
                            <div class="flex-1 flex flex-col items-center">
                                <div class="w-full bg-blue-500 rounded-t chart-bar" style="height: 90%"></div>
                                <span class="text-xs text-gray-500 mt-1">6月</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow p-4">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">库存状态</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">总数量</span>
                            <span class="font-medium" x-text="selectedTooling?.totalStock || 0"></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">可用数量</span>
                            <span class="font-medium text-green-600" x-text="selectedTooling?.availableStock || 0"></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">最小库存</span>
                            <span class="font-medium" x-text="selectedTooling?.minStock || 0"></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">存放位置</span>
                            <span class="font-medium" x-text="selectedTooling?.location || '-'"></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">采购价格</span>
                            <span class="font-medium" x-text="`¥${selectedTooling?.price || 0}`"></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">供应商</span>
                            <span class="font-medium" x-text="selectedTooling?.supplier || '-'"></span>
                        </div>
                    </div>
                    
                    <div class="mt-4 pt-4 border-t border-gray-200">
                        <button class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none">
                            <i class="ri-shopping-cart-line mr-2"></i> 采购申请
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 工装详情模态框 -->
    <div x-show="showDetailModal" x-cloak class="fixed inset-0 z-50 overflow-y-auto" style="display: none;">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-500 opacity-75" @click="showDetailModal = false"></div>
            </div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4" x-text="`工装详情 - ${selectedTooling?.name || ''}`"></h3>
                            
                            <!-- 标签页 -->
                            <div class="border-b border-gray-200 mb-4">
                                <nav class="-mb-px flex space-x-8">
                                    <button @click="activeTab = 'basic'" class="py-2 px-1 border-b-2 font-medium text-sm" :class="activeTab === 'basic' ? 'tab-active' : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'">
                                        基本信息
                                    </button>
                                    <button @click="activeTab = 'technical'" class="py-2 px-1 border-b-2 font-medium text-sm" :class="activeTab === 'technical' ? 'tab-active' : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'">
                                        技术参数
                                    </button>
                                    <button @click="activeTab = 'operations'" class="py-2 px-1 border-b-2 font-medium text-sm" :class="activeTab === 'operations' ? 'tab-active' : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'">
                                        工序关联
                                    </button>
                                    <button @click="activeTab = 'maintenance'" class="py-2 px-1 border-b-2 font-medium text-sm" :class="activeTab === 'maintenance' ? 'tab-active' : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'">
                                        维护记录
                                    </button>
                                </nav>
                            </div>
                            
                            <!-- 标签页内容 -->
                            <div class="mt-4">
                                <!-- 基本信息 -->
                                <div x-show="activeTab === 'basic'" class="space-y-4">
                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">工装编码</label>
                                            <div class="mt-1 text-sm text-gray-900" x-text="selectedTooling?.code || '-'"></div>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">工装名称</label>
                                            <div class="mt-1 text-sm text-gray-900" x-text="selectedTooling?.name || '-'"></div>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">工装类型</label>
                                            <div class="mt-1 text-sm text-gray-900" x-text="selectedTooling?.type || '-'"></div>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">规格型号</label>
                                            <div class="mt-1 text-sm text-gray-900" x-text="selectedTooling?.specification || '-'"></div>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">制造商</label>
                                            <div class="mt-1 text-sm text-gray-900" x-text="selectedTooling?.manufacturer || '-'"></div>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">状态</label>
                                            <div class="mt-1">
                                                <span class="status-badge" :class="getStatusClass(selectedTooling?.status)" x-text="getStatusText(selectedTooling?.status)"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 技术参数 -->
                                <div x-show="activeTab === 'technical'" class="space-y-4">
                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">适用材料</label>
                                            <div class="mt-1 text-sm text-gray-900" x-text="selectedTooling?.materials?.join(', ') || '-'"></div>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">适用尺寸范围</label>
                                            <div class="mt-1 text-sm text-gray-900" x-text="`${selectedTooling?.minSize || '-'} - ${selectedTooling?.maxSize || '-'}`"></div>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">精度等级</label>
                                            <div class="mt-1 text-sm text-gray-900" x-text="selectedTooling?.precision || '-'"></div>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">使用寿命</label>
                                            <div class="mt-1 text-sm text-gray-900" x-text="`${selectedTooling?.serviceLife || '-'} 次`"></div>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">技术文档</label>
                                        <div class="mt-1 space-y-2">
                                            <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                                <div class="flex items-center">
                                                    <i class="ri-file-pdf-line text-red-500 mr-2"></i>
                                                    <span class="text-sm">工装设计图纸.pdf</span>
                                                </div>
                                                <button class="text-blue-600 hover:text-blue-800 text-sm">下载</button>
                                            </div>
                                            <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                                <div class="flex items-center">
                                                    <i class="ri-file-text-line text-blue-500 mr-2"></i>
                                                    <span class="text-sm">技术规格书.docx</span>
                                                </div>
                                                <button class="text-blue-600 hover:text-blue-800 text-sm">下载</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 工序关联 -->
                                <div x-show="activeTab === 'operations'" class="space-y-4">
                                    <div class="overflow-hidden border border-gray-200 rounded-md">
                                        <table class="min-w-full divide-y divide-gray-200">
                                            <thead class="bg-gray-50">
                                                <tr>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">工序名称</th>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">必需/可选</th>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">使用优先级</th>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">替代工装</th>
                                                </tr>
                                            </thead>
                                            <tbody class="bg-white divide-y divide-gray-200">
                                                <template x-for="op in selectedTooling?.operationDetails || []" :key="op.id">
                                                    <tr>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="op.name"></td>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" 
                                                                  :class="op.required ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'"
                                                                  x-text="op.required ? '必需' : '可选'"></span>
                                                        </td>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="op.priority"></td>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="op.alternatives?.join(', ') || '-'"></td>
                                                    </tr>
                                                </template>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                
                                <!-- 维护记录 -->
                                <div x-show="activeTab === 'maintenance'" class="space-y-4">
                                    <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4" x-show="selectedTooling?.maintenanceOverdue">
                                        <div class="flex">
                                            <div class="flex-shrink-0">
                                                <i class="ri-alert-line text-yellow-400"></i>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm text-yellow-700">
                                                    工装维护已超期，存在安全风险，建议立即停用
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="overflow-hidden border border-gray-200 rounded-md">
                                        <table class="min-w-full divide-y divide-gray-200">
                                            <thead class="bg-gray-50">
                                                <tr>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">维护日期</th>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">维护类型</th>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">维护人员</th>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">维护结果</th>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">费用</th>
                                                </tr>
                                            </thead>
                                            <tbody class="bg-white divide-y divide-gray-200">
                                                <template x-for="record in selectedTooling?.maintenanceRecords || []" :key="record.id">
                                                    <tr>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="record.date"></td>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="record.type"></td>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="record.personnel"></td>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" 
                                                                  :class="getResultClass(record.result)"
                                                                  x-text="record.result"></span>
                                                        </td>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="`¥${record.cost}`"></td>
                                                    </tr>
                                                </template>
                                            </tbody>
                                        </table>
                                    </div>
                                    
                                    <div class="flex justify-end">
                                        <button @click="showMaintenanceModal = true; showDetailModal = false" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none">
                                            <i class="ri-add-line mr-2"></i> 添加维护记录
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none sm:ml-3 sm:w-auto sm:text-sm" @click="showDetailModal = false">
                        关闭
                    </button>
                    <button type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm" @click="editTooling(selectedTooling); showDetailModal = false">
                        编辑
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建/编辑工装模态框 -->
    <div x-show="showCreateModal || showEditModal" x-cloak class="fixed inset-0 z-50 overflow-y-auto" style="display: none;">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-500 opacity-75" @click="showCreateModal = false; showEditModal = false"></div>
            </div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4" x-text="showCreateModal ? '新建工装夹具' : '编辑工装夹具'"></h3>
                            
                            <!-- 表单 -->
                            <form @submit.prevent="saveTooling">
                                <div class="space-y-4">
                                    <!-- 基本信息 -->
                                    <div>
                                        <h4 class="text-md font-medium text-gray-900 mb-2">基本信息</h4>
                                        <div class="grid grid-cols-2 gap-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">工装编码 <span class="text-red-500">*</span></label>
                                                <input type="text" x-model="formData.code" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" required>
                                                <p x-show="errors.code" class="mt-1 text-sm text-red-600" x-text="errors.code"></p>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">工装名称 <span class="text-red-500">*</span></label>
                                                <input type="text" x-model="formData.name" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" required>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">工装类型 <span class="text-red-500">*</span></label>
                                                <select x-model="formData.type" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" required>
                                                    <option value="">请选择</option>
                                                    <option value="夹具">夹具</option>
                                                    <option value="模具">模具</option>
                                                    <option value="刀具">刀具</option>
                                                    <option value="量具">量具</option>
                                                </select>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">规格型号</label>
                                                <input type="text" x-model="formData.specification" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">制造商</label>
                                                <select x-model="formData.manufacturer" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                                    <option value="">请选择</option>
                                                    <option value="精密工装有限公司">精密工装有限公司</option>
                                                    <option value="华星模具制造">华星模具制造</option>
                                                    <option value="恒信刀具厂">恒信刀具厂</option>
                                                </select>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">状态</label>
                                                <select x-model="formData.status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                                    <option value="available">可用</option>
                                                    <option value="in-use">使用中</option>
                                                    <option value="maintenance">维护中</option>
                                                    <option value="disabled">停用</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 技术参数 -->
                                    <div>
                                        <h4 class="text-md font-medium text-gray-900 mb-2">技术参数</h4>
                                        <div class="grid grid-cols-2 gap-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">适用材料</label>
                                                <div class="mt-1 space-y-2">
                                                    <label class="flex items-center">
                                                        <input type="checkbox" class="rounded text-blue-600" x-model="formData.materials" value="玻璃">
                                                        <span class="ml-2 text-sm text-gray-700">玻璃</span>
                                                    </label>
                                                    <label class="flex items-center">
                                                        <input type="checkbox" class="rounded text-blue-600" x-model="formData.materials" value="铝型材">
                                                        <span class="ml-2 text-sm text-gray-700">铝型材</span>
                                                    </label>
                                                    <label class="flex items-center">
                                                        <input type="checkbox" class="rounded text-blue-600" x-model="formData.materials" value="五金件">
                                                        <span class="ml-2 text-sm text-gray-700">五金件</span>
                                                    </label>
                                                </div>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">适用尺寸范围</label>
                                                <div class="mt-1 grid grid-cols-2 gap-2">
                                                    <div>
                                                        <input type="number" x-model="formData.minSize" placeholder="最小尺寸" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                                    </div>
                                                    <div>
                                                        <input type="number" x-model="formData.maxSize" placeholder="最大尺寸" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                                    </div>
                                                </div>
                                                <p x-show="errors.sizeRange" class="mt-1 text-sm text-red-600" x-text="errors.sizeRange"></p>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">精度等级</label>
                                                <select x-model="formData.precision" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                                    <option value="">请选择</option>
                                                    <option value="高精度">高精度</option>
                                                    <option value="标准">标准</option>
                                                    <option value="一般">一般</option>
                                                </select>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">使用寿命（次）</label>
                                                <input type="number" x-model="formData.serviceLife" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 工序关联 -->
                                    <div>
                                        <h4 class="text-md font-medium text-gray-900 mb-2">工序关联</h4>
                                        <div class="space-y-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">适用工序</label>
                                                <div class="mt-1 grid grid-cols-2 gap-2">
                                                    <template x-for="op in operations" :key="op.id">
                                                        <label class="flex items-center">
                                                            <input type="checkbox" class="rounded text-blue-600" x-model="formData.operations" :value="op.id">
                                                            <span class="ml-2 text-sm text-gray-700" x-text="op.name"></span>
                                                        </label>
                                                    </template>
                                                </div>
                                            </div>
                                            <div class="grid grid-cols-2 gap-4">
                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700">必需/可选</label>
                                                    <div class="mt-1 space-y-2">
                                                        <label class="flex items-center">
                                                            <input type="radio" class="text-blue-600" x-model="formData.isRequired" :value="true">
                                                            <span class="ml-2 text-sm text-gray-700">必需</span>
                                                        </label>
                                                        <label class="flex items-center">
                                                            <input type="radio" class="text-blue-600" x-model="formData.isRequired" :value="false">
                                                            <span class="ml-2 text-sm text-gray-700">可选</span>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700">使用优先级</label>
                                                    <input type="number" x-model="formData.priority" min="1" max="10" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                                </div>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">替代工装</label>
                                                <select x-model="formData.alternatives" multiple class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                                    <template x-for="tool in toolings.filter(t => t.id !== formData.id)" :key="tool.id">
                                                        <option :value="tool.id" x-text="tool.name"></option>
                                                    </template>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 库存信息 -->
                                    <div>
                                        <h4 class="text-md font-medium text-gray-900 mb-2">库存信息</h4>
                                        <div class="grid grid-cols-2 gap-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">总数量 <span class="text-red-500">*</span></label>
                                                <input type="number" x-model="formData.totalStock" min="0" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" required>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">可用数量 <span class="text-red-500">*</span></label>
                                                <input type="number" x-model="formData.availableStock" min="0" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" required>
                                                <p x-show="errors.stock" class="mt-1 text-sm text-red-600" x-text="errors.stock"></p>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">最小库存</label>
                                                <input type="number" x-model="formData.minStock" min="0" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">存放位置</label>
                                                <input type="text" x-model="formData.location" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">采购价格（元）</label>
                                                <input type="number" x-model="formData.price" min="0" step="0.01" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">供应商</label>
                                                <select x-model="formData.supplier" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                                    <option value="">请选择</option>
                                                    <option value="精密工装有限公司">精密工装有限公司</option>
                                                    <option value="华星模具制造">华星模具制造</option>
                                                    <option value="恒信刀具厂">恒信刀具厂</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 维护计划 -->
                                    <div>
                                        <h4 class="text-md font-medium text-gray-900 mb-2">维护计划</h4>
                                        <div class="grid grid-cols-2 gap-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">维护类型</label>
                                                <select x-model="formData.maintenanceType" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                                    <option value="">请选择</option>
                                                    <option value="日常保养">日常保养</option>
                                                    <option value="定期检修">定期检修</option>
                                                    <option value="大修">大修</option>
                                                </select>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">维护周期（天）</label>
                                                <input type="number" x-model="formData.maintenanceCycle" min="1" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">维护时长（小时）</label>
                                                <input type="number" x-model="formData.maintenanceDuration" min="0.5" step="0.5" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">下次维护日期</label>
                                                <input type="date" x-model="formData.nextMaintenance" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                            </div>
                                        </div>
                                        <div class="mt-4">
                                            <label class="block text-sm font-medium text-gray-700">维护内容</label>
                                            <textarea x-model="formData.maintenanceContent" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none sm:ml-3 sm:w-auto sm:text-sm" @click="saveTooling">
                        保存
                    </button>
                    <button type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm" @click="showCreateModal = false; showEditModal = false">
                        取消
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 维护记录模态框 -->
    <div x-show="showMaintenanceModal" x-cloak class="fixed inset-0 z-50 overflow-y-auto" style="display: none;">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-500 opacity-75" @click="showMaintenanceModal = false"></div>
            </div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">添加维护记录</h3>
                            
                            <form @submit.prevent="saveMaintenance">
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">维护日期 <span class="text-red-500">*</span></label>
                                        <input type="date" x-model="maintenanceForm.date" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" required>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">维护类型 <span class="text-red-500">*</span></label>
                                        <select x-model="maintenanceForm.type" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" required>
                                            <option value="">请选择</option>
                                            <option value="日常保养">日常保养</option>
                                            <option value="定期检修">定期检修</option>
                                            <option value="大修">大修</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">维护人员 <span class="text-red-500">*</span></label>
                                        <select x-model="maintenanceForm.personnel" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" required>
                                            <option value="">请选择</option>
                                            <option value="张师傅">张师傅</option>
                                            <option value="李师傅">李师傅</option>
                                            <option value="王师傅">王师傅</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">维护结果 <span class="text-red-500">*</span></label>
                                        <select x-model="maintenanceForm.result" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" required>
                                            <option value="">请选择</option>
                                            <option value="正常">正常</option>
                                            <option value="异常">异常</option>
                                            <option value="需更换">需更换</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">维护费用（元）</label>
                                        <input type="number" x-model="maintenanceForm.cost" min="0" step="0.01" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">备注说明</label>
                                        <textarea x-model="maintenanceForm.notes" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"></textarea>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none sm:ml-3 sm:w-auto sm:text-sm" @click="saveMaintenance">
                        保存
                    </button>
                    <button type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm" @click="showMaintenanceModal = false">
                        取消
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toolingManagement() {
            return {
                // 数据状态
                toolings: [
                    {
                        id: 1,
                        code: 'TOOL-001',
                        name: '玻璃切割夹具',
                        type: '夹具',
                        specification: 'GJ-2000×1500',
                        manufacturer: '精密工装有限公司',
                        status: 'available',
                        operations: ['切割', '磨边'],
                        availableStock: 5,
                        totalStock: 8,
                        minStock: 2,
                        location: 'A区-3号架',
                        price: 3500,
                        supplier: '精密工装有限公司',
                        nextMaintenance: '2025-09-15',
                        materials: ['玻璃'],
                        minSize: 500,
                        maxSize: 2000,
                        precision: '高精度',
                        serviceLife: 10000,
                        isRequired: true,
                        priority: 1,
                        alternatives: [],
                        maintenanceType: '定期检修',
                        maintenanceCycle: 90,
                        maintenanceDuration: 4,
                        maintenanceContent: '检查夹具精度，紧固螺丝，清洁表面',
                        usageFrequency: 75,
                        efficiency: 85,
                        failureRate: 5,
                        maintenanceOverdue: false,
                        operationDetails: [
                            { id: 1, name: '切割', required: true, priority: 1, alternatives: [] },
                            { id: 2, name: '磨边', required: false, priority: 2, alternatives: ['TOOL-005'] }
                        ],
                        maintenanceRecords: [
                            { id: 1, date: '2025-06-15', type: '定期检修', personnel: '张师傅', result: '正常', cost: 200 },
                            { id: 2, date: '2025-03-15', type: '定期检修', personnel: '李师傅', result: '正常', cost: 180 }
                        ]
                    },
                    {
                        id: 2,
                        code: 'TOOL-002',
                        name: '铝型材切割模具',
                        type: '模具',
                        specification: 'LJ-45°',
                        manufacturer: '华星模具制造',
                        status: 'in-use',
                        operations: ['切割'],
                        availableStock: 1,
                        totalStock: 2,
                        minStock: 1,
                        location: 'B区-1号架',
                        price: 5800,
                        supplier: '华星模具制造',
                        nextMaintenance: '2025-08-20',
                        materials: ['铝型材'],
                        minSize: 20,
                        maxSize: 100,
                        precision: '标准',
                        serviceLife: 5000,
                        isRequired: true,
                        priority: 1,
                        alternatives: ['TOOL-006'],
                        maintenanceType: '定期检修',
                        maintenanceCycle: 60,
                        maintenanceDuration: 8,
                        maintenanceContent: '检查模具磨损情况，调整切割角度',
                        usageFrequency: 60,
                        efficiency: 90,
                        failureRate: 8,
                        maintenanceOverdue: false,
                        operationDetails: [
                            { id: 1, name: '切割', required: true, priority: 1, alternatives: ['TOOL-006'] }
                        ],
                        maintenanceRecords: [
                            { id: 1, date: '2025-06-20', type: '定期检修', personnel: '王师傅', result: '异常', cost: 350 },
                            { id: 2, date: '2025-04-20', type: '定期检修', personnel: '张师傅', result: '正常', cost: 300 }
                        ]
                    },
                    {
                        id: 3,
                        code: 'TOOL-003',
                        name: '玻璃钻孔刀具',
                        type: '刀具',
                        specification: 'ZK-Φ8mm',
                        manufacturer: '恒信刀具厂',
                        status: 'maintenance',
                        operations: ['钻孔'],
                        availableStock: 0,
                        totalStock: 10,
                        minStock: 3,
                        location: 'C区-2号架',
                        price: 120,
                        supplier: '恒信刀具厂',
                        nextMaintenance: '2025-07-25',
                        materials: ['玻璃'],
                        minSize: 3,
                        maxSize: 20,
                        precision: '高精度',
                        serviceLife: 500,
                        isRequired: true,
                        priority: 1,
                        alternatives: ['TOOL-007'],
                        maintenanceType: '日常保养',
                        maintenanceCycle: 30,
                        maintenanceDuration: 2,
                        maintenanceContent: '清洁刀具，检查磨损情况',
                        usageFrequency: 90,
                        efficiency: 80,
                        failureRate: 12,
                        maintenanceOverdue: true,
                        operationDetails: [
                            { id: 1, name: '钻孔', required: true, priority: 1, alternatives: ['TOOL-007'] }
                        ],
                        maintenanceRecords: [
                            { id: 1, date: '2025-06-25', type: '日常保养', personnel: '李师傅', result: '需更换', cost: 50 },
                            { id: 2, date: '2025-05-25', type: '日常保养', personnel: '张师傅', result: '正常', cost: 40 }
                        ]
                    },
                    {
                        id: 4,
                        code: 'TOOL-004',
                        name: '厚度测量量具',
                        type: '量具',
                        specification: 'CL-0.01mm',
                        manufacturer: '精密工装有限公司',
                        status: 'available',
                        operations: ['检测'],
                        availableStock: 3,
                        totalStock: 5,
                        minStock: 1,
                        location: 'D区-1号柜',
                        price: 850,
                        supplier: '精密工装有限公司',
                        nextMaintenance: '2025-10-10',
                        materials: ['玻璃', '铝型材', '五金件'],
                        minSize: 0,
                        maxSize: 50,
                        precision: '高精度',
                        serviceLife: 2000,
                        isRequired: false,
                        priority: 2,
                        alternatives: ['TOOL-008'],
                        maintenanceType: '定期检修',
                        maintenanceCycle: 180,
                        maintenanceDuration: 4,
                        maintenanceContent: '校准量具，检查精度',
                        usageFrequency: 40,
                        efficiency: 95,
                        failureRate: 3,
                        maintenanceOverdue: false,
                        operationDetails: [
                            { id: 1, name: '检测', required: false, priority: 2, alternatives: ['TOOL-008'] }
                        ],
                        maintenanceRecords: [
                            { id: 1, date: '2025-04-10', type: '定期检修', personnel: '王师傅', result: '正常', cost: 100 },
                            { id: 2, date: '2024-10-10', type: '定期检修', personnel: '张师傅', result: '正常', cost: 90 }
                        ]
                    }
                ],
                categories: [
                    { id: 'all', name: '全部工装', icon: 'ri-apps-line', count: 4 },
                    { id: 'fixture', name: '夹具', icon: 'ri-tools-line', count: 1 },
                    { id: 'mold', name: '模具', icon: 'ri-shape-line', count: 1 },
                    { id: 'cutter', name: '刀具', icon: 'ri-scissors-cut-line', count: 1 },
                    { id: 'gauge', name: '量具', icon: 'ri-ruler-line', count: 1 }
                ],
                operations: [
                    { id: 'cutting', name: '切割' },
                    { id: 'grinding', name: '磨边' },
                    { id: 'drilling', name: '钻孔' },
                    { id: 'testing', name: '检测' }
                ],
                
                // UI状态
                selectedCategory: 'all',
                selectedTooling: null,
                showDetailModal: false,
                showCreateModal: false,
                showEditModal: false,
                showMaintenanceModal: false,
                activeTab: 'basic',
                
                // 筛选条件
                filters: {
                    status: [],
                    operation: ''
                },
                
                // 表单数据
                formData: {
                    id: null,
                    code: '',
                    name: '',
                    type: '',
                    specification: '',
                    manufacturer: '',
                    status: 'available',
                    materials: [],
                    minSize: '',
                    maxSize: '',
                    precision: '',
                    serviceLife: '',
                    operations: [],
                    isRequired: true,
                    priority: 1,
                    alternatives: [],
                    totalStock: 0,
                    availableStock: 0,
                    minStock: 0,
                    location: '',
                    price: 0,
                    supplier: '',
                    maintenanceType: '',
                    maintenanceCycle: '',
                    maintenanceDuration: '',
                    maintenanceContent: '',
                    nextMaintenance: ''
                },
                
                // 维护记录表单
                maintenanceForm: {
                    date: '',
                    type: '',
                    personnel: '',
                    result: '',
                    cost: 0,
                    notes: ''
                },
                
                // 错误信息
                errors: {},
                
                // 计算属性
                get filteredToolings() {
                    let result = [...this.toolings];
                    
                    // 按分类筛选
                    if (this.selectedCategory !== 'all') {
                        const typeMap = {
                            'fixture': '夹具',
                            'mold': '模具',
                            'cutter': '刀具',
                            'gauge': '量具'
                        };
                        result = result.filter(t => t.type === typeMap[this.selectedCategory]);
                    }
                    
                    // 按状态筛选
                    if (this.filters.status.length > 0) {
                        result = result.filter(t => this.filters.status.includes(t.status));
                    }
                    
                    // 按工序筛选
                    if (this.filters.operation) {
                        const opName = this.operations.find(op => op.id === this.filters.operation)?.name;
                        if (opName) {
                            result = result.filter(t => t.operations.includes(opName));
                        }
                    }
                    
                    return result;
                },
                
                // 方法
                selectCategory(categoryId) {
                    this.selectedCategory = categoryId;
                },
                
                viewTooling(tooling) {
                    this.selectedTooling = tooling;
                    this.showDetailModal = true;
                    this.activeTab = 'basic';
                },
                
                editTooling(tooling) {
                    this.formData = { ...tooling };
                    this.showEditModal = true;
                },
                
                saveTooling() {
                    // 验证表单
                    this.errors = {};
                    
                    // 验证工装编码唯一性
                    if (this.formData.code) {
                        const existing = this.toolings.find(t => t.code === this.formData.code && t.id !== this.formData.id);
                        if (existing) {
                            this.errors.code = '工装编码已存在';
                        }
                    }
                    
                    // 验证尺寸范围
                    if (this.formData.minSize && this.formData.maxSize && parseFloat(this.formData.minSize) >= parseFloat(this.formData.maxSize)) {
                        this.errors.sizeRange = '最小尺寸必须小于最大尺寸';
                    }
                    
                    // 验证库存数量
                    if (this.formData.availableStock > this.formData.totalStock) {
                        this.errors.stock = '可用数量不能超过总数量';
                    }
                    
                    // 如果有错误，不保存
                    if (Object.keys(this.errors).length > 0) {
                        return;
                    }
                    
                    // 保存数据
                    if (this.showCreateModal) {
                        // 新建
                        const newId = Math.max(...this.toolings.map(t => t.id)) + 1;
                        this.toolings.push({
                            ...this.formData,
                            id: newId,
                            usageFrequency: 0,
                            efficiency: 0,
                            failureRate: 0,
                            maintenanceOverdue: false,
                            operationDetails: this.formData.operations.map(opId => {
                                const opName = this.operations.find(op => op.id === opId)?.name || '';
                                return {
                                    id: opId,
                                    name: opName,
                                    required: this.formData.isRequired,
                                    priority: this.formData.priority,
                                    alternatives: this.formData.alternatives
                                };
                            }),
                            maintenanceRecords: []
                        });
                        this.showCreateModal = false;
                    } else {
                        // 编辑
                        const index = this.toolings.findIndex(t => t.id === this.formData.id);
                        if (index !== -1) {
                            this.toolings[index] = {
                                ...this.toolings[index],
                                ...this.formData,
                                operationDetails: this.formData.operations.map(opId => {
                                    const opName = this.operations.find(op => op.id === opId)?.name || '';
                                    return {
                                        id: opId,
                                        name: opName,
                                        required: this.formData.isRequired,
                                        priority: this.formData.priority,
                                        alternatives: this.formData.alternatives
                                    };
                                })
                            };
                        }
                        this.showEditModal = false;
                    }
                    
                    // 重置表单
                    this.resetForm();
                },
                
                saveMaintenance() {
                    if (!this.selectedTooling) return;
                    
                    // 添加维护记录
                    const newRecord = {
                        id: Date.now(),
                        ...this.maintenanceForm
                    };
                    
                    const index = this.toolings.findIndex(t => t.id === this.selectedTooling.id);
                    if (index !== -1) {
                        this.toolings[index].maintenanceRecords.unshift(newRecord);
                        
                        // 更新工装状态
                        if (this.maintenanceForm.result === '需更换') {
                            this.toolings[index].status = 'disabled';
                        } else if (this.toolings[index].status === 'maintenance') {
                            this.toolings[index].status = 'available';
                        }
                        
                        // 更新下次维护日期
                        if (this.maintenanceForm.date) {
                            const maintenanceCycle = this.toolings[index].maintenanceCycle || 90;
                            const nextDate = new Date(this.maintenanceForm.date);
                            nextDate.setDate(nextDate.getDate() + maintenanceCycle);
                            this.toolings[index].nextMaintenance = nextDate.toISOString().split('T')[0];
                        }
                    }
                    
                    // 关闭模态框
                    this.showMaintenanceModal = false;
                    this.resetMaintenanceForm();
                    
                    // 显示详情模态框
                    this.showDetailModal = true;
                    this.activeTab = 'maintenance';
                },
                
                resetForm() {
                    this.formData = {
                        id: null,
                        code: '',
                        name: '',
                        type: '',
                        specification: '',
                        manufacturer: '',
                        status: 'available',
                        materials: [],
                        minSize: '',
                        maxSize: '',
                        precision: '',
                        serviceLife: '',
                        operations: [],
                        isRequired: true,
                        priority: 1,
                        alternatives: [],
                        totalStock: 0,
                        availableStock: 0,
                        minStock: 0,
                        location: '',
                        price: 0,
                        supplier: '',
                        maintenanceType: '',
                        maintenanceCycle: '',
                        maintenanceDuration: '',
                        maintenanceContent: '',
                        nextMaintenance: ''
                    };
                    this.errors = {};
                },
                
                resetMaintenanceForm() {
                    this.maintenanceForm = {
                        date: '',
                        type: '',
                        personnel: '',
                        result: '',
                        cost: 0,
                        notes: ''
                    };
                },
                
                getStatusClass(status) {
                    const statusMap = {
                        'available': 'status-available',
                        'in-use': 'status-in-use',
                        'maintenance': 'status-maintenance',
                        'disabled': 'status-disabled'
                    };
                    return statusMap[status] || '';
                },
                
                getStatusText(status) {
                    const statusMap = {
                        'available': '可用',
                        'in-use': '使用中',
                        'maintenance': '维护中',
                        'disabled': '停用'
                    };
                    return statusMap[status] || '';
                },
                
                getResultClass(result) {
                    const resultMap = {
                        '正常': 'bg-green-100 text-green-800',
                        '异常': 'bg-yellow-100 text-yellow-800',
                        '需更换': 'bg-red-100 text-red-800'
                    };
                    return resultMap[result] || '';
                }
            }
        }
    </script>
</body>
</html>
