<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>外协工序管理 - PDM系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        [x-cloak] { display: none !important; }
        .tree-node { transition: all 0.2s ease; }
        .tree-node:hover { background-color: #f0f9ff; }
        .tree-node.active { background-color: #dbeafe; border-left: 3px solid #3b82f6; }
        .operation-card { transition: all 0.2s ease; }
        .operation-card:hover { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
        .operation-card.selected { border-color: #3b82f6; box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1); }
        .supplier-card { transition: all 0.2s ease; }
        .supplier-card:hover { transform: translateY(-2px); box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
        .switch { position: relative; display: inline-block; width: 48px; height: 24px; }
        .switch input { opacity: 0; width: 0; height: 0; }
        .slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #ccc; transition: .4s; border-radius: 24px; }
        .slider:before { position: absolute; content: ""; height: 16px; width: 16px; left: 4px; bottom: 4px; background-color: white; transition: .4s; border-radius: 50%; }
        input:checked + .slider { background-color: #3b82f6; }
        input:checked + .slider:before { transform: translateX(24px); }
    </style>
</head>
<body class="bg-gray-50" x-data="outsourcingApp()">
    <div class="flex h-screen overflow-hidden">
        <!-- 左侧面板 -->
        <div class="w-64 bg-white border-r border-gray-200 flex flex-col">
            <!-- 工艺路线树 -->
            <div class="p-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-800 mb-3">工艺路线</h2>
                <div class="relative mb-3">
                    <input type="text" placeholder="搜索工艺路线..." 
                           class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           x-model="searchTerm">
                    <i class="ri-search-line absolute right-3 top-2.5 text-gray-400"></i>
                </div>
                <div class="overflow-y-auto max-h-96">
                    <template x-for="route in filteredRoutes" :key="route.id">
                        <div class="mb-1">
                            <div class="flex items-center p-2 rounded cursor-pointer tree-node"
                                 :class="selectedRoute?.id === route.id ? 'active' : ''"
                                 @click="selectRoute(route)">
                                <i class="ri-folder-line mr-2 text-gray-500"></i>
                                <span class="text-sm font-medium" x-text="route.name"></span>
                                <span class="ml-auto text-xs text-gray-500" x-text="route.operations.length + '工序'"></span>
                            </div>
                            <template x-if="selectedRoute?.id === route.id">
                                <div class="ml-6 mt-1">
                                    <template x-for="op in route.operations" :key="op.id">
                                        <div class="flex items-center p-2 rounded cursor-pointer tree-node"
                                             :class="selectedOperation?.id === op.id ? 'active' : ''"
                                             @click="selectOperation(op)">
                                            <i class="ri-file-list-3-line mr-2 text-gray-400"></i>
                                            <span class="text-sm" x-text="op.name"></span>
                                            <span class="ml-auto text-xs px-2 py-0.5 rounded-full"
                                                  :class="op.outsourcingStatus === '内制' ? 'bg-gray-100 text-gray-800' : 
                                                          op.outsourcingStatus === '外协' ? 'bg-orange-100 text-orange-800' : 
                                                          'bg-blue-100 text-blue-800'"
                                                  x-text="op.outsourcingStatus"></span>
                                        </div>
                                    </template>
                                </div>
                            </template>
                        </div>
                    </template>
                </div>
            </div>
            
            <!-- 外协统计 -->
            <div class="p-4 flex-1">
                <h3 class="text-md font-semibold text-gray-800 mb-3">外协统计</h3>
                <div class="space-y-3">
                    <div class="bg-blue-50 p-3 rounded-lg">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">外协工序</span>
                            <span class="text-lg font-bold text-blue-600" x-text="stats.outsourcingCount"></span>
                        </div>
                    </div>
                    <div class="bg-green-50 p-3 rounded-lg">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">内制工序</span>
                            <span class="text-lg font-bold text-green-600" x-text="stats.internalCount"></span>
                        </div>
                    </div>
                    <div class="bg-purple-50 p-3 rounded-lg">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">混合工序</span>
                            <span class="text-lg font-bold text-purple-600" x-text="stats.mixedCount"></span>
                        </div>
                    </div>
                    <div class="bg-orange-50 p-3 rounded-lg">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">外协成本</span>
                            <span class="text-lg font-bold text-orange-600" x-text="'¥' + stats.totalCost"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 中间面板 -->
        <div class="flex-1 flex flex-col bg-white">
            <!-- 操作工具栏 -->
            <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                <div>
                    <h1 class="text-xl font-bold text-gray-800">外协工序管理</h1>
                    <p class="text-sm text-gray-500" x-text="selectedRoute ? '当前工艺路线: ' + selectedRoute.name : '请选择工艺路线'"></p>
                </div>
                <div class="flex space-x-2">
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition flex items-center"
                            @click="showAddOperationModal = true">
                        <i class="ri-add-line mr-1"></i> 添加工序
                    </button>
                    <button class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition flex items-center">
                        <i class="ri-refresh-line mr-1"></i> 刷新
                    </button>
                </div>
            </div>
            
            <!-- 工序列表 -->
            <div class="flex-1 overflow-y-auto p-4">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <template x-for="operation in operations" :key="operation.id">
                        <div class="border border-gray-200 rounded-lg p-4 operation-card cursor-pointer"
                             :class="selectedOperation?.id === operation.id ? 'selected' : ''"
                             @click="selectOperation(operation)">
                            <div class="flex justify-between items-start mb-3">
                                <div>
                                    <h3 class="font-semibold text-gray-800" x-text="operation.name"></h3>
                                    <p class="text-sm text-gray-500" x-text="'工序号: ' + operation.code"></p>
                                </div>
                                <span class="text-xs px-2 py-1 rounded-full"
                                      :class="operation.outsourcingStatus === '内制' ? 'bg-gray-100 text-gray-800' : 
                                              operation.outsourcingStatus === '外协' ? 'bg-orange-100 text-orange-800' : 
                                              'bg-blue-100 text-blue-800'"
                                      x-text="operation.outsourcingStatus"></span>
                            </div>
                            
                            <div class="flex items-center justify-between mb-3">
                                <span class="text-sm text-gray-600">外协状态</span>
                                <label class="switch">
                                    <input type="checkbox" 
                                           :checked="operation.outsourcingStatus !== '内制'"
                                           @click.stop="toggleOutsourcingStatus(operation)">
                                    <span class="slider"></span>
                                </label>
                            </div>
                            
                            <div class="text-sm text-gray-600 mb-2">
                                <div class="flex justify-between">
                                    <span>外协类型:</span>
                                    <span x-text="operation.outsourcingType || '-'"></span>
                                </div>
                                <div class="flex justify-between">
                                    <span>主供应商:</span>
                                    <span x-text="operation.primarySupplier || '-'"></span>
                                </div>
                                <div class="flex justify-between">
                                    <span>标准交期:</span>
                                    <span x-text="operation.leadTime ? operation.leadTime + '天' : '-'"></span>
                                </div>
                            </div>
                            
                            <div class="flex justify-between items-center mt-3 pt-3 border-t border-gray-100">
                                <span class="text-sm font-medium text-gray-700">外协成本</span>
                                <span class="text-sm font-bold text-orange-600" x-text="operation.cost ? '¥' + operation.cost : '-'"></span>
                            </div>
                        </div>
                    </template>
                </div>
                
                <!-- 空状态 -->
                <div x-show="!operations.length" class="flex flex-col items-center justify-center h-64">
                    <i class="ri-inbox-line text-5xl text-gray-300 mb-3"></i>
                    <p class="text-gray-500">该工艺路线暂无工序</p>
                    <button class="mt-3 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
                            @click="showAddOperationModal = true">
                        添加工序
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 右侧面板 -->
        <div class="w-96 bg-white border-l border-gray-200 flex flex-col" x-show="selectedOperation">
            <!-- 外协配置面板 -->
            <div class="p-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-800 mb-3">外协配置</h2>
                
                <!-- 外协类型选择 -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">外协类型</label>
                    <div class="grid grid-cols-3 gap-2">
                        <template x-for="type in outsourcingTypes" :key="type">
                            <button class="px-3 py-2 text-sm rounded-lg border transition"
                                    :class="selectedOperation?.outsourcingType === type ? 'bg-blue-100 border-blue-500 text-blue-700' : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'"
                                    @click="updateOutsourcingType(type)"
                                    x-text="type"></button>
                        </template>
                    </div>
                </div>
                
                <!-- 供应商选择 -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">主供应商</label>
                    <div class="relative">
                        <select class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                x-model="selectedOperation.primarySupplierId"
                                @change="updatePrimarySupplier()">
                            <option value="">请选择主供应商</option>
                            <template x-for="supplier in suppliers" :key="supplier.id">
                                <option :value="supplier.id" x-text="supplier.name + ' (' + supplier.rating + '星)'"></option>
                            </template>
                        </select>
                    </div>
                </div>
                
                <!-- 备选供应商 -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">备选供应商</label>
                    <div class="space-y-2">
                        <template x-for="supplier in selectedOperation.backupSuppliers || []" :key="supplier.id">
                            <div class="flex items-center justify-between p-2 bg-gray-50 rounded-lg supplier-card">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-2">
                                        <span class="text-xs font-bold text-blue-700" x-text="supplier.name.charAt(0)"></span>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium" x-text="supplier.name"></p>
                                        <p class="text-xs text-gray-500" x-text="supplier.rating + '星供应商'"></p>
                                    </div>
                                </div>
                                <button class="text-gray-400 hover:text-red-500" @click="removeBackupSupplier(supplier.id)">
                                    <i class="ri-close-line"></i>
                                </button>
                            </div>
                        </template>
                        <button class="w-full py-2 text-sm text-blue-600 border border-dashed border-blue-300 rounded-lg hover:bg-blue-50 transition"
                                @click="showSupplierModal = true">
                            <i class="ri-add-line mr-1"></i> 添加备选供应商
                        </button>
                    </div>
                </div>
                
                <!-- 外协价格配置 -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">外协价格</label>
                    <div class="grid grid-cols-2 gap-2 mb-2">
                        <div>
                            <input type="number" placeholder="单价" 
                                   class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   x-model="selectedOperation.unitPrice"
                                   @input="updateCost()">
                        </div>
                        <div>
                            <select class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    x-model="selectedOperation.pricingMethod">
                                <option value="按件">按件</option>
                                <option value="按时">按时</option>
                                <option value="按面积">按面积</option>
                                <option value="按重量">按重量</option>
                            </select>
                        </div>
                    </div>
                    <div>
                        <label class="block text-xs text-gray-500 mb-1">价格有效期</label>
                        <input type="date" 
                               class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               x-model="selectedOperation.priceValidUntil">
                    </div>
                </div>
                
                <!-- 交期配置 -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">交期配置</label>
                    <div class="grid grid-cols-2 gap-2">
                        <div>
                            <label class="block text-xs text-gray-500 mb-1">标准交期(天)</label>
                            <input type="number" placeholder="天数" 
                                   class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   x-model="selectedOperation.standardLeadTime">
                        </div>
                        <div>
                            <label class="block text-xs text-gray-500 mb-1">加急交期(天)</label>
                            <input type="number" placeholder="天数" 
                                   class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   x-model="selectedOperation.urgentLeadTime">
                        </div>
                    </div>
                </div>
                
                <!-- 质量要求 -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">质量要求</label>
                    <div class="mb-2">
                        <label class="block text-xs text-gray-500 mb-1">技术标准</label>
                        <textarea class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                  rows="2" placeholder="输入技术要求..."
                                  x-model="selectedOperation.technicalStandard"></textarea>
                    </div>
                    <div class="grid grid-cols-2 gap-2">
                        <div>
                            <label class="block text-xs text-gray-500 mb-1">质量标准</label>
                            <select class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    x-model="selectedOperation.qualityStandard">
                                <option value="A级">A级</option>
                                <option value="B级">B级</option>
                                <option value="C级">C级</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-xs text-gray-500 mb-1">合格率要求(%)</label>
                            <input type="number" placeholder="合格率" 
                                   class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   x-model="selectedOperation.passRate">
                        </div>
                    </div>
                </div>
                
                <!-- 操作按钮 -->
                <div class="flex space-x-2">
                    <button class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
                            @click="saveOutsourcingConfig()">
                        保存配置
                    </button>
                    <button class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition"
                            @click="resetConfig()">
                        重置
                    </button>
                </div>
            </div>
            
            <!-- 成本分析面板 -->
            <div class="p-4 flex-1">
                <h2 class="text-lg font-semibold text-gray-800 mb-3">成本分析</h2>
                
                <div class="bg-gray-50 rounded-lg p-4 mb-4">
                    <div class="grid grid-cols-2 gap-4 mb-3">
                        <div>
                            <p class="text-sm text-gray-600">外协成本</p>
                            <p class="text-lg font-bold text-orange-600" x-text="'¥' + (selectedOperation?.outsourcingCost || 0)"></p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">内制成本</p>
                            <p class="text-lg font-bold text-green-600" x-text="'¥' + (selectedOperation?.internalCost || 0)"></p>
                        </div>
                    </div>
                    <div class="pt-3 border-t border-gray-200">
                        <div class="flex justify-between items-center">
                            <p class="text-sm text-gray-600">成本差异</p>
                            <p class="text-lg font-bold" 
                               :class="(selectedOperation?.outsourcingCost || 0) > (selectedOperation?.internalCost || 0) ? 'text-red-600' : 'text-green-600'"
                               x-text="((selectedOperation?.outsourcingCost || 0) - (selectedOperation?.internalCost || 0)) >= 0 ? 
                                      '+' + ((selectedOperation?.outsourcingCost || 0) - (selectedOperation?.internalCost || 0)) : 
                                      ((selectedOperation?.outsourcingCost || 0) - (selectedOperation?.internalCost || 0))"></p>
                        </div>
                    </div>
                </div>
                
                <div class="mb-4">
                    <h3 class="text-md font-medium text-gray-700 mb-2">成本对比</h3>
                    <div class="h-48">
                        <canvas id="costComparisonChart"></canvas>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-md font-medium text-gray-700 mb-2">成本趋势</h3>
                    <div class="h-48">
                        <canvas id="costTrendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 添加工序模态框 -->
    <div x-show="showAddOperationModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" x-cloak>
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div class="p-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">添加工序</h3>
            </div>
            <div class="p-4">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">工序名称</label>
                    <input type="text" placeholder="输入工序名称" 
                           class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           x-model="newOperation.name">
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">工序编号</label>
                    <input type="text" placeholder="输入工序编号" 
                           class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           x-model="newOperation.code">
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">工序描述</label>
                    <textarea class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              rows="3" placeholder="输入工序描述..."
                              x-model="newOperation.description"></textarea>
                </div>
            </div>
            <div class="p-4 border-t border-gray-200 flex justify-end space-x-2">
                <button class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition"
                        @click="showAddOperationModal = false">
                    取消
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
                        @click="addOperation()">
                    添加
                </button>
            </div>
        </div>
    </div>
    
    <!-- 供应商选择模态框 -->
    <div x-show="showSupplierModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" x-cloak>
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div class="p-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">选择备选供应商</h3>
            </div>
            <div class="p-4 max-h-96 overflow-y-auto">
                <div class="relative mb-3">
                    <input type="text" placeholder="搜索供应商..." 
                           class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           x-model="supplierSearchTerm">
                    <i class="ri-search-line absolute right-3 top-2.5 text-gray-400"></i>
                </div>
                <div class="space-y-2">
                    <template x-for="supplier in filteredSuppliers" :key="supplier.id">
                        <div class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
                             @click="selectBackupSupplier(supplier)">
                            <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                <span class="text-sm font-bold text-blue-700" x-text="supplier.name.charAt(0)"></span>
                            </div>
                            <div class="flex-1">
                                <p class="font-medium" x-text="supplier.name"></p>
                                <p class="text-sm text-gray-500" x-text="supplier.rating + '星供应商 · ' + supplier.capability"></p>
                            </div>
                            <div class="flex items-center">
                                <i class="ri-star-fill text-yellow-400"></i>
                                <span class="ml-1 text-sm" x-text="supplier.rating"></span>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
            <div class="p-4 border-t border-gray-200 flex justify-end">
                <button class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition"
                        @click="showSupplierModal = false">
                    关闭
                </button>
            </div>
        </div>
    </div>
    
    <!-- 通知提示 -->
    <div x-show="notification.show" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform translate-y-2"
         x-transition:enter-end="opacity-100 transform translate-y-0"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 transform translate-y-0"
         x-transition:leave-end="opacity-0 transform translate-y-2"
         class="fixed bottom-4 right-4 bg-white rounded-lg shadow-lg p-4 max-w-md z-50"
         x-cloak>
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <i class="ri-check-line text-green-500 text-xl" x-show="notification.type === 'success'"></i>
                <i class="ri-error-warning-line text-yellow-500 text-xl" x-show="notification.type === 'warning'"></i>
                <i class="ri-close-circle-line text-red-500 text-xl" x-show="notification.type === 'error'"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-900" x-text="notification.title"></p>
                <p class="mt-1 text-sm text-gray-500" x-text="notification.message"></p>
            </div>
            <div class="ml-auto pl-3">
                <button @click="notification.show = false" class="text-gray-400 hover:text-gray-500">
                    <i class="ri-close-line"></i>
                </button>
            </div>
        </div>
    </div>

    <script>
        function outsourcingApp() {
            return {
                // 数据状态
                routes: [
                    {
                        id: 1,
                        name: '发动机总装工艺路线',
                        operations: [
                            { id: 101, name: '缸体加工', code: 'OP001', outsourcingStatus: '内制', outsourcingType: '', primarySupplier: '', leadTime: 5, cost: 1200 },
                            { id: 102, name: '曲轴加工', code: 'OP002', outsourcingStatus: '外协', outsourcingType: '完全外协', primarySupplier: '精密机械有限公司', leadTime: 7, cost: 2500 },
                            { id: 103, name: '缸盖加工', code: 'OP003', outsourcingStatus: '混合', outsourcingType: '部分外协', primarySupplier: '汽车零部件公司', leadTime: 6, cost: 1800 }
                        ]
                    },
                    {
                        id: 2,
                        name: '变速箱装配工艺路线',
                        operations: [
                            { id: 201, name: '齿轮加工', code: 'OP101', outsourcingStatus: '外协', outsourcingType: '完全外协', primarySupplier: '齿轮制造厂', leadTime: 8, cost: 3200 },
                            { id: 202, name: '箱体加工', code: 'OP102', outsourcingStatus: '内制', outsourcingType: '', primarySupplier: '', leadTime: 4, cost: 1500 },
                            { id: 203, name: '总成装配', code: 'OP103', outsourcingStatus: '内制', outsourcingType: '', primarySupplier: '', leadTime: 3, cost: 800 }
                        ]
                    }
                ],
                selectedRoute: null,
                selectedOperation: null,
                operations: [],
                suppliers: [
                    { id: 1, name: '精密机械有限公司', rating: 4, capability: '高精度加工' },
                    { id: 2, name: '汽车零部件公司', rating: 5, capability: '发动机部件' },
                    { id: 3, name: '齿轮制造厂', rating: 4, capability: '齿轮加工' },
                    { id: 4, name: '金属加工有限公司', rating: 3, capability: '一般机加工' },
                    { id: 5, name: '精密铸造厂', rating: 4, capability: '铸造工艺' }
                ],
                outsourcingTypes: ['完全外协', '部分外协', '临时外协'],
                searchTerm: '',
                supplierSearchTerm: '',
                showAddOperationModal: false,
                showSupplierModal: false,
                newOperation: {
                    name: '',
                    code: '',
                    description: ''
                },
                notification: {
                    show: false,
                    type: 'success',
                    title: '',
                    message: ''
                },
                stats: {
                    outsourcingCount: 0,
                    internalCount: 0,
                    mixedCount: 0,
                    totalCost: 0
                },
                
                // 计算属性
                get filteredRoutes() {
                    if (!this.searchTerm) return this.routes;
                    return this.routes.filter(route => 
                        route.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                        route.operations.some(op => op.name.toLowerCase().includes(this.searchTerm.toLowerCase()))
                    );
                },
                
                get filteredSuppliers() {
                    if (!this.supplierSearchTerm) return this.suppliers;
                    return this.suppliers.filter(supplier => 
                        supplier.name.toLowerCase().includes(this.supplierSearchTerm.toLowerCase()) ||
                        supplier.capability.toLowerCase().includes(this.supplierSearchTerm.toLowerCase())
                    );
                },
                
                // 方法
                init() {
                    // 初始化默认选中第一个工艺路线
                    if (this.routes.length > 0) {
                        this.selectRoute(this.routes[0]);
                    }
                },
                
                selectRoute(route) {
                    this.selectedRoute = route;
                    this.operations = route.operations;
                    this.selectedOperation = null;
                    this.updateStats();
                },
                
                selectOperation(operation) {
                    this.selectedOperation = { ...operation };
                    
                    // 初始化供应商信息
                    if (operation.primarySupplier) {
                        const supplier = this.suppliers.find(s => s.name === operation.primarySupplier);
                        if (supplier) {
                            this.selectedOperation.primarySupplierId = supplier.id;
                        }
                    }
                    
                    // 初始化备选供应商
                    if (!this.selectedOperation.backupSuppliers) {
                        this.selectedOperation.backupSuppliers = [];
                    }
                    
                    // 初始化成本数据
                    this.selectedOperation.outsourcingCost = operation.cost || 0;
                    this.selectedOperation.internalCost = Math.round(operation.cost * 0.8) || 0;
                    
                    // 渲染图表
                    this.$nextTick(() => {
                        this.renderCostComparisonChart();
                        this.renderCostTrendChart();
                    });
                },
                
                toggleOutsourcingStatus(operation) {
                    if (operation.outsourcingStatus === '内制') {
                        operation.outsourcingStatus = '外协';
                        operation.outsourcingType = '完全外协';
                    } else {
                        operation.outsourcingStatus = '内制';
                        operation.outsourcingType = '';
                    }
                    this.updateStats();
                    this.showNotification('success', '状态更新成功', `工序 "${operation.name}" 已更新为${operation.outsourcingStatus}`);
                },
                
                updateOutsourcingType(type) {
                    if (this.selectedOperation) {
                        this.selectedOperation.outsourcingType = type;
                        
                        // 更新原数据
                        const operation = this.operations.find(op => op.id === this.selectedOperation.id);
                        if (operation) {
                            operation.outsourcingType = type;
                        }
                    }
                },
                
                updatePrimarySupplier() {
                    if (this.selectedOperation && this.selectedOperation.primarySupplierId) {
                        const supplier = this.suppliers.find(s => s.id == this.selectedOperation.primarySupplierId);
                        if (supplier) {
                            this.selectedOperation.primarySupplier = supplier.name;
                            
                            // 更新原数据
                            const operation = this.operations.find(op => op.id === this.selectedOperation.id);
                            if (operation) {
                                operation.primarySupplier = supplier.name;
                            }
                        }
                    }
                },
                
                selectBackupSupplier(supplier) {
                    if (this.selectedOperation) {
                        if (!this.selectedOperation.backupSuppliers) {
                            this.selectedOperation.backupSuppliers = [];
                        }
                        
                        // 检查是否已添加
                        const exists = this.selectedOperation.backupSuppliers.some(s => s.id === supplier.id);
                        if (!exists) {
                            this.selectedOperation.backupSuppliers.push(supplier);
                            this.showNotification('success', '添加成功', `已添加供应商 "${supplier.name}" 为备选供应商`);
                        } else {
                            this.showNotification('warning', '添加失败', `供应商 "${supplier.name}" 已在备选列表中`);
                        }
                    }
                    this.showSupplierModal = false;
                },
                
                removeBackupSupplier(supplierId) {
                    if (this.selectedOperation && this.selectedOperation.backupSuppliers) {
                        this.selectedOperation.backupSuppliers = this.selectedOperation.backupSuppliers.filter(s => s.id !== supplierId);
                    }
                },
                
                updateCost() {
                    if (this.selectedOperation && this.selectedOperation.unitPrice) {
                        // 简单计算成本（实际应用中会更复杂）
                        this.selectedOperation.outsourcingCost = parseFloat(this.selectedOperation.unitPrice) * 100;
                        this.selectedOperation.internalCost = Math.round(this.selectedOperation.outsourcingCost * 0.8);
                        
                        // 更新图表
                        this.$nextTick(() => {
                            this.renderCostComparisonChart();
                        });
                    }
                },
                
                saveOutsourcingConfig() {
                    if (this.selectedOperation) {
                        // 验证必填字段
                        if (this.selectedOperation.outsourcingStatus !== '内制' && !this.selectedOperation.primarySupplier) {
                            this.showNotification('error', '保存失败', '外协工序必须选择主供应商');
                            return;
                        }
                        
                        // 更新原数据
                        const operation = this.operations.find(op => op.id === this.selectedOperation.id);
                        if (operation) {
                            Object.assign(operation, this.selectedOperation);
                        }
                        
                        this.showNotification('success', '保存成功', '外协配置已保存');
                    }
                },
                
                resetConfig() {
                    if (this.selectedOperation) {
                        // 重置为原始数据
                        const operation = this.operations.find(op => op.id === this.selectedOperation.id);
                        if (operation) {
                            this.selectOperation(operation);
                        }
                        this.showNotification('success', '重置成功', '配置已重置为原始数据');
                    }
                },
                
                addOperation() {
                    if (this.newOperation.name && this.newOperation.code) {
                        const newOp = {
                            id: Date.now(),
                            name: this.newOperation.name,
                            code: this.newOperation.code,
                            outsourcingStatus: '内制',
                            outsourcingType: '',
                            primarySupplier: '',
                            leadTime: 0,
                            cost: 0
                        };
                        
                        this.operations.push(newOp);
                        this.selectedRoute.operations.push(newOp);
                        
                        // 重置表单
                        this.newOperation = { name: '', code: '', description: '' };
                        this.showAddOperationModal = false;
                        
                        this.updateStats();
                        this.showNotification('success', '添加成功', `工序 "${newOp.name}" 已添加`);
                    } else {
                        this.showNotification('error', '添加失败', '请填写工序名称和编号');
                    }
                },
                
                updateStats() {
                    this.stats.outsourcingCount = 0;
                    this.stats.internalCount = 0;
                    this.stats.mixedCount = 0;
                    this.stats.totalCost = 0;
                    
                    this.operations.forEach(op => {
                        if (op.outsourcingStatus === '外协') {
                            this.stats.outsourcingCount++;
                        } else if (op.outsourcingStatus === '内制') {
                            this.stats.internalCount++;
                        } else if (op.outsourcingStatus === '混合') {
                            this.stats.mixedCount++;
                        }
                        
                        if (op.cost) {
                            this.stats.totalCost += op.cost;
                        }
                    });
                },
                
                showNotification(type, title, message) {
                    this.notification = {
                        show: true,
                        type,
                        title,
                        message
                    };
                    
                    // 3秒后自动关闭
                    setTimeout(() => {
                        this.notification.show = false;
                    }, 3000);
                },
                
                renderCostComparisonChart() {
                    const ctx = document.getElementById('costComparisonChart');
                    if (!ctx) return;
                    
                    new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: ['外协成本', '内制成本'],
                            datasets: [{
                                label: '成本 (¥)',
                                data: [
                                    this.selectedOperation?.outsourcingCost || 0,
                                    this.selectedOperation?.internalCost || 0
                                ],
                                backgroundColor: [
                                    'rgba(251, 146, 60, 0.7)',
                                    'rgba(16, 185, 129, 0.7)'
                                ],
                                borderColor: [
                                    'rgba(251, 146, 60, 1)',
                                    'rgba(16, 185, 129, 1)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true
                                }
                            }
                        }
                    });
                },
                
                renderCostTrendChart() {
                    const ctx = document.getElementById('costTrendChart');
                    if (!ctx) return;
                    
                    // 模拟历史数据
                    const months = ['1月', '2月', '3月', '4月', '5月', '6月'];
                    const outsourcingData = [2200, 2300, 2400, 2350, 2500, this.selectedOperation?.outsourcingCost || 2500];
                    const internalData = [1800, 1850, 1900, 1880, 2000, this.selectedOperation?.internalCost || 2000];
                    
                    new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: months,
                            datasets: [
                                {
                                    label: '外协成本',
                                    data: outsourcingData,
                                    borderColor: 'rgba(251, 146, 60, 1)',
                                    backgroundColor: 'rgba(251, 146, 60, 0.1)',
                                    tension: 0.3,
                                    fill: true
                                },
                                {
                                    label: '内制成本',
                                    data: internalData,
                                    borderColor: 'rgba(16, 185, 129, 1)',
                                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                                    tension: 0.3,
                                    fill: true
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: false
                                }
                            }
                        }
                    });
                }
            }
        }
    </script>
</body>
</html>
