<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工序管理 - PDM系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        },
                        success: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                        },
                        warning: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            200: '#fde68a',
                            300: '#fcd34d',
                            400: '#fbbf24',
                            500: '#f59e0b',
                            600: '#d97706',
                            700: '#b45309',
                            800: '#92400e',
                            900: '#78350f',
                        },
                        error: {
                            50: '#fef2f2',
                            100: '#fee2e2',
                            200: '#fecaca',
                            300: '#fca5a5',
                            400: '#f87171',
                            500: '#ef4444',
                            600: '#dc2626',
                            700: '#b91c1c',
                            800: '#991b1b',
                            900: '#7f1d1d',
                        },
                        'title-gray': '#374151',
                        'content-gray': '#6b7280',
                        'border-gray': '#e5e7eb',
                        'bg-gray': '#f9fafb',
                    },
                    fontFamily: {
                        'chinese': ['PingFang SC', 'Microsoft YaHei', 'SimSun', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style>
        [x-cloak] { display: none !important; }
        .font-chinese {
            font-family: 'PingFang SC', 'Microsoft YaHei', 'SimSun', sans-serif;
        }
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
</head>
<body class="bg-gray-50 font-chinese" x-data="operationManagement()">
    <!-- 顶部导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="ri-tools-line text-2xl text-primary-600 mr-2"></i>
                        <span class="text-xl font-semibold text-title-gray">工艺管理系统</span>
                    </div>
                    <div class="hidden md:ml-10 md:flex md:space-x-8">
                        <a href="#" class="text-primary-600 border-b-2 border-primary-600 px-1 pt-1 text-sm font-medium">工序管理</a>
                        <a href="#" class="text-gray-500 hover:text-gray-700 border-transparent hover:border-gray-300 border-b-2 px-1 pt-1 text-sm font-medium">工艺路线</a>
                        <a href="#" class="text-gray-500 hover:text-gray-700 border-transparent hover:border-gray-300 border-b-2 px-1 pt-1 text-sm font-medium">BOM管理</a>
                        <a href="#" class="text-gray-500 hover:text-gray-700 border-transparent hover:border-gray-300 border-b-2 px-1 pt-1 text-sm font-medium">技术文档</a>
                    </div>
                </div>
                <div class="flex items-center">
                    <button class="p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <i class="ri-notification-3-line text-xl"></i>
                    </button>
                    <div class="ml-3 relative">
                        <div>
                            <button class="max-w-xs flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                <img class="h-8 w-8 rounded-full" src="https://picsum.photos/seed/user1/40/40.jpg" alt="用户头像">
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="flex h-screen pt-1">
        <!-- 左侧边栏 - 工序分类树 -->
        <div class="w-64 bg-white border-r border-gray-200 flex flex-col">
            <div class="p-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-title-gray">工序分类</h3>
            </div>
            <div class="flex-1 overflow-y-auto custom-scrollbar p-4">
                <div class="space-y-2">
                    <template x-for="category in categories" :key="category.id">
                        <div class="category-item">
                            <div class="flex items-center justify-between p-2 rounded hover:bg-gray-50 cursor-pointer"
                                 :class="selectedCategory === category.id ? 'bg-primary-50 text-primary-600 border-l-2 border-primary-600' : ''"
                                 @click="selectCategory(category.id)">
                                <div class="flex items-center">
                                    <i class="ri-folder-line mr-2" :class="category.expanded ? 'ri-folder-open-line' : 'ri-folder-line'"></i>
                                    <span x-text="category.name"></span>
                                </div>
                                <i class="ri-arrow-down-s-line transition-transform"
                                   :class="category.expanded ? 'rotate-180' : ''"
                                   @click.stop="toggleCategory(category.id)"></i>
                            </div>
                            <div x-show="category.expanded" x-transition class="ml-6 mt-1 space-y-1">
                                <template x-for="subcategory in category.subcategories" :key="subcategory.id">
                                    <div class="flex items-center p-2 rounded hover:bg-gray-50 cursor-pointer text-sm"
                                         :class="selectedCategory === subcategory.id ? 'bg-primary-50 text-primary-600 border-l-2 border-primary-600' : ''"
                                         @click="selectCategory(subcategory.id)">
                                        <i class="ri-file-list-3-line mr-2"></i>
                                        <span x-text="subcategory.name"></span>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
            
            <!-- 筛选区域 -->
            <div class="p-4 border-t border-gray-200">
                <h4 class="text-sm font-medium text-title-gray mb-2">工序类型</h4>
                <div class="space-y-2">
                    <template x-for="type in operationTypes" :key="type.id">
                        <label class="flex items-center">
                            <input type="checkbox" :value="type.id" x-model="selectedTypes" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                            <span class="ml-2 text-sm text-gray-700" x-text="type.name"></span>
                        </label>
                    </template>
                </div>
                
                <h4 class="text-sm font-medium text-title-gray mt-4 mb-2">状态</h4>
                <div class="space-y-2">
                    <template x-for="status in statuses" :key="status.id">
                        <label class="flex items-center">
                            <input type="checkbox" :value="status.id" x-model="selectedStatuses" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                            <span class="ml-2 text-sm text-gray-700" x-text="status.name"></span>
                        </label>
                    </template>
                </div>
            </div>
        </div>

        <!-- 中间主内容区 -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- 顶部操作栏 -->
            <div class="bg-white border-b border-gray-200 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <h1 class="text-2xl font-bold text-title-gray">工序管理</h1>
                        <div class="relative">
                            <input type="text" placeholder="搜索工序编码或名称" x-model="searchKeyword" @keyup.enter="searchOperations"
                                   class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500 w-80">
                            <i class="ri-search-line absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button @click="showImportModal = true" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="ri-upload-2-line mr-2"></i>
                            导入
                        </button>
                        <button @click="exportOperations" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="ri-download-2-line mr-2"></i>
                            导出
                        </button>
                        <button @click="openCreateOperation" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="ri-add-line mr-2"></i>
                            新建工序
                        </button>
                    </div>
                </div>
            </div>

            <!-- 工序列表 -->
            <div class="flex-1 overflow-y-auto custom-scrollbar bg-gray-50">
                <div class="px-6 py-4">
                    <div class="bg-white shadow rounded-lg overflow-hidden">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            <input type="checkbox" x-model="selectAll" @change="toggleSelectAll" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" @click="sortBy('code')">
                                            工序编码
                                            <i class="ri-arrow-up-down-line text-xs"></i>
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" @click="sortBy('name')">
                                            工序名称
                                            <i class="ri-arrow-up-down-line text-xs"></i>
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            工序类型
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            标准工时
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            状态
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" @click="sortBy('updateTime')">
                                            最后更新
                                            <i class="ri-arrow-up-down-line text-xs"></i>
                                        </th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            操作
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <template x-for="operation in filteredOperations" :key="operation.id">
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <input type="checkbox" :value="operation.id" x-model="selectedOperations" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900" x-text="operation.code"></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900" x-text="operation.name"></div>
                                                <div class="text-sm text-gray-500" x-text="operation.description"></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                                                      :class="getOperationTypeClass(operation.type)"
                                                      x-text="getOperationTypeName(operation.type)"></span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                <div>准备: <span x-text="operation.setupTime"></span>分钟</div>
                                                <div>单件: <span x-text="operation.cycleTime"></span>分钟</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                                                      :class="getStatusClass(operation.status)"
                                                      x-text="getStatusName(operation.status)"></span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                <div x-text="operation.updateTime"></div>
                                                <div class="text-xs text-gray-400" x-text="operation.updateUser"></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <button @click="viewOperation(operation)" class="text-primary-600 hover:text-primary-900 mr-3">
                                                    <i class="ri-eye-line"></i>
                                                </button>
                                                <button @click="editOperation(operation)" class="text-indigo-600 hover:text-indigo-900 mr-3">
                                                    <i class="ri-edit-line"></i>
                                                </button>
                                                <button @click="deleteOperation(operation)" class="text-red-600 hover:text-red-900">
                                                    <i class="ri-delete-bin-line"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    </template>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 空状态 -->
                        <div x-show="filteredOperations.length === 0" class="text-center py-12">
                            <i class="ri-inbox-line text-4xl text-gray-400"></i>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">暂无工序数据</h3>
                            <p class="mt-1 text-sm text-gray-500">请创建新的工序或调整筛选条件</p>
                            <div class="mt-6">
                                <button @click="openCreateOperation" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                    <i class="ri-add-line mr-2"></i>
                                    新建工序
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-4 rounded-lg shadow">
                        <div class="flex-1 flex justify-between sm:hidden">
                            <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                上一页
                            </button>
                            <button @click="nextPage" :disabled="currentPage >= totalPages" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                下一页
                            </button>
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700">
                                    显示第 <span class="font-medium" x-text="(currentPage - 1) * pageSize + 1"></span> 至 
                                    <span class="font-medium" x-text="Math.min(currentPage * pageSize, totalItems)"></span> 项，
                                    共 <span class="font-medium" x-text="totalItems"></span> 项结果
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <i class="ri-arrow-left-s-line"></i>
                                    </button>
                                    <template x-for="page in visiblePages" :key="page">
                                        <button @click="goToPage(page)" 
                                                :class="page === currentPage ? 'z-10 bg-primary-50 border-primary-500 text-primary-600' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'"
                                                class="relative inline-flex items-center px-4 py-2 border text-sm font-medium"
                                                x-text="page"></button>
                                    </template>
                                    <button @click="nextPage" :disabled="currentPage >= totalPages" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <i class="ri-arrow-right-s-line"></i>
                                    </button>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧边栏 - 工序统计和关联信息 -->
        <div class="w-80 bg-white border-l border-gray-200 flex flex-col">
            <!-- 工序统计 -->
            <div class="p-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-title-gray mb-4">工序统计</h3>
                <div class="grid grid-cols-2 gap-4">
                    <div class="bg-primary-50 rounded-lg p-3">
                        <div class="text-2xl font-bold text-primary-600" x-text="statistics.total"></div>
                        <div class="text-sm text-gray-600">总工序数</div>
                    </div>
                    <div class="bg-success-50 rounded-lg p-3">
                        <div class="text-2xl font-bold text-success-600" x-text="statistics.active"></div>
                        <div class="text-sm text-gray-600">生效中</div>
                    </div>
                    <div class="bg-warning-50 rounded-lg p-3">
                        <div class="text-2xl font-bold text-warning-600" x-text="statistics.draft"></div>
                        <div class="text-sm text-gray-600">草稿</div>
                    </div>
                    <div class="bg-gray-100 rounded-lg p-3">
                        <div class="text-2xl font-bold text-gray-600" x-text="statistics.inactive"></div>
                        <div class="text-sm text-gray-600">已停用</div>
                    </div>
                </div>
            </div>
            
            <!-- 工序类型分布 -->
            <div class="p-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-title-gray mb-4">工序类型分布</h3>
                <div class="space-y-3">
                    <template x-for="typeStat in typeStatistics" :key="typeStat.type">
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span x-text="getOperationTypeName(typeStat.type)"></span>
                                <span x-text="typeStat.count + ' (' + typeStat.percentage + '%)'"></span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-primary-600 h-2 rounded-full" :style="`width: ${typeStat.percentage}%`"></div>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
            
            <!-- 关联信息 -->
            <div class="p-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-title-gray mb-4">关联信息</h3>
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">工艺路线使用</span>
                        <span class="text-sm font-medium" x-text="relationInfo.routes + ' 个'"></span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">BOM关联</span>
                        <span class="text-sm font-medium" x-text="relationInfo.boms + ' 个'"></span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">文档关联</span>
                        <span class="text-sm font-medium" x-text="relationInfo.documents + ' 个'"></span>
                    </div>
                </div>
            </div>
            
            <!-- 操作历史 -->
            <div class="flex-1 overflow-y-auto custom-scrollbar p-4">
                <h3 class="text-lg font-medium text-title-gray mb-4">操作历史</h3>
                <div class="space-y-4">
                    <template x-for="history in operationHistory" :key="history.id">
                        <div class="border-l-2 border-gray-200 pl-4 pb-4">
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-medium text-gray-900" x-text="history.action"></span>
                                <span class="text-xs text-gray-500" x-text="history.time"></span>
                            </div>
                            <div class="text-sm text-gray-600 mt-1" x-text="history.user"></div>
                            <div class="text-xs text-gray-500 mt-1" x-text="history.detail"></div>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>

    <!-- 工序详情模态框 -->
    <div x-show="showDetailModal" x-cloak class="fixed inset-0 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div x-show="showDetailModal" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div x-show="showDetailModal" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                工序详情 - <span x-text="currentOperation?.code"></span>
                            </h3>
                            <div class="mt-4">
                                <div class="border-b border-gray-200">
                                    <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                                        <button @click="detailTab = 'basic'" :class="detailTab === 'basic' ? 'border-primary-500 text-primary-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                            基本信息
                                        </button>
                                        <button @click="detailTab = 'process'" :class="detailTab === 'process' ? 'border-primary-500 text-primary-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                            工艺参数
                                        </button>
                                        <button @click="detailTab = 'quality'" :class="detailTab === 'quality' ? 'border-primary-500 text-primary-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                            质量要求
                                        </button>
                                        <button @click="detailTab = 'resource'" :class="detailTab === 'resource' ? 'border-primary-500 text-primary-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                            资源要求
                                        </button>
                                        <button @click="detailTab = 'version'" :class="detailTab === 'version' ? 'border-primary-500 text-primary-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                            版本管理
                                        </button>
                                    </nav>
                                </div>
                                
                                <div class="mt-4">
                                    <!-- 基本信息 -->
                                    <div x-show="detailTab === 'basic'" class="space-y-4">
                                        <div class="grid grid-cols-2 gap-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">工序编码</label>
                                                <div class="mt-1 text-sm text-gray-900" x-text="currentOperation?.code"></div>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">工序名称</label>
                                                <div class="mt-1 text-sm text-gray-900" x-text="currentOperation?.name"></div>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">工序类型</label>
                                                <div class="mt-1">
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                                                          :class="getOperationTypeClass(currentOperation?.type)"
                                                          x-text="getOperationTypeName(currentOperation?.type)"></span>
                                                </div>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">状态</label>
                                                <div class="mt-1">
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                                                          :class="getStatusClass(currentOperation?.status)"
                                                          x-text="getStatusName(currentOperation?.status)"></span>
                                                </div>
                                            </div>
                                            <div class="col-span-2">
                                                <label class="block text-sm font-medium text-gray-700">工序描述</label>
                                                <div class="mt-1 text-sm text-gray-900" x-text="currentOperation?.description"></div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 工艺参数 -->
                                    <div x-show="detailTab === 'process'" class="space-y-4">
                                        <div class="grid grid-cols-3 gap-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">准备工时</label>
                                                <div class="mt-1 text-sm text-gray-900"><span x-text="currentOperation?.setupTime"></span> 分钟</div>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">单件工时</label>
                                                <div class="mt-1 text-sm text-gray-900"><span x-text="currentOperation?.cycleTime"></span> 分钟</div>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">拆卸工时</label>
                                                <div class="mt-1 text-sm text-gray-900"><span x-text="currentOperation?.teardownTime || 0"></span> 分钟</div>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">工艺温度</label>
                                                <div class="mt-1 text-sm text-gray-900"><span x-text="currentOperation?.processTemp || '-'"></span> °C</div>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">工艺压力</label>
                                                <div class="mt-1 text-sm text-gray-900"><span x-text="currentOperation?.processPressure || '-'"></span> MPa</div>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">工艺速度</label>
                                                <div class="mt-1 text-sm text-gray-900"><span x-text="currentOperation?.processSpeed || '-'"></span> m/min</div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 质量要求 -->
                                    <div x-show="detailTab === 'quality'" class="space-y-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">质量标准</label>
                                            <div class="mt-1 text-sm text-gray-900" x-text="currentOperation?.qualityStandard"></div>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">检验项目</label>
                                            <div class="mt-1">
                                                <div class="flex flex-wrap gap-2">
                                                    <template x-for="item in currentOperation?.inspectionItems || []" :key="item">
                                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800" x-text="item"></span>
                                                    </template>
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">合格标准</label>
                                            <div class="mt-1 text-sm text-gray-900" x-text="currentOperation?.acceptanceCriteria"></div>
                                        </div>
                                    </div>
                                    
                                    <!-- 资源要求 -->
                                    <div x-show="detailTab === 'resource'" class="space-y-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">设备要求</label>
                                            <div class="mt-1">
                                                <div class="flex flex-wrap gap-2">
                                                    <template x-for="equipment in currentOperation?.equipmentRequirements || []" :key="equipment">
                                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800" x-text="equipment"></span>
                                                    </template>
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">技能要求</label>
                                            <div class="mt-1 text-sm text-gray-900" x-text="currentOperation?.skillRequirement"></div>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">人员数量</label>
                                            <div class="mt-1 text-sm text-gray-900"><span x-text="currentOperation?.personnelCount || 1"></span> 人</div>
                                        </div>
                                    </div>
                                    
                                    <!-- 版本管理 -->
                                    <div x-show="detailTab === 'version'" class="space-y-4">
                                        <div class="bg-gray-50 rounded-lg p-4">
                                            <div class="flex justify-between items-center">
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900">当前版本</div>
                                                    <div class="text-lg font-bold text-primary-600" x-text="currentOperation?.version"></div>
                                                </div>
                                                <div>
                                                    <div class="text-sm text-gray-500">生效日期</div>
                                                    <div class="text-sm text-gray-900" x-text="currentOperation?.effectiveDate"></div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">历史版本</label>
                                            <div class="mt-2 space-y-2">
                                                <template x-for="version in currentOperation?.versions || []" :key="version.number">
                                                    <div class="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg">
                                                        <div>
                                                            <div class="text-sm font-medium text-gray-900">v<span x-text="version.number"></span></div>
                                                            <div class="text-xs text-gray-500" x-text="version.date"></div>
                                                        </div>
                                                        <div class="text-xs text-gray-500" x-text="version.changeLog"></div>
                                                    </div>
                                                </template>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button @click="editOperation(currentOperation)" type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm">
                        编辑
                    </button>
                    <button @click="showDetailModal = false" type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 工序编辑模态框 -->
    <div x-show="showEditModal" x-cloak class="fixed inset-0 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div x-show="showEditModal" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div x-show="showEditModal" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
                <form @submit.prevent="saveOperation">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                    <span x-text="isEditing ? '编辑工序' : '新建工序'"></span>
                                </h3>
                                <div class="mt-4">
                                    <div class="border-b border-gray-200">
                                        <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                                            <button @click="editTab = 'basic'" :class="editTab === 'basic' ? 'border-primary-500 text-primary-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                                基本信息
                                            </button>
                                            <button @click="editTab = 'process'" :class="editTab === 'process' ? 'border-primary-500 text-primary-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                                工艺参数
                                            </button>
                                            <button @click="editTab = 'quality'" :class="editTab === 'quality' ? 'border-primary-500 text-primary-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                                质量要求
                                            </button>
                                            <button @click="editTab = 'resource'" :class="editTab === 'resource' ? 'border-primary-500 text-primary-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                                资源要求
                                            </button>
                                        </nav>
                                    </div>
                                    
                                    <div class="mt-4">
                                        <!-- 基本信息 -->
                                        <div x-show="editTab === 'basic'" class="space-y-4">
                                            <div class="grid grid-cols-2 gap-4">
                                                <div>
                                                    <label for="operationCode" class="block text-sm font-medium text-gray-700">工序编码 <span class="text-red-500">*</span></label>
                                                    <input type="text" id="operationCode" x-model="editForm.code" required
                                                           class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                                    <div x-show="errors.code" class="mt-1 text-sm text-red-600" x-text="errors.code"></div>
                                                </div>
                                                <div>
                                                    <label for="operationName" class="block text-sm font-medium text-gray-700">工序名称 <span class="text-red-500">*</span></label>
                                                    <input type="text" id="operationName" x-model="editForm.name" required
                                                           class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                                    <div x-show="errors.name" class="mt-1 text-sm text-red-600" x-text="errors.name"></div>
                                                </div>
                                                <div>
                                                    <label for="operationType" class="block text-sm font-medium text-gray-700">工序类型 <span class="text-red-500">*</span></label>
                                                    <select id="operationType" x-model="editForm.type" required
                                                            class="mt-1 block w-full bg-white border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                                        <option value="">请选择</option>
                                                        <template x-for="type in operationTypes" :key="type.id">
                                                            <option :value="type.id" x-text="type.name"></option>
                                                        </template>
                                                    </select>
                                                    <div x-show="errors.type" class="mt-1 text-sm text-red-600" x-text="errors.type"></div>
                                                </div>
                                                <div>
                                                    <label for="operationStatus" class="block text-sm font-medium text-gray-700">状态</label>
                                                    <select id="operationStatus" x-model="editForm.status"
                                                            class="mt-1 block w-full bg-white border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                                        <template x-for="status in statuses" :key="status.id">
                                                            <option :value="status.id" x-text="status.name"></option>
                                                        </template>
                                                    </select>
                                                </div>
                                                <div class="col-span-2">
                                                    <label for="operationDescription" class="block text-sm font-medium text-gray-700">工序描述</label>
                                                    <textarea id="operationDescription" x-model="editForm.description" rows="3"
                                                              class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"></textarea>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- 工艺参数 -->
                                        <div x-show="editTab === 'process'" class="space-y-4">
                                            <div class="grid grid-cols-3 gap-4">
                                                <div>
                                                    <label for="setupTime" class="block text-sm font-medium text-gray-700">准备工时 (分钟) <span class="text-red-500">*</span></label>
                                                    <input type="number" id="setupTime" x-model.number="editForm.setupTime" min="0" required
                                                           class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                                    <div x-show="errors.setupTime" class="mt-1 text-sm text-red-600" x-text="errors.setupTime"></div>
                                                </div>
                                                <div>
                                                    <label for="cycleTime" class="block text-sm font-medium text-gray-700">单件工时 (分钟) <span class="text-red-500">*</span></label>
                                                    <input type="number" id="cycleTime" x-model.number="editForm.cycleTime" min="0" required
                                                           class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                                    <div x-show="errors.cycleTime" class="mt-1 text-sm text-red-600" x-text="errors.cycleTime"></div>
                                                </div>
                                                <div>
                                                    <label for="teardownTime" class="block text-sm font-medium text-gray-700">拆卸工时 (分钟)</label>
                                                    <input type="number" id="teardownTime" x-model.number="editForm.teardownTime" min="0"
                                                           class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                                </div>
                                                <div>
                                                    <label for="processTemp" class="block text-sm font-medium text-gray-700">工艺温度 (°C)</label>
                                                    <input type="number" id="processTemp" x-model.number="editForm.processTemp" step="0.1"
                                                           class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                                </div>
                                                <div>
                                                    <label for="processPressure" class="block text-sm font-medium text-gray-700">工艺压力 (MPa)</label>
                                                    <input type="number" id="processPressure" x-model.number="editForm.processPressure" step="0.01"
                                                           class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                                </div>
                                                <div>
                                                    <label for="processSpeed" class="block text-sm font-medium text-gray-700">工艺速度 (m/min)</label>
                                                    <input type="number" id="processSpeed" x-model.number="editForm.processSpeed" step="0.1"
                                                           class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- 质量要求 -->
                                        <div x-show="editTab === 'quality'" class="space-y-4">
                                            <div>
                                                <label for="qualityStandard" class="block text-sm font-medium text-gray-700">质量标准</label>
                                                <textarea id="qualityStandard" x-model="editForm.qualityStandard" rows="3"
                                                          class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"></textarea>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">检验项目</label>
                                                <div class="mt-1 space-y-2">
                                                    <template x-for="(item, index) in editForm.inspectionItems" :key="index">
                                                        <div class="flex items-center space-x-2">
                                                            <input type="text" x-model="editForm.inspectionItems[index]" 
                                                                   class="flex-1 border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                                            <button type="button" @click="editForm.inspectionItems.splice(index, 1)" class="text-red-600 hover:text-red-800">
                                                                <i class="ri-delete-bin-line"></i>
                                                            </button>
                                                        </div>
                                                    </template>
                                                    <button type="button" @click="editForm.inspectionItems.push('')" class="text-primary-600 hover:text-primary-800 text-sm">
                                                        <i class="ri-add-line"></i> 添加检验项目
                                                    </button>
                                                </div>
                                            </div>
                                            <div>
                                                <label for="acceptanceCriteria" class="block text-sm font-medium text-gray-700">合格标准</label>
                                                <textarea id="acceptanceCriteria" x-model="editForm.acceptanceCriteria" rows="3"
                                                          class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"></textarea>
                                            </div>
                                        </div>
                                        
                                        <!-- 资源要求 -->
                                        <div x-show="editTab === 'resource'" class="space-y-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">设备要求</label>
                                                <div class="mt-1 space-y-2">
                                                    <template x-for="(equipment, index) in editForm.equipmentRequirements" :key="index">
                                                        <div class="flex items-center space-x-2">
                                                            <input type="text" x-model="editForm.equipmentRequirements[index]" 
                                                                   class="flex-1 border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                                            <button type="button" @click="editForm.equipmentRequirements.splice(index, 1)" class="text-red-600 hover:text-red-800">
                                                                <i class="ri-delete-bin-line"></i>
                                                            </button>
                                                        </div>
                                                    </template>
                                                    <button type="button" @click="editForm.equipmentRequirements.push('')" class="text-primary-600 hover:text-primary-800 text-sm">
                                                        <i class="ri-add-line"></i> 添加设备要求
                                                    </button>
                                                </div>
                                            </div>
                                            <div>
                                                <label for="skillRequirement" class="block text-sm font-medium text-gray-700">技能要求</label>
                                                <select id="skillRequirement" x-model="editForm.skillRequirement"
                                                        class="mt-1 block w-full bg-white border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                                    <option value="">请选择</option>
                                                    <option value="初级">初级</option>
                                                    <option value="中级">中级</option>
                                                    <option value="高级">高级</option>
                                                    <option value="专家">专家</option>
                                                </select>
                                            </div>
                                            <div>
                                                <label for="personnelCount" class="block text-sm font-medium text-gray-700">人员数量</label>
                                                <input type="number" id="personnelCount" x-model.number="editForm.personnelCount" min="1"
                                                       class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm">
                            保存
                        </button>
                        <button @click="showEditModal = false" type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                            取消
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div x-show="showDeleteModal" x-cloak class="fixed inset-0 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div x-show="showDeleteModal" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div x-show="showDeleteModal" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                            <i class="ri-error-warning-line text-red-600"></i>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                删除工序
                            </h3>
                            <div class="mt-2">
                                <p class="text-sm text-gray-500">
                                    您确定要删除工序 "<span x-text="operationToDelete?.name"></span>" 吗？此操作不可撤销。
                                </p>
                                <div x-show="operationToDelete?.isUsed" class="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                                    <p class="text-sm text-yellow-800">
                                        <i class="ri-alert-line"></i> 该工序正在被工艺路线使用，不能直接删除。您可以选择停用该工序。
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button x-show="!operationToDelete?.isUsed" @click="confirmDelete" type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                        删除
                    </button>
                    <button x-show="operationToDelete?.isUsed" @click="deactivateOperation" type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-yellow-600 text-base font-medium text-white hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 sm:ml-3 sm:w-auto sm:text-sm">
                        停用
                    </button>
                    <button @click="showDeleteModal = false" type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        取消
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 导入模态框 -->
    <div x-show="showImportModal" x-cloak class="fixed inset-0 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div x-show="showImportModal" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div x-show="showImportModal" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                导入工序
                            </h3>
                            <div class="mt-4">
                                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                    <i class="ri-file-upload-line text-4xl text-gray-400"></i>
                                    <div class="mt-4">
                                        <label for="file-upload" class="cursor-pointer bg-white rounded-md font-medium text-primary-600 hover:text-primary-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500">
                                            <span>上传文件</span>
                                            <input id="file-upload" name="file-upload" type="file" class="sr-only" @change="handleFileUpload" accept=".xlsx,.xls,.csv">
                                        </label>
                                        <p class="pl-1">或拖拽文件到此处</p>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-2">支持 XLSX, XLS, CSV 格式</p>
                                </div>
                                <div x-show="importFile" class="mt-4 p-3 bg-gray-50 rounded-md">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <i class="ri-file-excel-2-line text-green-600 text-xl mr-2"></i>
                                            <span class="text-sm text-gray-700" x-text="importFile?.name"></span>
                                        </div>
                                        <button @click="importFile = null" class="text-gray-400 hover:text-gray-600">
                                            <i class="ri-close-line"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="mt-4">
                                    <a href="#" class="text-primary-600 hover:text-primary-800 text-sm">
                                        <i class="ri-download-line"></i> 下载导入模板
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button @click="confirmImport" :disabled="!importFile" type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed">
                        导入
                    </button>
                    <button @click="showImportModal = false" type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        取消
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 通知提示 -->
    <div x-show="notification.show" x-cloak
         x-transition:enter="transform ease-out duration-300 transition"
         x-transition:enter-start="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
         x-transition:enter-end="translate-y-0 opacity-100 sm:translate-x-0"
         x-transition:leave="transition ease-in duration-100"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed bottom-0 right-0 mb-4 mr-4 max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden">
        <div class="p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i x-show="notification.type === 'success'" class="ri-checkbox-circle-line text-green-400 text-xl"></i>
                    <i x-show="notification.type === 'error'" class="ri-error-warning-line text-red-400 text-xl"></i>
                    <i x-show="notification.type === 'warning'" class="ri-alert-line text-yellow-400 text-xl"></i>
                    <i x-show="notification.type === 'info'" class="ri-information-line text-blue-400 text-xl"></i>
                </div>
                <div class="ml-3 w-0 flex-1 pt-0.5">
                    <p class="text-sm font-medium text-gray-900" x-text="notification.title"></p>
                    <p class="mt-1 text-sm text-gray-500" x-text="notification.message"></p>
                </div>
                <div class="ml-4 flex-shrink-0 flex">
                    <button @click="notification.show = false" class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <i class="ri-close-line"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function operationManagement() {
            return {
                // 页面状态
                searchKeyword: '',
                selectedCategory: null,
                selectedTypes: [],
                selectedStatuses: [],
                selectedOperations: [],
                selectAll: false,
                currentPage: 1,
                pageSize: 10,
                sortField: 'updateTime',
                sortOrder: 'desc',
                
                // 模态框状态
                showDetailModal: false,
                showEditModal: false,
                showDeleteModal: false,
                showImportModal: false,
                detailTab: 'basic',
                editTab: 'basic',
                
                // 表单状态
                isEditing: false,
                currentOperation: null,
                operationToDelete: null,
                importFile: null,
                errors: {},
                
                // 通知状态
                notification: {
                    show: false,
                    type: 'info',
                    title: '',
                    message: '',
                    timeout: null
                },
                
                // 编辑表单
                editForm: {
                    id: null,
                    code: '',
                    name: '',
                    type: '',
                    status: 'draft',
                    description: '',
                    setupTime: 0,
                    cycleTime: 0,
                    teardownTime: 0,
                    processTemp: null,
                    processPressure: null,
                    processSpeed: null,
                    qualityStandard: '',
                    inspectionItems: [''],
                    acceptanceCriteria: '',
                    equipmentRequirements: [''],
                    skillRequirement: '',
                    personnelCount: 1
                },
                
                // 分类数据
                categories: [
                    {
                        id: 1,
                        name: '玻璃加工',
                        expanded: true,
                        subcategories: [
                            { id: 11, name: '切割工序' },
                            { id: 12, name: '磨边工序' },
                            { id: 13, name: '钢化工序' },
                            { id: 14, name: '合片工序' }
                        ]
                    },
                    {
                        id: 2,
                        name: '型材加工',
                        expanded: false,
                        subcategories: [
                            { id: 21, name: '切割工序' },
                            { id: 22, name: '钻孔工序' },
                            { id: 23, name: '焊接工序' }
                        ]
                    },
                    {
                        id: 3,
                        name: '组装工序',
                        expanded: false,
                        subcategories: [
                            { id: 31, name: '框架组装' },
                            { id: 32, name: '玻璃安装' },
                            { id: 33, name: '密封处理' }
                        ]
                    }
                ],
                
                // 工序类型
                operationTypes: [
                    { id: 'cutting', name: '切割' },
                    { id: 'grinding', name: '磨边' },
                    { id: 'tempering', name: '钢化' },
                    { id: 'laminating', name: '合片' },
                    { id: 'drilling', name: '钻孔' },
                    { id: 'welding', name: '焊接' },
                    { id: 'assembly', name: '组装' },
                    { id: 'sealing', name: '密封' }
                ],
                
                // 状态
                statuses: [
                    { id: 'draft', name: '草稿' },
                    { id: 'active', name: '生效' },
                    { id: 'inactive', name: '停用' }
                ],
                
                // 工序数据
                operations: [
                    {
                        id: 1,
                        code: 'OP-CUT-001',
                        name: '玻璃原片切割',
                        type: 'cutting',
                        status: 'active',
                        description: '使用切割机将玻璃原片切割成所需尺寸',
                        setupTime: 10,
                        cycleTime: 2,
                        teardownTime: 5,
                        processTemp: null,
                        processPressure: null,
                        processSpeed: 15,
                        qualityStandard: '切割尺寸误差±0.5mm，边缘无崩边',
                        inspectionItems: ['尺寸检查', '边缘质量'],
                        acceptanceCriteria: '尺寸误差≤0.5mm，边缘无崩边、裂纹',
                        equipmentRequirements: ['玻璃切割机'],
                        skillRequirement: '中级',
                        personnelCount: 1,
                        updateTime: '2025-07-30 14:30',
                        updateUser: '张工',
                        version: '1.0',
                        effectiveDate: '2025-07-01',
                        isUsed: true,
                        versions: [
                            { number: '0.9', date: '2025-06-15', changeLog: '初始版本' }
                        ]
                    },
                    {
                        id: 2,
                        code: 'OP-GRD-001',
                        name: '玻璃边缘磨边',
                        type: 'grinding',
                        status: 'active',
                        description: '对切割后的玻璃边缘进行磨边处理',
                        setupTime: 15,
                        cycleTime: 3,
                        teardownTime: 5,
                        processTemp: null,
                        processPressure: null,
                        processSpeed: 8,
                        qualityStandard: '边缘光滑，无毛刺',
                        inspectionItems: ['边缘光滑度', '尺寸检查'],
                        acceptanceCriteria: '边缘光滑无毛刺，尺寸符合要求',
                        equipmentRequirements: ['玻璃磨边机'],
                        skillRequirement: '中级',
                        personnelCount: 1,
                        updateTime: '2025-07-29 10:15',
                        updateUser: '李工',
                        version: '1.2',
                        effectiveDate: '2025-07-10',
                        isUsed: true,
                        versions: [
                            { number: '1.1', date: '2025-06-20', changeLog: '优化工艺参数' },
                            { number: '1.0', date: '2025-06-01', changeLog: '初始版本' }
                        ]
                    },
                    {
                        id: 3,
                        code: 'OP-TEMP-001',
                        name: '玻璃钢化处理',
                        type: 'tempering',
                        status: 'active',
                        description: '对玻璃进行钢化处理，提高强度',
                        setupTime: 30,
                        cycleTime: 15,
                        teardownTime: 10,
                        processTemp: 650,
                        processPressure: null,
                        processSpeed: 5,
                        qualityStandard: '钢化均匀，无变形',
                        inspectionItems: ['钢化应力', '平整度', '尺寸检查'],
                        acceptanceCriteria: '钢化应力≥90MPa，平整度≤0.2mm/100mm',
                        equipmentRequirements: ['玻璃钢化炉'],
                        skillRequirement: '高级',
                        personnelCount: 2,
                        updateTime: '2025-07-28 16:45',
                        updateUser: '王工',
                        version: '2.0',
                        effectiveDate: '2025-07-15',
                        isUsed: false,
                        versions: [
                            { number: '1.5', date: '2025-06-25', changeLog: '调整温度参数' },
                            { number: '1.0', date: '2025-05-10', changeLog: '初始版本' }
                        ]
                    },
                    {
                        id: 4,
                        code: 'OP-LAM-001',
                        name: '中空玻璃合片',
                        type: 'laminating',
                        status: 'draft',
                        description: '将两片玻璃合成为中空玻璃',
                        setupTime: 20,
                        cycleTime: 8,
                        teardownTime: 5,
                        processTemp: null,
                        processPressure: 0.8,
                        processSpeed: 3,
                        qualityStandard: '合片平整，密封良好',
                        inspectionItems: ['合片平整度', '密封性', '尺寸检查'],
                        acceptanceCriteria: '合片平整度≤0.3mm，密封性良好',
                        equipmentRequirements: ['中空玻璃生产线', '密封胶涂布机'],
                        skillRequirement: '高级',
                        personnelCount: 2,
                        updateTime: '2025-07-31 09:20',
                        updateUser: '赵工',
                        version: '0.8',
                        effectiveDate: '',
                        isUsed: false,
                        versions: []
                    }
                ],
                
                // 统计数据
                statistics: {
                    total: 4,
                    active: 3,
                    draft: 1,
                    inactive: 0
                },
                
                // 类型统计
                typeStatistics: [
                    { type: 'cutting', count: 1, percentage: 25 },
                    { type: 'grinding', count: 1, percentage: 25 },
                    { type: 'tempering', count: 1, percentage: 25 },
                    { type: 'laminating', count: 1, percentage: 25 }
                ],
                
                // 关联信息
                relationInfo: {
                    routes: 12,
                    boms: 8,
                    documents: 15
                },
                
                // 操作历史
                operationHistory: [
                    { id: 1, action: '创建工序', user: '张工', time: '2025-07-30 14:30', detail: '创建工序 OP-CUT-001' },
                    { id: 2, action: '更新工序', user: '李工', time: '2025-07-29 10:15', detail: '更新工序 OP-GRD-001' },
                    { id: 3, action: '更新工序', user: '王工', time: '2025-07-28 16:45', detail: '更新工序 OP-TEMP-001' },
                    { id: 4, action: '创建工序', user: '赵工', time: '2025-07-31 09:20', detail: '创建工序 OP-LAM-001' }
                ],
                
                // 计算属性
                get filteredOperations() {
                    let result = [...this.operations];
                    
                    // 搜索过滤
                    if (this.searchKeyword) {
                        const keyword = this.searchKeyword.toLowerCase();
                        result = result.filter(op => 
                            op.code.toLowerCase().includes(keyword) || 
                            op.name.toLowerCase().includes(keyword)
                        );
                    }
                    
                    // 分类过滤
                    if (this.selectedCategory) {
                        // 这里简化处理，实际应根据分类ID过滤
                        result = result.filter(op => {
                            if (this.selectedCategory >= 11 && this.selectedCategory <= 14) {
                                return op.type === 'cutting' || op.type === 'grinding' || op.type === 'tempering' || op.type === 'laminating';
                            } else if (this.selectedCategory >= 21 && this.selectedCategory <= 23) {
                                return op.type === 'cutting' || op.type === 'drilling' || op.type === 'welding';
                            } else if (this.selectedCategory >= 31 && this.selectedCategory <= 33) {
                                return op.type === 'assembly' || op.type === 'sealing';
                            }
                            return true;
                        });
                    }
                    
                    // 类型过滤
                    if (this.selectedTypes.length > 0) {
                        result = result.filter(op => this.selectedTypes.includes(op.type));
                    }
                    
                    // 状态过滤
                    if (this.selectedStatuses.length > 0) {
                        result = result.filter(op => this.selectedStatuses.includes(op.status));
                    }
                    
                    // 排序
                    result.sort((a, b) => {
                        let aVal = a[this.sortField];
                        let bVal = b[this.sortField];
                        
                        if (this.sortOrder === 'asc') {
                            return aVal > bVal ? 1 : -1;
                        } else {
                            return aVal < bVal ? 1 : -1;
                        }
                    });
                    
                    return result;
                },
                
                get totalItems() {
                    return this.filteredOperations.length;
                },
                
                get totalPages() {
                    return Math.ceil(this.totalItems / this.pageSize);
                },
                
                get visiblePages() {
                    const pages = [];
                    const maxVisible = 5;
                    let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));
                    let end = Math.min(this.totalPages, start + maxVisible - 1);
                    
                    if (end - start + 1 < maxVisible) {
                        start = Math.max(1, end - maxVisible + 1);
                    }
                    
                    for (let i = start; i <= end; i++) {
                        pages.push(i);
                    }
                    
                    return pages;
                },
                
                // 方法
                selectCategory(categoryId) {
                    this.selectedCategory = categoryId;
                    this.currentPage = 1;
                },
                
                toggleCategory(categoryId) {
                    const category = this.categories.find(c => c.id === categoryId);
                    if (category) {
                        category.expanded = !category.expanded;
                    }
                },
                
                searchOperations() {
                    this.currentPage = 1;
                },
                
                sortBy(field) {
                    if (this.sortField === field) {
                        this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
                    } else {
                        this.sortField = field;
                        this.sortOrder = 'asc';
                    }
                },
                
                toggleSelectAll() {
                    if (this.selectAll) {
                        this.selectedOperations = this.filteredOperations.map(op => op.id);
                    } else {
                        this.selectedOperations = [];
                    }
                },
                
                openCreateOperation() {
                    this.isEditing = false;
                    this.resetEditForm();
                    this.showEditModal = true;
                },
                
                viewOperation(operation) {
                    this.currentOperation = operation;
                    this.showDetailModal = true;
                },
                
                editOperation(operation) {
                    this.isEditing = true;
                    this.currentOperation = operation;
                    this.resetEditForm();
                    
                    // 填充表单数据
                    this.editForm = { ...operation };
                    this.showEditModal = true;
                },
                
                deleteOperation(operation) {
                    this.operationToDelete = operation;
                    this.showDeleteModal = true;
                },
                
                confirmDelete() {
                    const index = this.operations.findIndex(op => op.id === this.operationToDelete.id);
                    if (index !== -1) {
                        this.operations.splice(index, 1);
                        this.showNotification('success', '删除成功', `工序 "${this.operationToDelete.name}" 已删除`);
                    }
                    this.showDeleteModal = false;
                    this.operationToDelete = null;
                },
                
                deactivateOperation() {
                    const operation = this.operations.find(op => op.id === this.operationToDelete.id);
                    if (operation) {
                        operation.status = 'inactive';
                        this.showNotification('warning', '停用成功', `工序 "${operation.name}" 已停用`);
                    }
                    this.showDeleteModal = false;
                    this.operationToDelete = null;
                },
                
                saveOperation() {
                    // 验证表单
                    if (!this.validateForm()) {
                        return;
                    }
                    
                    if (this.isEditing) {
                        // 更新工序
                        const index = this.operations.findIndex(op => op.id === this.editForm.id);
                        if (index !== -1) {
                            this.operations[index] = { ...this.editForm };
                            this.showNotification('success', '更新成功', `工序 "${this.editForm.name}" 已更新`);
                        }
                    } else {
                        // 创建新工序
                        const newOperation = {
                            ...this.editForm,
                            id: this.operations.length + 1,
                            updateTime: new Date().toLocaleString('zh-CN'),
                            updateUser: '当前用户',
                            version: '1.0',
                            effectiveDate: new Date().toISOString().split('T')[0],
                            isUsed: false,
                            versions: []
                        };
                        this.operations.push(newOperation);
                        this.showNotification('success', '创建成功', `工序 "${newOperation.name}" 已创建`);
                    }
                    
                    this.showEditModal = false;
                },
                
                validateForm() {
                    this.errors = {};
                    
                    if (!this.editForm.code) {
                        this.errors.code = '工序编码不能为空';
                    } else if (this.operations.some(op => op.code === this.editForm.code && op.id !== this.editForm.id)) {
                        this.errors.code = '工序编码已存在';
                    }
                    
                    if (!this.editForm.name) {
                        this.errors.name = '工序名称不能为空';
                    }
                    
                    if (!this.editForm.type) {
                        this.errors.type = '请选择工序类型';
                    }
                    
                    if (this.editForm.setupTime <= 0) {
                        this.errors.setupTime = '准备工时必须大于0';
                    }
                    
                    if (this.editForm.cycleTime <= 0) {
                        this.errors.cycleTime = '单件工时必须大于0';
                    }
                    
                    return Object.keys(this.errors).length === 0;
                },
                
                resetEditForm() {
                    this.editForm = {
                        id: null,
                        code: '',
                        name: '',
                        type: '',
                        status: 'draft',
                        description: '',
                        setupTime: 0,
                        cycleTime: 0,
                        teardownTime: 0,
                        processTemp: null,
                        processPressure: null,
                        processSpeed: null,
                        qualityStandard: '',
                        inspectionItems: [''],
                        acceptanceCriteria: '',
                        equipmentRequirements: [''],
                        skillRequirement: '',
                        personnelCount: 1
                    };
                    this.errors = {};
                },
                
                handleFileUpload(event) {
                    const file = event.target.files[0];
                    if (file) {
                        this.importFile = file;
                    }
                },
                
                confirmImport() {
                    if (this.importFile) {
                        // 模拟导入过程
                        this.showNotification('success', '导入成功', `文件 "${this.importFile.name}" 导入成功`);
                        this.showImportModal = false;
                        this.importFile = null;
                    }
                },
                
                exportOperations() {
                    // 模拟导出过程
                    this.showNotification('info', '导出中', '正在导出工序数据...');
                    
                    setTimeout(() => {
                        this.showNotification('success', '导出成功', '工序数据已导出');
                    }, 1500);
                },
                
                showNotification(type, title, message) {
                    // 清除之前的定时器
                    if (this.notification.timeout) {
                        clearTimeout(this.notification.timeout);
                    }
                    
                    this.notification = {
                        show: true,
                        type,
                        title,
                        message,
                        timeout: setTimeout(() => {
                            this.notification.show = false;
                        }, 3000)
                    };
                },
                
                // 分页方法
                prevPage() {
                    if (this.currentPage > 1) {
                        this.currentPage--;
                    }
                },
                
                nextPage() {
                    if (this.currentPage < this.totalPages) {
                        this.currentPage++;
                    }
                },
                
                goToPage(page) {
                    this.currentPage = page;
                },
                
                // 辅助方法
                getOperationTypeClass(type) {
                    const classes = {
                        'cutting': 'bg-blue-100 text-blue-800',
                        'grinding': 'bg-green-100 text-green-800',
                        'tempering': 'bg-yellow-100 text-yellow-800',
                        'laminating': 'bg-purple-100 text-purple-800',
                        'drilling': 'bg-pink-100 text-pink-800',
                        'welding': 'bg-indigo-100 text-indigo-800',
                        'assembly': 'bg-gray-100 text-gray-800',
                        'sealing': 'bg-red-100 text-red-800'
                    };
                    return classes[type] || 'bg-gray-100 text-gray-800';
                },
                
                getOperationTypeName(type) {
                    const typeObj = this.operationTypes.find(t => t.id === type);
                    return typeObj ? typeObj.name : type;
                },
                
                getStatusClass(status) {
                    const classes = {
                        'draft': 'bg-yellow-100 text-yellow-800',
                        'active': 'bg-green-100 text-green-800',
                        'inactive': 'bg-gray-100 text-gray-800'
                    };
                    return classes[status] || 'bg-gray-100 text-gray-800';
                },
                
                getStatusName(status) {
                    const statusObj = this.statuses.find(s => s.id === status);
                    return statusObj ? statusObj.name : status;
                },
                
                // 监听选中操作变化
                init() {
                    this.$watch('selectedOperations', (value) => {
                        this.selectAll = value.length === this.filteredOperations.length && this.filteredOperations.length > 0;
                    });
                    
                    this.$watch('filteredOperations', () => {
                        this.selectedOperations = [];
                        this.selectAll = false;
                    });
                }
            }
        }
    </script>
</body>
</html>
