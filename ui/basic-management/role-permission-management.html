<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>角色权限管理 - 玻璃深加工ERP系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1890FF',
                        success: '#52C41A',
                        warning: '#FAAD14',
                        error: '#F5222D',
                        'title-gray': '#262626',
                        'body-gray': '#595959',
                        'aux-gray': '#8C8C8C',
                        'disabled-gray': '#BFBFBF',
                        'border-gray': '#D9D9D9',
                        'bg-gray': '#FAFAFA'
                    },
                    fontFamily: {
                        'chinese': ['PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', sans-serif;
        }
        .permission-tree {
            max-height: 400px;
            overflow-y: auto;
        }
        .permission-item:hover {
            background-color: #f8fafc;
        }
        .tree-indent-1 { padding-left: 1rem; }
        .tree-indent-2 { padding-left: 2rem; }
        .tree-indent-3 { padding-left: 3rem; }
    </style>
</head>
<body class="bg-bg-gray font-chinese">
    <div x-data="rolePermissionApp()" class="min-h-screen">
        <!-- 顶部导航栏 -->
        <header class="bg-white shadow-sm border-b border-border-gray h-16">
            <div class="flex items-center justify-between px-6 h-full">
                <div class="flex items-center space-x-4">
                    <h1 class="text-xl font-medium text-title-gray">角色权限管理</h1>
                    <span class="text-aux-gray text-sm">/ 基础管理</span>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="text-aux-gray hover:text-title-gray">
                        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </button>
                    <span class="text-aux-gray">admin</span>
                </div>
            </div>
        </header>

        <div class="flex h-[calc(100vh-64px)]">
            <!-- 左侧角色列表 -->
            <aside class="w-80 bg-white shadow-sm border-r border-border-gray">
                <div class="p-4">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-title-gray">角色列表</h3>
                        <button 
                            @click="showRoleModal = true; editingRole = null; resetRoleForm()"
                            class="px-3 py-1.5 bg-primary text-white text-sm font-medium rounded-md hover:bg-blue-600 transition-colors"
                        >
                            <svg class="h-4 w-4 mr-1 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                            </svg>
                            新增角色
                        </button>
                    </div>

                    <!-- 搜索框 -->
                    <div class="mb-4">
                        <div class="relative">
                            <input
                                type="text"
                                x-model="roleSearchQuery"
                                placeholder="搜索角色..."
                                class="w-full pl-10 pr-4 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary text-sm"
                            >
                            <svg class="absolute left-3 top-2.5 h-4 w-4 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                        </div>
                    </div>

                    <!-- 角色列表 -->
                    <div class="space-y-2">
                        <template x-for="role in filteredRoles" :key="role.id">
                            <div 
                                @click="selectRole(role)"
                                :class="selectedRole?.id === role.id ? 'bg-primary text-white' : 'hover:bg-gray-50'"
                                class="p-3 rounded-lg cursor-pointer transition-colors"
                            >
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div 
                                            class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium"
                                            :class="selectedRole?.id === role.id ? 'bg-white text-primary' : 'bg-primary text-white'"
                                        >
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium" x-text="role.name"></div>
                                            <div class="text-sm opacity-75" x-text="role.description"></div>
                                        </div>
                                    </div>
                                    <div class="flex flex-col items-end">
                                        <span 
                                            class="text-xs px-2 py-0.5 rounded-full"
                                            :class="role.status === 'active' ? 
                                                   (selectedRole?.id === role.id ? 'bg-white text-green-600' : 'bg-green-100 text-green-800') : 
                                                   (selectedRole?.id === role.id ? 'bg-white text-red-600' : 'bg-red-100 text-red-800')"
                                            x-text="role.status === 'active' ? '启用' : '禁用'"
                                        ></span>
                                        <div class="text-xs mt-1 opacity-75" x-text="`${role.userCount}个用户`"></div>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>

                    <!-- 空状态 -->
                    <div x-show="filteredRoles.length === 0" class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                        </svg>
                        <p class="mt-2 text-aux-gray text-sm">
                            <span x-show="roleSearchQuery">未找到匹配的角色</span>
                            <span x-show="!roleSearchQuery">暂无角色数据</span>
                        </p>
                    </div>
                </div>
            </aside>

            <!-- 主内容区域 -->
            <main class="flex-1 overflow-hidden">
                <div class="h-full flex flex-col">
                    <!-- 页面标题区 -->
                    <div class="bg-white border-b border-border-gray px-6 py-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <h2 class="text-lg font-medium text-title-gray">
                                    <span x-text="selectedRole ? selectedRole.name : '请选择角色'"></span>
                                </h2>
                                <p class="text-sm text-aux-gray mt-1" x-show="selectedRole">
                                    <span x-text="selectedRole?.description"></span> · 
                                    <span x-text="`${selectedRole?.userCount}个用户`"></span>
                                </p>
                            </div>
                            <div class="flex space-x-3" x-show="selectedRole">
                                <button 
                                    @click="editRole(selectedRole)"
                                    class="px-4 py-2 bg-white border border-border-gray text-body-gray text-sm font-medium rounded-md hover:bg-gray-50 transition-colors"
                                >
                                    编辑角色
                                </button>
                                <button 
                                    @click="copyRole(selectedRole)"
                                    class="px-4 py-2 bg-white border border-border-gray text-body-gray text-sm font-medium rounded-md hover:bg-gray-50 transition-colors"
                                >
                                    复制角色
                                </button>
                                <button 
                                    @click="savePermissions"
                                    class="px-4 py-2 bg-primary text-white text-sm font-medium rounded-md hover:bg-blue-600 transition-colors"
                                >
                                    保存权限
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 权限配置区域 -->
                    <div x-show="!selectedRole" class="flex-1 flex items-center justify-center">
                        <div class="text-center">
                            <svg class="mx-auto h-16 w-16 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.031 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                            </svg>
                            <p class="mt-4 text-aux-gray">请从左侧选择一个角色来配置权限</p>
                        </div>
                    </div>

                    <div x-show="selectedRole" class="flex-1 p-6 overflow-auto">
                        <!-- 快速操作 -->
                        <div class="bg-white rounded-lg shadow-sm border border-border-gray p-4 mb-6">
                            <div class="flex items-center justify-between">
                                <h3 class="text-md font-medium text-title-gray">快速操作</h3>
                                <div class="flex space-x-2">
                                    <button 
                                        @click="selectAllPermissions"
                                        class="px-3 py-1.5 bg-primary text-white text-sm rounded-md hover:bg-blue-600 transition-colors"
                                    >
                                        全选
                                    </button>
                                    <button 
                                        @click="clearAllPermissions"
                                        class="px-3 py-1.5 bg-error text-white text-sm rounded-md hover:bg-red-600 transition-colors"
                                    >
                                        清空
                                    </button>
                                    <button 
                                        @click="selectModulePermissions('basic')"
                                        class="px-3 py-1.5 bg-success text-white text-sm rounded-md hover:bg-green-600 transition-colors"
                                    >
                                        基础权限
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 权限树 -->
                        <div class="bg-white rounded-lg shadow-sm border border-border-gray">
                            <div class="p-4 border-b border-border-gray">
                                <h3 class="text-md font-medium text-title-gray">权限配置</h3>
                                <p class="text-sm text-aux-gray mt-1">勾选相应权限来配置角色访问控制</p>
                            </div>

                            <div class="permission-tree">
                                <template x-for="module in permissionModules" :key="module.id">
                                    <div class="border-b border-border-gray last:border-b-0">
                                        <!-- 模块标题 -->
                                        <div class="permission-item p-4 flex items-center space-x-3 cursor-pointer" 
                                             @click="toggleModule(module)">
                                            <button class="text-aux-gray hover:text-title-gray">
                                                <svg x-show="!module.expanded" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                                </svg>
                                                <svg x-show="module.expanded" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                                                </svg>
                                            </button>
                                            
                                            <input 
                                                type="checkbox"
                                                :checked="isModuleSelected(module)"
                                                :indeterminate="isModuleIndeterminate(module)"
                                                @change="toggleModulePermissions(module, $event.target.checked)"
                                                @click.stop
                                                class="h-4 w-4 text-primary focus:ring-primary border-border-gray rounded"
                                            >
                                            
                                            <div class="flex-1">
                                                <div class="flex items-center space-x-2">
                                                    <div 
                                                        class="w-8 h-8 rounded flex items-center justify-center text-sm font-medium text-white"
                                                        :style="`background-color: ${module.color}`"
                                                    >
                                                        <span x-text="module.icon"></span>
                                                    </div>
                                                    <div>
                                                        <div class="font-medium text-title-gray" x-text="module.name"></div>
                                                        <div class="text-sm text-aux-gray" x-text="module.description"></div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="text-sm text-aux-gray">
                                                <span x-text="getSelectedPermissionsCount(module)"></span> / 
                                                <span x-text="getTotalPermissionsCount(module)"></span>
                                            </div>
                                        </div>

                                        <!-- 功能组 -->
                                        <div x-show="module.expanded" class="bg-gray-50">
                                            <template x-for="group in module.groups" :key="group.id">
                                                <div class="border-b border-gray-200 last:border-b-0">
                                                    <div class="permission-item tree-indent-1 p-3 flex items-center space-x-3 cursor-pointer"
                                                         @click="toggleGroup(group)">
                                                        <button class="text-aux-gray hover:text-title-gray">
                                                            <svg x-show="!group.expanded" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                                            </svg>
                                                            <svg x-show="group.expanded" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                                                            </svg>
                                                        </button>
                                                        
                                                        <input 
                                                            type="checkbox"
                                                            :checked="isGroupSelected(group)"
                                                            :indeterminate="isGroupIndeterminate(group)"
                                                            @change="toggleGroupPermissions(group, $event.target.checked)"
                                                            @click.stop
                                                            class="h-4 w-4 text-primary focus:ring-primary border-border-gray rounded"
                                                        >
                                                        
                                                        <div class="flex-1">
                                                            <div class="font-medium text-title-gray text-sm" x-text="group.name"></div>
                                                            <div class="text-xs text-aux-gray" x-text="group.description"></div>
                                                        </div>
                                                        
                                                        <div class="text-xs text-aux-gray">
                                                            <span x-text="getSelectedPermissionsCount(group)"></span> / 
                                                            <span x-text="group.permissions.length"></span>
                                                        </div>
                                                    </div>

                                                    <!-- 权限列表 -->
                                                    <div x-show="group.expanded" class="bg-white">
                                                        <template x-for="permission in group.permissions" :key="permission.id">
                                                            <div class="permission-item tree-indent-2 p-3 flex items-center space-x-3">
                                                                <input 
                                                                    type="checkbox"
                                                                    :value="permission.id"
                                                                    x-model="selectedPermissions"
                                                                    class="h-4 w-4 text-primary focus:ring-primary border-border-gray rounded"
                                                                >
                                                                
                                                                <div class="flex-1">
                                                                    <div class="font-medium text-title-gray text-sm" x-text="permission.name"></div>
                                                                    <div class="text-xs text-aux-gray" x-text="permission.description"></div>
                                                                </div>
                                                                
                                                                <span 
                                                                    class="text-xs px-2 py-0.5 rounded-full"
                                                                    :class="permission.level === 'high' ? 'bg-red-100 text-red-800' : 
                                                                           permission.level === 'medium' ? 'bg-yellow-100 text-yellow-800' : 
                                                                           'bg-green-100 text-green-800'"
                                                                    x-text="getLevelLabel(permission.level)"
                                                                ></span>
                                                            </div>
                                                        </template>
                                                    </div>
                                                </div>
                                            </template>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <!-- 新增/编辑角色模态框 -->
        <div x-show="showRoleModal" 
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
             @click="showRoleModal = false">
            
            <div class="relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white"
                 @click.stop>
                <div class="mt-3">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-medium text-title-gray" x-text="editingRole ? '编辑角色' : '新增角色'"></h3>
                        <button @click="showRoleModal = false" class="text-aux-gray hover:text-title-gray">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                    
                    <form @submit.prevent="submitRoleForm" class="space-y-6">
                        <div>
                            <label for="role-name" class="block text-sm font-medium text-title-gray mb-2">
                                角色名称 <span class="text-error">*</span>
                            </label>
                            <input
                                id="role-name"
                                type="text"
                                x-model="roleForm.name"
                                class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm placeholder-aux-gray focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                placeholder="请输入角色名称"
                                required
                            >
                        </div>
                        
                        <div>
                            <label for="role-description" class="block text-sm font-medium text-title-gray mb-2">
                                角色描述
                            </label>
                            <textarea
                                id="role-description"
                                x-model="roleForm.description"
                                rows="3"
                                class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm placeholder-aux-gray focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                placeholder="请输入角色描述"
                            ></textarea>
                        </div>
                        
                        <div>
                            <label for="role-status" class="block text-sm font-medium text-title-gray mb-2">
                                状态 <span class="text-error">*</span>
                            </label>
                            <select
                                id="role-status"
                                x-model="roleForm.status"
                                class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                required
                            >
                                <option value="active">启用</option>
                                <option value="inactive">禁用</option>
                            </select>
                        </div>
                        
                        <div class="flex justify-end space-x-3 pt-4 border-t border-border-gray">
                            <button
                                type="button"
                                @click="showRoleModal = false"
                                class="px-4 py-2 text-sm font-medium text-body-gray bg-white border border-border-gray rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                            >
                                取消
                            </button>
                            <button
                                type="submit"
                                class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
                            >
                                <span x-text="editingRole ? '更新角色' : '创建角色'"></span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function rolePermissionApp() {
            return {
                // 角色数据
                roles: [
                    {
                        id: 1,
                        name: '系统管理员',
                        description: '拥有系统所有权限',
                        status: 'active',
                        userCount: 2,
                        permissions: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']
                    },
                    {
                        id: 2,
                        name: '生产管理员', 
                        description: '负责生产相关模块管理',
                        status: 'active',
                        userCount: 5,
                        permissions: ['4', '5', '6', '10', '11']
                    },
                    {
                        id: 3,
                        name: '销售人员',
                        description: '负责销售订单和客户管理',
                        status: 'active',
                        userCount: 8,
                        permissions: ['7', '8', '9']
                    },
                    {
                        id: 4,
                        name: '财务人员',
                        description: '负责财务数据查看和管理',
                        status: 'active',
                        userCount: 3,
                        permissions: ['12']
                    }
                ],

                // 权限模块
                permissionModules: [
                    {
                        id: 'basic',
                        name: '基础管理',
                        description: '用户、角色、组织架构等基础功能',
                        color: '#1890FF',
                        icon: '⚙️',
                        expanded: true,
                        groups: [
                            {
                                id: 'user',
                                name: '用户管理',
                                description: '用户账号的增删改查',
                                expanded: true,
                                permissions: [
                                    { id: '1', name: '查看用户', description: '查看用户列表和详情', level: 'low' },
                                    { id: '2', name: '新增用户', description: '创建新用户账号', level: 'medium' },
                                    { id: '3', name: '编辑用户', description: '修改用户信息', level: 'medium' },
                                    { id: '4', name: '删除用户', description: '删除用户账号', level: 'high' }
                                ]
                            },
                            {
                                id: 'role',
                                name: '角色权限',
                                description: '角色和权限配置',
                                expanded: false,
                                permissions: [
                                    { id: '5', name: '查看角色', description: '查看角色列表', level: 'low' },
                                    { id: '6', name: '管理角色', description: '创建、编辑、删除角色', level: 'high' }
                                ]
                            }
                        ]
                    },
                    {
                        id: 'production',
                        name: '生产管理',
                        description: '生产计划、工艺管理、质量控制',
                        color: '#52C41A',
                        icon: '🏭',
                        expanded: false,
                        groups: [
                            {
                                id: 'plan',
                                name: '生产计划',
                                description: '生产排程和计划管理',
                                expanded: false,
                                permissions: [
                                    { id: '7', name: '查看生产计划', description: '查看生产计划列表', level: 'low' },
                                    { id: '8', name: '制定生产计划', description: '创建和调整生产计划', level: 'medium' },
                                    { id: '9', name: '审批生产计划', description: '审批生产计划变更', level: 'high' }
                                ]
                            },
                            {
                                id: 'quality',
                                name: '质量管理',
                                description: '质量检测和控制',
                                expanded: false,
                                permissions: [
                                    { id: '10', name: '质量检测', description: '执行质量检测任务', level: 'medium' },
                                    { id: '11', name: '质量报告', description: '生成质量分析报告', level: 'medium' }
                                ]
                            }
                        ]
                    },
                    {
                        id: 'sales',
                        name: '销售管理',
                        description: '销售订单、客户关系管理',
                        color: '#FAAD14',
                        icon: '💼',
                        expanded: false,
                        groups: [
                            {
                                id: 'order',
                                name: '订单管理',
                                description: '销售订单处理',
                                expanded: false,
                                permissions: [
                                    { id: '12', name: '查看订单', description: '查看销售订单', level: 'low' },
                                    { id: '13', name: '创建订单', description: '创建新订单', level: 'medium' },
                                    { id: '14', name: '修改订单', description: '修改订单信息', level: 'medium' },
                                    { id: '15', name: '取消订单', description: '取消订单', level: 'high' }
                                ]
                            }
                        ]
                    },
                    {
                        id: 'finance',
                        name: '财务管理',
                        description: '财务数据、成本分析、报表统计',
                        color: '#F5222D',
                        icon: '💰',
                        expanded: false,
                        groups: [
                            {
                                id: 'report',
                                name: '财务报表',
                                description: '财务报表查看和生成',
                                expanded: false,
                                permissions: [
                                    { id: '16', name: '查看财务报表', description: '查看各类财务报表', level: 'medium' },
                                    { id: '17', name: '导出财务数据', description: '导出财务数据', level: 'high' }
                                ]
                            }
                        ]
                    }
                ],

                // UI状态
                selectedRole: null,
                showRoleModal: false,
                editingRole: null,
                roleSearchQuery: '',
                selectedPermissions: [],

                // 表单数据
                roleForm: {
                    name: '',
                    description: '',
                    status: 'active'
                },

                // 计算属性
                get filteredRoles() {
                    if (!this.roleSearchQuery) {
                        return this.roles;
                    }
                    const query = this.roleSearchQuery.toLowerCase();
                    return this.roles.filter(role => 
                        role.name.toLowerCase().includes(query) ||
                        role.description.toLowerCase().includes(query)
                    );
                },

                // 方法
                selectRole(role) {
                    this.selectedRole = role;
                    this.selectedPermissions = [...role.permissions];
                },

                editRole(role) {
                    this.editingRole = role;
                    this.roleForm = {
                        name: role.name,
                        description: role.description,
                        status: role.status
                    };
                    this.showRoleModal = true;
                },

                copyRole(role) {
                    this.editingRole = null;
                    this.roleForm = {
                        name: role.name + '_副本',
                        description: role.description,
                        status: 'active'
                    };
                    this.showRoleModal = true;
                },

                resetRoleForm() {
                    this.roleForm = {
                        name: '',
                        description: '',
                        status: 'active'
                    };
                },

                async submitRoleForm() {
                    try {
                        if (this.editingRole) {
                            // 更新角色
                            Object.assign(this.editingRole, {
                                name: this.roleForm.name,
                                description: this.roleForm.description,
                                status: this.roleForm.status
                            });
                            alert('角色更新成功');
                        } else {
                            // 创建新角色
                            const newRole = {
                                id: Date.now(),
                                name: this.roleForm.name,
                                description: this.roleForm.description,
                                status: this.roleForm.status,
                                userCount: 0,
                                permissions: []
                            };
                            this.roles.push(newRole);
                            alert('角色创建成功');
                        }
                        
                        this.showRoleModal = false;
                        this.resetRoleForm();
                        this.editingRole = null;
                    } catch (error) {
                        alert('操作失败：' + error.message);
                    }
                },

                // 权限相关方法
                toggleModule(module) {
                    module.expanded = !module.expanded;
                },

                toggleGroup(group) {
                    group.expanded = !group.expanded;
                },

                isModuleSelected(module) {
                    const modulePermissions = this.getModulePermissions(module);
                    return modulePermissions.length > 0 && modulePermissions.every(p => this.selectedPermissions.includes(p));
                },

                isModuleIndeterminate(module) {
                    const modulePermissions = this.getModulePermissions(module);
                    const selectedCount = modulePermissions.filter(p => this.selectedPermissions.includes(p)).length;
                    return selectedCount > 0 && selectedCount < modulePermissions.length;
                },

                isGroupSelected(group) {
                    return group.permissions.every(p => this.selectedPermissions.includes(p.id));
                },

                isGroupIndeterminate(group) {
                    const selectedCount = group.permissions.filter(p => this.selectedPermissions.includes(p.id)).length;
                    return selectedCount > 0 && selectedCount < group.permissions.length;
                },

                getModulePermissions(module) {
                    const permissions = [];
                    module.groups.forEach(group => {
                        group.permissions.forEach(permission => {
                            permissions.push(permission.id);
                        });
                    });
                    return permissions;
                },

                getSelectedPermissionsCount(item) {
                    if (item.permissions) {
                        // 这是一个group
                        return item.permissions.filter(p => this.selectedPermissions.includes(p.id)).length;
                    } else {
                        // 这是一个module
                        const modulePermissions = this.getModulePermissions(item);
                        return modulePermissions.filter(p => this.selectedPermissions.includes(p)).length;
                    }
                },

                getTotalPermissionsCount(module) {
                    return this.getModulePermissions(module).length;
                },

                toggleModulePermissions(module, checked) {
                    const modulePermissions = this.getModulePermissions(module);
                    if (checked) {
                        // 添加所有模块权限
                        modulePermissions.forEach(p => {
                            if (!this.selectedPermissions.includes(p)) {
                                this.selectedPermissions.push(p);
                            }
                        });
                    } else {
                        // 移除所有模块权限
                        this.selectedPermissions = this.selectedPermissions.filter(p => !modulePermissions.includes(p));
                    }
                },

                toggleGroupPermissions(group, checked) {
                    const groupPermissions = group.permissions.map(p => p.id);
                    if (checked) {
                        // 添加所有组权限
                        groupPermissions.forEach(p => {
                            if (!this.selectedPermissions.includes(p)) {
                                this.selectedPermissions.push(p);
                            }
                        });
                    } else {
                        // 移除所有组权限
                        this.selectedPermissions = this.selectedPermissions.filter(p => !groupPermissions.includes(p));
                    }
                },

                selectAllPermissions() {
                    const allPermissions = [];
                    this.permissionModules.forEach(module => {
                        allPermissions.push(...this.getModulePermissions(module));
                    });
                    this.selectedPermissions = [...allPermissions];
                },

                clearAllPermissions() {
                    this.selectedPermissions = [];
                },

                selectModulePermissions(moduleId) {
                    const module = this.permissionModules.find(m => m.id === moduleId);
                    if (module) {
                        const modulePermissions = this.getModulePermissions(module);
                        modulePermissions.forEach(p => {
                            if (!this.selectedPermissions.includes(p)) {
                                this.selectedPermissions.push(p);
                            }
                        });
                    }
                },

                getLevelLabel(level) {
                    const labels = {
                        'low': '低',
                        'medium': '中',
                        'high': '高'
                    };
                    return labels[level] || level;
                },

                savePermissions() {
                    if (!this.selectedRole) return;
                    
                    this.selectedRole.permissions = [...this.selectedPermissions];
                    alert('权限配置保存成功');
                },

                // 初始化
                init() {
                    // 默认选择第一个角色
                    if (this.roles.length > 0) {
                        this.selectRole(this.roles[0]);
                    }
                }
            }
        }
    </script>
</body>
</html>