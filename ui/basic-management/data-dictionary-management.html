<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据字典管理 - 玻璃深加工ERP系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1890FF',
                        success: '#52C41A',
                        warning: '#FAAD14',
                        error: '#F5222D',
                        'title-gray': '#262626',
                        'body-gray': '#595959',
                        'aux-gray': '#8C8C8C',
                        'disabled-gray': '#BFBFBF',
                        'border-gray': '#D9D9D9',
                        'bg-gray': '#FAFAFA'
                    },
                    fontFamily: {
                        'chinese': ['PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', sans-serif;
        }
        .table-row:hover {
            background-color: #f8fafc;
        }
        .category-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }
        .category-card {
            transition: all 0.3s ease;
        }
    </style>
</head>
<body class="bg-bg-gray font-chinese">
    <div x-data="dataDictionaryApp()" class="min-h-screen">
        <!-- 顶部导航栏 -->
        <header class="bg-white shadow-sm border-b border-border-gray h-16">
            <div class="flex items-center justify-between px-6 h-full">
                <div class="flex items-center space-x-4">
                    <h1 class="text-xl font-medium text-title-gray">数据字典管理</h1>
                    <span class="text-aux-gray text-sm">/ 基础管理</span>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="text-aux-gray hover:text-title-gray">
                        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </button>
                    <span class="text-aux-gray">admin</span>
                </div>
            </div>
        </header>

        <div class="flex h-[calc(100vh-64px)]">
            <!-- 左侧分类列表 -->
            <aside class="w-80 bg-white shadow-sm border-r border-border-gray">
                <div class="p-4">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-title-gray">字典分类</h3>
                        <button 
                            @click="showCategoryModal = true; editingCategory = null; resetCategoryForm()"
                            class="px-3 py-1.5 bg-primary text-white text-sm font-medium rounded-md hover:bg-blue-600 transition-colors"
                        >
                            <svg class="h-4 w-4 mr-1 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                            </svg>
                            新增分类
                        </button>
                    </div>

                    <!-- 搜索框 -->
                    <div class="mb-4">
                        <div class="relative">
                            <input
                                type="text"
                                x-model="categorySearchQuery"
                                placeholder="搜索分类..."
                                class="w-full pl-10 pr-4 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary text-sm"
                            >
                            <svg class="absolute left-3 top-2.5 h-4 w-4 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                        </div>
                    </div>

                    <!-- 分类列表 -->
                    <div class="space-y-3">
                        <template x-for="category in filteredCategories" :key="category.id">
                            <div 
                                @click="selectCategory(category)"
                                :class="selectedCategory?.id === category.id ? 'ring-2 ring-primary bg-blue-50' : 'hover:shadow-md'"
                                class="category-card p-4 rounded-lg cursor-pointer border border-border-gray bg-white"
                            >
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-2 mb-2">
                                            <div 
                                                class="w-8 h-8 rounded flex items-center justify-center text-sm font-medium text-white"
                                                :style="`background-color: ${category.color}`"
                                            >
                                                <span x-text="category.icon"></span>
                                            </div>
                                            <div>
                                                <div class="font-medium text-title-gray" x-text="category.name"></div>
                                                <div class="text-xs text-aux-gray" x-text="category.code"></div>
                                            </div>
                                        </div>
                                        <div class="text-sm text-aux-gray mb-2" x-text="category.description"></div>
                                        <div class="flex items-center justify-between">
                                            <span class="text-xs text-aux-gray" x-text="`${category.itemCount} 个字典项`"></span>
                                            <span 
                                                class="text-xs px-2 py-0.5 rounded-full"
                                                :class="category.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                                                x-text="category.status === 'active' ? '启用' : '禁用'"
                                            ></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>

                    <!-- 空状态 -->
                    <div x-show="filteredCategories.length === 0" class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                        </svg>
                        <p class="mt-2 text-aux-gray text-sm">
                            <span x-show="categorySearchQuery">未找到匹配的分类</span>
                            <span x-show="!categorySearchQuery">暂无分类数据</span>
                        </p>
                    </div>
                </div>
            </aside>

            <!-- 主内容区域 -->
            <main class="flex-1 overflow-hidden">
                <div class="h-full flex flex-col">
                    <!-- 页面标题区 -->
                    <div class="bg-white border-b border-border-gray px-6 py-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <h2 class="text-lg font-medium text-title-gray">
                                    <span x-text="selectedCategory ? selectedCategory.name : '请选择字典分类'"></span>
                                </h2>
                                <p class="text-sm text-aux-gray mt-1" x-show="selectedCategory">
                                    <span x-text="selectedCategory?.description"></span> · 
                                    <span x-text="`编码: ${selectedCategory?.code}`"></span>
                                </p>
                            </div>
                            <div class="flex space-x-3" x-show="selectedCategory">
                                <button 
                                    @click="exportDictionary"
                                    class="px-4 py-2 bg-white border border-border-gray text-body-gray text-sm font-medium rounded-md hover:bg-gray-50 transition-colors"
                                >
                                    <svg class="h-4 w-4 mr-2 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                    </svg>
                                    导出
                                </button>
                                <button 
                                    @click="showImportModal = true"
                                    class="px-4 py-2 bg-white border border-border-gray text-body-gray text-sm font-medium rounded-md hover:bg-gray-50 transition-colors"
                                >
                                    <svg class="h-4 w-4 mr-2 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"/>
                                    </svg>
                                    导入
                                </button>
                                <button 
                                    @click="editCategory(selectedCategory)"
                                    class="px-4 py-2 bg-white border border-border-gray text-body-gray text-sm font-medium rounded-md hover:bg-gray-50 transition-colors"
                                >
                                    编辑分类
                                </button>
                                <button 
                                    @click="showItemModal = true; editingItem = null; resetItemForm()"
                                    class="px-4 py-2 bg-primary text-white text-sm font-medium rounded-md hover:bg-blue-600 transition-colors"
                                >
                                    <svg class="h-4 w-4 mr-2 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                    </svg>
                                    新增字典项
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 搜索和筛选区域 -->
                    <div x-show="selectedCategory" class="bg-white border-b border-border-gray px-6 py-4">
                        <div class="flex items-center space-x-4">
                            <div class="flex-1">
                                <div class="relative">
                                    <input
                                        type="text"
                                        x-model="itemSearchQuery"
                                        @input="handleItemSearch"
                                        placeholder="搜索字典项名称、编码、值..."
                                        class="w-full pl-10 pr-4 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                    >
                                    <svg class="absolute left-3 top-2.5 h-5 w-5 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                    </svg>
                                </div>
                            </div>
                            
                            <div class="flex space-x-3">
                                <select 
                                    x-model="filterStatus"
                                    @change="applyItemFilters"
                                    class="px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                >
                                    <option value="">全部状态</option>
                                    <option value="active">启用</option>
                                    <option value="inactive">禁用</option>
                                </select>
                                
                                <button 
                                    @click="resetItemFilters"
                                    class="px-3 py-2 text-aux-gray hover:text-title-gray border border-border-gray rounded-md hover:bg-gray-50 transition-colors"
                                >
                                    重置
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 字典项列表 -->
                    <div x-show="!selectedCategory" class="flex-1 flex items-center justify-center">
                        <div class="text-center">
                            <svg class="mx-auto h-16 w-16 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                            </svg>
                            <p class="mt-4 text-aux-gray">请从左侧选择一个字典分类查看详情</p>
                        </div>
                    </div>

                    <div x-show="selectedCategory" class="flex-1 overflow-auto">
                        <div class="bg-white">
                            <table class="min-w-full divide-y divide-border-gray">
                                <thead class="bg-bg-gray">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider cursor-pointer" @click="sortItemsBy('code')">
                                            编码
                                            <svg x-show="itemSortField === 'code'" :class="itemSortDirection === 'asc' ? '' : 'transform rotate-180'" class="inline h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"/>
                                            </svg>
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider cursor-pointer" @click="sortItemsBy('name')">
                                            名称
                                            <svg x-show="itemSortField === 'name'" :class="itemSortDirection === 'asc' ? '' : 'transform rotate-180'" class="inline h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"/>
                                            </svg>
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">
                                            值
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider cursor-pointer" @click="sortItemsBy('sort')">
                                            排序
                                            <svg x-show="itemSortField === 'sort'" :class="itemSortDirection === 'asc' ? '' : 'transform rotate-180'" class="inline h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"/>
                                            </svg>
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider cursor-pointer" @click="sortItemsBy('status')">
                                            状态
                                            <svg x-show="itemSortField === 'status'" :class="itemSortDirection === 'asc' ? '' : 'transform rotate-180'" class="inline h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"/>
                                            </svg>
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">
                                            描述
                                        </th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-aux-gray uppercase tracking-wider">
                                            操作
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-border-gray">
                                    <template x-for="item in paginatedItems" :key="item.id">
                                        <tr class="table-row">
                                            <td class="px-6 py-4">
                                                <div class="text-sm font-medium text-title-gray" x-text="item.code"></div>
                                            </td>
                                            <td class="px-6 py-4">
                                                <div class="text-sm text-title-gray" x-text="item.name"></div>
                                            </td>
                                            <td class="px-6 py-4">
                                                <div class="text-sm text-title-gray" x-text="item.value"></div>
                                            </td>
                                            <td class="px-6 py-4">
                                                <div class="text-sm text-aux-gray" x-text="item.sort"></div>
                                            </td>
                                            <td class="px-6 py-4">
                                                <span 
                                                    class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                                    :class="item.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                                                    x-text="item.status === 'active' ? '启用' : '禁用'"
                                                ></span>
                                            </td>
                                            <td class="px-6 py-4">
                                                <div class="text-sm text-aux-gray" x-text="item.description || '-'"></div>
                                            </td>
                                            <td class="px-6 py-4 text-right text-sm font-medium">
                                                <div class="flex justify-end space-x-2">
                                                    <button 
                                                        @click="editItem(item)"
                                                        class="text-primary hover:text-blue-600"
                                                    >
                                                        编辑
                                                    </button>
                                                    <button 
                                                        @click="toggleItemStatus(item)"
                                                        :class="item.status === 'active' ? 'text-error hover:text-red-600' : 'text-success hover:text-green-600'"
                                                        x-text="item.status === 'active' ? '禁用' : '启用'"
                                                    >
                                                    </button>
                                                    <button 
                                                        @click="deleteItem(item)"
                                                        class="text-error hover:text-red-600"
                                                    >
                                                        删除
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </template>
                                </tbody>
                            </table>
                            
                            <!-- 空状态 -->
                            <div x-show="filteredItems.length === 0 && selectedCategory" class="text-center py-16">
                                <svg class="mx-auto h-16 w-16 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                                </svg>
                                <p class="mt-4 text-aux-gray">
                                    <span x-show="itemSearchQuery || filterStatus">未找到匹配的字典项</span>
                                    <span x-show="!itemSearchQuery && !filterStatus">暂无字典项数据</span>
                                </p>
                                <button 
                                    x-show="itemSearchQuery || filterStatus"
                                    @click="resetItemFilters"
                                    class="mt-2 text-primary hover:text-blue-600"
                                >
                                    清除筛选条件
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 分页 -->
                    <div x-show="selectedCategory && filteredItems.length > 0" class="bg-white border-t border-border-gray px-6 py-3">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-aux-gray">
                                显示第 <span x-text="(itemCurrentPage - 1) * itemPageSize + 1"></span> 到 <span x-text="Math.min(itemCurrentPage * itemPageSize, filteredItems.length)"></span> 条，
                                共 <span x-text="filteredItems.length"></span> 条记录
                            </div>
                            <div class="flex items-center space-x-2">
                                <button 
                                    @click="changeItemPage(itemCurrentPage - 1)"
                                    :disabled="itemCurrentPage === 1"
                                    :class="itemCurrentPage === 1 ? 'text-disabled-gray cursor-not-allowed' : 'text-aux-gray hover:text-title-gray'"
                                    class="px-2 py-1"
                                >
                                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                                    </svg>
                                </button>
                                
                                <template x-for="page in visibleItemPages" :key="page">
                                    <button 
                                        @click="changeItemPage(page)"
                                        :class="page === itemCurrentPage ? 'bg-primary text-white' : 'text-aux-gray hover:text-title-gray hover:bg-gray-50'"
                                        class="px-3 py-1 border border-border-gray rounded text-sm"
                                        x-text="page"
                                    ></button>
                                </template>
                                
                                <button 
                                    @click="changeItemPage(itemCurrentPage + 1)"
                                    :disabled="itemCurrentPage === itemTotalPages"
                                    :class="itemCurrentPage === itemTotalPages ? 'text-disabled-gray cursor-not-allowed' : 'text-aux-gray hover:text-title-gray'"
                                    class="px-2 py-1"
                                >
                                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <!-- 新增/编辑分类模态框 -->
        <div x-show="showCategoryModal" 
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
             @click="showCategoryModal = false">
            
            <div class="relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white"
                 @click.stop>
                <div class="mt-3">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-medium text-title-gray" x-text="editingCategory ? '编辑分类' : '新增分类'"></h3>
                        <button @click="showCategoryModal = false" class="text-aux-gray hover:text-title-gray">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                    
                    <form @submit.prevent="submitCategoryForm" class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="category-name" class="block text-sm font-medium text-title-gray mb-2">
                                    分类名称 <span class="text-error">*</span>
                                </label>
                                <input
                                    id="category-name"
                                    type="text"
                                    x-model="categoryForm.name"
                                    class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm placeholder-aux-gray focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                    placeholder="请输入分类名称"
                                    required
                                >
                            </div>
                            
                            <div>
                                <label for="category-code" class="block text-sm font-medium text-title-gray mb-2">
                                    分类编码 <span class="text-error">*</span>
                                </label>
                                <input
                                    id="category-code"
                                    type="text"
                                    x-model="categoryForm.code"
                                    class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm placeholder-aux-gray focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                    placeholder="请输入分类编码"
                                    required
                                >
                            </div>
                            
                            <div>
                                <label for="category-icon" class="block text-sm font-medium text-title-gray mb-2">
                                    图标
                                </label>
                                <input
                                    id="category-icon"
                                    type="text"
                                    x-model="categoryForm.icon"
                                    class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm placeholder-aux-gray focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                    placeholder="请输入图标 (emoji)"
                                >
                            </div>
                            
                            <div>
                                <label for="category-color" class="block text-sm font-medium text-title-gray mb-2">
                                    颜色
                                </label>
                                <input
                                    id="category-color"
                                    type="color"
                                    x-model="categoryForm.color"
                                    class="w-full h-10 border border-border-gray rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                >
                            </div>
                        </div>
                        
                        <div>
                            <label for="category-description" class="block text-sm font-medium text-title-gray mb-2">
                                描述
                            </label>
                            <textarea
                                id="category-description"
                                x-model="categoryForm.description"
                                rows="3"
                                class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm placeholder-aux-gray focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                placeholder="请输入分类描述"
                            ></textarea>
                        </div>
                        
                        <div>
                            <label for="category-status" class="block text-sm font-medium text-title-gray mb-2">
                                状态 <span class="text-error">*</span>
                            </label>
                            <select
                                id="category-status"
                                x-model="categoryForm.status"
                                class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                required
                            >
                                <option value="active">启用</option>
                                <option value="inactive">禁用</option>
                            </select>
                        </div>
                        
                        <div class="flex justify-end space-x-3 pt-4 border-t border-border-gray">
                            <button
                                type="button"
                                @click="showCategoryModal = false"
                                class="px-4 py-2 text-sm font-medium text-body-gray bg-white border border-border-gray rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                            >
                                取消
                            </button>
                            <button
                                type="submit"
                                class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
                            >
                                <span x-text="editingCategory ? '更新分类' : '创建分类'"></span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 新增/编辑字典项模态框 -->
        <div x-show="showItemModal" 
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
             @click="showItemModal = false">
            
            <div class="relative top-20 mx-auto p-5 border w-[600px] shadow-lg rounded-md bg-white"
                 @click.stop>
                <div class="mt-3">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-medium text-title-gray" x-text="editingItem ? '编辑字典项' : '新增字典项'"></h3>
                        <button @click="showItemModal = false" class="text-aux-gray hover:text-title-gray">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                    
                    <form @submit.prevent="submitItemForm" class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="item-code" class="block text-sm font-medium text-title-gray mb-2">
                                    编码 <span class="text-error">*</span>
                                </label>
                                <input
                                    id="item-code"
                                    type="text"
                                    x-model="itemForm.code"
                                    class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm placeholder-aux-gray focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                    placeholder="请输入编码"
                                    required
                                >
                            </div>
                            
                            <div>
                                <label for="item-name" class="block text-sm font-medium text-title-gray mb-2">
                                    名称 <span class="text-error">*</span>
                                </label>
                                <input
                                    id="item-name"
                                    type="text"
                                    x-model="itemForm.name"
                                    class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm placeholder-aux-gray focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                    placeholder="请输入名称"
                                    required
                                >
                            </div>
                            
                            <div>
                                <label for="item-value" class="block text-sm font-medium text-title-gray mb-2">
                                    值 <span class="text-error">*</span>
                                </label>
                                <input
                                    id="item-value"
                                    type="text"
                                    x-model="itemForm.value"
                                    class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm placeholder-aux-gray focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                    placeholder="请输入值"
                                    required
                                >
                            </div>
                            
                            <div>
                                <label for="item-sort" class="block text-sm font-medium text-title-gray mb-2">
                                    排序
                                </label>
                                <input
                                    id="item-sort"
                                    type="number"
                                    x-model="itemForm.sort"
                                    class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm placeholder-aux-gray focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                    placeholder="请输入排序值"
                                    min="0"
                                >
                            </div>
                            
                            <div class="md:col-span-2">
                                <label for="item-status" class="block text-sm font-medium text-title-gray mb-2">
                                    状态 <span class="text-error">*</span>
                                </label>
                                <select
                                    id="item-status"
                                    x-model="itemForm.status"
                                    class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                    required
                                >
                                    <option value="active">启用</option>
                                    <option value="inactive">禁用</option>
                                </select>
                            </div>
                        </div>
                        
                        <div>
                            <label for="item-description" class="block text-sm font-medium text-title-gray mb-2">
                                描述
                            </label>
                            <textarea
                                id="item-description"
                                x-model="itemForm.description"
                                rows="3"
                                class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm placeholder-aux-gray focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                placeholder="请输入描述"
                            ></textarea>
                        </div>
                        
                        <div class="flex justify-end space-x-3 pt-4 border-t border-border-gray">
                            <button
                                type="button"
                                @click="showItemModal = false"
                                class="px-4 py-2 text-sm font-medium text-body-gray bg-white border border-border-gray rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                            >
                                取消
                            </button>
                            <button
                                type="submit"
                                class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
                            >
                                <span x-text="editingItem ? '更新字典项' : '创建字典项'"></span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 导入模态框 -->
        <div x-show="showImportModal" 
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
             @click="showImportModal = false">
            
            <div class="relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white"
                 @click.stop>
                <div class="mt-3">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-medium text-title-gray">导入字典项</h3>
                        <button @click="showImportModal = false" class="text-aux-gray hover:text-title-gray">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                    
                    <div class="space-y-4">
                        <div class="border-2 border-dashed border-border-gray rounded-lg p-6 text-center">
                            <svg class="mx-auto h-12 w-12 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"/>
                            </svg>
                            <p class="mt-2 text-sm text-aux-gray">点击上传或拖拽Excel文件到此处</p>
                            <input type="file" accept=".xlsx,.xls" class="hidden" id="import-file">
                            <button 
                                onclick="document.getElementById('import-file').click()"
                                class="mt-2 px-4 py-2 bg-primary text-white text-sm rounded-md hover:bg-blue-600 transition-colors"
                            >
                                选择文件
                            </button>
                        </div>
                        
                        <div class="text-sm text-aux-gray">
                            <p class="font-medium mb-2">导入说明：</p>
                            <ul class="list-disc list-inside space-y-1">
                                <li>支持Excel格式（.xlsx, .xls）</li>
                                <li>第一行必须为表头</li>
                                <li>必需字段：编码、名称、值</li>
                                <li>可选字段：排序、状态、描述</li>
                                <li>导入将覆盖同编码的现有数据</li>
                            </ul>
                        </div>
                        
                        <div class="flex justify-end space-x-3 pt-4 border-t border-border-gray">
                            <button
                                @click="showImportModal = false"
                                class="px-4 py-2 text-sm font-medium text-body-gray bg-white border border-border-gray rounded-md hover:bg-gray-50"
                            >
                                取消
                            </button>
                            <button
                                @click="processImport"
                                class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-blue-600 transition-colors"
                            >
                                开始导入
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function dataDictionaryApp() {
            return {
                // 分类数据
                categories: [
                    {
                        id: 1,
                        name: '玻璃类型',
                        code: 'GLASS_TYPE',
                        description: '各种玻璃产品类型分类',
                        icon: '🪟',
                        color: '#1890FF',
                        status: 'active',
                        itemCount: 8
                    },
                    {
                        id: 2,
                        name: '玻璃厚度',
                        code: 'GLASS_THICKNESS',
                        description: '标准玻璃厚度规格',
                        icon: '📏',
                        color: '#52C41A',
                        status: 'active',
                        itemCount: 12
                    },
                    {
                        id: 3,
                        name: '工艺类型',
                        code: 'PROCESS_TYPE',
                        description: '玻璃深加工工艺分类',
                        icon: '⚙️',
                        color: '#FAAD14',
                        status: 'active',
                        itemCount: 15
                    },
                    {
                        id: 4,
                        name: '质量等级',
                        code: 'QUALITY_GRADE',
                        description: '产品质量等级分类',
                        icon: '⭐',
                        color: '#F5222D',
                        status: 'active',
                        itemCount: 5
                    },
                    {
                        id: 5,
                        name: '订单状态',
                        code: 'ORDER_STATUS',
                        description: '订单流转状态定义',
                        icon: '📋',
                        color: '#722ED1',
                        status: 'active',
                        itemCount: 7
                    }
                ],

                // 字典项数据 (模拟数据)
                dictionaryItems: {
                    1: [
                        { id: 1, code: 'TEMPERED', name: '钢化玻璃', value: 'tempered', sort: 1, status: 'active', description: '经过热处理的强化玻璃', categoryId: 1 },
                        { id: 2, code: 'LAMINATED', name: '夹层玻璃', value: 'laminated', sort: 2, status: 'active', description: '多层玻璃胶合制品', categoryId: 1 },
                        { id: 3, code: 'HOLLOW', name: '中空玻璃', value: 'hollow', sort: 3, status: 'active', description: '双层或多层密封玻璃', categoryId: 1 },
                        { id: 4, code: 'REFLECTIVE', name: '镀膜玻璃', value: 'reflective', sort: 4, status: 'active', description: '表面镀有特殊薄膜的玻璃', categoryId: 1 },
                        { id: 5, code: 'FROSTED', name: '磨砂玻璃', value: 'frosted', sort: 5, status: 'active', description: '表面经过磨砂处理的玻璃', categoryId: 1 },
                        { id: 6, code: 'PATTERNED', name: '压花玻璃', value: 'patterned', sort: 6, status: 'active', description: '表面带有花纹图案的玻璃', categoryId: 1 },
                        { id: 7, code: 'FIREPROOF', name: '防火玻璃', value: 'fireproof', sort: 7, status: 'active', description: '具有防火性能的特种玻璃', categoryId: 1 },
                        { id: 8, code: 'BULLETPROOF', name: '防弹玻璃', value: 'bulletproof', sort: 8, status: 'inactive', description: '具有防弹功能的安全玻璃', categoryId: 1 }
                    ],
                    2: [
                        { id: 9, code: 'T3', name: '3mm', value: '3', sort: 1, status: 'active', description: '3毫米厚度玻璃', categoryId: 2 },
                        { id: 10, code: 'T4', name: '4mm', value: '4', sort: 2, status: 'active', description: '4毫米厚度玻璃', categoryId: 2 },
                        { id: 11, code: 'T5', name: '5mm', value: '5', sort: 3, status: 'active', description: '5毫米厚度玻璃', categoryId: 2 },
                        { id: 12, code: 'T6', name: '6mm', value: '6', sort: 4, status: 'active', description: '6毫米厚度玻璃', categoryId: 2 },
                        { id: 13, code: 'T8', name: '8mm', value: '8', sort: 5, status: 'active', description: '8毫米厚度玻璃', categoryId: 2 },
                        { id: 14, code: 'T10', name: '10mm', value: '10', sort: 6, status: 'active', description: '10毫米厚度玻璃', categoryId: 2 },
                        { id: 15, code: 'T12', name: '12mm', value: '12', sort: 7, status: 'active', description: '12毫米厚度玻璃', categoryId: 2 },
                        { id: 16, code: 'T15', name: '15mm', value: '15', sort: 8, status: 'active', description: '15毫米厚度玻璃', categoryId: 2 },
                        { id: 17, code: 'T19', name: '19mm', value: '19', sort: 9, status: 'active', description: '19毫米厚度玻璃', categoryId: 2 },
                        { id: 18, code: 'T25', name: '25mm', value: '25', sort: 10, status: 'active', description: '25毫米厚度玻璃', categoryId: 2 }
                    ],
                    3: [
                        { id: 19, code: 'CUT', name: '切割', value: 'cutting', sort: 1, status: 'active', description: '玻璃切割加工', categoryId: 3 },
                        { id: 20, code: 'EDGE', name: '磨边', value: 'edging', sort: 2, status: 'active', description: '玻璃边缘加工', categoryId: 3 },
                        { id: 21, code: 'DRILL', name: '打孔', value: 'drilling', sort: 3, status: 'active', description: '玻璃钻孔加工', categoryId: 3 },
                        { id: 22, code: 'TEMPER', name: '钢化', value: 'tempering', sort: 4, status: 'active', description: '玻璃钢化处理', categoryId: 3 },
                        { id: 23, code: 'LAMINATE', name: '夹胶', value: 'laminating', sort: 5, status: 'active', description: '玻璃夹胶制作', categoryId: 3 }
                    ]
                },

                // UI状态
                selectedCategory: null,
                showCategoryModal: false,
                showItemModal: false,
                showImportModal: false,
                editingCategory: null,
                editingItem: null,

                // 搜索和筛选
                categorySearchQuery: '',
                itemSearchQuery: '',
                filterStatus: '',

                // 排序
                itemSortField: 'sort',
                itemSortDirection: 'asc',

                // 分页
                itemCurrentPage: 1,
                itemPageSize: 10,

                // 表单数据
                categoryForm: {
                    name: '',
                    code: '',
                    description: '',
                    icon: '📋',
                    color: '#1890FF',
                    status: 'active'
                },

                itemForm: {
                    code: '',
                    name: '',
                    value: '',
                    sort: 0,
                    status: 'active',
                    description: ''
                },

                // 计算属性
                get filteredCategories() {
                    if (!this.categorySearchQuery) {
                        return this.categories;
                    }
                    const query = this.categorySearchQuery.toLowerCase();
                    return this.categories.filter(category => 
                        category.name.toLowerCase().includes(query) ||
                        category.code.toLowerCase().includes(query) ||
                        category.description.toLowerCase().includes(query)
                    );
                },

                get currentCategoryItems() {
                    if (!this.selectedCategory) return [];
                    return this.dictionaryItems[this.selectedCategory.id] || [];
                },

                get filteredItems() {
                    let items = this.currentCategoryItems;

                    // 搜索过滤
                    if (this.itemSearchQuery) {
                        const query = this.itemSearchQuery.toLowerCase();
                        items = items.filter(item => 
                            item.name.toLowerCase().includes(query) ||
                            item.code.toLowerCase().includes(query) ||
                            item.value.toLowerCase().includes(query)
                        );
                    }

                    // 状态过滤
                    if (this.filterStatus) {
                        items = items.filter(item => item.status === this.filterStatus);
                    }

                    // 排序
                    items.sort((a, b) => {
                        let aVal = a[this.itemSortField];
                        let bVal = b[this.itemSortField];
                        
                        if (this.itemSortDirection === 'asc') {
                            return aVal < bVal ? -1 : aVal > bVal ? 1 : 0;
                        } else {
                            return aVal > bVal ? -1 : aVal < bVal ? 1 : 0;
                        }
                    });

                    return items;
                },

                get paginatedItems() {
                    const start = (this.itemCurrentPage - 1) * this.itemPageSize;
                    const end = start + this.itemPageSize;
                    return this.filteredItems.slice(start, end);
                },

                get itemTotalPages() {
                    return Math.ceil(this.filteredItems.length / this.itemPageSize);
                },

                get visibleItemPages() {
                    const pages = [];
                    const total = this.itemTotalPages;
                    const current = this.itemCurrentPage;
                    
                    if (total <= 7) {
                        for (let i = 1; i <= total; i++) {
                            pages.push(i);
                        }
                    } else {
                        if (current <= 4) {
                            for (let i = 1; i <= 5; i++) {
                                pages.push(i);
                            }
                            pages.push('...');
                            pages.push(total);
                        } else if (current >= total - 3) {
                            pages.push(1);
                            pages.push('...');
                            for (let i = total - 4; i <= total; i++) {
                                pages.push(i);
                            }
                        } else {
                            pages.push(1);
                            pages.push('...');
                            for (let i = current - 1; i <= current + 1; i++) {
                                pages.push(i);
                            }
                            pages.push('...');
                            pages.push(total);
                        }
                    }
                    
                    return pages;
                },

                // 方法
                selectCategory(category) {
                    this.selectedCategory = category;
                    this.itemCurrentPage = 1;
                    this.resetItemFilters();
                },

                editCategory(category) {
                    this.editingCategory = category;
                    this.categoryForm = {
                        name: category.name,
                        code: category.code,
                        description: category.description,
                        icon: category.icon,
                        color: category.color,
                        status: category.status
                    };
                    this.showCategoryModal = true;
                },

                resetCategoryForm() {
                    this.categoryForm = {
                        name: '',
                        code: '',
                        description: '',
                        icon: '📋',
                        color: '#1890FF',
                        status: 'active'
                    };
                },

                async submitCategoryForm() {
                    try {
                        if (this.editingCategory) {
                            // 更新分类
                            Object.assign(this.editingCategory, {
                                name: this.categoryForm.name,
                                code: this.categoryForm.code,
                                description: this.categoryForm.description,
                                icon: this.categoryForm.icon,
                                color: this.categoryForm.color,
                                status: this.categoryForm.status
                            });
                            alert('分类更新成功');
                        } else {
                            // 创建新分类
                            const newCategory = {
                                id: Date.now(),
                                name: this.categoryForm.name,
                                code: this.categoryForm.code,
                                description: this.categoryForm.description,
                                icon: this.categoryForm.icon,
                                color: this.categoryForm.color,
                                status: this.categoryForm.status,
                                itemCount: 0
                            };
                            this.categories.push(newCategory);
                            this.dictionaryItems[newCategory.id] = [];
                            alert('分类创建成功');
                        }
                        
                        this.showCategoryModal = false;
                        this.resetCategoryForm();
                        this.editingCategory = null;
                    } catch (error) {
                        alert('操作失败：' + error.message);
                    }
                },

                // 字典项相关方法
                handleItemSearch() {
                    this.itemCurrentPage = 1;
                },

                applyItemFilters() {
                    this.itemCurrentPage = 1;
                },

                resetItemFilters() {
                    this.itemSearchQuery = '';
                    this.filterStatus = '';
                    this.itemCurrentPage = 1;
                },

                sortItemsBy(field) {
                    if (this.itemSortField === field) {
                        this.itemSortDirection = this.itemSortDirection === 'asc' ? 'desc' : 'asc';
                    } else {
                        this.itemSortField = field;
                        this.itemSortDirection = 'asc';
                    }
                },

                changeItemPage(page) {
                    if (page >= 1 && page <= this.itemTotalPages) {
                        this.itemCurrentPage = page;
                    }
                },

                editItem(item) {
                    this.editingItem = item;
                    this.itemForm = {
                        code: item.code,
                        name: item.name,
                        value: item.value,
                        sort: item.sort,
                        status: item.status,
                        description: item.description || ''
                    };
                    this.showItemModal = true;
                },

                resetItemForm() {
                    this.itemForm = {
                        code: '',
                        name: '',
                        value: '',
                        sort: this.getNextSortValue(),
                        status: 'active',
                        description: ''
                    };
                },

                getNextSortValue() {
                    const items = this.currentCategoryItems;
                    if (items.length === 0) return 1;
                    return Math.max(...items.map(item => item.sort)) + 1;
                },

                async submitItemForm() {
                    try {
                        if (!this.selectedCategory) {
                            alert('请先选择分类');
                            return;
                        }

                        if (this.editingItem) {
                            // 更新字典项
                            Object.assign(this.editingItem, {
                                code: this.itemForm.code,
                                name: this.itemForm.name,
                                value: this.itemForm.value,
                                sort: this.itemForm.sort,
                                status: this.itemForm.status,
                                description: this.itemForm.description
                            });
                            alert('字典项更新成功');
                        } else {
                            // 创建新字典项
                            const newItem = {
                                id: Date.now(),
                                code: this.itemForm.code,
                                name: this.itemForm.name,
                                value: this.itemForm.value,
                                sort: this.itemForm.sort,
                                status: this.itemForm.status,
                                description: this.itemForm.description,
                                categoryId: this.selectedCategory.id
                            };
                            
                            if (!this.dictionaryItems[this.selectedCategory.id]) {
                                this.dictionaryItems[this.selectedCategory.id] = [];
                            }
                            this.dictionaryItems[this.selectedCategory.id].push(newItem);
                            
                            // 更新分类的项目计数
                            this.selectedCategory.itemCount++;
                            
                            alert('字典项创建成功');
                        }
                        
                        this.showItemModal = false;
                        this.resetItemForm();
                        this.editingItem = null;
                    } catch (error) {
                        alert('操作失败：' + error.message);
                    }
                },

                toggleItemStatus(item) {
                    const newStatus = item.status === 'active' ? 'inactive' : 'active';
                    if (confirm(`确定要${newStatus === 'active' ? '启用' : '禁用'}字典项"${item.name}"吗？`)) {
                        item.status = newStatus;
                        alert(`字典项${newStatus === 'active' ? '启用' : '禁用'}成功`);
                    }
                },

                deleteItem(item) {
                    if (confirm(`确定要删除字典项"${item.name}"吗？删除后将无法恢复。`)) {
                        const items = this.dictionaryItems[this.selectedCategory.id];
                        const index = items.findIndex(i => i.id === item.id);
                        if (index !== -1) {
                            items.splice(index, 1);
                            this.selectedCategory.itemCount--;
                            alert('字典项删除成功');
                        }
                    }
                },

                exportDictionary() {
                    if (!this.selectedCategory) {
                        alert('请先选择分类');
                        return;
                    }
                    alert('字典导出功能开发中...');
                },

                processImport() {
                    alert('批量导入功能开发中...');
                },

                // 初始化
                init() {
                    // 默认选择第一个分类
                    if (this.categories.length > 0) {
                        this.selectCategory(this.categories[0]);
                    }
                }
            }
        }
    </script>
</body>
</html>