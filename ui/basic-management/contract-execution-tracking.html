<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合同执行跟踪 - 玻璃深加工ERP系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1890FF',
                        success: '#52C41A',
                        warning: '#FAAD14',
                        error: '#F5222D',
                        'title-gray': '#262626',
                        'body-gray': '#595959',
                        'aux-gray': '#8C8C8C',
                        'disabled-gray': '#BFBFBF',
                        'border-gray': '#D9D9D9',
                        'bg-gray': '#FAFAFA'
                    },
                    fontFamily: {
                        'chinese': ['PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', sans-serif;
        }
        .milestone-timeline {
            position: relative;
        }
        .milestone-timeline::before {
            content: '';
            position: absolute;
            left: 20px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, #10b981 0%, #10b981 var(--progress, 50%), #e5e7eb var(--progress, 50%), #e5e7eb 100%);
        }
        .milestone-item {
            position: relative;
            padding-left: 60px;
            padding-bottom: 24px;
        }
        .milestone-dot {
            position: absolute;
            left: 12px;
            top: 4px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: 0 0 0 2px #e5e7eb;
        }
        .milestone-dot.completed {
            background-color: #10b981;
            box-shadow: 0 0 0 2px #10b981;
        }
        .milestone-dot.current {
            background-color: #3b82f6;
            box-shadow: 0 0 0 2px #3b82f6;
            animation: pulse 2s infinite;
        }
        .milestone-dot.pending {
            background-color: #e5e7eb;
        }
        .risk-indicator {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }
        .performance-chart {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .gantt-bar {
            height: 20px;
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }
        .gantt-progress {
            height: 100%;
            background: linear-gradient(90deg, #52c41a 0%, #389e0d 100%);
            transition: width 0.3s ease;
        }
        .alert-badge {
            animation: blink 1s infinite;
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.8; }
        }
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.5; }
        }
    </style>
</head>
<body class="bg-bg-gray font-chinese">
    <div x-data="contractTrackingApp()" class="min-h-screen">
        <!-- 顶部导航栏 -->
        <header class="bg-white shadow-sm border-b border-border-gray h-16">
            <div class="flex items-center justify-between px-6 h-full">
                <div class="flex items-center space-x-4">
                    <h1 class="text-xl font-medium text-title-gray">合同执行跟踪</h1>
                    <div class="text-sm text-aux-gray">
                        玻璃深加工ERP系统 / 基础管理 / 合同执行跟踪
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2 px-3 py-1 bg-error text-white rounded-md text-sm" x-show="getDelayedContracts() > 0">
                        <svg class="w-4 h-4 alert-badge" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <span x-text="getDelayedContracts() + ' 个合同延期'"></span>
                    </div>
                    <button @click="showReportModal = true" 
                            class="px-4 py-2 bg-warning text-white rounded-md hover:bg-yellow-600 transition-colors flex items-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        <span>执行报告</span>
                    </button>
                    <button @click="refreshData()" 
                            class="px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600 transition-colors flex items-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        <span>刷新数据</span>
                    </button>
                </div>
            </div>
        </header>

        <div class="flex">
            <!-- 左侧边栏 -->
            <aside class="w-64 bg-white border-r border-border-gray min-h-screen">
                <div class="p-4">
                    <!-- 合同选择 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-title-gray mb-2">选择合同</label>
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" 
                                    class="w-full px-3 py-2 border border-border-gray rounded-md text-left bg-white hover:border-primary focus:outline-none focus:ring-2 focus:ring-primary">
                                <span x-text="selectedContract ? selectedContract.contractNo + ' - ' + selectedContract.projectName : '请选择合同'" 
                                      :class="selectedContract ? 'text-title-gray' : 'text-aux-gray'" class="block truncate"></span>
                                <svg class="w-4 h-4 absolute right-3 top-3 transform transition-transform" 
                                     :class="open ? 'rotate-180' : ''" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div x-show="open" @click.away="open = false" 
                                 class="absolute z-10 w-full mt-1 bg-white border border-border-gray rounded-md shadow-lg max-h-80 overflow-y-auto">
                                <div class="p-2">
                                    <input type="text" x-model="contractSearchQuery" placeholder="搜索合同..." 
                                           class="w-full px-3 py-2 border border-border-gray rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                                <div class="divide-y divide-border-gray">
                                    <template x-for="contract in filteredExecutingContracts" :key="contract.id">
                                        <button @click="selectContract(contract); open = false" 
                                                class="w-full px-3 py-3 text-left hover:bg-blue-50 transition-colors">
                                            <div class="text-sm font-medium text-title-gray" x-text="contract.contractNo"></div>
                                            <div class="text-sm text-aux-gray truncate" x-text="contract.projectName"></div>
                                            <div class="flex items-center justify-between mt-1">
                                                <span class="text-xs px-2 py-1 rounded"
                                                      :class="contract.riskLevel === 'high' ? 'bg-red-100 text-red-800' : 
                                                             contract.riskLevel === 'medium' ? 'bg-yellow-100 text-yellow-800' : 
                                                             'bg-green-100 text-green-800'"
                                                      x-text="getRiskLevelText(contract.riskLevel)"></span>
                                                <span class="text-xs text-aux-gray" x-text="contract.progress + '%'"></span>
                                            </div>
                                        </button>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 执行状态概览 -->
                    <div class="mb-6" x-show="selectedContract">
                        <label class="block text-sm font-medium text-title-gray mb-2">执行概览</label>
                        <div class="space-y-3">
                            <div class="p-3 bg-bg-gray rounded-md">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm text-body-gray">总体进度</span>
                                    <span class="text-sm font-medium" x-text="selectedContract?.progress + '%'"></span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-primary h-2 rounded-full transition-all duration-500" 
                                         :style="`width: ${selectedContract?.progress || 0}%`"></div>
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-2">
                                <div class="p-2 text-center bg-success text-white rounded">
                                    <div class="text-lg font-bold" x-text="selectedContract?.completedMilestones || 0"></div>
                                    <div class="text-xs">已完成</div>
                                </div>
                                <div class="p-2 text-center bg-warning text-white rounded">
                                    <div class="text-lg font-bold" x-text="selectedContract?.totalMilestones || 0"></div>
                                    <div class="text-xs">总里程碑</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 风险指标 -->
                    <div class="mb-6" x-show="selectedContract">
                        <label class="block text-sm font-medium text-title-gray mb-2">风险指标</label>
                        <div class="space-y-2">
                            <template x-for="risk in selectedContract?.risks || []" :key="risk.id">
                                <div class="p-2 border rounded-md" 
                                     :class="risk.level === 'high' ? 'border-red-200 bg-red-50' : 
                                            risk.level === 'medium' ? 'border-yellow-200 bg-yellow-50' : 
                                            'border-green-200 bg-green-50'">
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm font-medium" x-text="risk.name"></span>
                                        <span class="text-xs px-2 py-1 rounded"
                                              :class="risk.level === 'high' ? 'bg-red-100 text-red-800' : 
                                                     risk.level === 'medium' ? 'bg-yellow-100 text-yellow-800' : 
                                                     'bg-green-100 text-green-800'"
                                              x-text="getRiskLevelText(risk.level)"></span>
                                    </div>
                                    <div class="text-xs text-aux-gray mt-1" x-text="risk.description"></div>
                                </div>
                            </template>
                        </div>
                    </div>

                    <!-- 快速操作 -->
                    <div class="mb-6" x-show="selectedContract">
                        <label class="block text-sm font-medium text-title-gray mb-2">快速操作</label>
                        <div class="space-y-2">
                            <button @click="updateMilestone()" 
                                    class="w-full px-3 py-2 text-sm bg-primary text-white rounded-md hover:bg-blue-600 transition-colors">
                                更新里程碑
                            </button>
                            <button @click="addRiskAlert()" 
                                    class="w-full px-3 py-2 text-sm bg-warning text-white rounded-md hover:bg-yellow-600 transition-colors">
                                风险预警
                            </button>
                            <button @click="generateReport()" 
                                    class="w-full px-3 py-2 text-sm bg-success text-white rounded-md hover:bg-green-600 transition-colors">
                                生成报告
                            </button>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- 主内容区域 -->
            <main class="flex-1 p-6">
                <!-- 选择合同提示 -->
                <div x-show="!selectedContract" class="bg-white rounded-lg shadow-sm border border-border-gray p-8 text-center">
                    <div class="text-center">
                        <svg class="w-20 h-20 mx-auto mb-4 text-disabled-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <h3 class="text-lg font-medium text-title-gray mb-2">请选择要跟踪的合同</h3>
                        <p class="text-aux-gray mb-4">从左侧选择一个执行中的合同以查看详细的执行跟踪信息</p>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                            <div class="p-4 border border-border-gray rounded-lg">
                                <div class="text-2xl font-bold text-primary" x-text="getExecutingContractsCount()"></div>
                                <div class="text-sm text-aux-gray">执行中合同</div>
                            </div>
                            <div class="p-4 border border-border-gray rounded-lg">
                                <div class="text-2xl font-bold text-warning" x-text="getDelayedContracts()"></div>
                                <div class="text-sm text-aux-gray">延期合同</div>
                            </div>
                            <div class="p-4 border border-border-gray rounded-lg">
                                <div class="text-2xl font-bold text-success" x-text="getAverageProgress().toFixed(1) + '%'"></div>
                                <div class="text-sm text-aux-gray">平均进度</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 合同执行详情 -->
                <div x-show="selectedContract" class="space-y-6">
                    <!-- 合同基本信息 -->
                    <div class="bg-white rounded-lg shadow-sm border border-border-gray p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h2 class="text-lg font-medium text-title-gray">合同执行概况</h2>
                            <div class="flex items-center space-x-2">
                                <span class="text-sm text-aux-gray">最后更新:</span>
                                <span class="text-sm font-medium" x-text="selectedContract?.lastUpdate || '2024-07-31 14:30'"></span>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            <div class="performance-chart rounded-lg p-4 text-white">
                                <div class="text-sm opacity-90">执行进度</div>
                                <div class="text-2xl font-bold" x-text="selectedContract?.progress + '%'"></div>
                                <div class="text-sm opacity-75">目标: 按期完成</div>
                            </div>
                            <div class="bg-success rounded-lg p-4 text-white">
                                <div class="text-sm opacity-90">已完成里程碑</div>
                                <div class="text-2xl font-bold" x-text="selectedContract?.completedMilestones + '/' + selectedContract?.totalMilestones"></div>
                                <div class="text-sm opacity-75">完成率: <span x-text="((selectedContract?.completedMilestones / selectedContract?.totalMilestones) * 100).toFixed(0) + '%'"></span></div>
                            </div>
                            <div class="bg-warning rounded-lg p-4 text-white">
                                <div class="text-sm opacity-90">剩余天数</div>
                                <div class="text-2xl font-bold" x-text="selectedContract?.remainingDays"></div>
                                <div class="text-sm opacity-75">预计交付: <span x-text="selectedContract?.expectedDelivery"></span></div>
                            </div>
                            <div class="risk-indicator rounded-lg p-4 text-white">
                                <div class="text-sm opacity-90">风险等级</div>
                                <div class="text-2xl font-bold" x-text="getRiskLevelText(selectedContract?.riskLevel)"></div>
                                <div class="text-sm opacity-75">需关注: <span x-text="selectedContract?.risks?.length || 0"></span> 项</div>
                            </div>
                        </div>
                    </div>

                    <!-- 里程碑时间线 -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 里程碑进度 -->
                        <div class="bg-white rounded-lg shadow-sm border border-border-gray p-6">
                            <h3 class="text-lg font-medium text-title-gray mb-4">里程碑进度</h3>
                            <div class="milestone-timeline" :style="`--progress: ${selectedContract?.progress || 0}%`">
                                <template x-for="milestone in selectedContract?.milestones || []" :key="milestone.id">
                                    <div class="milestone-item">
                                        <div class="milestone-dot" 
                                             :class="milestone.status === 'completed' ? 'completed' : 
                                                    milestone.status === 'current' ? 'current' : 'pending'"></div>
                                        <div class="flex items-start justify-between">
                                            <div class="flex-1">
                                                <h4 class="text-sm font-medium text-title-gray" x-text="milestone.name"></h4>
                                                <p class="text-sm text-aux-gray mt-1" x-text="milestone.description"></p>
                                                <div class="flex items-center space-x-4 mt-2">
                                                    <span class="text-xs text-aux-gray">计划: <span x-text="milestone.plannedDate"></span></span>
                                                    <span x-show="milestone.actualDate" class="text-xs text-aux-gray">实际: <span x-text="milestone.actualDate"></span></span>
                                                </div>
                                            </div>
                                            <div class="flex flex-col items-end space-y-1">
                                                <span class="text-xs px-2 py-1 rounded"
                                                      :class="milestone.status === 'completed' ? 'bg-green-100 text-green-800' : 
                                                             milestone.status === 'current' ? 'bg-blue-100 text-blue-800' : 
                                                             'bg-gray-100 text-gray-800'"
                                                      x-text="getMilestoneStatusText(milestone.status)"></span>
                                                <span x-show="milestone.delayDays > 0" class="text-xs px-2 py-1 bg-red-100 text-red-800 rounded">
                                                    延期<span x-text="milestone.delayDays"></span>天
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>

                        <!-- 甘特图 -->
                        <div class="bg-white rounded-lg shadow-sm border border-border-gray p-6">
                            <h3 class="text-lg font-medium text-title-gray mb-4">进度甘特图</h3>
                            <div class="space-y-3">
                                <template x-for="milestone in selectedContract?.milestones || []" :key="milestone.id">
                                    <div>
                                        <div class="flex items-center justify-between mb-1">
                                            <span class="text-sm font-medium text-title-gray" x-text="milestone.name"></span>
                                            <span class="text-xs text-aux-gray" x-text="milestone.progress + '%'"></span>
                                        </div>
                                        <div class="gantt-bar bg-gray-200 rounded">
                                            <div class="gantt-progress rounded" 
                                                 :style="`width: ${milestone.progress}%`"></div>
                                        </div>
                                        <div class="flex justify-between text-xs text-aux-gray mt-1">
                                            <span x-text="milestone.plannedDate"></span>
                                            <span x-text="milestone.endDate"></span>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>

                    <!-- 执行数据图表 -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 进度趋势图 -->
                        <div class="bg-white rounded-lg shadow-sm border border-border-gray p-6">
                            <h3 class="text-lg font-medium text-title-gray mb-4">进度趋势</h3>
                            <div class="h-64">
                                <canvas id="progressChart"></canvas>
                            </div>
                        </div>

                        <!-- 风险分析图 -->
                        <div class="bg-white rounded-lg shadow-sm border border-border-gray p-6">
                            <h3 class="text-lg font-medium text-title-gray mb-4">风险分析</h3>
                            <div class="h-64">
                                <canvas id="riskChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- 执行日志 -->
                    <div class="bg-white rounded-lg shadow-sm border border-border-gray p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-title-gray">执行日志</h3>
                            <button @click="addExecutionLog()" 
                                    class="px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600 transition-colors text-sm">
                                添加记录
                            </button>
                        </div>
                        <div class="space-y-3 max-h-80 overflow-y-auto">
                            <template x-for="log in selectedContract?.executionLogs || []" :key="log.id">
                                <div class="border-l-4 pl-4 py-2" 
                                     :class="log.type === 'milestone' ? 'border-green-400' : 
                                            log.type === 'risk' ? 'border-red-400' : 
                                            log.type === 'progress' ? 'border-blue-400' : 
                                            'border-gray-400'">
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1">
                                            <div class="flex items-center space-x-2">
                                                <span class="text-sm font-medium text-title-gray" x-text="log.title"></span>
                                                <span class="text-xs px-2 py-1 rounded"
                                                      :class="log.type === 'milestone' ? 'bg-green-100 text-green-800' : 
                                                             log.type === 'risk' ? 'bg-red-100 text-red-800' : 
                                                             log.type === 'progress' ? 'bg-blue-100 text-blue-800' : 
                                                             'bg-gray-100 text-gray-800'"
                                                      x-text="getLogTypeText(log.type)"></span>
                                            </div>
                                            <p class="text-sm text-aux-gray mt-1" x-text="log.description"></p>
                                            <div class="flex items-center space-x-4 mt-2">
                                                <span class="text-xs text-aux-gray">操作人: <span x-text="log.operator"></span></span>
                                                <span class="text-xs text-aux-gray" x-text="log.timestamp"></span>
                                            </div>
                                        </div>
                                        <div x-show="log.attachment" class="ml-4">
                                            <button class="text-xs text-primary hover:text-blue-600">查看附件</button>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <!-- 执行报告模态框 -->
        <div x-show="showReportModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" x-transition>
            <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl m-4 max-h-screen overflow-y-auto">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-xl font-medium text-title-gray">合同执行报告</h2>
                        <button @click="showReportModal = false" class="text-aux-gray hover:text-title-gray">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <div class="space-y-6">
                        <!-- 报告概要 -->
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div class="p-4 bg-blue-50 rounded-lg">
                                <div class="text-lg font-bold text-blue-600" x-text="getExecutingContractsCount()"></div>
                                <div class="text-sm text-aux-gray">执行中合同</div>
                            </div>
                            <div class="p-4 bg-green-50 rounded-lg">
                                <div class="text-lg font-bold text-green-600" x-text="getOnTimeContracts()"></div>
                                <div class="text-sm text-aux-gray">按期执行</div>
                            </div>
                            <div class="p-4 bg-yellow-50 rounded-lg">
                                <div class="text-lg font-bold text-yellow-600" x-text="getDelayedContracts()"></div>
                                <div class="text-sm text-aux-gray">延期合同</div>
                            </div>
                            <div class="p-4 bg-red-50 rounded-lg">
                                <div class="text-lg font-bold text-red-600" x-text="getHighRiskContracts()"></div>
                                <div class="text-sm text-aux-gray">高风险合同</div>
                            </div>
                        </div>

                        <!-- 详细数据表格 -->
                        <div class="overflow-x-auto">
                            <table class="w-full border-collapse border border-border-gray">
                                <thead class="bg-bg-gray">
                                    <tr>
                                        <th class="border border-border-gray px-4 py-2 text-left text-sm font-medium text-aux-gray">合同编号</th>
                                        <th class="border border-border-gray px-4 py-2 text-left text-sm font-medium text-aux-gray">项目名称</th>
                                        <th class="border border-border-gray px-4 py-2 text-left text-sm font-medium text-aux-gray">执行进度</th>
                                        <th class="border border-border-gray px-4 py-2 text-left text-sm font-medium text-aux-gray">风险等级</th>
                                        <th class="border border-border-gray px-4 py-2 text-left text-sm font-medium text-aux-gray">剩余天数</th>
                                        <th class="border border-border-gray px-4 py-2 text-left text-sm font-medium text-aux-gray">执行状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <template x-for="contract in executingContracts" :key="contract.id">
                                        <tr class="hover:bg-bg-gray">
                                            <td class="border border-border-gray px-4 py-2 text-sm" x-text="contract.contractNo"></td>
                                            <td class="border border-border-gray px-4 py-2 text-sm" x-text="contract.projectName"></td>
                                            <td class="border border-border-gray px-4 py-2 text-sm">
                                                <div class="flex items-center">
                                                    <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                                        <div class="bg-primary h-2 rounded-full" :style="`width: ${contract.progress}%`"></div>
                                                    </div>
                                                    <span x-text="contract.progress + '%'"></span>
                                                </div>
                                            </td>
                                            <td class="border border-border-gray px-4 py-2 text-sm">
                                                <span class="px-2 py-1 text-xs rounded"
                                                      :class="contract.riskLevel === 'high' ? 'bg-red-100 text-red-800' : 
                                                             contract.riskLevel === 'medium' ? 'bg-yellow-100 text-yellow-800' : 
                                                             'bg-green-100 text-green-800'"
                                                      x-text="getRiskLevelText(contract.riskLevel)"></span>
                                            </td>
                                            <td class="border border-border-gray px-4 py-2 text-sm" x-text="contract.remainingDays + '天'"></td>
                                            <td class="border border-border-gray px-4 py-2 text-sm">
                                                <span class="px-2 py-1 text-xs rounded"
                                                      :class="contract.remainingDays < 0 ? 'bg-red-100 text-red-800' : 
                                                             contract.remainingDays < 30 ? 'bg-yellow-100 text-yellow-800' : 
                                                             'bg-green-100 text-green-800'"
                                                      x-text="contract.remainingDays < 0 ? '已延期' : 
                                                             contract.remainingDays < 30 ? '即将到期' : '正常执行'"></span>
                                            </td>
                                        </tr>
                                    </template>
                                </tbody>
                            </table>
                        </div>

                        <div class="flex items-center justify-end space-x-4 pt-4 border-t border-border-gray">
                            <button @click="exportReport()" 
                                    class="px-4 py-2 border border-border-gray rounded-md hover:bg-bg-gray transition-colors">
                                导出报告
                            </button>
                            <button @click="showReportModal = false" 
                                    class="px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600 transition-colors">
                                关闭
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function contractTrackingApp() {
            return {
                selectedContract: null,
                contractSearchQuery: '',
                showReportModal: false,

                // 模拟执行中的合同数据
                executingContracts: [
                    {
                        id: 'CT2024001',
                        contractNo: 'CT2024001',
                        projectName: '华美商务大厦玻璃幕墙工程',
                        customerName: '华美地产集团',
                        progress: 65,
                        riskLevel: 'medium',
                        remainingDays: 45,
                        completedMilestones: 4,
                        totalMilestones: 6,
                        expectedDelivery: '2024-08-31',
                        lastUpdate: '2024-07-31 14:30',
                        risks: [
                            { id: 1, name: '材料供应延迟', level: 'medium', description: '上游供应商交期延迟5天' },
                            { id: 2, name: '天气影响', level: 'low', description: '近期多雨天气可能影响安装进度' }
                        ],
                        milestones: [
                            { id: 1, name: '设计确认', description: '完成设计方案确认', status: 'completed', progress: 100, plannedDate: '2024-02-15', actualDate: '2024-02-12', delayDays: 0 },
                            { id: 2, name: '材料采购', description: '完成原材料采购', status: 'completed', progress: 100, plannedDate: '2024-03-01', actualDate: '2024-03-05', delayDays: 4 },
                            { id: 3, name: '加工制造', description: '完成玻璃深加工', status: 'completed', progress: 100, plannedDate: '2024-04-15', actualDate: '2024-04-20', delayDays: 5 },
                            { id: 4, name: '现场测量', description: '完成现场精确测量', status: 'completed', progress: 100, plannedDate: '2024-05-01', actualDate: '2024-04-28', delayDays: 0 },
                            { id: 5, name: '安装施工', description: '进行现场安装', status: 'current', progress: 75, plannedDate: '2024-06-01', endDate: '2024-07-31', delayDays: 0 },
                            { id: 6, name: '验收交付', description: '项目验收和交付', status: 'pending', progress: 0, plannedDate: '2024-08-15', endDate: '2024-08-31', delayDays: 0 }
                        ],
                        executionLogs: [
                            { id: 1, type: 'progress', title: '安装进度更新', description: '第5层玻璃幕墙安装完成，整体进度达到75%', operator: '张工程师', timestamp: '2024-07-31 14:30', attachment: true },
                            { id: 2, type: 'risk', title: '材料供应预警', description: '下批次玻璃材料供应商通知延期5天交货', operator: '李采购', timestamp: '2024-07-30 09:15', attachment: false },
                            { id: 3, type: 'milestone', title: '里程碑完成', description: '现场测量工作已完成，精度符合要求', operator: '王测量师', timestamp: '2024-04-28 16:45', attachment: true }
                        ]
                    },
                    {
                        id: 'CT2024002',
                        contractNo: 'CT2024002',
                        projectName: '绿城住宅小区门窗工程',
                        customerName: '绿城建设集团',
                        progress: 35,
                        riskLevel: 'high',
                        remainingDays: -5,
                        completedMilestones: 2,
                        totalMilestones: 5,
                        expectedDelivery: '2024-07-15',
                        lastUpdate: '2024-07-31 10:20',
                        risks: [
                            { id: 1, name: '进度严重滞后', level: 'high', description: '当前进度比计划滞后15天' },
                            { id: 2, name: '质量问题', level: 'medium', description: '部分门窗存在密封性问题需返工' }
                        ],
                        milestones: [
                            { id: 1, name: '合同签署', description: '完成合同签署', status: 'completed', progress: 100, plannedDate: '2024-02-10', actualDate: '2024-02-10', delayDays: 0 },
                            { id: 2, name: '深化设计', description: '完成门窗深化设计', status: 'completed', progress: 100, plannedDate: '2024-03-01', actualDate: '2024-03-10', delayDays: 9 },
                            { id: 3, name: '生产制造', description: '门窗生产制造', status: 'current', progress: 60, plannedDate: '2024-04-15', endDate: '2024-05-30', delayDays: 12 },
                            { id: 4, name: '现场安装', description: '现场安装施工', status: 'pending', progress: 0, plannedDate: '2024-06-01', endDate: '2024-07-15', delayDays: 0 },
                            { id: 5, name: '竣工验收', description: '项目竣工验收', status: 'pending', progress: 0, plannedDate: '2024-07-15', endDate: '2024-07-31', delayDays: 0 }
                        ],
                        executionLogs: [
                            { id: 1, type: 'risk', title: '质量问题发现', description: '发现部分门窗密封胶条不符合标准，需要更换', operator: '质检员刘师傅', timestamp: '2024-07-31 10:20', attachment: true },
                            { id: 2, type: 'progress', title: '生产进度滞后', description: '由于质量问题返工，生产进度较计划滞后12天', operator: '生产主管', timestamp: '2024-07-30 16:00', attachment: false }
                        ]
                    },
                    {
                        id: 'CT2024003',
                        contractNo: 'CT2024003',
                        projectName: '科技园玻璃幕墙维护',
                        customerName: '科技园管委会',
                        progress: 45,
                        riskLevel: 'low',
                        remainingDays: 198,
                        completedMilestones: 2,
                        totalMilestones: 4,
                        expectedDelivery: '2025-02-14',
                        lastUpdate: '2024-07-31 11:45',
                        risks: [
                            { id: 1, name: '设备老化', level: 'low', description: '部分玻璃幕墙存在轻微老化现象' }
                        ],
                        milestones: [
                            { id: 1, name: '现状评估', description: '完成玻璃幕墙现状评估', status: 'completed', progress: 100, plannedDate: '2024-03-01', actualDate: '2024-02-28', delayDays: 0 },
                            { id: 2, name: '维护方案', description: '制定年度维护方案', status: 'completed', progress: 100, plannedDate: '2024-03-15', actualDate: '2024-03-12', delayDays: 0 },
                            { id: 3, name: '定期维护', description: '执行定期维护计划', status: 'current', progress: 45, plannedDate: '2024-04-01', endDate: '2024-12-31', delayDays: 0 },
                            { id: 4, name: '年度总结', description: '年度维护工作总结', status: 'pending', progress: 0, plannedDate: '2025-01-15', endDate: '2025-02-14', delayDays: 0 }
                        ],
                        executionLogs: [
                            { id: 1, type: 'progress', title: '第三季度维护完成', description: '完成科技园A、B栋玻璃幕墙清洁和检查', operator: '维护班组长', timestamp: '2024-07-31 11:45', attachment: false },
                            { id: 2, type: 'milestone', title: '维护方案确认', description: '年度维护方案已获得客户确认', operator: '项目经理', timestamp: '2024-03-12 14:30', attachment: true }
                        ]
                    }
                ],

                // 计算属性
                get filteredExecutingContracts() {
                    let filtered = this.executingContracts;
                    if (this.contractSearchQuery) {
                        const query = this.contractSearchQuery.toLowerCase();
                        filtered = filtered.filter(contract => 
                            contract.contractNo.toLowerCase().includes(query) ||
                            contract.projectName.toLowerCase().includes(query) ||
                            contract.customerName.toLowerCase().includes(query)
                        );
                    }
                    return filtered;
                },

                // 初始化
                init() {
                    this.$nextTick(() => {
                        if (this.selectedContract) {
                            this.initCharts();
                        }
                    });
                },

                // 方法
                selectContract(contract) {
                    this.selectedContract = contract;
                    this.$nextTick(() => {
                        this.initCharts();
                    });
                },

                getExecutingContractsCount() {
                    return this.executingContracts.length;
                },

                getDelayedContracts() {
                    return this.executingContracts.filter(contract => contract.remainingDays < 0).length;
                },

                getOnTimeContracts() {
                    return this.executingContracts.filter(contract => contract.remainingDays >= 0).length;
                },

                getHighRiskContracts() {
                    return this.executingContracts.filter(contract => contract.riskLevel === 'high').length;
                },

                getAverageProgress() {
                    if (this.executingContracts.length === 0) return 0;
                    const totalProgress = this.executingContracts.reduce((sum, contract) => sum + contract.progress, 0);
                    return totalProgress / this.executingContracts.length;
                },

                getRiskLevelText(level) {
                    const levels = {
                        'low': '低风险',
                        'medium': '中风险',
                        'high': '高风险'
                    };
                    return levels[level] || '未知';
                },

                getMilestoneStatusText(status) {
                    const statuses = {
                        'completed': '已完成',
                        'current': '进行中',
                        'pending': '待开始'
                    };
                    return statuses[status] || '未知';
                },

                getLogTypeText(type) {
                    const types = {
                        'milestone': '里程碑',
                        'risk': '风险预警',
                        'progress': '进度更新',
                        'general': '一般记录'
                    };
                    return types[type] || '其他';
                },

                updateMilestone() {
                    alert('更新里程碑功能 - 实际应用中会打开里程碑编辑界面');
                },

                addRiskAlert() {
                    alert('添加风险预警功能 - 实际应用中会打开风险录入界面');
                },

                generateReport() {
                    this.showReportModal = true;
                },

                addExecutionLog() {
                    alert('添加执行记录功能 - 实际应用中会打开记录编辑界面');
                },

                refreshData() {
                    alert('数据已刷新 - 实际应用中会从服务器获取最新数据');
                },

                exportReport() {
                    alert('报告导出功能 - 实际应用中会生成并下载Excel报告');
                },

                initCharts() {
                    this.initProgressChart();
                    this.initRiskChart();
                },

                initProgressChart() {
                    const ctx = document.getElementById('progressChart');
                    if (ctx && this.selectedContract) {
                        new Chart(ctx, {
                            type: 'line',
                            data: {
                                labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
                                datasets: [{
                                    label: '实际进度',
                                    data: [0, 10, 25, 40, 55, 60, this.selectedContract.progress],
                                    borderColor: '#1890FF',
                                    backgroundColor: 'rgba(24, 144, 255, 0.1)',
                                    tension: 0.4
                                }, {
                                    label: '计划进度',
                                    data: [0, 15, 30, 45, 60, 75, 85],
                                    borderColor: '#52C41A',
                                    backgroundColor: 'rgba(82, 196, 26, 0.1)',
                                    borderDash: [5, 5],
                                    tension: 0.4
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: {
                                        position: 'top'
                                    }
                                },
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        max: 100,
                                        ticks: {
                                            callback: function(value) {
                                                return value + '%';
                                            }
                                        }
                                    }
                                }
                            }
                        });
                    }
                },

                initRiskChart() {
                    const ctx = document.getElementById('riskChart');
                    if (ctx && this.selectedContract) {
                        const riskCounts = {
                            low: this.selectedContract.risks.filter(r => r.level === 'low').length,
                            medium: this.selectedContract.risks.filter(r => r.level === 'medium').length,
                            high: this.selectedContract.risks.filter(r => r.level === 'high').length
                        };

                        new Chart(ctx, {
                            type: 'doughnut',
                            data: {
                                labels: ['低风险', '中风险', '高风险'],
                                datasets: [{
                                    data: [riskCounts.low, riskCounts.medium, riskCounts.high],
                                    backgroundColor: ['#52C41A', '#FAAD14', '#F5222D'],
                                    borderWidth: 0
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: {
                                        position: 'bottom'
                                    }
                                }
                            }
                        });
                    }
                }
            }
        }
    </script>
</body>
</html>