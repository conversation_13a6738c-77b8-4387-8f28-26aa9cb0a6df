<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据权限管理 - 玻璃深加工ERP系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1890FF',
                        success: '#52C41A',
                        warning: '#FAAD14',
                        error: '#F5222D',
                        'title-gray': '#262626',
                        'body-gray': '#595959',
                        'aux-gray': '#8C8C8C',
                        'disabled-gray': '#BFBFBF',
                        'border-gray': '#D9D9D9',
                        'bg-gray': '#FAFAFA'
                    },
                    fontFamily: {
                        'chinese': ['PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', sans-serif;
        }
        .permission-tree .tree-node:hover {
            background-color: #f0f9ff;
        }
        .permission-visualization {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }
        .loading-spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #1890FF;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-bg-gray font-chinese">
    <div x-data="dataPermissionApp()" class="min-h-screen">
        <!-- 顶部导航栏 -->
        <header class="bg-white shadow-sm border-b border-border-gray h-16">
            <div class="flex items-center justify-between px-6 h-full">
                <div class="flex items-center space-x-4">
                    <h1 class="text-xl font-medium text-title-gray">数据权限管理</h1>
                    <div class="text-sm text-aux-gray">
                        玻璃深加工ERP系统 / 基础管理 / 数据权限管理
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button @click="testPermissions" 
                            class="px-4 py-2 bg-warning text-white rounded-md hover:bg-yellow-600 transition-colors flex items-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>权限测试</span>
                    </button>
                    <button @click="savePermissions" 
                            class="px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600 transition-colors flex items-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                        <span>保存配置</span>
                    </button>
                </div>
            </div>
        </header>

        <div class="flex">
            <!-- 左侧边栏 -->
            <aside class="w-60 bg-white border-r border-border-gray min-h-screen">
                <div class="p-4">
                    <!-- 角色/用户选择器 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-title-gray mb-2">选择对象</label>
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" 
                                    class="w-full px-3 py-2 border border-border-gray rounded-md text-left bg-white hover:border-primary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                <span x-text="selectedTarget ? selectedTarget.name : '请选择角色或用户'" 
                                      :class="selectedTarget ? 'text-title-gray' : 'text-aux-gray'"></span>
                                <svg class="w-4 h-4 absolute right-3 top-3 transform transition-transform" 
                                     :class="open ? 'rotate-180' : ''" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div x-show="open" @click.away="open = false" 
                                 class="absolute z-10 w-full mt-1 bg-white border border-border-gray rounded-md shadow-lg">
                                <div class="p-2">
                                    <input type="text" x-model="searchQuery" placeholder="搜索角色或用户..." 
                                           class="w-full px-3 py-2 border border-border-gray rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                                <div class="max-h-60 overflow-y-auto">
                                    <div class="px-3 py-2 text-xs font-medium text-aux-gray bg-bg-gray">角色</div>
                                    <template x-for="role in filteredRoles" :key="role.id">
                                        <button @click="selectTarget(role, 'role'); open = false" 
                                                class="w-full px-3 py-2 text-left hover:bg-blue-50 flex items-center space-x-2">
                                            <div class="w-6 h-6 bg-primary rounded text-white text-xs flex items-center justify-center">R</div>
                                            <span x-text="role.name" class="text-sm"></span>
                                        </button>
                                    </template>
                                    <div class="px-3 py-2 text-xs font-medium text-aux-gray bg-bg-gray">用户</div>
                                    <template x-for="user in filteredUsers" :key="user.id">
                                        <button @click="selectTarget(user, 'user'); open = false" 
                                                class="w-full px-3 py-2 text-left hover:bg-blue-50 flex items-center space-x-2">
                                            <div class="w-6 h-6 bg-success rounded text-white text-xs flex items-center justify-center">U</div>
                                            <span x-text="user.name" class="text-sm"></span>
                                        </button>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 权限类型筛选 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-title-gray mb-2">权限类型</label>
                        <div class="space-y-2">
                            <template x-for="type in permissionTypes" :key="type.id">
                                <label class="flex items-center">
                                    <input type="checkbox" x-model="selectedPermissionTypes" :value="type.id" 
                                           class="w-4 h-4 text-primary border-border-gray rounded focus:ring-primary">
                                    <span x-text="type.name" class="ml-2 text-sm text-body-gray"></span>
                                </label>
                            </template>
                        </div>
                    </div>

                    <!-- 组织树结构 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-title-gray mb-2">组织架构</label>
                        <div class="permission-tree">
                            <template x-for="org in organizationTree" :key="org.id">
                                <div class="tree-node">
                                    <div @click="toggleOrgNode(org)" 
                                         class="flex items-center py-1 px-2 rounded cursor-pointer hover:bg-blue-50">
                                        <svg class="w-4 h-4 mr-1 transform transition-transform" 
                                             :class="org.expanded ? 'rotate-90' : ''" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span x-text="org.name" class="text-sm"></span>
                                    </div>
                                    <div x-show="org.expanded" class="ml-4">
                                        <template x-for="child in org.children" :key="child.id">
                                            <div @click="selectOrgNode(child)" 
                                                 class="flex items-center py-1 px-2 rounded cursor-pointer hover:bg-blue-50"
                                                 :class="selectedOrgNodes.includes(child.id) ? 'bg-blue-100' : ''">
                                                <span x-text="child.name" class="text-sm"></span>
                                            </div>
                                        </template>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- 主内容区域 -->
            <main class="flex-1 p-6">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- 权限规则配置 -->
                    <div class="bg-white rounded-lg shadow-sm border border-border-gray">
                        <div class="p-4 border-b border-border-gray">
                            <h2 class="text-lg font-medium text-title-gray">权限规则配置</h2>
                        </div>
                        <div class="p-4">
                            <template x-show="selectedTarget">
                                <div class="space-y-4">
                                    <!-- 基础权限范围 -->
                                    <div>
                                        <label class="block text-sm font-medium text-title-gray mb-2">数据访问范围</label>
                                        <div class="grid grid-cols-2 gap-2">
                                            <template x-for="scope in dataScopes" :key="scope.id">
                                                <label class="flex items-center p-3 border border-border-gray rounded-md hover:border-primary cursor-pointer"
                                                       :class="selectedDataScope === scope.id ? 'border-primary bg-blue-50' : ''">
                                                    <input type="radio" x-model="selectedDataScope" :value="scope.id" 
                                                           class="w-4 h-4 text-primary border-border-gray focus:ring-primary">
                                                    <div class="ml-2">
                                                        <div x-text="scope.name" class="text-sm font-medium"></div>
                                                        <div x-text="scope.description" class="text-xs text-aux-gray"></div>
                                                    </div>
                                                </label>
                                            </template>
                                        </div>
                                    </div>

                                    <!-- 业务数据权限 -->
                                    <div>
                                        <label class="block text-sm font-medium text-title-gray mb-2">业务数据权限</label>
                                        <div class="space-y-2">
                                            <template x-for="permission in businessPermissions" :key="permission.id">
                                                <div class="flex items-center justify-between p-3 border border-border-gray rounded-md">
                                                    <div class="flex items-center">
                                                        <input type="checkbox" x-model="selectedBusinessPermissions" :value="permission.id" 
                                                               class="w-4 h-4 text-primary border-border-gray rounded focus:ring-primary">
                                                        <div class="ml-3">
                                                            <div x-text="permission.name" class="text-sm font-medium"></div>
                                                            <div x-text="permission.description" class="text-xs text-aux-gray"></div>
                                                        </div>
                                                    </div>
                                                    <div class="flex items-center space-x-2">
                                                        <span class="text-xs px-2 py-1 rounded"
                                                              :class="permission.level === 'high' ? 'bg-red-100 text-red-800' : 
                                                                     permission.level === 'medium' ? 'bg-yellow-100 text-yellow-800' : 
                                                                     'bg-green-100 text-green-800'"
                                                              x-text="permission.levelText"></span>
                                                    </div>
                                                </div>
                                            </template>
                                        </div>
                                    </div>

                                    <!-- 特殊规则配置 -->
                                    <div>
                                        <label class="block text-sm font-medium text-title-gray mb-2">特殊规则</label>
                                        <div class="space-y-3">
                                            <div class="p-3 border border-border-gray rounded-md">
                                                <label class="flex items-center">
                                                    <input type="checkbox" x-model="specialRules.dataMasking" 
                                                           class="w-4 h-4 text-primary border-border-gray rounded focus:ring-primary">
                                                    <span class="ml-2 text-sm">启用敏感数据脱敏</span>
                                                </label>
                                                <div x-show="specialRules.dataMasking" class="mt-2 ml-6">
                                                    <select x-model="specialRules.maskingLevel" 
                                                            class="w-full px-3 py-2 border border-border-gray rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary">
                                                        <option value="partial">部分脱敏</option>
                                                        <option value="full">完全脱敏</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="p-3 border border-border-gray rounded-md">
                                                <label class="flex items-center">
                                                    <input type="checkbox" x-model="specialRules.timeRestriction" 
                                                           class="w-4 h-4 text-primary border-border-gray rounded focus:ring-primary">
                                                    <span class="ml-2 text-sm">时间访问限制</span>
                                                </label>
                                                <div x-show="specialRules.timeRestriction" class="mt-2 ml-6 grid grid-cols-2 gap-2">
                                                    <input type="time" x-model="specialRules.startTime" 
                                                           class="px-3 py-2 border border-border-gray rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary">
                                                    <input type="time" x-model="specialRules.endTime" 
                                                           class="px-3 py-2 border border-border-gray rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                            <template x-show="!selectedTarget">
                                <div class="text-center py-8 text-aux-gray">
                                    <svg class="w-16 h-16 mx-auto mb-4 text-disabled-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                    </svg>
                                    <p>请先选择角色或用户</p>
                                </div>
                            </template>
                        </div>
                    </div>

                    <!-- 权限可视化 -->
                    <div class="bg-white rounded-lg shadow-sm border border-border-gray">
                        <div class="p-4 border-b border-border-gray">
                            <h2 class="text-lg font-medium text-title-gray">权限范围可视化</h2>
                        </div>
                        <div class="p-4">
                            <template x-show="selectedTarget">
                                <div class="space-y-4">
                                    <!-- 权限概览 -->
                                    <div class="permission-visualization rounded-lg p-4 text-white">
                                        <div class="flex items-center justify-between mb-3">
                                            <h3 class="font-medium">权限概览</h3>
                                            <span x-text="selectedTarget.name" class="text-sm opacity-90"></span>
                                        </div>
                                        <div class="grid grid-cols-3 gap-4 text-center">
                                            <div>
                                                <div class="text-2xl font-bold" x-text="permissionStats.dataScope"></div>
                                                <div class="text-sm opacity-90">数据范围</div>
                                            </div>
                                            <div>
                                                <div class="text-2xl font-bold" x-text="permissionStats.businessCount"></div>
                                                <div class="text-sm opacity-90">业务权限</div>
                                            </div>
                                            <div>
                                                <div class="text-2xl font-bold" x-text="permissionStats.riskLevel"></div>
                                                <div class="text-sm opacity-90">风险等级</div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 权限分布图表 -->
                                    <div class="chart-container">
                                        <canvas id="permissionChart"></canvas>
                                    </div>

                                    <!-- 影响范围列表 -->
                                    <div>
                                        <h4 class="text-sm font-medium text-title-gray mb-2">影响数据范围</h4>
                                        <div class="space-y-2 max-h-40 overflow-y-auto">
                                            <template x-for="impact in permissionImpacts" :key="impact.id">
                                                <div class="flex items-center justify-between p-2 bg-bg-gray rounded">
                                                    <div class="flex items-center space-x-2">
                                                        <div class="w-2 h-2 rounded-full" :class="impact.accessible ? 'bg-success' : 'bg-error'"></div>
                                                        <span x-text="impact.module" class="text-sm"></span>
                                                    </div>
                                                    <span x-text="impact.count + '条记录'" class="text-sm text-aux-gray"></span>
                                                </div>
                                            </template>
                                        </div>
                                    </div>
                                </div>
                            </template>
                            <template x-show="!selectedTarget">
                                <div class="text-center py-8 text-aux-gray">
                                    <svg class="w-16 h-16 mx-auto mb-4 text-disabled-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                    <p>选择对象后显示权限可视化</p>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>

                <!-- 权限测试工具 -->
                <div class="mt-6 bg-white rounded-lg shadow-sm border border-border-gray" x-show="showTestPanel">
                    <div class="p-4 border-b border-border-gray">
                        <div class="flex items-center justify-between">
                            <h2 class="text-lg font-medium text-title-gray">权限测试工具</h2>
                            <button @click="showTestPanel = false" class="text-aux-gray hover:text-title-gray">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="p-4">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h3 class="text-sm font-medium text-title-gray mb-3">测试配置</h3>
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm text-body-gray mb-1">测试用户</label>
                                        <select x-model="testConfig.userId" 
                                                class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                            <option value="">选择测试用户</option>
                                            <template x-for="user in users" :key="user.id">
                                                <option :value="user.id" x-text="user.name"></option>
                                            </template>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm text-body-gray mb-1">测试数据类型</label>
                                        <select x-model="testConfig.dataType" 
                                                class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                            <option value="">选择数据类型</option>
                                            <option value="orders">订单数据</option>
                                            <option value="customers">客户数据</option>
                                            <option value="products">产品数据</option>
                                            <option value="financial">财务数据</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm text-body-gray mb-1">测试操作</label>
                                        <div class="space-y-2">
                                            <template x-for="operation in testOperations" :key="operation.id">
                                                <label class="flex items-center">
                                                    <input type="checkbox" x-model="testConfig.operations" :value="operation.id" 
                                                           class="w-4 h-4 text-primary border-border-gray rounded focus:ring-primary">
                                                    <span x-text="operation.name" class="ml-2 text-sm"></span>
                                                </label>
                                            </template>
                                        </div>
                                    </div>
                                    <button @click="runPermissionTest" 
                                            :disabled="!testConfig.userId || !testConfig.dataType || testConfig.operations.length === 0"
                                            class="w-full px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600 disabled:bg-disabled-gray disabled:cursor-not-allowed transition-colors">
                                        <div x-show="!testRunning" class="flex items-center justify-center space-x-2">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m6-7a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            <span>运行测试</span>
                                        </div>
                                        <div x-show="testRunning" class="flex items-center justify-center space-x-2">
                                            <div class="loading-spinner"></div>
                                            <span>测试中...</span>
                                        </div>
                                    </button>
                                </div>
                            </div>
                            <div>
                                <h3 class="text-sm font-medium text-title-gray mb-3">测试结果</h3>
                                <div x-show="testResults.length > 0" class="space-y-2 max-h-80 overflow-y-auto">
                                    <template x-for="result in testResults" :key="result.id">
                                        <div class="p-3 border rounded-md" 
                                             :class="result.passed ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'">
                                            <div class="flex items-center justify-between mb-2">
                                                <span x-text="result.operation" class="text-sm font-medium"></span>
                                                <span class="text-xs px-2 py-1 rounded" 
                                                      :class="result.passed ? 'bg-success text-white' : 'bg-error text-white'"
                                                      x-text="result.passed ? '通过' : '拒绝'"></span>
                                            </div>
                                            <div x-text="result.message" class="text-sm text-body-gray"></div>
                                            <div x-show="result.details" x-text="result.details" class="text-xs text-aux-gray mt-1"></div>
                                        </div>
                                    </template>
                                </div>
                                <div x-show="testResults.length === 0" class="text-center py-8 text-aux-gray">
                                    <svg class="w-12 h-12 mx-auto mb-3 text-disabled-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                                    </svg>
                                    <p>运行测试后显示结果</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        function dataPermissionApp() {
            return {
                // 基础数据
                selectedTarget: null,
                searchQuery: '',
                selectedPermissionTypes: ['organizational', 'business'],
                selectedOrgNodes: [],
                selectedDataScope: 'department',
                selectedBusinessPermissions: [],
                specialRules: {
                    dataMasking: false,
                    maskingLevel: 'partial',
                    timeRestriction: false,
                    startTime: '09:00',
                    endTime: '18:00'
                },
                showTestPanel: false,
                testRunning: false,
                testConfig: {
                    userId: '',
                    dataType: '',
                    operations: []
                },
                testResults: [],

                // 静态数据
                roles: [
                    { id: 'admin', name: '系统管理员', type: 'role' },
                    { id: 'manager', name: '部门经理', type: 'role' },
                    { id: 'operator', name: '操作员', type: 'role' },
                    { id: 'viewer', name: '查看者', type: 'role' }
                ],
                users: [
                    { id: 'user1', name: '张三', department: '生产部', type: 'user' },
                    { id: 'user2', name: '李四', department: '销售部', type: 'user' },
                    { id: 'user3', name: '王五', department: '财务部', type: 'user' },
                    { id: 'user4', name: '赵六', department: '采购部', type: 'user' }
                ],
                permissionTypes: [
                    { id: 'organizational', name: '组织数据权限' },
                    { id: 'business', name: '业务数据权限' },
                    { id: 'financial', name: '财务数据权限' },
                    { id: 'sensitive', name: '敏感数据权限' }
                ],
                organizationTree: [
                    {
                        id: 'company',
                        name: '华美玻璃制品有限公司',
                        expanded: true,
                        children: [
                            { id: 'production', name: '生产部' },
                            { id: 'sales', name: '销售部' },
                            { id: 'finance', name: '财务部' },
                            { id: 'procurement', name: '采购部' },
                            { id: 'quality', name: '质检部' }
                        ]
                    }
                ],
                dataScopes: [
                    { id: 'self', name: '仅本人', description: '只能访问自己创建的数据' },
                    { id: 'department', name: '本部门', description: '可访问所在部门的数据' },
                    { id: 'department_sub', name: '本部门及下级', description: '可访问本部门及下级部门数据' },
                    { id: 'all', name: '全部', description: '可访问所有数据' }
                ],
                businessPermissions: [
                    { id: 'order_view', name: '订单查看', description: '查看订单信息', level: 'low', levelText: '低风险' },
                    { id: 'order_edit', name: '订单编辑', description: '编辑订单信息', level: 'medium', levelText: '中风险' },
                    { id: 'customer_view', name: '客户查看', description: '查看客户信息', level: 'low', levelText: '低风险' },
                    { id: 'customer_edit', name: '客户编辑', description: '编辑客户信息', level: 'medium', levelText: '中风险' },
                    { id: 'price_view', name: '价格查看', description: '查看产品价格', level: 'high', levelText: '高风险' },
                    { id: 'financial_view', name: '财务查看', description: '查看财务数据', level: 'high', levelText: '高风险' }
                ],
                testOperations: [
                    { id: 'read', name: '读取数据' },
                    { id: 'create', name: '创建数据' },
                    { id: 'update', name: '更新数据' },
                    { id: 'delete', name: '删除数据' },
                    { id: 'export', name: '导出数据' }
                ],

                // 计算属性
                get filteredRoles() {
                    return this.roles.filter(role => 
                        role.name.toLowerCase().includes(this.searchQuery.toLowerCase())
                    );
                },
                get filteredUsers() {
                    return this.users.filter(user => 
                        user.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
                        user.department.toLowerCase().includes(this.searchQuery.toLowerCase())
                    );
                },
                get permissionStats() {
                    if (!this.selectedTarget) return { dataScope: 0, businessCount: 0, riskLevel: '低' };
                    
                    const scopeNames = { 'self': '本人', 'department': '部门', 'department_sub': '部门+', 'all': '全部' };
                    const highRiskCount = this.selectedBusinessPermissions.filter(id => {
                        const perm = this.businessPermissions.find(p => p.id === id);
                        return perm && perm.level === 'high';
                    }).length;
                    
                    return {
                        dataScope: scopeNames[this.selectedDataScope] || '未设置',
                        businessCount: this.selectedBusinessPermissions.length,
                        riskLevel: highRiskCount > 0 ? '高' : this.selectedBusinessPermissions.length > 3 ? '中' : '低'
                    };
                },
                get permissionImpacts() {
                    return [
                        { id: 1, module: '销售订单', accessible: true, count: 245 },
                        { id: 2, module: '客户信息', accessible: true, count: 156 },
                        { id: 3, module: '产品数据', accessible: true, count: 89 },
                        { id: 4, module: '财务数据', accessible: this.selectedBusinessPermissions.includes('financial_view'), count: 23 },
                        { id: 5, module: '生产计划', accessible: false, count: 67 }
                    ];
                },

                // 初始化
                init() {
                    this.$nextTick(() => {
                        this.initChart();
                    });
                },

                // 方法
                selectTarget(target, type) {
                    this.selectedTarget = { ...target, type };
                    this.loadPermissionSettings();
                    this.$nextTick(() => {
                        this.updateChart();
                    });
                },

                loadPermissionSettings() {
                    // 模拟加载权限设置
                    if (this.selectedTarget.id === 'admin') {
                        this.selectedDataScope = 'all';
                        this.selectedBusinessPermissions = ['order_view', 'order_edit', 'customer_view', 'customer_edit', 'price_view', 'financial_view'];
                    } else if (this.selectedTarget.id === 'manager') {
                        this.selectedDataScope = 'department_sub';
                        this.selectedBusinessPermissions = ['order_view', 'order_edit', 'customer_view', 'customer_edit'];
                    } else {
                        this.selectedDataScope = 'department';
                        this.selectedBusinessPermissions = ['order_view', 'customer_view'];
                    }
                },

                toggleOrgNode(org) {
                    org.expanded = !org.expanded;
                },

                selectOrgNode(node) {
                    const index = this.selectedOrgNodes.indexOf(node.id);
                    if (index > -1) {
                        this.selectedOrgNodes.splice(index, 1);
                    } else {
                        this.selectedOrgNodes.push(node.id);
                    }
                },

                testPermissions() {
                    this.showTestPanel = true;
                },

                runPermissionTest() {
                    this.testRunning = true;
                    this.testResults = [];
                    
                    // 模拟测试
                    setTimeout(() => {
                        const testUser = this.users.find(u => u.id === this.testConfig.userId);
                        const results = this.testConfig.operations.map(opId => {
                            const operation = this.testOperations.find(op => op.id === opId);
                            const passed = Math.random() > 0.3; // 70% 通过率
                            
                            return {
                                id: Date.now() + Math.random(),
                                operation: operation.name,
                                passed: passed,
                                message: passed ? 
                                    `${testUser.name} 有权限执行 ${operation.name} 操作` : 
                                    `${testUser.name} 无权限执行 ${operation.name} 操作`,
                                details: passed ? 
                                    `符合 ${this.selectedDataScope} 数据范围权限` : 
                                    '超出用户数据访问范围'
                            };
                        });
                        
                        this.testResults = results;
                        this.testRunning = false;
                    }, 2000);
                },

                savePermissions() {
                    if (!this.selectedTarget) {
                        alert('请先选择角色或用户');
                        return;
                    }
                    
                    // 模拟保存
                    setTimeout(() => {
                        alert('权限配置已保存');
                    }, 500);
                },

                initChart() {
                    const ctx = document.getElementById('permissionChart');
                    if (ctx) {
                        this.chart = new Chart(ctx, {
                            type: 'doughnut',
                            data: {
                                labels: ['已授权', '未授权'],
                                datasets: [{
                                    data: [0, 100],
                                    backgroundColor: ['#52C41A', '#F0F0F0'],
                                    borderWidth: 0
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: {
                                        position: 'bottom'
                                    }
                                }
                            }
                        });
                    }
                },

                updateChart() {
                    if (this.chart) {
                        const total = this.businessPermissions.length;
                        const granted = this.selectedBusinessPermissions.length;
                        const denied = total - granted;
                        
                        this.chart.data.datasets[0].data = [granted, denied];
                        this.chart.update();
                    }
                }
            }
        }
    </script>
</body>
</html>