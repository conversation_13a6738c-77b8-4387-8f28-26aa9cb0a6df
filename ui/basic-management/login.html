<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - 玻璃深加工ERP系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1890FF',
                        success: '#52C41A',
                        warning: '#FAAD14',
                        error: '#F5222D',
                        'title-gray': '#262626',
                        'body-gray': '#595959',
                        'aux-gray': '#8C8C8C',
                        'disabled-gray': '#BFBFBF',
                        'border-gray': '#D9D9D9',
                        'bg-gray': '#FAFAFA'
                    },
                    fontFamily: {
                        'chinese': ['PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', sans-serif;
        }
        .glass-pattern {
            background: linear-gradient(135deg, rgba(24, 144, 255, 0.1) 0%, rgba(24, 144, 255, 0.05) 100%);
        }
        .login-form-shadow {
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        }
        .loading-spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #1890FF;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 font-chinese">
    <div x-data="loginApp()" class="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <!-- 登录表单容器 -->
        <div class="max-w-md w-full space-y-8">
            <!-- 顶部Logo和标题区域 -->
            <div class="text-center">
                <!-- Logo占位符 -->
                <div class="mx-auto h-20 w-20 bg-primary rounded-full flex items-center justify-center mb-6 glass-pattern">
                    <svg class="h-10 w-10 text-primary" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                    </svg>
                </div>
                <h2 class="text-3xl font-bold text-title-gray mb-2">玻璃深加工ERP系统</h2>
                <p class="text-aux-gray text-sm">Glass Deep Processing ERP System</p>
                <p class="text-aux-gray text-xs mt-1">Version 2.0</p>
            </div>

            <!-- 登录表单 -->
            <div class="bg-white rounded-lg login-form-shadow p-8">
                <form @submit.prevent="submitLogin" class="space-y-6">
                    <div>
                        <label for="username" class="block text-sm font-medium text-title-gray mb-2">
                            工号 <span class="text-error">*</span>
                        </label>
                        <input
                            id="username"
                            name="username"
                            type="text"
                            x-model="form.username"
                            :class="errors.username ? 'border-error focus:border-error focus:ring-error' : 'border-border-gray focus:border-primary focus:ring-primary'"
                            class="w-full px-3 py-2 border rounded-md shadow-sm placeholder-aux-gray focus:outline-none focus:ring-1 transition-colors"
                            placeholder="请输入工号"
                            autocomplete="username"
                            :disabled="loading"
                        >
                        <p x-show="errors.username" x-text="errors.username" class="mt-1 text-sm text-error"></p>
                    </div>

                    <div>
                        <label for="password" class="block text-sm font-medium text-title-gray mb-2">
                            密码 <span class="text-error">*</span>
                        </label>
                        <div class="relative">
                            <input
                                id="password"
                                name="password"
                                :type="showPassword ? 'text' : 'password'"
                                x-model="form.password"
                                :class="errors.password ? 'border-error focus:border-error focus:ring-error' : 'border-border-gray focus:border-primary focus:ring-primary'"
                                class="w-full px-3 py-2 pr-10 border rounded-md shadow-sm placeholder-aux-gray focus:outline-none focus:ring-1 transition-colors"
                                placeholder="请输入密码"
                                autocomplete="current-password"
                                :disabled="loading"
                            >
                            <button
                                type="button"
                                @click="showPassword = !showPassword"
                                class="absolute inset-y-0 right-0 pr-3 flex items-center"
                                :disabled="loading"
                            >
                                <svg x-show="!showPassword" class="h-5 w-5 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                </svg>
                                <svg x-show="showPassword" class="h-5 w-5 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L12 12m6.121-6.121A9.969 9.969 0 0121 12c0 .466-.046.925-.138 1.371m-7.887 2.75L21 21" />
                                </svg>
                            </button>
                        </div>
                        <p x-show="errors.password" x-text="errors.password" class="mt-1 text-sm text-error"></p>
                    </div>

                    <!-- 记住我和忘记密码 -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <input
                                id="remember-me"
                                name="remember-me"
                                type="checkbox"
                                x-model="form.rememberMe"
                                class="h-4 w-4 text-primary focus:ring-primary border-border-gray rounded"
                                :disabled="loading"
                            >
                            <label for="remember-me" class="ml-2 block text-sm text-body-gray">
                                记住我
                            </label>
                        </div>

                        <div>
                            <button 
                                type="button" 
                                @click="showForgotPassword = true"
                                class="text-sm text-primary hover:text-blue-500 transition-colors"
                                :disabled="loading"
                            >
                                忘记密码？
                            </button>
                        </div>
                    </div>

                    <!-- 登录按钮 -->
                    <div>
                        <button
                            type="submit"
                            :disabled="loading"
                            :class="loading ? 'bg-disabled-gray cursor-not-allowed' : 'bg-primary hover:bg-blue-600'"
                            class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
                        >
                            <span x-show="loading" class="loading-spinner mr-2"></span>
                            <span x-text="loading ? '登录中...' : '登录'"></span>
                        </button>
                    </div>

                    <!-- 错误提示 -->
                    <div x-show="generalError" class="p-4 bg-red-50 border border-red-200 rounded-md">
                        <div class="flex">
                            <svg class="h-5 w-5 text-error" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                            </svg>
                            <div class="ml-3">
                                <p class="text-sm text-error" x-text="generalError"></p>
                            </div>
                        </div>
                    </div>

                    <!-- 成功提示 -->
                    <div x-show="successMessage" class="p-4 bg-green-50 border border-green-200 rounded-md">
                        <div class="flex">
                            <svg class="h-5 w-5 text-success" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                            </svg>
                            <div class="ml-3">
                                <p class="text-sm text-success" x-text="successMessage"></p>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- 版权信息 -->
            <div class="text-center text-aux-gray text-xs">
                <p>&copy; 2025 玻璃深加工ERP系统. 保留所有权利.</p>
                <p>请使用现代浏览器以获得最佳体验</p>
            </div>
        </div>

        <!-- 忘记密码模态框 -->
        <div x-show="showForgotPassword" 
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
             @click="showForgotPassword = false">
            
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"
                 @click.stop>
                <div class="mt-3">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-title-gray">重置密码</h3>
                        <button @click="showForgotPassword = false" class="text-aux-gray hover:text-title-gray">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                    
                    <form @submit.prevent="submitForgotPassword" class="space-y-4">
                        <div>
                            <label for="reset-email" class="block text-sm font-medium text-title-gray mb-2">
                                工号或邮箱地址 <span class="text-error">*</span>
                            </label>
                            <input
                                id="reset-email"
                                type="text"
                                x-model="resetForm.identifier"
                                class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm placeholder-aux-gray focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                placeholder="请输入工号或邮箱地址"
                                :disabled="resetLoading"
                            >
                        </div>
                        
                        <div class="flex justify-end space-x-3 pt-4">
                            <button
                                type="button"
                                @click="showForgotPassword = false"
                                class="px-4 py-2 text-sm font-medium text-body-gray bg-white border border-border-gray rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                                :disabled="resetLoading"
                            >
                                取消
                            </button>
                            <button
                                type="submit"
                                :disabled="resetLoading"
                                :class="resetLoading ? 'bg-disabled-gray cursor-not-allowed' : 'bg-primary hover:bg-blue-600'"
                                class="px-4 py-2 text-sm font-medium text-white border border-transparent rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
                            >
                                <span x-show="resetLoading" class="loading-spinner mr-2"></span>
                                <span x-text="resetLoading ? '发送中...' : '发送重置链接'"></span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function loginApp() {
            return {
                // 表单数据
                form: {
                    username: '',
                    password: '',
                    rememberMe: false
                },
                resetForm: {
                    identifier: ''
                },
                
                // UI状态
                loading: false,
                resetLoading: false,
                showPassword: false,
                showForgotPassword: false,
                
                // 错误和成功消息
                errors: {},
                generalError: '',
                successMessage: '',

                // 验证表单
                validateForm() {
                    this.errors = {};
                    let isValid = true;

                    if (!this.form.username.trim()) {
                        this.errors.username = '请输入工号';
                        isValid = false;
                    }

                    if (!this.form.password.trim()) {
                        this.errors.password = '请输入密码';
                        isValid = false;
                    } else if (this.form.password.length < 6) {
                        this.errors.password = '密码长度不能少于6位';
                        isValid = false;
                    }

                    return isValid;
                },

                // 提交登录
                async submitLogin() {
                    this.generalError = '';
                    this.successMessage = '';
                    
                    if (!this.validateForm()) {
                        return;
                    }

                    this.loading = true;

                    try {
                        // 模拟API调用
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        
                        // 模拟登录验证
                        if (this.form.username === 'admin' && this.form.password === '123456') {
                            this.successMessage = '登录成功，正在跳转...';
                            
                            // 模拟跳转延迟
                            setTimeout(() => {
                                alert('登录成功！在实际系统中，这里会跳转到主页面。');
                            }, 1000);
                        } else {
                            this.generalError = '工号或密码错误，请重新输入';
                        }
                    } catch (error) {
                        this.generalError = '登录失败，请检查网络连接后重试';
                    } finally {
                        this.loading = false;
                    }
                },

                // 提交忘记密码
                async submitForgotPassword() {
                    if (!this.resetForm.identifier.trim()) {
                        alert('请输入工号或邮箱地址');
                        return;
                    }

                    this.resetLoading = true;

                    try {
                        // 模拟API调用
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        
                        alert('重置链接已发送到您的邮箱，请查收');
                        this.showForgotPassword = false;
                        this.resetForm.identifier = '';
                    } catch (error) {
                        alert('发送失败，请稍后重试');
                    } finally {
                        this.resetLoading = false;
                    }
                }
            }
        }
    </script>
</body>
</html>