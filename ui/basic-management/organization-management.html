<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>组织架构管理 - 玻璃深加工ERP系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1890FF',
                        success: '#52C41A',
                        warning: '#FAAD14',
                        error: '#F5222D',
                        'title-gray': '#262626',
                        'body-gray': '#595959',
                        'aux-gray': '#8C8C8C',
                        'disabled-gray': '#BFBFBF',
                        'border-gray': '#D9D9D9',
                        'bg-gray': '#FAFAFA'
                    },
                    fontFamily: {
                        'chinese': ['PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', sans-serif;
        }
        .tree-node {
            position: relative;
        }
        .tree-line::before {
            content: '';
            position: absolute;
            left: -20px;
            top: 24px;
            width: 20px;
            height: 1px;
            background-color: #d9d9d9;
        }
        .tree-line::after {
            content: '';
            position: absolute;
            left: -20px;
            top: -8px;
            width: 1px;
            height: 32px;
            background-color: #d9d9d9;
        }
        .tree-line.last-child::after {
            height: 32px;
        }
        .dragging {
            opacity: 0.5;
            transform: rotate(5deg);
        }
        .drop-zone {
            border: 2px dashed #1890FF;
            background-color: rgba(24, 144, 255, 0.1);
        }
    </style>
</head>
<body class="bg-bg-gray font-chinese">
    <div x-data="organizationApp()" class="min-h-screen">
        <!-- 顶部导航栏 -->
        <header class="bg-white shadow-sm border-b border-border-gray h-16">
            <div class="flex items-center justify-between px-6 h-full">
                <div class="flex items-center space-x-4">
                    <h1 class="text-xl font-medium text-title-gray">组织架构管理</h1>
                    <span class="text-aux-gray text-sm">/ 基础管理</span>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="text-aux-gray hover:text-title-gray">
                        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5-5-5h5v-12"/>
                        </svg>
                    </button>
                    <span class="text-aux-gray">admin</span>
                </div>
            </div>
        </header>

        <div class="flex h-[calc(100vh-64px)]">
            <!-- 侧边栏 -->
            <aside class="w-60 bg-white shadow-sm border-r border-border-gray">
                <div class="p-4">
                    <div class="mb-4">
                        <button 
                            @click="showAddOrgModal = true; editingOrg = null"
                            class="w-full flex items-center justify-center px-4 py-2 bg-primary text-white text-sm font-medium rounded-md hover:bg-blue-600 transition-colors"
                        >
                            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                            </svg>
                            新增组织
                        </button>
                    </div>

                    <!-- 组织树 -->
                    <div class="space-y-1">
                        <div class="text-sm font-medium text-aux-gray mb-2">组织架构</div>
                        <template x-for="org in organizationTree" :key="org.id">
                            <div class="tree-node">
                                <div 
                                    @click="selectOrganization(org)"
                                    :class="selectedOrg?.id === org.id ? 'bg-primary text-white' : 'hover:bg-gray-50'"
                                    class="flex items-center px-3 py-2 text-sm rounded-md cursor-pointer transition-colors"
                                    draggable="true"
                                    @dragstart="dragStart($event, org)"
                                    @dragover.prevent
                                    @drop="drop($event, org)"
                                >
                                    <button 
                                        @click.stop="toggleExpand(org)"
                                        class="mr-2 text-aux-gray hover:text-title-gray"
                                        x-show="org.children && org.children.length > 0"
                                    >
                                        <svg x-show="!org.expanded" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                        </svg>
                                        <svg x-show="org.expanded" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                                        </svg>
                                    </button>
                                    
                                    <svg class="h-4 w-4 mr-2 text-current" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                                    </svg>
                                    
                                    <span class="flex-1" x-text="org.name"></span>
                                    
                                    <span 
                                        class="text-xs px-2 py-0.5 rounded-full"
                                        :class="org.type === 'company' ? 'bg-blue-100 text-blue-800' : 
                                               org.type === 'department' ? 'bg-green-100 text-green-800' : 
                                               'bg-gray-100 text-gray-800'"
                                        x-text="getOrgTypeLabel(org.type)"
                                    ></span>
                                </div>
                                
                                <!-- 子组织 -->
                                <div x-show="org.expanded && org.children" class="ml-6 mt-1 space-y-1">
                                    <template x-for="child in org.children" :key="child.id">
                                        <div 
                                            @click="selectOrganization(child)"
                                            :class="selectedOrg?.id === child.id ? 'bg-primary text-white' : 'hover:bg-gray-50'"
                                            class="tree-line flex items-center px-3 py-2 text-sm rounded-md cursor-pointer transition-colors"
                                            draggable="true"
                                            @dragstart="dragStart($event, child)"
                                            @dragover.prevent
                                            @drop="drop($event, child)"
                                        >
                                            <svg class="h-4 w-4 mr-2 text-current" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                            </svg>
                                            <span class="flex-1" x-text="child.name"></span>
                                            <span 
                                                class="text-xs px-2 py-0.5 rounded-full"
                                                :class="child.type === 'company' ? 'bg-blue-100 text-blue-800' : 
                                                       child.type === 'department' ? 'bg-green-100 text-green-800' : 
                                                       'bg-gray-100 text-gray-800'"
                                                x-text="getOrgTypeLabel(child.type)"
                                            ></span>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </aside>

            <!-- 主内容区域 -->
            <main class="flex-1 overflow-hidden">
                <div class="h-full flex flex-col">
                    <!-- 页面标题区 -->
                    <div class="bg-white border-b border-border-gray px-6 py-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <h2 class="text-lg font-medium text-title-gray">
                                    <span x-text="selectedOrg ? selectedOrg.name : '请选择组织'"></span>
                                </h2>
                                <p class="text-sm text-aux-gray mt-1" x-show="selectedOrg">
                                    组织编码：<span x-text="selectedOrg?.code"></span>
                                </p>
                            </div>
                            <div class="flex space-x-3" x-show="selectedOrg">
                                <button 
                                    @click="showAddOrgModal = true; editingOrg = null; newOrgForm.parentId = selectedOrg.id"
                                    class="px-4 py-2 bg-primary text-white text-sm font-medium rounded-md hover:bg-blue-600 transition-colors"
                                >
                                    新增子部门
                                </button>
                                <button 
                                    @click="editOrganization(selectedOrg)"
                                    class="px-4 py-2 bg-white border border-border-gray text-body-gray text-sm font-medium rounded-md hover:bg-gray-50 transition-colors"
                                >
                                    编辑
                                </button>
                                <button 
                                    @click="deleteOrganization(selectedOrg)"
                                    class="px-4 py-2 bg-white border border-error text-error text-sm font-medium rounded-md hover:bg-red-50 transition-colors"
                                    x-show="selectedOrg && selectedOrg.id !== 1"
                                >
                                    删除
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 内容区域 -->
                    <div class="flex-1 p-6 overflow-auto">
                        <div x-show="!selectedOrg" class="text-center py-16">
                            <svg class="mx-auto h-16 w-16 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                            </svg>
                            <p class="mt-4 text-aux-gray">请从左侧选择一个组织查看详情</p>
                        </div>

                        <div x-show="selectedOrg" class="space-y-6">
                            <!-- 基本信息卡片 -->
                            <div class="bg-white rounded-lg shadow-sm border border-border-gray p-6">
                                <h3 class="text-lg font-medium text-title-gray mb-4">基本信息</h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-body-gray mb-2">组织名称</label>
                                        <p class="text-title-gray" x-text="selectedOrg?.name"></p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-body-gray mb-2">组织编码</label>
                                        <p class="text-title-gray" x-text="selectedOrg?.code"></p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-body-gray mb-2">组织类型</label>
                                        <p class="text-title-gray" x-text="getOrgTypeLabel(selectedOrg?.type)"></p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-body-gray mb-2">负责人</label>
                                        <p class="text-title-gray" x-text="selectedOrg?.manager || '未设置'"></p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-body-gray mb-2">联系电话</label>
                                        <p class="text-title-gray" x-text="selectedOrg?.phone || '未设置'"></p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-body-gray mb-2">创建时间</label>
                                        <p class="text-title-gray" x-text="selectedOrg?.createdAt"></p>
                                    </div>
                                </div>
                                <div class="mt-6" x-show="selectedOrg?.description">
                                    <label class="block text-sm font-medium text-body-gray mb-2">组织描述</label>
                                    <p class="text-title-gray" x-text="selectedOrg?.description"></p>
                                </div>
                            </div>

                            <!-- 人员统计卡片 -->
                            <div class="bg-white rounded-lg shadow-sm border border-border-gray p-6">
                                <h3 class="text-lg font-medium text-title-gray mb-4">人员统计</h3>
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                    <div class="text-center p-4 bg-blue-50 rounded-lg">
                                        <div class="text-2xl font-bold text-primary">15</div>
                                        <div class="text-sm text-aux-gray">总人数</div>
                                    </div>
                                    <div class="text-center p-4 bg-green-50 rounded-lg">
                                        <div class="text-2xl font-bold text-success">12</div>
                                        <div class="text-sm text-aux-gray">在职人数</div>
                                    </div>
                                    <div class="text-center p-4 bg-yellow-50 rounded-lg">
                                        <div class="text-2xl font-bold text-warning">2</div>
                                        <div class="text-sm text-aux-gray">请假人数</div>
                                    </div>
                                    <div class="text-center p-4 bg-red-50 rounded-lg">
                                        <div class="text-2xl font-bold text-error">1</div>
                                        <div class="text-sm text-aux-gray">离职人数</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 子部门列表 -->
                            <div class="bg-white rounded-lg shadow-sm border border-border-gray p-6" x-show="selectedOrg?.children && selectedOrg.children.length > 0">
                                <h3 class="text-lg font-medium text-title-gray mb-4">子部门</h3>
                                <div class="space-y-3">
                                    <template x-for="child in selectedOrg?.children" :key="child.id">
                                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                            <div class="flex items-center space-x-3">
                                                <svg class="h-5 w-5 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                                </svg>
                                                <div>
                                                    <div class="font-medium text-title-gray" x-text="child.name"></div>
                                                    <div class="text-sm text-aux-gray" x-text="'负责人: ' + (child.manager || '未设置')"></div>
                                                </div>
                                            </div>
                                            <button 
                                                @click="selectOrganization(child)"
                                                class="text-primary hover:text-blue-600 text-sm font-medium"
                                            >
                                                查看详情
                                            </button>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <!-- 新增/编辑组织模态框 -->
        <div x-show="showAddOrgModal" 
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
             @click="showAddOrgModal = false">
            
            <div class="relative top-20 mx-auto p-5 border w-[600px] shadow-lg rounded-md bg-white"
                 @click.stop>
                <div class="mt-3">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-medium text-title-gray" x-text="editingOrg ? '编辑组织' : '新增组织'"></h3>
                        <button @click="showAddOrgModal = false" class="text-aux-gray hover:text-title-gray">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                    
                    <form @submit.prevent="submitOrgForm" class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="org-name" class="block text-sm font-medium text-title-gray mb-2">
                                    组织名称 <span class="text-error">*</span>
                                </label>
                                <input
                                    id="org-name"
                                    type="text"
                                    x-model="newOrgForm.name"
                                    class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm placeholder-aux-gray focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                    placeholder="请输入组织名称"
                                    required
                                >
                            </div>
                            
                            <div>
                                <label for="org-code" class="block text-sm font-medium text-title-gray mb-2">
                                    组织编码 <span class="text-error">*</span>
                                </label>
                                <input
                                    id="org-code"
                                    type="text"
                                    x-model="newOrgForm.code"
                                    class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm placeholder-aux-gray focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                    placeholder="请输入组织编码"
                                    required
                                >
                            </div>
                            
                            <div>
                                <label for="org-type" class="block text-sm font-medium text-title-gray mb-2">
                                    组织类型 <span class="text-error">*</span>
                                </label>
                                <select
                                    id="org-type"
                                    x-model="newOrgForm.type"
                                    class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                    required
                                >
                                    <option value="">请选择组织类型</option>
                                    <option value="company">公司</option>
                                    <option value="department">部门</option>
                                    <option value="group">小组</option>
                                </select>
                            </div>
                            
                            <div>
                                <label for="org-manager" class="block text-sm font-medium text-title-gray mb-2">
                                    负责人
                                </label>
                                <input
                                    id="org-manager"
                                    type="text"
                                    x-model="newOrgForm.manager"
                                    class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm placeholder-aux-gray focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                    placeholder="请输入负责人姓名"
                                >
                            </div>
                            
                            <div>
                                <label for="org-phone" class="block text-sm font-medium text-title-gray mb-2">
                                    联系电话
                                </label>
                                <input
                                    id="org-phone"
                                    type="tel"
                                    x-model="newOrgForm.phone"
                                    class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm placeholder-aux-gray focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                    placeholder="请输入联系电话"
                                >
                            </div>
                            
                            <div>
                                <label for="org-parent" class="block text-sm font-medium text-title-gray mb-2">
                                    上级组织
                                </label>
                                <select
                                    id="org-parent"
                                    x-model="newOrgForm.parentId"
                                    class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                >
                                    <option value="">无上级组织</option>
                                    <template x-for="org in flatOrgList" :key="org.id">
                                        <option :value="org.id" x-text="org.name" :disabled="editingOrg && org.id === editingOrg.id"></option>
                                    </template>
                                </select>
                            </div>
                        </div>
                        
                        <div>
                            <label for="org-description" class="block text-sm font-medium text-title-gray mb-2">
                                组织描述
                            </label>
                            <textarea
                                id="org-description"
                                x-model="newOrgForm.description"
                                rows="3"
                                class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm placeholder-aux-gray focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                placeholder="请输入组织描述"
                            ></textarea>
                        </div>
                        
                        <div class="flex justify-end space-x-3 pt-4 border-t border-border-gray">
                            <button
                                type="button"
                                @click="showAddOrgModal = false"
                                class="px-4 py-2 text-sm font-medium text-body-gray bg-white border border-border-gray rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                            >
                                取消
                            </button>
                            <button
                                type="submit"
                                class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
                            >
                                <span x-text="editingOrg ? '更新' : '创建'"></span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function organizationApp() {
            return {
                // 组织数据
                organizationTree: [
                    {
                        id: 1,
                        name: '玻璃深加工集团',
                        code: 'GROUP001',
                        type: 'company',
                        manager: '张总',
                        phone: '021-12345678',
                        description: '专业从事建筑玻璃、家具玻璃、装饰玻璃等深加工业务',
                        createdAt: '2023-01-15',
                        expanded: true,
                        children: [
                            {
                                id: 2,
                                name: '生产部',
                                code: 'PROD001',
                                type: 'department',
                                manager: '李经理',
                                phone: '021-12345679',
                                description: '负责产品生产和质量控制',
                                createdAt: '2023-01-16',
                                children: [
                                    {
                                        id: 5,
                                        name: '切割车间',
                                        code: 'CUT001',
                                        type: 'group',
                                        manager: '王主任',
                                        phone: '021-12345682',
                                        createdAt: '2023-01-20'
                                    },
                                    {
                                        id: 6,
                                        name: '钢化车间',
                                        code: 'TEMP001',
                                        type: 'group',
                                        manager: '刘主任',
                                        phone: '021-12345683',
                                        createdAt: '2023-01-21'
                                    }
                                ]
                            },
                            {
                                id: 3,
                                name: '销售部',
                                code: 'SALES001',
                                type: 'department',
                                manager: '赵经理',
                                phone: '021-12345680',
                                description: '负责客户开发和订单管理',
                                createdAt: '2023-01-17'
                            },
                            {
                                id: 4,
                                name: '技术部',
                                code: 'TECH001',
                                type: 'department',
                                manager: '孙经理',
                                phone: '021-12345681',
                                description: '负责产品设计和工艺优化',
                                createdAt: '2023-01-18'
                            }
                        ]
                    }
                ],
                
                // UI状态
                selectedOrg: null,
                showAddOrgModal: false,
                editingOrg: null,
                draggedOrg: null,
                
                // 表单数据
                newOrgForm: {
                    name: '',
                    code: '',
                    type: '',
                    manager: '',
                    phone: '',
                    description: '',
                    parentId: null
                },

                // 计算属性：扁平化组织列表
                get flatOrgList() {
                    const flatten = (orgs, result = []) => {
                        orgs.forEach(org => {
                            result.push(org);
                            if (org.children) {
                                flatten(org.children, result);
                            }
                        });
                        return result;
                    };
                    return flatten(this.organizationTree);
                },

                // 选择组织
                selectOrganization(org) {
                    this.selectedOrg = org;
                },

                // 切换展开状态
                toggleExpand(org) {
                    org.expanded = !org.expanded;
                },

                // 获取组织类型标签
                getOrgTypeLabel(type) {
                    const labels = {
                        'company': '公司',
                        'department': '部门',
                        'group': '小组'
                    };
                    return labels[type] || type;
                },

                // 编辑组织
                editOrganization(org) {
                    this.editingOrg = org;
                    this.newOrgForm = {
                        name: org.name,
                        code: org.code,
                        type: org.type,
                        manager: org.manager || '',
                        phone: org.phone || '',
                        description: org.description || '',
                        parentId: this.getParentId(org)
                    };
                    this.showAddOrgModal = true;
                },

                // 获取父级ID
                getParentId(targetOrg) {
                    const findParent = (orgs, target) => {
                        for (let org of orgs) {
                            if (org.children && org.children.find(child => child.id === target.id)) {
                                return org.id;
                            }
                            if (org.children) {
                                const parentId = findParent(org.children, target);
                                if (parentId) return parentId;
                            }
                        }
                        return null;
                    };
                    return findParent(this.organizationTree, targetOrg);
                },

                // 提交表单
                async submitOrgForm() {
                    try {
                        if (this.editingOrg) {
                            // 更新组织
                            Object.assign(this.editingOrg, {
                                name: this.newOrgForm.name,
                                code: this.newOrgForm.code,
                                type: this.newOrgForm.type,
                                manager: this.newOrgForm.manager,
                                phone: this.newOrgForm.phone,
                                description: this.newOrgForm.description
                            });
                            
                            // 处理父级组织变更
                            if (this.newOrgForm.parentId !== this.getParentId(this.editingOrg)) {
                                this.moveOrganization(this.editingOrg, this.newOrgForm.parentId);
                            }
                        } else {
                            // 创建新组织
                            const newOrg = {
                                id: Date.now(),
                                name: this.newOrgForm.name,
                                code: this.newOrgForm.code,
                                type: this.newOrgForm.type,
                                manager: this.newOrgForm.manager,
                                phone: this.newOrgForm.phone,
                                description: this.newOrgForm.description,
                                createdAt: new Date().toISOString().split('T')[0],
                                children: []
                            };
                            
                            this.addOrganization(newOrg, this.newOrgForm.parentId);
                        }
                        
                        this.showAddOrgModal = false;
                        this.resetForm();
                        alert(this.editingOrg ? '组织更新成功' : '组织创建成功');
                    } catch (error) {
                        alert('操作失败：' + error.message);
                    }
                },

                // 添加组织
                addOrganization(newOrg, parentId) {
                    if (!parentId) {
                        this.organizationTree.push(newOrg);
                    } else {
                        const parent = this.findOrgById(parentId);
                        if (parent) {
                            if (!parent.children) {
                                parent.children = [];
                            }
                            parent.children.push(newOrg);
                            parent.expanded = true;
                        }
                    }
                },

                // 移动组织
                moveOrganization(org, newParentId) {
                    // 从原位置删除
                    this.removeOrgFromTree(org);
                    
                    // 添加到新位置
                    this.addOrganization(org, newParentId);
                },

                // 从树中删除组织
                removeOrgFromTree(targetOrg) {
                    const removeFromArray = (array) => {
                        const index = array.findIndex(org => org.id === targetOrg.id);
                        if (index !== -1) {
                            array.splice(index, 1);
                            return true;
                        }
                        return false;
                    };

                    const removeRecursive = (orgs) => {
                        if (removeFromArray(orgs)) return true;
                        
                        for (let org of orgs) {
                            if (org.children && removeRecursive(org.children)) {
                                return true;
                            }
                        }
                        return false;
                    };

                    removeRecursive(this.organizationTree);
                },

                // 根据ID查找组织
                findOrgById(id) {
                    const search = (orgs) => {
                        for (let org of orgs) {
                            if (org.id === id) return org;
                            if (org.children) {
                                const found = search(org.children);
                                if (found) return found;
                            }
                        }
                        return null;
                    };
                    return search(this.organizationTree);
                },

                // 删除组织
                deleteOrganization(org) {
                    if (confirm(`确定要删除组织"${org.name}"吗？删除后将无法恢复。`)) {
                        this.removeOrgFromTree(org);
                        if (this.selectedOrg && this.selectedOrg.id === org.id) {
                            this.selectedOrg = null;
                        }
                        alert('组织删除成功');
                    }
                },

                // 重置表单
                resetForm() {
                    this.newOrgForm = {
                        name: '',
                        code: '',
                        type: '',
                        manager: '',
                        phone: '',
                        description: '',
                        parentId: null
                    };
                    this.editingOrg = null;
                },

                // 拖拽开始
                dragStart(event, org) {
                    this.draggedOrg = org;
                    event.dataTransfer.effectAllowed = 'move';
                    event.target.classList.add('dragging');
                },

                // 拖拽结束
                dragEnd(event) {
                    event.target.classList.remove('dragging');
                    this.draggedOrg = null;
                },

                // 拖拽放置
                drop(event, targetOrg) {
                    event.preventDefault();
                    
                    if (!this.draggedOrg || this.draggedOrg.id === targetOrg.id) {
                        return;
                    }

                    // 检查是否拖拽到自己的子节点
                    if (this.isDescendant(this.draggedOrg, targetOrg)) {
                        alert('不能将组织移动到自己的子组织下');
                        return;
                    }

                    if (confirm(`确定要将"${this.draggedOrg.name}"移动到"${targetOrg.name}"下吗？`)) {
                        this.moveOrganization(this.draggedOrg, targetOrg.id);
                        alert('组织移动成功');
                    }
                },

                // 检查是否为子孙节点
                isDescendant(ancestor, descendant) {
                    const check = (org) => {
                        if (!org.children) return false;
                        return org.children.some(child => 
                            child.id === descendant.id || check(child)
                        );
                    };
                    return check(ancestor);
                },

                // 初始化
                init() {
                    // 默认选择第一个组织
                    if (this.organizationTree.length > 0) {
                        this.selectedOrg = this.organizationTree[0];
                    }
                }
            }
        }
    </script>
</body>
</html>