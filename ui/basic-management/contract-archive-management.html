<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合同档案管理 - 玻璃深加工ERP系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1890FF',
                        success: '#52C41A',
                        warning: '#FAAD14',
                        error: '#F5222D',
                        'title-gray': '#262626',
                        'body-gray': '#595959',
                        'aux-gray': '#8C8C8C',
                        'disabled-gray': '#BFBFBF',
                        'border-gray': '#D9D9D9',
                        'bg-gray': '#FAFAFA'
                    },
                    fontFamily: {
                        'chinese': ['PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', sans-serif;
        }
        .table-row:hover {
            background-color: #f8fafc;
        }
        .contract-status-draft { background-color: #fef3c7; color: #92400e; }
        .contract-status-reviewing { background-color: #dbeafe; color: #1e40af; }
        .contract-status-approved { background-color: #d1fae5; color: #065f46; }
        .contract-status-rejected { background-color: #fee2e2; color: #991b1b; }
        .contract-status-executing { background-color: #e0e7ff; color: #3730a3; }
        .contract-status-completed { background-color: #f3f4f6; color: #374151; }
        .contract-status-terminated { background-color: #fecaca; color: #7f1d1d; }
        
        .workflow-step {
            position: relative;
        }
        .workflow-step:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 24px;
            left: 12px;
            width: 2px;
            height: calc(100% - 24px);
            background-color: #e5e7eb;
        }
        .workflow-step.active:not(:last-child)::after {
            background-color: #10b981;
        }
        
        .document-preview {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .milestone-progress {
            background: linear-gradient(90deg, #10b981 0%, #059669 100%);
        }
    </style>
</head>
<body class="bg-bg-gray font-chinese">
    <div x-data="contractArchiveApp()" class="min-h-screen">
        <!-- 顶部导航栏 -->
        <header class="bg-white shadow-sm border-b border-border-gray h-16">
            <div class="flex items-center justify-between px-6 h-full">
                <div class="flex items-center space-x-4">
                    <h1 class="text-xl font-medium text-title-gray">合同档案管理</h1>
                    <div class="text-sm text-aux-gray">
                        玻璃深加工ERP系统 / 基础管理 / 合同档案管理
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button @click="showTemplateModal = true" 
                            class="px-4 py-2 bg-warning text-white rounded-md hover:bg-yellow-600 transition-colors flex items-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span>合同模板</span>
                    </button>
                    <button @click="openContractModal()" 
                            class="px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600 transition-colors flex items-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        <span>新建合同</span>
                    </button>
                </div>
            </div>
        </header>

        <div class="flex">
            <!-- 左侧边栏 -->
            <aside class="w-60 bg-white border-r border-border-gray min-h-screen">
                <div class="p-4">
                    <!-- 合同状态筛选 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-title-gray mb-2">合同状态</label>
                        <div class="space-y-2">
                            <button @click="selectedStatus = ''" 
                                    class="w-full text-left px-3 py-2 rounded-md hover:bg-blue-50 transition-colors"
                                    :class="selectedStatus === '' ? 'bg-primary text-white' : 'text-body-gray'">
                                全部状态 (<span x-text="contracts.length"></span>)
                            </button>
                            <template x-for="status in contractStatuses" :key="status.id">
                                <button @click="selectedStatus = status.id" 
                                        class="w-full text-left px-3 py-2 rounded-md hover:bg-blue-50 transition-colors flex items-center justify-between"
                                        :class="selectedStatus === status.id ? 'bg-primary text-white' : 'text-body-gray'">
                                    <span x-text="status.name"></span>
                                    <span x-text="getContractCountByStatus(status.id)" class="text-xs opacity-75"></span>
                                </button>
                            </template>
                        </div>
                    </div>

                    <!-- 合同类型筛选 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-title-gray mb-2">合同类型</label>
                        <div class="space-y-2">
                            <template x-for="type in contractTypes" :key="type.id">
                                <label class="flex items-center">
                                    <input type="checkbox" x-model="selectedTypes" :value="type.id" 
                                           class="w-4 h-4 text-primary border-border-gray rounded focus:ring-primary">
                                    <span x-text="type.name" class="ml-2 text-sm text-body-gray"></span>
                                </label>
                            </template>
                        </div>
                    </div>

                    <!-- 时间范围筛选 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-title-gray mb-2">时间范围</label>
                        <div class="space-y-2">
                            <div>
                                <label class="block text-xs text-aux-gray mb-1">开始日期</label>
                                <input type="date" x-model="dateRange.start" 
                                       class="w-full px-2 py-1 border border-border-gray rounded text-sm focus:outline-none focus:ring-1 focus:ring-primary">
                            </div>
                            <div>
                                <label class="block text-xs text-aux-gray mb-1">结束日期</label>
                                <input type="date" x-model="dateRange.end" 
                                       class="w-full px-2 py-1 border border-border-gray rounded text-sm focus:outline-none focus:ring-1 focus:ring-primary">
                            </div>
                        </div>
                    </div>

                    <!-- 合同金额筛选 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-title-gray mb-2">合同金额</label>
                        <div class="space-y-2">
                            <template x-for="range in amountRanges" :key="range.id">
                                <label class="flex items-center">
                                    <input type="radio" x-model="selectedAmountRange" :value="range.id" 
                                           class="w-4 h-4 text-primary border-border-gray focus:ring-primary">
                                    <span x-text="range.name" class="ml-2 text-sm text-body-gray"></span>
                                </label>
                            </template>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- 主内容区域 -->
            <main class="flex-1 p-6">
                <!-- 统计卡片 -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div class="bg-white p-4 rounded-lg shadow-sm border border-border-gray">
                        <div class="flex items-center">
                            <div class="bg-primary w-12 h-12 rounded-lg flex items-center justify-center text-white mr-3">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            <div>
                                <div class="text-2xl font-bold text-title-gray" x-text="filteredContracts.length"></div>
                                <div class="text-sm text-aux-gray">总合同数</div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow-sm border border-border-gray">
                        <div class="flex items-center">
                            <div class="bg-success w-12 h-12 rounded-lg flex items-center justify-center text-white mr-3">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <div class="text-2xl font-bold text-title-gray" x-text="getContractCountByStatus('executing')"></div>
                                <div class="text-sm text-aux-gray">执行中</div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow-sm border border-border-gray">
                        <div class="flex items-center">
                            <div class="bg-warning w-12 h-12 rounded-lg flex items-center justify-center text-white mr-3">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                            </div>
                            <div>
                                <div class="text-2xl font-bold text-title-gray" x-text="formatCurrency(getTotalAmount())"></div>
                                <div class="text-sm text-aux-gray">合同总额</div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow-sm border border-border-gray">
                        <div class="flex items-center">
                            <div class="bg-error w-12 h-12 rounded-lg flex items-center justify-center text-white mr-3">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                            </div>
                            <div>
                                <div class="text-2xl font-bold text-title-gray" x-text="getExpiredContracts()"></div>
                                <div class="text-sm text-aux-gray">即将到期</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="bg-white p-4 rounded-lg shadow-sm border border-border-gray mb-6">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                        <div class="flex-1 max-w-md">
                            <div class="relative">
                                <input type="text" x-model="searchQuery" placeholder="搜索合同编号、客户名称或项目名称..." 
                                       class="w-full pl-10 pr-4 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                <svg class="w-5 h-5 absolute left-3 top-2.5 text-aux-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="flex items-center space-x-4">
                            <select x-model="sortBy" class="px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                <option value="signDate">按签约日期排序</option>
                                <option value="contractNo">按合同编号排序</option>
                                <option value="customerName">按客户名称排序</option>
                                <option value="amount">按合同金额排序</option>
                                <option value="status">按状态排序</option>
                            </select>
                            <button @click="exportContracts" 
                                    class="px-4 py-2 border border-border-gray rounded-md hover:bg-bg-gray transition-colors flex items-center space-x-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <span>导出</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 合同列表 -->
                <div class="bg-white rounded-lg shadow-sm border border-border-gray">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-bg-gray">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">合同信息</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">客户信息</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">合同金额</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">关键日期</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-border-gray">
                                <template x-for="contract in paginatedContracts" :key="contract.id">
                                    <tr class="table-row">
                                        <td class="px-6 py-4">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 w-10 h-10 document-preview rounded-lg text-white font-medium text-sm flex items-center justify-center">
                                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                                                    </svg>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-title-gray" x-text="contract.contractNo"></div>
                                                    <div class="text-sm text-aux-gray" x-text="contract.projectName"></div>
                                                    <div class="flex items-center space-x-2 mt-1">
                                                        <span class="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded" 
                                                              x-text="getContractTypeName(contract.type)"></span>
                                                        <span x-show="contract.isUrgent" class="text-xs px-2 py-1 bg-red-100 text-red-800 rounded">紧急</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="text-sm font-medium text-title-gray" x-text="contract.customerName"></div>
                                            <div class="text-sm text-aux-gray" x-text="contract.contactPerson"></div>
                                            <div class="text-sm text-aux-gray" x-text="contract.contactPhone"></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-title-gray" x-text="formatCurrency(contract.totalAmount)"></div>
                                            <div class="text-sm text-aux-gray">
                                                已收: <span x-text="formatCurrency(contract.paidAmount)" class="text-success"></span>
                                            </div>
                                            <div class="text-sm text-aux-gray">
                                                余额: <span x-text="formatCurrency(contract.totalAmount - contract.paidAmount)" 
                                                          :class="(contract.totalAmount - contract.paidAmount) > 0 ? 'text-warning' : 'text-success'"></span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                                  :class="`contract-status-${contract.status}`"
                                                  x-text="getContractStatusName(contract.status)"></span>
                                            <div x-show="contract.progress" class="mt-2">
                                                <div class="w-full bg-gray-200 rounded-full h-1.5">
                                                    <div class="milestone-progress h-1.5 rounded-full transition-all duration-300" 
                                                         :style="`width: ${contract.progress}%`"></div>
                                                </div>
                                                <div class="text-xs text-aux-gray mt-1" x-text="`${contract.progress}% 完成`"></div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-body-gray">
                                            <div class="space-y-1">
                                                <div>签约: <span x-text="contract.signDate"></span></div>
                                                <div>开始: <span x-text="contract.startDate"></span></div>
                                                <div>结束: <span x-text="contract.endDate"></span></div>
                                                <div x-show="isContractExpiringSoon(contract)" class="text-error text-xs">
                                                    ⚠️ 即将到期
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex items-center space-x-2">
                                                <button @click="viewContract(contract)" 
                                                        class="text-primary hover:text-blue-600">查看</button>
                                                <button @click="editContract(contract)" 
                                                        :disabled="contract.status === 'completed' || contract.status === 'terminated'"
                                                        class="text-warning hover:text-yellow-600 disabled:text-disabled-gray disabled:cursor-not-allowed">编辑</button>
                                                <button @click="showApprovalModal(contract)" 
                                                        x-show="contract.status === 'draft' || contract.status === 'reviewing'"
                                                        class="text-success hover:text-green-600">审批</button>
                                                <button @click="showVersionHistory(contract)" 
                                                        class="text-aux-gray hover:text-body-gray">版本</button>
                                            </div>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="px-6 py-3 border-t border-border-gray">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-aux-gray">
                                显示 <span x-text="(currentPage - 1) * pageSize + 1"></span> 到 
                                <span x-text="Math.min(currentPage * pageSize, filteredContracts.length)"></span> 条，
                                共 <span x-text="filteredContracts.length"></span> 条记录
                            </div>
                            <div class="flex items-center space-x-2">
                                <button @click="currentPage = Math.max(1, currentPage - 1)" 
                                        :disabled="currentPage === 1"
                                        class="px-3 py-1 border border-border-gray rounded text-sm hover:bg-bg-gray disabled:opacity-50 disabled:cursor-not-allowed">
                                    上一页
                                </button>
                                <template x-for="page in visiblePages" :key="page">
                                    <button @click="currentPage = page" 
                                            class="px-3 py-1 border rounded text-sm"
                                            :class="currentPage === page ? 'border-primary bg-primary text-white' : 'border-border-gray hover:bg-bg-gray'"
                                            x-text="page"></button>
                                </template>
                                <button @click="currentPage = Math.min(totalPages, currentPage + 1)" 
                                        :disabled="currentPage === totalPages"
                                        class="px-3 py-1 border border-border-gray rounded text-sm hover:bg-bg-gray disabled:opacity-50 disabled:cursor-not-allowed">
                                    下一页
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <!-- 合同详情/新建模态框 -->
        <div x-show="showContractModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" x-transition>
            <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl m-4 max-h-screen overflow-y-auto">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-xl font-medium text-title-gray" x-text="currentContract.id ? '编辑合同' : '新建合同'"></h2>
                        <button @click="closeContractModal()" class="text-aux-gray hover:text-title-gray">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <form @submit.prevent="saveContract()">
                        <!-- 基本信息 -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-title-gray mb-4">基本信息</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-title-gray mb-1">合同编号 *</label>
                                    <input type="text" x-model="currentContract.contractNo" required 
                                           :readonly="currentContract.id"
                                           class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-title-gray mb-1">合同类型 *</label>
                                    <select x-model="currentContract.type" required 
                                            class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                        <option value="">选择类型</option>
                                        <template x-for="type in contractTypes" :key="type.id">
                                            <option :value="type.id" x-text="type.name"></option>
                                        </template>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-title-gray mb-1">项目名称 *</label>
                                    <input type="text" x-model="currentContract.projectName" required 
                                           class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-title-gray mb-1">客户名称 *</label>
                                    <input type="text" x-model="currentContract.customerName" required 
                                           class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-title-gray mb-1">联系人</label>
                                    <input type="text" x-model="currentContract.contactPerson" 
                                           class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-title-gray mb-1">联系电话</label>
                                    <input type="tel" x-model="currentContract.contactPhone" 
                                           class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                            </div>
                        </div>

                        <!-- 合同金额 -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-title-gray mb-4">合同金额</h3>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-title-gray mb-1">合同总额 *</label>
                                    <input type="number" x-model.number="currentContract.totalAmount" required step="0.01"
                                           class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-title-gray mb-1">已收金额</label>
                                    <input type="number" x-model.number="currentContract.paidAmount" step="0.01"
                                           class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-title-gray mb-1">余额</label>
                                    <input type="number" :value="currentContract.totalAmount - currentContract.paidAmount" readonly
                                           class="w-full px-3 py-2 border border-border-gray rounded-md bg-bg-gray text-aux-gray">
                                </div>
                            </div>
                        </div>

                        <!-- 时间信息 -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-title-gray mb-4">时间信息</h3>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-title-gray mb-1">签约日期 *</label>
                                    <input type="date" x-model="currentContract.signDate" required 
                                           class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-title-gray mb-1">开始日期 *</label>
                                    <input type="date" x-model="currentContract.startDate" required 
                                           class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-title-gray mb-1">结束日期 *</label>
                                    <input type="date" x-model="currentContract.endDate" required 
                                           class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                            </div>
                        </div>

                        <!-- 其他信息 -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-title-gray mb-4">其他信息</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-title-gray mb-1">合同描述</label>
                                    <textarea x-model="currentContract.description" rows="3" 
                                              class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-2 focus:ring-primary"></textarea>
                                </div>
                                <div class="flex items-center space-x-4">
                                    <label class="flex items-center">
                                        <input type="checkbox" x-model="currentContract.isUrgent" 
                                               class="w-4 h-4 text-primary border-border-gray rounded focus:ring-primary">
                                        <span class="ml-2 text-sm">紧急合同</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" x-model="currentContract.autoRenewal" 
                                               class="w-4 h-4 text-primary border-border-gray rounded focus:ring-primary">
                                        <span class="ml-2 text-sm">自动续约</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="flex items-center justify-end space-x-4 pt-4 border-t border-border-gray">
                            <button type="button" @click="closeContractModal()" 
                                    class="px-4 py-2 border border-border-gray rounded-md hover:bg-bg-gray transition-colors">
                                取消
                            </button>
                            <button type="button" @click="saveAsDraft()" 
                                    class="px-4 py-2 bg-aux-gray text-white rounded-md hover:bg-gray-600 transition-colors">
                                保存草稿
                            </button>
                            <button type="submit" 
                                    class="px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600 transition-colors">
                                保存并提交审批
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 合同模板模态框 -->
        <div x-show="showTemplateModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" x-transition>
            <div class="bg-white rounded-lg shadow-xl w-full max-w-3xl m-4 max-h-screen overflow-y-auto">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-xl font-medium text-title-gray">合同模板库</h2>
                        <button @click="showTemplateModal = false" class="text-aux-gray hover:text-title-gray">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <template x-for="template in contractTemplates" :key="template.id">
                            <div class="border border-border-gray rounded-lg p-4 hover:border-primary transition-colors cursor-pointer"
                                 @click="useTemplate(template)">
                                <div class="flex items-start">
                                    <div class="document-preview w-12 h-12 rounded-lg text-white flex items-center justify-center mr-4">
                                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <h3 class="text-sm font-medium text-title-gray" x-text="template.name"></h3>
                                        <p class="text-sm text-aux-gray mt-1" x-text="template.description"></p>
                                        <div class="flex items-center justify-between mt-3">
                                            <span class="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded" 
                                                  x-text="getContractTypeName(template.type)"></span>
                                            <span class="text-xs text-aux-gray" x-text="template.usage + ' 次使用'"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function contractArchiveApp() {
            return {
                // 基础数据
                selectedStatus: '',
                selectedTypes: ['glass_processing', 'installation', 'maintenance'],
                dateRange: { start: '', end: '' },
                selectedAmountRange: '',
                searchQuery: '',
                sortBy: 'signDate',
                currentPage: 1,
                pageSize: 10,
                showContractModal: false,
                showTemplateModal: false,
                currentContract: {
                    id: null,
                    contractNo: '',
                    type: '',
                    projectName: '',
                    customerName: '',
                    contactPerson: '',
                    contactPhone: '',
                    totalAmount: 0,
                    paidAmount: 0,
                    signDate: '',
                    startDate: '',
                    endDate: '',
                    description: '',
                    isUrgent: false,
                    autoRenewal: false,
                    status: 'draft'
                },

                // 静态数据
                contractStatuses: [
                    { id: 'draft', name: '草稿' },
                    { id: 'reviewing', name: '审批中' },
                    { id: 'approved', name: '已审批' },
                    { id: 'rejected', name: '已拒绝' },
                    { id: 'executing', name: '执行中' },
                    { id: 'completed', name: '已完成' },
                    { id: 'terminated', name: '已终止' }
                ],
                contractTypes: [
                    { id: 'glass_processing', name: '玻璃深加工合同' },
                    { id: 'installation', name: '安装服务合同' },
                    { id: 'maintenance', name: '维护服务合同' },
                    { id: 'supply', name: '原料供应合同' },
                    { id: 'outsourcing', name: '外协加工合同' }
                ],
                amountRanges: [
                    { id: '', name: '全部金额' },
                    { id: 'small', name: '10万以下' },
                    { id: 'medium', name: '10-50万' },
                    { id: 'large', name: '50-100万' },
                    { id: 'xlarge', name: '100万以上' }
                ],
                contractTemplates: [
                    { id: 'tpl1', name: '玻璃幕墙工程合同', description: '适用于大型玻璃幕墙项目', type: 'glass_processing', usage: 45 },
                    { id: 'tpl2', name: '钢化玻璃加工合同', description: '钢化玻璃深加工标准合同', type: 'glass_processing', usage: 67 },
                    { id: 'tpl3', name: '安装服务合同', description: '玻璃产品现场安装服务', type: 'installation', usage: 23 },
                    { id: 'tpl4', name: '维护保养合同', description: '玻璃设施维护保养服务', type: 'maintenance', usage: 12 }
                ],
                contracts: [
                    {
                        id: 'CT2024001',
                        contractNo: 'CT2024001',
                        type: 'glass_processing',
                        projectName: '华美商务大厦玻璃幕墙工程',
                        customerName: '华美地产集团',
                        contactPerson: '张经理',
                        contactPhone: '138-0000-0001',
                        totalAmount: 2850000,
                        paidAmount: 1425000,
                        signDate: '2024-01-15',
                        startDate: '2024-02-01',
                        endDate: '2024-08-31',
                        status: 'executing',
                        progress: 65,
                        isUrgent: false,
                        description: '大型商务楼宇玻璃幕墙深加工及安装工程'
                    },
                    {
                        id: 'CT2024002',
                        contractNo: 'CT2024002',
                        type: 'glass_processing',
                        projectName: '绿城住宅小区门窗工程',
                        customerName: '绿城建设集团',
                        contactPerson: '李主管',
                        contactPhone: '138-0000-0002',
                        totalAmount: 580000,
                        paidAmount: 116000,
                        signDate: '2024-02-10',
                        startDate: '2024-03-01',
                        endDate: '2024-07-15',
                        status: 'executing',
                        progress: 35,
                        isUrgent: true,
                        description: '住宅楼盘门窗玻璃定制加工'
                    },
                    {
                        id: 'CT2024003',
                        contractNo: 'CT2024003',
                        type: 'maintenance',
                        projectName: '科技园玻璃幕墙维护',
                        customerName: '科技园管委会',
                        contactPerson: '王主任',
                        contactPhone: '138-0000-0003',
                        totalAmount: 120000,
                        paidAmount: 60000,
                        signDate: '2024-01-20',
                        startDate: '2024-02-15',
                        endDate: '2025-02-14',
                        status: 'executing',
                        progress: 45,
                        isUrgent: false,
                        description: '年度玻璃幕墙维护保养服务'
                    },
                    {
                        id: 'CT2024004',
                        contractNo: 'CT2024004',
                        type: 'glass_processing',
                        projectName: '星际酒店大堂装修',
                        customerName: '星际酒店管理公司',
                        contactPerson: '赵总经理',
                        contactPhone: '138-0000-0004',
                        totalAmount: 450000,
                        paidAmount: 0,
                        signDate: '2024-03-05',
                        startDate: '2024-04-01',
                        endDate: '2024-06-30',
                        status: 'approved',
                        progress: 0,
                        isUrgent: false,
                        description: '酒店大堂艺术玻璃装饰工程'
                    },
                    {
                        id: 'CT2024005',
                        contractNo: 'CT2024005',
                        type: 'supply',
                        projectName: '原料玻璃供应合同',
                        customerName: '制造业客户A',
                        contactPerson: '陈采购',
                        contactPhone: '138-0000-0005',
                        totalAmount: 180000,
                        paidAmount: 180000,
                        signDate: '2024-01-01',
                        startDate: '2024-01-05',
                        endDate: '2024-03-31',
                        status: 'completed',
                        progress: 100,
                        isUrgent: false,
                        description: '原料玻璃批量供应'
                    }
                ],

                // 计算属性
                get filteredContracts() {
                    let filtered = this.contracts;
                    
                    // 状态筛选
                    if (this.selectedStatus) {
                        filtered = filtered.filter(contract => contract.status === this.selectedStatus);
                    }
                    
                    // 类型筛选
                    if (this.selectedTypes.length > 0) {
                        filtered = filtered.filter(contract => this.selectedTypes.includes(contract.type));
                    }
                    
                    // 日期范围筛选
                    if (this.dateRange.start) {
                        filtered = filtered.filter(contract => contract.signDate >= this.dateRange.start);
                    }
                    if (this.dateRange.end) {
                        filtered = filtered.filter(contract => contract.signDate <= this.dateRange.end);
                    }
                    
                    // 金额范围筛选
                    if (this.selectedAmountRange) {
                        filtered = filtered.filter(contract => {
                            const amount = contract.totalAmount;
                            switch (this.selectedAmountRange) {
                                case 'small': return amount < 100000;
                                case 'medium': return amount >= 100000 && amount < 500000;
                                case 'large': return amount >= 500000 && amount < 1000000;
                                case 'xlarge': return amount >= 1000000;
                                default: return true;
                            }
                        });
                    }
                    
                    // 搜索筛选
                    if (this.searchQuery) {
                        const query = this.searchQuery.toLowerCase();
                        filtered = filtered.filter(contract => 
                            contract.contractNo.toLowerCase().includes(query) ||
                            contract.customerName.toLowerCase().includes(query) ||
                            contract.projectName.toLowerCase().includes(query)
                        );
                    }
                    
                    // 排序
                    filtered.sort((a, b) => {
                        switch (this.sortBy) {
                            case 'signDate':
                                return new Date(b.signDate) - new Date(a.signDate);
                            case 'contractNo':
                                return a.contractNo.localeCompare(b.contractNo);
                            case 'customerName':
                                return a.customerName.localeCompare(b.customerName);
                            case 'amount':
                                return b.totalAmount - a.totalAmount;
                            case 'status':
                                return a.status.localeCompare(b.status);
                            default:
                                return 0;
                        }
                    });
                    
                    return filtered;
                },
                get paginatedContracts() {
                    const start = (this.currentPage - 1) * this.pageSize;
                    const end = start + this.pageSize;
                    return this.filteredContracts.slice(start, end);
                },
                get totalPages() {
                    return Math.ceil(this.filteredContracts.length / this.pageSize);
                },
                get visiblePages() {
                    const pages = [];
                    const start = Math.max(1, this.currentPage - 2);
                    const end = Math.min(this.totalPages, this.currentPage + 2);
                    for (let i = start; i <= end; i++) {
                        pages.push(i);
                    }
                    return pages;
                },

                // 方法
                getContractCountByStatus(status) {
                    return this.contracts.filter(contract => contract.status === status).length;
                },

                getTotalAmount() {
                    return this.filteredContracts.reduce((total, contract) => total + contract.totalAmount, 0);
                },

                getExpiredContracts() {
                    const today = new Date();
                    const thirtyDaysFromNow = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000);
                    return this.contracts.filter(contract => {
                        const endDate = new Date(contract.endDate);
                        return endDate <= thirtyDaysFromNow && contract.status === 'executing';
                    }).length;
                },

                getContractStatusName(status) {
                    const statusObj = this.contractStatuses.find(s => s.id === status);
                    return statusObj ? statusObj.name : '未知';
                },

                getContractTypeName(type) {
                    const typeObj = this.contractTypes.find(t => t.id === type);
                    return typeObj ? typeObj.name : '未知';
                },

                isContractExpiringSoon(contract) {
                    if (contract.status !== 'executing') return false;
                    const today = new Date();
                    const endDate = new Date(contract.endDate);
                    const daysLeft = Math.ceil((endDate - today) / (1000 * 60 * 60 * 24));
                    return daysLeft <= 30 && daysLeft >= 0;
                },

                formatCurrency(amount) {
                    return '¥' + amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 });
                },

                openContractModal(contract = null) {
                    if (contract) {
                        this.currentContract = { ...contract };
                    } else {
                        this.currentContract = {
                            id: null,
                            contractNo: this.generateContractNo(),
                            type: '',
                            projectName: '',
                            customerName: '',
                            contactPerson: '',
                            contactPhone: '',
                            totalAmount: 0,
                            paidAmount: 0,
                            signDate: new Date().toISOString().split('T')[0],
                            startDate: '',
                            endDate: '',
                            description: '',
                            isUrgent: false,
                            autoRenewal: false,
                            status: 'draft'
                        };
                    }
                    this.showContractModal = true;
                },

                closeContractModal() {
                    this.showContractModal = false;
                    this.currentContract = {
                        id: null,
                        contractNo: '',
                        type: '',
                        projectName: '',
                        customerName: '',
                        contactPerson: '',
                        contactPhone: '',
                        totalAmount: 0,
                        paidAmount: 0,
                        signDate: '',
                        startDate: '',
                        endDate: '',
                        description: '',
                        isUrgent: false,
                        autoRenewal: false,
                        status: 'draft'
                    };
                },

                generateContractNo() {
                    const year = new Date().getFullYear();
                    const count = this.contracts.length + 1;
                    return `CT${year}${count.toString().padStart(3, '0')}`;
                },

                saveContract() {
                    // 验证
                    if (!this.currentContract.contractNo || !this.currentContract.type || !this.currentContract.projectName || !this.currentContract.customerName) {
                        alert('请填写必填字段');
                        return;
                    }

                    this.currentContract.status = 'reviewing';
                    this.currentContract.progress = 0;

                    if (this.currentContract.id) {
                        // 编辑
                        const index = this.contracts.findIndex(c => c.id === this.currentContract.id);
                        if (index > -1) {
                            this.contracts[index] = { ...this.currentContract };
                        }
                    } else {
                        // 新增
                        this.currentContract.id = this.currentContract.contractNo;
                        this.contracts.push({ ...this.currentContract });
                    }

                    this.closeContractModal();
                    alert('合同已提交审批');
                },

                saveAsDraft() {
                    this.currentContract.status = 'draft';
                    
                    if (this.currentContract.id) {
                        const index = this.contracts.findIndex(c => c.id === this.currentContract.id);
                        if (index > -1) {
                            this.contracts[index] = { ...this.currentContract };
                        }
                    } else {
                        this.currentContract.id = this.currentContract.contractNo;
                        this.contracts.push({ ...this.currentContract });
                    }

                    this.closeContractModal();
                    alert('合同草稿已保存');
                },

                viewContract(contract) {
                    alert(`查看合同：${contract.contractNo} - ${contract.projectName}`);
                },

                editContract(contract) {
                    this.openContractModal(contract);
                },

                showApprovalModal(contract) {
                    const result = confirm(`确定要审批合同 "${contract.contractNo}" 吗？`);
                    if (result) {
                        contract.status = 'approved';
                        alert('合同已审批通过');
                    }
                },

                showVersionHistory(contract) {
                    alert(`合同 "${contract.contractNo}" 版本历史：\n- V1.0 (${contract.signDate}) 初始版本\n- V1.1 (2024-02-15) 金额调整\n- V1.2 (2024-03-01) 工期延长`);
                },

                useTemplate(template) {
                    this.currentContract = {
                        id: null,
                        contractNo: this.generateContractNo(),
                        type: template.type,
                        projectName: '',
                        customerName: '',
                        contactPerson: '',
                        contactPhone: '',
                        totalAmount: 0,
                        paidAmount: 0,
                        signDate: new Date().toISOString().split('T')[0],
                        startDate: '',
                        endDate: '',
                        description: template.description,
                        isUrgent: false,
                        autoRenewal: false,
                        status: 'draft'
                    };
                    this.showTemplateModal = false;
                    this.showContractModal = true;
                },

                exportContracts() {
                    const data = this.filteredContracts.map(contract => ({
                        contractNo: contract.contractNo,
                        projectName: contract.projectName,
                        customerName: contract.customerName,
                        totalAmount: contract.totalAmount,
                        status: this.getContractStatusName(contract.status),
                        signDate: contract.signDate
                    }));
                    
                    console.log('导出合同数据:', data);
                    alert('导出功能演示完成，数据已输出到控制台');
                }
            }
        }
    </script>
</body>
</html>