<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>计量单位管理 - 玻璃深加工ERP系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1890FF',
                        success: '#52C41A',
                        warning: '#FAAD14',
                        error: '#F5222D',
                        'title-gray': '#262626',
                        'body-gray': '#595959',
                        'aux-gray': '#8C8C8C',
                        'disabled-gray': '#BFBFBF',
                        'border-gray': '#D9D9D9',
                        'bg-gray': '#FAFAFA'
                    },
                    fontFamily: {
                        'chinese': ['PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', sans-serif;
        }
        .table-row:hover {
            background-color: #f8fafc;
        }
        .unit-category {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .calculator-display {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .conversion-path {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            height: 2px;
            position: relative;
        }
        .conversion-arrow {
            position: absolute;
            right: -8px;
            top: -3px;
            width: 0;
            height: 0;
            border-left: 8px solid #764ba2;
            border-top: 4px solid transparent;
            border-bottom: 4px solid transparent;
        }
    </style>
</head>
<body class="bg-bg-gray font-chinese">
    <div x-data="unitManagementApp()" class="min-h-screen">
        <!-- 顶部导航栏 -->
        <header class="bg-white shadow-sm border-b border-border-gray h-16">
            <div class="flex items-center justify-between px-6 h-full">
                <div class="flex items-center space-x-4">
                    <h1 class="text-xl font-medium text-title-gray">计量单位管理</h1>
                    <div class="text-sm text-aux-gray">
                        玻璃深加工ERP系统 / 基础管理 / 计量单位管理
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button @click="showCalculator = !showCalculator" 
                            class="px-4 py-2 bg-warning text-white rounded-md hover:bg-yellow-600 transition-colors flex items-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                        </svg>
                        <span>单位换算器</span>
                    </button>
                    <button @click="openUnitModal()" 
                            class="px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600 transition-colors flex items-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        <span>新增单位</span>
                    </button>
                </div>
            </div>
        </header>

        <div class="flex">
            <!-- 左侧边栏 -->
            <aside class="w-60 bg-white border-r border-border-gray min-h-screen">
                <div class="p-4">
                    <!-- 单位分类 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-title-gray mb-2">单位分类</label>
                        <div class="space-y-2">
                            <button @click="selectedCategory = ''" 
                                    class="w-full text-left px-3 py-2 rounded-md hover:bg-blue-50 transition-colors"
                                    :class="selectedCategory === '' ? 'bg-primary text-white' : 'text-body-gray'">
                                全部单位
                            </button>
                            <template x-for="category in unitCategories" :key="category.id">
                                <button @click="selectedCategory = category.id" 
                                        class="w-full text-left px-3 py-2 rounded-md hover:bg-blue-50 transition-colors flex items-center space-x-2"
                                        :class="selectedCategory === category.id ? 'bg-primary text-white' : 'text-body-gray'">
                                    <span x-html="category.icon" class="w-4 h-4"></span>
                                    <span x-text="category.name"></span>
                                    <span x-text="'(' + getUnitsCountByCategory(category.id) + ')'" class="text-xs opacity-75"></span>
                                </button>
                            </template>
                        </div>
                    </div>

                    <!-- 单位类型筛选 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-title-gray mb-2">单位类型</label>
                        <div class="space-y-2">
                            <template x-for="type in unitTypes" :key="type.id">
                                <label class="flex items-center">
                                    <input type="checkbox" x-model="selectedTypes" :value="type.id" 
                                           class="w-4 h-4 text-primary border-border-gray rounded focus:ring-primary">
                                    <span x-text="type.name" class="ml-2 text-sm text-body-gray"></span>
                                </label>
                            </template>
                        </div>
                    </div>

                    <!-- 玻璃行业常用单位 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-title-gray mb-2">玻璃行业常用</label>
                        <div class="space-y-2">
                            <template x-for="unit in commonGlassUnits" :key="unit.id">
                                <div class="flex items-center justify-between p-2 bg-bg-gray rounded-md">
                                    <div class="flex items-center space-x-2">
                                        <span x-text="unit.symbol" class="text-sm font-medium text-primary"></span>
                                        <span x-text="unit.name" class="text-xs text-aux-gray"></span>
                                    </div>
                                    <button @click="quickAddUnit(unit)" 
                                            class="text-xs text-primary hover:text-blue-600">
                                        添加
                                    </button>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- 主内容区域 -->
            <main class="flex-1 p-6">
                <!-- 统计卡片 -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div class="bg-white p-4 rounded-lg shadow-sm border border-border-gray">
                        <div class="flex items-center">
                            <div class="unit-category w-12 h-12 rounded-lg flex items-center justify-center text-white mr-3">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                            </div>
                            <div>
                                <div class="text-2xl font-bold text-title-gray" x-text="filteredUnits.length"></div>
                                <div class="text-sm text-aux-gray">总单位数</div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow-sm border border-border-gray">
                        <div class="flex items-center">
                            <div class="bg-success w-12 h-12 rounded-lg flex items-center justify-center text-white mr-3">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
                                </svg>
                            </div>
                            <div>
                                <div class="text-2xl font-bold text-title-gray" x-text="baseUnits.length"></div>
                                <div class="text-sm text-aux-gray">基础单位</div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow-sm border border-border-gray">
                        <div class="flex items-center">
                            <div class="bg-warning w-12 h-12 rounded-lg flex items-center justify-center text-white mr-3">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <div class="text-2xl font-bold text-title-gray" x-text="derivedUnits.length"></div>
                                <div class="text-sm text-aux-gray">派生单位</div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow-sm border border-border-gray">
                        <div class="flex items-center">
                            <div class="bg-error w-12 h-12 rounded-lg flex items-center justify-center text-white mr-3">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                            </div>
                            <div>
                                <div class="text-2xl font-bold text-title-gray" x-text="getUsageCount()"></div>
                                <div class="text-sm text-aux-gray">使用中单位</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="bg-white p-4 rounded-lg shadow-sm border border-border-gray mb-6">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                        <div class="flex-1 max-w-md">
                            <div class="relative">
                                <input type="text" x-model="searchQuery" placeholder="搜索单位名称、符号或描述..." 
                                       class="w-full pl-10 pr-4 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                <svg class="w-5 h-5 absolute left-3 top-2.5 text-aux-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="flex items-center space-x-4">
                            <select x-model="sortBy" class="px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                <option value="name">按名称排序</option>
                                <option value="symbol">按符号排序</option>
                                <option value="category">按分类排序</option>
                                <option value="usage">按使用频率排序</option>
                            </select>
                            <button @click="exportUnits" 
                                    class="px-4 py-2 border border-border-gray rounded-md hover:bg-bg-gray transition-colors flex items-center space-x-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <span>导出</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 单位列表 -->
                <div class="bg-white rounded-lg shadow-sm border border-border-gray">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-bg-gray">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">单位信息</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">分类</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">转换关系</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">使用情况</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-border-gray">
                                <template x-for="unit in paginatedUnits" :key="unit.id">
                                    <tr class="table-row">
                                        <td class="px-6 py-4">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 w-10 h-10 bg-primary rounded-lg flex items-center justify-center text-white font-medium text-sm">
                                                    <span x-text="unit.symbol"></span>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-title-gray" x-text="unit.name"></div>
                                                    <div class="text-sm text-aux-gray" x-text="unit.description"></div>
                                                    <div class="flex items-center space-x-2 mt-1">
                                                        <span class="text-xs px-2 py-1 rounded"
                                                              :class="unit.isBase ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'"
                                                              x-text="unit.isBase ? '基础单位' : '派生单位'"></span>
                                                        <span x-show="unit.isSystem" class="text-xs px-2 py-1 bg-gray-100 text-gray-800 rounded">系统</span>
                                                        <span x-show="unit.isCustom" class="text-xs px-2 py-1 bg-purple-100 text-purple-800 rounded">自定义</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <span x-html="getCategoryIcon(unit.category)" class="w-4 h-4 mr-2"></span>
                                                <span x-text="getCategoryName(unit.category)" class="text-sm text-body-gray"></span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4">
                                            <template x-show="unit.conversionFactors && unit.conversionFactors.length > 0">
                                                <div class="space-y-1">
                                                    <template x-for="conversion in unit.conversionFactors.slice(0, 2)" :key="conversion.toUnit">
                                                        <div class="flex items-center text-sm">
                                                            <span class="text-aux-gray">1</span>
                                                            <span x-text="unit.symbol" class="mx-1 text-primary font-medium"></span>
                                                            <span class="text-aux-gray">=</span>
                                                            <span x-text="conversion.factor" class="mx-1 font-medium"></span>
                                                            <span x-text="conversion.toSymbol" class="text-primary"></span>
                                                        </div>
                                                    </template>
                                                    <template x-show="unit.conversionFactors.length > 2">
                                                        <span class="text-xs text-aux-gray">...</span>
                                                    </template>
                                                </div>
                                            </template>
                                            <template x-show="!unit.conversionFactors || unit.conversionFactors.length === 0">
                                                <span class="text-sm text-aux-gray">无转换关系</span>
                                            </template>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                                    <div class="bg-primary h-2 rounded-full transition-all duration-300" 
                                                         :style="`width: ${unit.usagePercentage}%`"></div>
                                                </div>
                                                <span x-text="unit.usageCount" class="text-sm text-body-gray"></span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex items-center space-x-2">
                                                <button @click="editUnit(unit)" 
                                                        class="text-primary hover:text-blue-600">编辑</button>
                                                <button @click="viewConversions(unit)" 
                                                        class="text-warning hover:text-yellow-600">转换</button>
                                                <button @click="deleteUnit(unit)" 
                                                        :disabled="unit.isSystem || unit.usageCount > 0"
                                                        class="text-error hover:text-red-600 disabled:text-disabled-gray disabled:cursor-not-allowed">删除</button>
                                            </div>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="px-6 py-3 border-t border-border-gray">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-aux-gray">
                                显示 <span x-text="(currentPage - 1) * pageSize + 1"></span> 到 
                                <span x-text="Math.min(currentPage * pageSize, filteredUnits.length)"></span> 条，
                                共 <span x-text="filteredUnits.length"></span> 条记录
                            </div>
                            <div class="flex items-center space-x-2">
                                <button @click="currentPage = Math.max(1, currentPage - 1)" 
                                        :disabled="currentPage === 1"
                                        class="px-3 py-1 border border-border-gray rounded text-sm hover:bg-bg-gray disabled:opacity-50 disabled:cursor-not-allowed">
                                    上一页
                                </button>
                                <template x-for="page in visiblePages" :key="page">
                                    <button @click="currentPage = page" 
                                            class="px-3 py-1 border rounded text-sm"
                                            :class="currentPage === page ? 'border-primary bg-primary text-white' : 'border-border-gray hover:bg-bg-gray'"
                                            x-text="page"></button>
                                </template>
                                <button @click="currentPage = Math.min(totalPages, currentPage + 1)" 
                                        :disabled="currentPage === totalPages"
                                        class="px-3 py-1 border border-border-gray rounded text-sm hover:bg-bg-gray disabled:opacity-50 disabled:cursor-not-allowed">
                                    下一页
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <!-- 单位换算器面板 -->
        <div x-show="showCalculator" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" x-transition>
            <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl m-4 max-h-screen overflow-y-auto">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-xl font-medium text-title-gray">单位换算器</h2>
                        <button @click="showCalculator = false" class="text-aux-gray hover:text-title-gray">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                    
                    <div class="calculator-display rounded-lg p-6 text-white mb-6">
                        <div class="text-center">
                            <div class="text-3xl font-bold mb-2">
                                <span x-text="calculator.inputValue || '0'"></span>
                                <span x-text="calculator.fromUnit ? getUnitSymbol(calculator.fromUnit) : ''" class="text-lg ml-2 opacity-90"></span>
                            </div>
                            <div class="text-xl opacity-75">
                                = <span x-text="calculator.result || '0'"></span>
                                <span x-text="calculator.toUnit ? getUnitSymbol(calculator.toUnit) : ''" class="ml-1"></span>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-title-gray mb-2">从</label>
                            <div class="space-y-3">
                                <input type="number" x-model.number="calculator.inputValue" @input="calculateConversion()" 
                                       placeholder="输入数值" 
                                       class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                <select x-model="calculator.fromUnit" @change="calculateConversion()" 
                                        class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                    <option value="">选择源单位</option>
                                    <template x-for="category in unitCategories" :key="category.id">
                                        <optgroup :label="category.name">
                                            <template x-for="unit in getUnitsByCategory(category.id)" :key="unit.id">
                                                <option :value="unit.id" x-text="`${unit.name} (${unit.symbol})`"></option>
                                            </template>
                                        </optgroup>
                                    </template>
                                </select>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-title-gray mb-2">到</label>
                            <div class="space-y-3">
                                <input type="number" x-model.number="calculator.result" readonly 
                                       class="w-full px-3 py-2 border border-border-gray rounded-md bg-bg-gray text-aux-gray">
                                <select x-model="calculator.toUnit" @change="calculateConversion()" 
                                        class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                    <option value="">选择目标单位</option>
                                    <template x-for="category in unitCategories" :key="category.id">
                                        <optgroup :label="category.name">
                                            <template x-for="unit in getUnitsByCategory(category.id)" :key="unit.id">
                                                <option :value="unit.id" x-text="`${unit.name} (${unit.symbol})`"></option>
                                            </template>
                                        </optgroup>
                                    </template>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 flex items-center justify-between">
                        <button @click="swapUnits()" 
                                class="flex items-center space-x-2 px-4 py-2 border border-border-gray rounded-md hover:bg-bg-gray">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                            </svg>
                            <span>交换单位</span>
                        </button>
                        <button @click="clearCalculator()" 
                                class="px-4 py-2 border border-border-gray rounded-md hover:bg-bg-gray">
                            清空
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 新增/编辑单位模态框 -->
        <div x-show="showUnitModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" x-transition>
            <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl m-4 max-h-screen overflow-y-auto">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-xl font-medium text-title-gray" x-text="currentUnit.id ? '编辑单位' : '新增单位'"></h2>
                        <button @click="closeUnitModal()" class="text-aux-gray hover:text-title-gray">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <form @submit.prevent="saveUnit()">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-title-gray mb-1">单位名称 *</label>
                                <input type="text" x-model="currentUnit.name" required 
                                       class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-title-gray mb-1">单位符号 *</label>
                                <input type="text" x-model="currentUnit.symbol" required 
                                       class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                            </div>
                        </div>

                        <div class="mb-4">
                            <label class="block text-sm font-medium text-title-gray mb-1">描述</label>
                            <textarea x-model="currentUnit.description" rows="2" 
                                      class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-2 focus:ring-primary"></textarea>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-title-gray mb-1">分类 *</label>
                                <select x-model="currentUnit.category" required 
                                        class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                    <option value="">选择分类</option>
                                    <template x-for="category in unitCategories" :key="category.id">
                                        <option :value="category.id" x-text="category.name"></option>
                                    </template>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-title-gray mb-1">单位类型</label>
                                <div class="flex items-center space-x-4 mt-2">
                                    <label class="flex items-center">
                                        <input type="radio" x-model="currentUnit.isBase" :value="true" 
                                               class="w-4 h-4 text-primary border-border-gray focus:ring-primary">
                                        <span class="ml-2 text-sm">基础单位</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" x-model="currentUnit.isBase" :value="false" 
                                               class="w-4 h-4 text-primary border-border-gray focus:ring-primary">
                                        <span class="ml-2 text-sm">派生单位</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div x-show="!currentUnit.isBase" class="mb-4">
                            <label class="block text-sm font-medium text-title-gray mb-2">转换关系</label>
                            <div class="space-y-3">
                                <template x-for="(conversion, index) in currentUnit.conversionFactors" :key="index">
                                    <div class="flex items-center space-x-2">
                                        <span class="text-sm text-aux-gray">1</span>
                                        <span x-text="currentUnit.symbol" class="text-sm font-medium"></span>
                                        <span class="text-sm text-aux-gray">=</span>
                                        <input type="number" x-model.number="conversion.factor" step="any" 
                                               class="w-24 px-2 py-1 border border-border-gray rounded text-sm focus:outline-none focus:ring-1 focus:ring-primary">
                                        <select x-model="conversion.toUnit" 
                                                class="flex-1 px-2 py-1 border border-border-gray rounded text-sm focus:outline-none focus:ring-1 focus:ring-primary">
                                            <option value="">选择目标单位</option>
                                            <template x-for="unit in getBaseUnitsByCategory(currentUnit.category)" :key="unit.id">
                                                <option :value="unit.id" x-text="`${unit.name} (${unit.symbol})`"></option>
                                            </template>
                                        </select>
                                        <button type="button" @click="removeConversion(index)" 
                                                class="text-error hover:text-red-600">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </template>
                                <button type="button" @click="addConversion()" 
                                        class="flex items-center space-x-2 text-primary hover:text-blue-600 text-sm">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    <span>添加转换关系</span>
                                </button>
                            </div>
                        </div>

                        <div class="flex items-center justify-end space-x-4 pt-4 border-t border-border-gray">
                            <button type="button" @click="closeUnitModal()" 
                                    class="px-4 py-2 border border-border-gray rounded-md hover:bg-bg-gray transition-colors">
                                取消
                            </button>
                            <button type="submit" 
                                    class="px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600 transition-colors">
                                保存
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function unitManagementApp() {
            return {
                // 基础数据
                selectedCategory: '',
                selectedTypes: ['base', 'derived'],
                searchQuery: '',
                sortBy: 'name',
                currentPage: 1,
                pageSize: 10,
                showCalculator: false,
                showUnitModal: false,
                currentUnit: {
                    id: null,
                    name: '',
                    symbol: '',
                    description: '',
                    category: '',
                    isBase: true,
                    isSystem: false,
                    isCustom: true,
                    conversionFactors: []
                },
                calculator: {
                    inputValue: 1,
                    fromUnit: '',
                    toUnit: '',
                    result: 0
                },

                // 静态数据
                unitCategories: [
                    { id: 'length', name: '长度', icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>' },
                    { id: 'area', name: '面积', icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"></path>' },
                    { id: 'volume', name: '体积', icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>' },
                    { id: 'weight', name: '重量', icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 0a3 3 0 013 3H9a3 3 0 013-3zm0 0a3 3 0 00-3 3h6a3 3 0 00-3-3zM9 7h6v4l1 10H8l1-10V7z"></path>' },
                    { id: 'thickness', name: '厚度', icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>' }
                ],
                unitTypes: [
                    { id: 'base', name: '基础单位' },
                    { id: 'derived', name: '派生单位' },
                    { id: 'system', name: '系统单位' },
                    { id: 'custom', name: '自定义单位' }
                ],
                commonGlassUnits: [
                    { id: 'sqm', name: '平方米', symbol: 'm²', category: 'area' },
                    { id: 'lm', name: '线性米', symbol: 'lm', category: 'length' },
                    { id: 'mm', name: '毫米', symbol: 'mm', category: 'thickness' },
                    { id: 'piece', name: '片', symbol: '片', category: 'quantity' },
                    { id: 'kg', name: '千克', symbol: 'kg', category: 'weight' }
                ],
                units: [
                    // 长度单位
                    { id: 'meter', name: '米', symbol: 'm', description: '国际标准长度单位', category: 'length', isBase: true, isSystem: true, isCustom: false, usageCount: 89, usagePercentage: 85, conversionFactors: [] },
                    { id: 'centimeter', name: '厘米', symbol: 'cm', description: '米的百分之一', category: 'length', isBase: false, isSystem: true, isCustom: false, usageCount: 67, usagePercentage: 65, conversionFactors: [{ factor: 0.01, toUnit: 'meter', toSymbol: 'm' }] },
                    { id: 'millimeter', name: '毫米', symbol: 'mm', description: '米的千分之一', category: 'length', isBase: false, isSystem: true, isCustom: false, usageCount: 156, usagePercentage: 95, conversionFactors: [{ factor: 0.001, toUnit: 'meter', toSymbol: 'm' }] },
                    
                    // 面积单位
                    { id: 'square_meter', name: '平方米', symbol: 'm²', description: '国际标准面积单位', category: 'area', isBase: true, isSystem: true, isCustom: false, usageCount: 234, usagePercentage: 100, conversionFactors: [] },
                    { id: 'square_centimeter', name: '平方厘米', symbol: 'cm²', description: '平方米的万分之一', category: 'area', isBase: false, isSystem: true, isCustom: false, usageCount: 45, usagePercentage: 30, conversionFactors: [{ factor: 0.0001, toUnit: 'square_meter', toSymbol: 'm²' }] },
                    
                    // 重量单位
                    { id: 'kilogram', name: '千克', symbol: 'kg', description: '国际标准质量单位', category: 'weight', isBase: true, isSystem: true, isCustom: false, usageCount: 123, usagePercentage: 80, conversionFactors: [] },
                    { id: 'gram', name: '克', symbol: 'g', description: '千克的千分之一', category: 'weight', isBase: false, isSystem: true, isCustom: false, usageCount: 78, usagePercentage: 50, conversionFactors: [{ factor: 0.001, toUnit: 'kilogram', toSymbol: 'kg' }] },
                    
                    // 厚度单位
                    { id: 'mm_thickness', name: '毫米厚度', symbol: 'mm', description: '玻璃厚度专用单位', category: 'thickness', isBase: true, isSystem: false, isCustom: true, usageCount: 89, usagePercentage: 70, conversionFactors: [] },
                    
                    // 体积单位
                    { id: 'cubic_meter', name: '立方米', symbol: 'm³', description: '国际标准体积单位', category: 'volume', isBase: true, isSystem: true, isCustom: false, usageCount: 34, usagePercentage: 40, conversionFactors: [] },
                    { id: 'liter', name: '升', symbol: 'L', description: '立方米的千分之一', category: 'volume', isBase: false, isSystem: true, isCustom: false, usageCount: 12, usagePercentage: 20, conversionFactors: [{ factor: 0.001, toUnit: 'cubic_meter', toSymbol: 'm³' }] }
                ],

                // 计算属性
                get filteredUnits() {
                    let filtered = this.units;
                    
                    // 分类筛选
                    if (this.selectedCategory) {
                        filtered = filtered.filter(unit => unit.category === this.selectedCategory);
                    }
                    
                    // 类型筛选
                    filtered = filtered.filter(unit => {
                        return this.selectedTypes.some(type => {
                            if (type === 'base') return unit.isBase;
                            if (type === 'derived') return !unit.isBase;
                            if (type === 'system') return unit.isSystem;
                            if (type === 'custom') return unit.isCustom;
                            return false;
                        });
                    });
                    
                    // 搜索筛选
                    if (this.searchQuery) {
                        const query = this.searchQuery.toLowerCase();
                        filtered = filtered.filter(unit => 
                            unit.name.toLowerCase().includes(query) ||
                            unit.symbol.toLowerCase().includes(query) ||
                            unit.description.toLowerCase().includes(query)
                        );
                    }
                    
                    // 排序
                    filtered.sort((a, b) => {
                        switch (this.sortBy) {
                            case 'name':
                                return a.name.localeCompare(b.name);
                            case 'symbol':
                                return a.symbol.localeCompare(b.symbol);
                            case 'category':
                                return a.category.localeCompare(b.category);
                            case 'usage':
                                return b.usageCount - a.usageCount;
                            default:
                                return 0;
                        }
                    });
                    
                    return filtered;
                },
                get paginatedUnits() {
                    const start = (this.currentPage - 1) * this.pageSize;
                    const end = start + this.pageSize;
                    return this.filteredUnits.slice(start, end);
                },
                get totalPages() {
                    return Math.ceil(this.filteredUnits.length / this.pageSize);
                },
                get visiblePages() {
                    const pages = [];
                    const start = Math.max(1, this.currentPage - 2);
                    const end = Math.min(this.totalPages, this.currentPage + 2);
                    for (let i = start; i <= end; i++) {
                        pages.push(i);
                    }
                    return pages;
                },
                get baseUnits() {
                    return this.units.filter(unit => unit.isBase);
                },
                get derivedUnits() {
                    return this.units.filter(unit => !unit.isBase);
                },

                // 方法
                getUnitsCountByCategory(categoryId) {
                    return this.units.filter(unit => unit.category === categoryId).length;
                },

                getUsageCount() {
                    return this.units.filter(unit => unit.usageCount > 0).length;
                },

                getCategoryName(categoryId) {
                    const category = this.unitCategories.find(c => c.id === categoryId);
                    return category ? category.name : '未知';
                },

                getCategoryIcon(categoryId) {
                    const category = this.unitCategories.find(c => c.id === categoryId);
                    return category ? `<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">${category.icon}</svg>` : '';
                },

                getUnitsByCategory(categoryId) {
                    return this.units.filter(unit => unit.category === categoryId);
                },

                getBaseUnitsByCategory(categoryId) {
                    return this.units.filter(unit => unit.category === categoryId && unit.isBase);
                },

                getUnitSymbol(unitId) {
                    const unit = this.units.find(u => u.id === unitId);
                    return unit ? unit.symbol : '';
                },

                openUnitModal(unit = null) {
                    if (unit) {
                        this.currentUnit = { ...unit, conversionFactors: [...(unit.conversionFactors || [])] };
                    } else {
                        this.currentUnit = {
                            id: null,
                            name: '',
                            symbol: '',
                            description: '',
                            category: '',
                            isBase: true,
                            isSystem: false,
                            isCustom: true,
                            conversionFactors: []
                        };
                    }
                    this.showUnitModal = true;
                },

                closeUnitModal() {
                    this.showUnitModal = false;
                    this.currentUnit = {
                        id: null,
                        name: '',
                        symbol: '',
                        description: '',
                        category: '',
                        isBase: true,
                        isSystem: false,
                        isCustom: true,
                        conversionFactors: []
                    };
                },

                addConversion() {
                    this.currentUnit.conversionFactors.push({
                        factor: 1,
                        toUnit: '',
                        toSymbol: ''
                    });
                },

                removeConversion(index) {
                    this.currentUnit.conversionFactors.splice(index, 1);
                },

                saveUnit() {
                    // 验证
                    if (!this.currentUnit.name || !this.currentUnit.symbol || !this.currentUnit.category) {
                        alert('请填写必填字段');
                        return;
                    }

                    // 为转换因子添加符号
                    this.currentUnit.conversionFactors.forEach(cf => {
                        if (cf.toUnit) {
                            cf.toSymbol = this.getUnitSymbol(cf.toUnit);
                        }
                    });

                    if (this.currentUnit.id) {
                        // 编辑
                        const index = this.units.findIndex(u => u.id === this.currentUnit.id);
                        if (index > -1) {
                            this.units[index] = { ...this.currentUnit, usageCount: this.units[index].usageCount, usagePercentage: this.units[index].usagePercentage };
                        }
                    } else {
                        // 新增
                        this.currentUnit.id = 'unit_' + Date.now();
                        this.currentUnit.usageCount = 0;
                        this.currentUnit.usagePercentage = 0;
                        this.units.push({ ...this.currentUnit });
                    }

                    this.closeUnitModal();
                },

                editUnit(unit) {
                    this.openUnitModal(unit);
                },

                deleteUnit(unit) {
                    if (confirm(`确定要删除单位 "${unit.name}" 吗？`)) {
                        const index = this.units.findIndex(u => u.id === unit.id);
                        if (index > -1) {
                            this.units.splice(index, 1);
                        }
                    }
                },

                viewConversions(unit) {
                    alert(`单位 "${unit.name}" 的转换关系：\n${JSON.stringify(unit.conversionFactors, null, 2)}`);
                },

                quickAddUnit(unit) {
                    const exists = this.units.find(u => u.symbol === unit.symbol && u.category === unit.category);
                    if (exists) {
                        alert('该单位已存在');
                        return;
                    }
                    
                    const newUnit = {
                        id: 'unit_' + Date.now(),
                        name: unit.name,
                        symbol: unit.symbol,
                        description: `玻璃行业常用${unit.name}`,
                        category: unit.category,
                        isBase: true,
                        isSystem: false,
                        isCustom: true,
                        usageCount: 0,
                        usagePercentage: 0,
                        conversionFactors: []
                    };
                    
                    this.units.push(newUnit);
                },

                calculateConversion() {
                    if (!this.calculator.fromUnit || !this.calculator.toUnit || !this.calculator.inputValue) {
                        this.calculator.result = 0;
                        return;
                    }

                    // 简单的转换逻辑（实际应用中需要更复杂的转换算法）
                    const fromUnit = this.units.find(u => u.id === this.calculator.fromUnit);
                    const toUnit = this.units.find(u => u.id === this.calculator.toUnit);
                    
                    if (fromUnit && toUnit && fromUnit.category === toUnit.category) {
                        // 模拟转换计算
                        if (fromUnit.id === toUnit.id) {
                            this.calculator.result = this.calculator.inputValue;
                        } else {
                            // 查找转换因子
                            const conversion = fromUnit.conversionFactors?.find(cf => cf.toUnit === toUnit.id);
                            if (conversion) {
                                this.calculator.result = this.calculator.inputValue * conversion.factor;
                            } else {
                                // 反向查找
                                const reverseConversion = toUnit.conversionFactors?.find(cf => cf.toUnit === fromUnit.id);
                                if (reverseConversion) {
                                    this.calculator.result = this.calculator.inputValue / reverseConversion.factor;
                                } else {
                                    this.calculator.result = this.calculator.inputValue; // 默认1:1
                                }
                            }
                        }
                    } else {
                        this.calculator.result = 0;
                    }
                },

                swapUnits() {
                    const temp = this.calculator.fromUnit;
                    this.calculator.fromUnit = this.calculator.toUnit;
                    this.calculator.toUnit = temp;
                    this.calculator.inputValue = this.calculator.result || this.calculator.inputValue;
                    this.calculateConversion();
                },

                clearCalculator() {
                    this.calculator = {
                        inputValue: 1,
                        fromUnit: '',
                        toUnit: '',
                        result: 0
                    };
                },

                exportUnits() {
                    const data = this.filteredUnits.map(unit => ({
                        name: unit.name,
                        symbol: unit.symbol,
                        description: unit.description,
                        category: this.getCategoryName(unit.category),
                        type: unit.isBase ? '基础单位' : '派生单位',
                        usage: unit.usageCount
                    }));
                    
                    console.log('导出数据:', data);
                    alert('导出功能演示完成，数据已输出到控制台');
                }
            }
        }
    </script>
</body>
</html>