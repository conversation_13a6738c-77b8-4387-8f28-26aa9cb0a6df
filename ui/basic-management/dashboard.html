<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基础管理中心 - 玻璃深加工ERP系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1890FF',
                        success: '#52C41A',
                        warning: '#FAAD14',
                        error: '#F5222D',
                        'title-gray': '#262626',
                        'body-gray': '#595959',
                        'aux-gray': '#8C8C8C',
                        'disabled-gray': '#BFBFBF',
                        'border-gray': '#D9D9D9',
                        'bg-gray': '#FAFAFA'
                    },
                    fontFamily: {
                        'chinese': ['PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', sans-serif;
        }
        .module-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .module-card {
            transition: all 0.3s ease;
        }
        .kpi-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .activity-item {
            border-left: 3px solid transparent;
            transition: all 0.3s ease;
        }
        .activity-item:hover {
            border-left-color: #1890FF;
            background-color: #f8fafc;
        }
        .notification-badge {
            animation: pulse 2s infinite;
        }
        .search-highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .system-status {
            position: relative;
            overflow: hidden;
        }
        .system-status::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: shine 3s infinite;
        }
        @keyframes shine {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body class="bg-bg-gray font-chinese">
    <div x-data="dashboardApp()" class="min-h-screen">
        <!-- 顶部导航栏 -->
        <header class="bg-white shadow-sm border-b border-border-gray h-16">
            <div class="flex items-center justify-between px-6 h-full">
                <div class="flex items-center space-x-4">
                    <h1 class="text-xl font-medium text-title-gray">基础管理中心</h1>
                    <div class="text-sm text-aux-gray">
                        玻璃深加工ERP系统 / 基础管理
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- 全局搜索 -->
                    <div class="relative" x-data="{ searchFocused: false }">
                        <input type="text" x-model="globalSearchQuery" @focus="searchFocused = true" @blur="searchFocused = false"
                               placeholder="全局搜索..." 
                               class="w-64 pl-10 pr-4 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all"
                               :class="searchFocused ? 'w-80' : 'w-64'">
                        <svg class="w-5 h-5 absolute left-3 top-2.5 text-aux-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <!-- 搜索建议 -->
                        <div x-show="globalSearchQuery && searchFocused" @click.away="searchFocused = false"
                             class="absolute z-10 w-full mt-1 bg-white border border-border-gray rounded-md shadow-lg max-h-80 overflow-y-auto">
                            <template x-for="suggestion in searchSuggestions" :key="suggestion.id">
                                <button @click="navigateToModule(suggestion)" 
                                        class="w-full px-4 py-3 text-left hover:bg-blue-50 transition-colors border-b border-border-gray last:border-b-0">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 rounded-lg flex items-center justify-center text-white text-sm"
                                             :class="suggestion.color" x-html="suggestion.icon"></div>
                                        <div class="flex-1">
                                            <div class="text-sm font-medium text-title-gray" x-html="highlightSearch(suggestion.name)"></div>
                                            <div class="text-xs text-aux-gray" x-text="suggestion.description"></div>
                                        </div>
                                    </div>
                                </button>
                            </template>
                        </div>
                    </div>
                    
                    <!-- 系统通知 -->
                    <div class="relative" x-data="{ showNotifications: false }">
                        <button @click="showNotifications = !showNotifications" 
                                class="relative p-2 text-aux-gray hover:text-title-gray transition-colors">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5-5 5-5h-5m-6 6h5m-6 4h10a2 2 0 002-2V5a2 2 0 00-2-2H9a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                            </svg>
                            <span x-show="unreadNotifications > 0" 
                                  class="notification-badge absolute -top-1 -right-1 w-5 h-5 bg-error text-white text-xs rounded-full flex items-center justify-center"
                                  x-text="unreadNotifications"></span>
                        </button>
                        <div x-show="showNotifications" @click.away="showNotifications = false"
                             class="absolute right-0 mt-2 w-80 bg-white border border-border-gray rounded-md shadow-lg z-10">
                            <div class="p-4 border-b border-border-gray">
                                <h3 class="text-sm font-medium text-title-gray">系统通知</h3>
                            </div>
                            <div class="max-h-80 overflow-y-auto">
                                <template x-for="notification in notifications" :key="notification.id">
                                    <div class="p-4 border-b border-border-gray hover:bg-bg-gray cursor-pointer"
                                         :class="!notification.read ? 'bg-blue-50' : ''">
                                        <div class="flex items-start space-x-3">
                                            <div class="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm"
                                                 :class="notification.type === 'warning' ? 'bg-warning' : 
                                                        notification.type === 'error' ? 'bg-error' : 'bg-primary'">
                                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                                </svg>
                                            </div>
                                            <div class="flex-1">
                                                <div class="text-sm font-medium text-title-gray" x-text="notification.title"></div>
                                                <div class="text-sm text-aux-gray mt-1" x-text="notification.message"></div>
                                                <div class="text-xs text-aux-gray mt-2" x-text="notification.time"></div>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>

                    <!-- 用户信息 -->
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white text-sm">
                            管
                        </div>
                        <div class="text-sm">
                            <div class="font-medium text-title-gray">系统管理员</div>
                            <div class="text-aux-gray"><EMAIL></div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="p-6">
            <!-- 系统概览卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white p-6 rounded-lg shadow-sm border border-border-gray system-status">
                    <div class="flex items-center">
                        <div class="kpi-gradient w-12 h-12 rounded-lg flex items-center justify-center text-white mr-4">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-title-gray" x-text="systemStatus.activeUsers"></div>
                            <div class="text-sm text-aux-gray">在线用户</div>
                            <div class="text-xs text-success mt-1">↑ 12% 比昨日</div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow-sm border border-border-gray">
                    <div class="flex items-center">
                        <div class="bg-success w-12 h-12 rounded-lg flex items-center justify-center text-white mr-4">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-title-gray" x-text="systemStatus.completedTasks"></div>
                            <div class="text-sm text-aux-gray">今日完成任务</div>
                            <div class="text-xs text-success mt-1">↑ 8% 比昨日</div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow-sm border border-border-gray">
                    <div class="flex items-center">
                        <div class="bg-warning w-12 h-12 rounded-lg flex items-center justify-center text-white mr-4">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-title-gray" x-text="systemStatus.pendingApprovals"></div>
                            <div class="text-sm text-aux-gray">待审批事项</div>
                            <div class="text-xs text-warning mt-1">需要关注</div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow-sm border border-border-gray">
                    <div class="flex items-center">
                        <div class="bg-primary w-12 h-12 rounded-lg flex items-center justify-center text-white mr-4">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-title-gray" x-text="systemStatus.systemHealth + '%'"></div>
                            <div class="text-sm text-aux-gray">系统健康度</div>
                            <div class="text-xs text-success mt-1">运行正常</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 功能模块导航 -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-lg shadow-sm border border-border-gray p-6">
                        <div class="flex items-center justify-between mb-6">
                            <h2 class="text-lg font-medium text-title-gray">基础管理模块</h2>
                            <div class="flex items-center space-x-2">
                                <button @click="viewMode = 'grid'" 
                                        class="p-2 rounded-md hover:bg-bg-gray transition-colors"
                                        :class="viewMode === 'grid' ? 'bg-bg-gray text-primary' : 'text-aux-gray'">
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                                    </svg>
                                </button>
                                <button @click="viewMode = 'list'" 
                                        class="p-2 rounded-md hover:bg-bg-gray transition-colors"
                                        :class="viewMode === 'list' ? 'bg-bg-gray text-primary' : 'text-aux-gray'">
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- 网格视图 -->
                        <div x-show="viewMode === 'grid'" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <template x-for="module in managementModules" :key="module.id">
                                <div class="module-card p-4 border border-border-gray rounded-lg hover:shadow-md cursor-pointer"
                                     @click="navigateToModule(module)">
                                    <div class="flex items-center space-x-3 mb-3">
                                        <div class="w-10 h-10 rounded-lg flex items-center justify-center text-white text-sm"
                                             :class="module.color" x-html="module.icon"></div>
                                        <div class="flex-1">
                                            <h3 class="text-sm font-medium text-title-gray" x-text="module.name"></h3>
                                            <p class="text-xs text-aux-gray" x-text="module.description"></p>
                                        </div>
                                    </div>
                                    <div class="flex items-center justify-between text-xs">
                                        <span class="text-aux-gray">状态:</span>
                                        <span class="px-2 py-1 rounded"
                                              :class="module.status === 'active' ? 'bg-green-100 text-green-800' : 
                                                     module.status === 'maintenance' ? 'bg-yellow-100 text-yellow-800' : 
                                                     'bg-gray-100 text-gray-800'"
                                              x-text="getStatusText(module.status)"></span>
                                    </div>
                                    <div class="flex items-center justify-between text-xs mt-2">
                                        <span class="text-aux-gray">使用频率:</span>
                                        <div class="flex items-center space-x-1">
                                            <div class="w-12 bg-gray-200 rounded-full h-1">
                                                <div class="bg-primary h-1 rounded-full transition-all duration-300" 
                                                     :style="`width: ${module.usage}%`"></div>
                                            </div>
                                            <span x-text="module.usage + '%'"></span>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>

                        <!-- 列表视图 -->
                        <div x-show="viewMode === 'list'" class="space-y-2">
                            <template x-for="module in managementModules" :key="module.id">
                                <div class="flex items-center p-3 border border-border-gray rounded-lg hover:bg-bg-gray cursor-pointer transition-colors"
                                     @click="navigateToModule(module)">
                                    <div class="w-8 h-8 rounded-lg flex items-center justify-center text-white text-sm mr-4"
                                         :class="module.color" x-html="module.icon"></div>
                                    <div class="flex-1">
                                        <div class="flex items-center justify-between">
                                            <h3 class="text-sm font-medium text-title-gray" x-text="module.name"></h3>
                                            <span class="text-xs px-2 py-1 rounded"
                                                  :class="module.status === 'active' ? 'bg-green-100 text-green-800' : 
                                                         module.status === 'maintenance' ? 'bg-yellow-100 text-yellow-800' : 
                                                         'bg-gray-100 text-gray-800'"
                                                  x-text="getStatusText(module.status)"></span>
                                        </div>
                                        <p class="text-xs text-aux-gray mt-1" x-text="module.description"></p>
                                    </div>
                                    <div class="ml-4">
                                        <svg class="w-5 h-5 text-aux-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>

                <!-- 右侧信息面板 -->
                <div class="space-y-6">
                    <!-- 最近活动 -->
                    <div class="bg-white rounded-lg shadow-sm border border-border-gray p-6">
                        <h3 class="text-lg font-medium text-title-gray mb-4">最近活动</h3>
                        <div class="space-y-3 max-h-80 overflow-y-auto">
                            <template x-for="activity in recentActivities" :key="activity.id">
                                <div class="activity-item p-3 rounded-md">
                                    <div class="flex items-start space-x-3">
                                        <div class="w-8 h-8 rounded-full flex items-center justify-center text-white text-xs"
                                             :class="activity.type === 'user' ? 'bg-primary' : 
                                                    activity.type === 'contract' ? 'bg-success' : 
                                                    activity.type === 'permission' ? 'bg-warning' : 
                                                    'bg-aux-gray'">
                                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                        <div class="flex-1">
                                            <div class="text-sm text-title-gray" x-text="activity.title"></div>
                                            <div class="text-xs text-aux-gray mt-1" x-text="activity.description"></div>
                                            <div class="flex items-center justify-between mt-2">
                                                <span class="text-xs text-aux-gray" x-text="activity.user"></span>
                                                <span class="text-xs text-aux-gray" x-text="activity.time"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>

                    <!-- 快速操作 -->
                    <div class="bg-white rounded-lg shadow-sm border border-border-gray p-6">
                        <h3 class="text-lg font-medium text-title-gray mb-4">快速操作</h3>
                        <div class="space-y-3">
                            <template x-for="action in quickActions" :key="action.id">
                                <button @click="performQuickAction(action)" 
                                        class="w-full flex items-center p-3 border border-border-gray rounded-md hover:bg-bg-gray transition-colors text-left">
                                    <div class="w-8 h-8 rounded-lg flex items-center justify-center text-white text-sm mr-3"
                                         :class="action.color" x-html="action.icon"></div>
                                    <div class="flex-1">
                                        <div class="text-sm font-medium text-title-gray" x-text="action.name"></div>
                                        <div class="text-xs text-aux-gray" x-text="action.description"></div>
                                    </div>
                                    <svg class="w-4 h-4 text-aux-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </button>
                            </template>
                        </div>
                    </div>

                    <!-- 系统状态 -->
                    <div class="bg-white rounded-lg shadow-sm border border-border-gray p-6">
                        <h3 class="text-lg font-medium text-title-gray mb-4">系统状态</h3>
                        <div class="space-y-4">
                            <template x-for="service in systemServices" :key="service.id">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-3 h-3 rounded-full"
                                             :class="service.status === 'online' ? 'bg-success' : 
                                                    service.status === 'maintenance' ? 'bg-warning' : 
                                                    'bg-error'"></div>
                                        <span class="text-sm text-title-gray" x-text="service.name"></span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span class="text-xs text-aux-gray" x-text="service.uptime"></span>
                                        <span class="text-xs px-2 py-1 rounded"
                                              :class="service.status === 'online' ? 'bg-green-100 text-green-800' : 
                                                     service.status === 'maintenance' ? 'bg-yellow-100 text-yellow-800' : 
                                                     'bg-red-100 text-red-800'"
                                              x-text="getServiceStatusText(service.status)"></span>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 使用统计图表 -->
            <div class="mt-8 bg-white rounded-lg shadow-sm border border-border-gray p-6">
                <h3 class="text-lg font-medium text-title-gray mb-4">模块使用统计</h3>
                <div class="h-64">
                    <canvas id="usageChart"></canvas>
                </div>
            </div>
        </main>
    </div>

    <script>
        function dashboardApp() {
            return {
                globalSearchQuery: '',
                viewMode: 'grid',
                unreadNotifications: 3,

                systemStatus: {
                    activeUsers: 24,
                    completedTasks: 156,
                    pendingApprovals: 8,
                    systemHealth: 98
                },

                managementModules: [
                    {
                        id: 'login',
                        name: '用户认证',
                        description: '登录注册及身份验证',
                        color: 'bg-primary',
                        icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>',
                        status: 'active',
                        usage: 95,
                        url: 'login.html'
                    },
                    {
                        id: 'org',
                        name: '组织管理',
                        description: '组织架构与部门管理',
                        color: 'bg-success',
                        icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>',
                        status: 'active',
                        usage: 85,
                        url: 'organization-management.html'
                    },
                    {
                        id: 'user',
                        name: '用户管理',
                        description: '用户账户与权限管理',
                        color: 'bg-warning',
                        icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>',
                        status: 'active',
                        usage: 78,
                        url: 'user-management.html'
                    },
                    {
                        id: 'role',
                        name: '角色权限',
                        description: '角色定义与权限分配',
                        color: 'bg-error',
                        icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>',
                        status: 'active',
                        usage: 72,
                        url: 'role-permission-management.html'
                    },
                    {
                        id: 'data-permission',
                        name: '数据权限',
                        description: '数据访问权限控制',
                        color: 'bg-purple-500',
                        icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>',
                        status: 'active',
                        usage: 68,
                        url: 'data-permission-management.html'
                    },
                    {
                        id: 'dict',
                        name: '数据字典',
                        description: '系统数据字典管理',
                        color: 'bg-indigo-500',
                        icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>',
                        status: 'active',
                        usage: 45,
                        url: 'data-dictionary-management.html'
                    },
                    {
                        id: 'contract',
                        name: '合同档案',
                        description: '合同档案管理',
                        color: 'bg-green-600',
                        icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>',
                        status: 'active',
                        usage: 82,
                        url: 'contract-archive-management.html'
                    },
                    {
                        id: 'tracking',
                        name: '执行跟踪',
                        description: '合同执行状态跟踪',
                        color: 'bg-blue-600',
                        icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>',
                        status: 'active',
                        usage: 76,
                        url: 'contract-execution-tracking.html'
                    },
                    {
                        id: 'unit',
                        name: '计量单位',
                        description: '计量单位管理',
                        color: 'bg-yellow-600',
                        icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>',
                        status: 'active',
                        usage: 58,
                        url: 'unit-management.html'
                    }
                ],

                notifications: [
                    {
                        id: 1,
                        type: 'warning',
                        title: '合同即将到期',
                        message: '华美商务大厦项目合同将于30天后到期，请及时处理续约事宜',
                        time: '5分钟前',
                        read: false
                    },
                    {
                        id: 2,
                        type: 'info',
                        title: '系统维护通知',
                        message: '系统将于今晚23:00-01:00进行例行维护，期间部分功能可能受影响',
                        time: '1小时前',
                        read: false
                    },
                    {
                        id: 3,
                        type: 'error',
                        title: '权限异常提醒',
                        message: '检测到用户"张三"尝试访问未授权的数据权限模块',
                        time: '2小时前',
                        read: false
                    }
                ],

                recentActivities: [
                    {
                        id: 1,
                        type: 'user',
                        title: '新用户注册',
                        description: '用户"李工程师"完成注册并通过审核',
                        user: '系统管理员',
                        time: '10分钟前'
                    },
                    {
                        id: 2,
                        type: 'contract',
                        title: '合同状态更新',
                        description: 'CT2024001合同执行进度更新至65%',
                        user: '张项目经理',
                        time: '30分钟前'
                    },
                    {
                        id: 3,
                        type: 'permission',
                        title: '权限配置变更',
                        description: '更新了销售部门的数据访问权限范围',
                        user: '权限管理员',
                        time: '1小时前'
                    },
                    {
                        id: 4,
                        type: 'system',
                        title: '数据字典更新',
                        description: '新增玻璃类型分类数据项',
                        user: '数据管理员',
                        time: '2小时前'
                    }
                ],

                quickActions: [
                    {
                        id: 1,
                        name: '新建用户',
                        description: '快速创建系统用户账户',
                        color: 'bg-primary',
                        icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>',
                        action: 'createUser'
                    },
                    {
                        id: 2,
                        name: '权限检查',
                        description: '检查用户权限配置',
                        color: 'bg-warning',
                        icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>',
                        action: 'checkPermissions'
                    },
                    {
                        id: 3,
                        name: '系统备份',
                        description: '执行系统数据备份',
                        color: 'bg-success',
                        icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>',
                        action: 'systemBackup'
                    },
                    {
                        id: 4,
                        name: '生成报告',
                        description: '生成系统使用统计报告',
                        color: 'bg-purple-500',
                        icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>',
                        action: 'generateReport'
                    }
                ],

                systemServices: [
                    { id: 1, name: '认证服务', status: 'online', uptime: '99.8%' },
                    { id: 2, name: '数据库服务', status: 'online', uptime: '99.9%' },
                    { id: 3, name: '文件服务', status: 'online', uptime: '99.5%' },
                    { id: 4, name: '消息服务', status: 'maintenance', uptime: '98.2%' },
                    { id: 5, name: '搜索服务', status: 'online', uptime: '99.7%' }
                ],

                // 计算属性
                get searchSuggestions() {
                    if (!this.globalSearchQuery) return [];
                    
                    const query = this.globalSearchQuery.toLowerCase();
                    return this.managementModules.filter(module => 
                        module.name.toLowerCase().includes(query) ||
                        module.description.toLowerCase().includes(query)
                    ).slice(0, 5);
                },

                // 初始化
                init() {
                    this.$nextTick(() => {
                        this.initUsageChart();
                    });

                    // 模拟实时数据更新
                    setInterval(() => {
                        this.systemStatus.activeUsers = Math.floor(Math.random() * 10) + 20;
                        this.systemStatus.completedTasks = Math.floor(Math.random() * 20) + 150;
                    }, 30000);
                },

                // 方法
                navigateToModule(module) {
                    if (module.url) {
                        window.location.href = module.url;
                    } else {
                        alert(`即将进入${module.name}模块`);
                    }
                },

                highlightSearch(text) {
                    if (!this.globalSearchQuery) return text;
                    
                    const regex = new RegExp(`(${this.globalSearchQuery})`, 'gi');
                    return text.replace(regex, '<span class="search-highlight">$1</span>');
                },

                getStatusText(status) {
                    const statusMap = {
                        'active': '正常',
                        'maintenance': '维护中',
                        'disabled': '已禁用'
                    };
                    return statusMap[status] || '未知';
                },

                getServiceStatusText(status) {
                    const statusMap = {
                        'online': '在线',
                        'maintenance': '维护',
                        'offline': '离线'
                    };
                    return statusMap[status] || '未知';
                },

                performQuickAction(action) {
                    switch (action.action) {
                        case 'createUser':
                            window.location.href = 'user-management.html';
                            break;
                        case 'checkPermissions':
                            window.location.href = 'data-permission-management.html';
                            break;
                        case 'systemBackup':
                            alert('系统备份功能 - 实际应用中会执行备份操作');
                            break;
                        case 'generateReport':
                            alert('报告生成功能 - 实际应用中会生成统计报告');
                            break;
                        default:
                            alert(`执行快速操作: ${action.name}`);
                    }
                },

                initUsageChart() {
                    const ctx = document.getElementById('usageChart');
                    if (ctx) {
                        new Chart(ctx, {
                            type: 'bar',
                            data: {
                                labels: this.managementModules.map(m => m.name),
                                datasets: [{
                                    label: '使用频率 (%)',
                                    data: this.managementModules.map(m => m.usage),
                                    backgroundColor: [
                                        '#1890FF', '#52C41A', '#FAAD14', '#F5222D', 
                                        '#722ED1', '#13C2C2', '#A0D911', '#FA8C16',
                                        '#EB2F96'
                                    ],
                                    borderRadius: 4
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: {
                                        display: false
                                    }
                                },
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        max: 100,
                                        ticks: {
                                            callback: function(value) {
                                                return value + '%';
                                            }
                                        }
                                    },
                                    x: {
                                        ticks: {
                                            maxRotation: 45,
                                            minRotation: 45
                                        }
                                    }
                                }
                            }
                        });
                    }
                }
            }
        }
    </script>
</body>
</html>