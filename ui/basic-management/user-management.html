<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 玻璃深加工ERP系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1890FF',
                        success: '#52C41A',
                        warning: '#FAAD14',
                        error: '#F5222D',
                        'title-gray': '#262626',
                        'body-gray': '#595959',
                        'aux-gray': '#8C8C8C',
                        'disabled-gray': '#BFBFBF',
                        'border-gray': '#D9D9D9',
                        'bg-gray': '#FAFAFA'
                    },
                    fontFamily: {
                        'chinese': ['PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', sans-serif;
        }
        .table-row:hover {
            background-color: #f8fafc;
        }
        .avatar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="bg-bg-gray font-chinese">
    <div x-data="userManagementApp()" class="min-h-screen">
        <!-- 顶部导航栏 -->
        <header class="bg-white shadow-sm border-b border-border-gray h-16">
            <div class="flex items-center justify-between px-6 h-full">
                <div class="flex items-center space-x-4">
                    <h1 class="text-xl font-medium text-title-gray">用户管理</h1>
                    <span class="text-aux-gray text-sm">/ 基础管理</span>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="text-aux-gray hover:text-title-gray" @click="showHelp = true">
                        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </button>
                    <span class="text-aux-gray">admin</span>
                </div>
            </div>
        </header>

        <div class="flex h-[calc(100vh-64px)]">
            <!-- 主内容区域 -->
            <main class="flex-1 overflow-hidden">
                <div class="h-full flex flex-col">
                    <!-- 页面标题区 -->
                    <div class="bg-white border-b border-border-gray px-6 py-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <h2 class="text-lg font-medium text-title-gray">用户列表</h2>
                                <p class="text-sm text-aux-gray mt-1">管理系统用户信息、权限和状态</p>
                            </div>
                            <div class="flex space-x-3">
                                <button 
                                    @click="exportUsers"
                                    class="px-4 py-2 bg-white border border-border-gray text-body-gray text-sm font-medium rounded-md hover:bg-gray-50 transition-colors"
                                >
                                    <svg class="h-4 w-4 mr-2 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                    </svg>
                                    导出
                                </button>
                                <button 
                                    @click="showImportModal = true"
                                    class="px-4 py-2 bg-white border border-border-gray text-body-gray text-sm font-medium rounded-md hover:bg-gray-50 transition-colors"
                                >
                                    <svg class="h-4 w-4 mr-2 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"/>
                                    </svg>
                                    批量导入
                                </button>
                                <button 
                                    @click="showUserModal = true; editingUser = null; resetUserForm()"
                                    class="px-4 py-2 bg-primary text-white text-sm font-medium rounded-md hover:bg-blue-600 transition-colors"
                                >
                                    <svg class="h-4 w-4 mr-2 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                    </svg>
                                    新增用户
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 搜索和筛选区域 -->
                    <div class="bg-white border-b border-border-gray px-6 py-4">
                        <div class="flex items-center space-x-4">
                            <div class="flex-1">
                                <div class="relative">
                                    <input
                                        type="text"
                                        x-model="searchQuery"
                                        @input="handleSearch"
                                        placeholder="搜索用户姓名、工号、邮箱..."
                                        class="w-full pl-10 pr-4 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                    >
                                    <svg class="absolute left-3 top-2.5 h-5 w-5 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                    </svg>
                                </div>
                            </div>
                            
                            <div class="flex space-x-3">
                                <select 
                                    x-model="filterDepartment"
                                    @change="applyFilters"
                                    class="px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                >
                                    <option value="">全部部门</option>
                                    <option value="生产部">生产部</option>
                                    <option value="销售部">销售部</option>
                                    <option value="技术部">技术部</option>
                                    <option value="财务部">财务部</option>
                                </select>
                                
                                <select 
                                    x-model="filterStatus"
                                    @change="applyFilters"
                                    class="px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                >
                                    <option value="">全部状态</option>
                                    <option value="active">启用</option>
                                    <option value="inactive">禁用</option>
                                    <option value="pending">待激活</option>
                                </select>
                                
                                <button 
                                    @click="resetFilters"
                                    class="px-3 py-2 text-aux-gray hover:text-title-gray border border-border-gray rounded-md hover:bg-gray-50 transition-colors"
                                >
                                    重置
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 批量操作栏 -->
                    <div x-show="selectedUsers.length > 0" class="bg-blue-50 border-b border-blue-200 px-6 py-3">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <span class="text-primary font-medium" x-text="`已选择 ${selectedUsers.length} 个用户`"></span>
                                <button 
                                    @click="selectedUsers = []"
                                    class="text-aux-gray hover:text-title-gray text-sm"
                                >
                                    清除选择
                                </button>
                            </div>
                            <div class="flex space-x-2">
                                <button 
                                    @click="batchOperation('enable')"
                                    class="px-3 py-1.5 bg-success text-white text-sm rounded-md hover:bg-green-600 transition-colors"
                                >
                                    批量启用
                                </button>
                                <button 
                                    @click="batchOperation('disable')"
                                    class="px-3 py-1.5 bg-warning text-white text-sm rounded-md hover:bg-yellow-600 transition-colors"
                                >
                                    批量禁用
                                </button>
                                <button 
                                    @click="batchOperation('delete')"
                                    class="px-3 py-1.5 bg-error text-white text-sm rounded-md hover:bg-red-600 transition-colors"
                                >
                                    批量删除
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 用户列表 -->
                    <div class="flex-1 overflow-auto">
                        <div class="bg-white">
                            <table class="min-w-full divide-y divide-border-gray">
                                <thead class="bg-bg-gray">
                                    <tr>
                                        <th class="px-6 py-3 text-left">
                                            <input 
                                                type="checkbox"
                                                @change="toggleAllUsers"
                                                :checked="selectedUsers.length === filteredUsers.length && filteredUsers.length > 0"
                                                :indeterminate="selectedUsers.length > 0 && selectedUsers.length < filteredUsers.length"
                                                class="h-4 w-4 text-primary focus:ring-primary border-border-gray rounded"
                                            >
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider cursor-pointer" @click="sortBy('name')">
                                            用户信息
                                            <svg x-show="sortField === 'name'" :class="sortDirection === 'asc' ? '' : 'transform rotate-180'" class="inline h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"/>
                                            </svg>
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider cursor-pointer" @click="sortBy('department')">
                                            部门职位
                                            <svg x-show="sortField === 'department'" :class="sortDirection === 'asc' ? '' : 'transform rotate-180'" class="inline h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"/>
                                            </svg>
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">
                                            联系信息
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider cursor-pointer" @click="sortBy('status')">
                                            状态
                                            <svg x-show="sortField === 'status'" :class="sortDirection === 'asc' ? '' : 'transform rotate-180'" class="inline h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"/>
                                            </svg>
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider cursor-pointer" @click="sortBy('lastLoginAt')">
                                            最后登录
                                            <svg x-show="sortField === 'lastLoginAt'" :class="sortDirection === 'asc' ? '' : 'transform rotate-180'" class="inline h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"/>
                                            </svg>
                                        </th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-aux-gray uppercase tracking-wider">
                                            操作
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-border-gray">
                                    <template x-for="user in paginatedUsers" :key="user.id">
                                        <tr :class="selectedUsers.includes(user.id) ? 'bg-blue-50' : ''" class="table-row">
                                            <td class="px-6 py-4">
                                                <input 
                                                    type="checkbox"
                                                    :value="user.id"
                                                    x-model="selectedUsers"
                                                    class="h-4 w-4 text-primary focus:ring-primary border-border-gray rounded"
                                                >
                                            </td>
                                            <td class="px-6 py-4">
                                                <div class="flex items-center">
                                                    <div class="flex-shrink-0 h-10 w-10">
                                                        <div class="h-10 w-10 rounded-full avatar flex items-center justify-center">
                                                            <span class="text-white font-medium text-sm" x-text="user.name.charAt(0)"></span>
                                                        </div>
                                                    </div>
                                                    <div class="ml-4">
                                                        <div class="text-sm font-medium text-title-gray" x-text="user.name"></div>
                                                        <div class="text-sm text-aux-gray" x-text="'工号: ' + user.employeeId"></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4">
                                                <div class="text-sm text-title-gray" x-text="user.department"></div>
                                                <div class="text-sm text-aux-gray" x-text="user.position"></div>
                                            </td>
                                            <td class="px-6 py-4">
                                                <div class="text-sm text-title-gray" x-text="user.email"></div>
                                                <div class="text-sm text-aux-gray" x-text="user.phone"></div>
                                            </td>
                                            <td class="px-6 py-4">
                                                <span 
                                                    class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                                    :class="user.status === 'active' ? 'bg-green-100 text-green-800' : 
                                                           user.status === 'inactive' ? 'bg-red-100 text-red-800' : 
                                                           'bg-yellow-100 text-yellow-800'"
                                                    x-text="getStatusLabel(user.status)"
                                                ></span>
                                            </td>
                                            <td class="px-6 py-4 text-sm text-aux-gray">
                                                <div x-text="user.lastLoginAt || '从未登录'"></div>
                                                <div x-show="user.lastLoginAt" class="text-xs" x-text="getRelativeTime(user.lastLoginAt)"></div>
                                            </td>
                                            <td class="px-6 py-4 text-right text-sm font-medium">
                                                <div class="flex justify-end space-x-2">
                                                    <button 
                                                        @click="viewUser(user)"
                                                        class="text-primary hover:text-blue-600"
                                                    >
                                                        查看
                                                    </button>
                                                    <button 
                                                        @click="editUser(user)"
                                                        class="text-primary hover:text-blue-600"
                                                    >
                                                        编辑
                                                    </button>
                                                    <button 
                                                        @click="resetPassword(user)"
                                                        class="text-warning hover:text-yellow-600"
                                                    >
                                                        重置密码
                                                    </button>
                                                    <button 
                                                        @click="toggleUserStatus(user)"
                                                        :class="user.status === 'active' ? 'text-error hover:text-red-600' : 'text-success hover:text-green-600'"
                                                        x-text="user.status === 'active' ? '禁用' : '启用'"
                                                    >
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </template>
                                </tbody>
                            </table>
                            
                            <!-- 空状态 -->
                            <div x-show="filteredUsers.length === 0" class="text-center py-16">
                                <svg class="mx-auto h-16 w-16 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                                </svg>
                                <p class="mt-4 text-aux-gray">
                                    <span x-show="searchQuery || filterDepartment || filterStatus">未找到匹配的用户</span>
                                    <span x-show="!searchQuery && !filterDepartment && !filterStatus">暂无用户数据</span>
                                </p>
                                <button 
                                    x-show="searchQuery || filterDepartment || filterStatus"
                                    @click="resetFilters"
                                    class="mt-2 text-primary hover:text-blue-600"
                                >
                                    清除筛选条件
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 分页 -->
                    <div class="bg-white border-t border-border-gray px-6 py-3">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-aux-gray">
                                显示第 <span x-text="(currentPage - 1) * pageSize + 1"></span> 到 <span x-text="Math.min(currentPage * pageSize, filteredUsers.length)"></span> 条，
                                共 <span x-text="filteredUsers.length"></span> 条记录
                            </div>
                            <div class="flex items-center space-x-2">
                                <button 
                                    @click="changePage(currentPage - 1)"
                                    :disabled="currentPage === 1"
                                    :class="currentPage === 1 ? 'text-disabled-gray cursor-not-allowed' : 'text-aux-gray hover:text-title-gray'"
                                    class="px-2 py-1"
                                >
                                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                                    </svg>
                                </button>
                                
                                <template x-for="page in visiblePages" :key="page">
                                    <button 
                                        @click="changePage(page)"
                                        :class="page === currentPage ? 'bg-primary text-white' : 'text-aux-gray hover:text-title-gray hover:bg-gray-50'"
                                        class="px-3 py-1 border border-border-gray rounded text-sm"
                                        x-text="page"
                                    ></button>
                                </template>
                                
                                <button 
                                    @click="changePage(currentPage + 1)"
                                    :disabled="currentPage === totalPages"
                                    :class="currentPage === totalPages ? 'text-disabled-gray cursor-not-allowed' : 'text-aux-gray hover:text-title-gray'"
                                    class="px-2 py-1"
                                >
                                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <!-- 新增/编辑用户模态框 -->
        <div x-show="showUserModal" 
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
             @click="showUserModal = false">
            
            <div class="relative top-10 mx-auto p-5 border w-[800px] shadow-lg rounded-md bg-white max-h-[90vh] overflow-y-auto"
                 @click.stop>
                <div class="mt-3">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-medium text-title-gray" x-text="editingUser ? '编辑用户' : '新增用户'"></h3>
                        <button @click="showUserModal = false" class="text-aux-gray hover:text-title-gray">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                    
                    <form @submit.prevent="submitUserForm" class="space-y-6">
                        <!-- 基本信息 -->
                        <div class="border-b border-border-gray pb-6">
                            <h4 class="text-md font-medium text-title-gray mb-4">基本信息</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="user-name" class="block text-sm font-medium text-title-gray mb-2">
                                        姓名 <span class="text-error">*</span>
                                    </label>
                                    <input
                                        id="user-name"
                                        type="text"
                                        x-model="userForm.name"
                                        class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm placeholder-aux-gray focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                        placeholder="请输入用户姓名"
                                        required
                                    >
                                </div>
                                
                                <div>
                                    <label for="user-employee-id" class="block text-sm font-medium text-title-gray mb-2">
                                        工号 <span class="text-error">*</span>
                                    </label>
                                    <input
                                        id="user-employee-id"
                                        type="text"
                                        x-model="userForm.employeeId"
                                        class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm placeholder-aux-gray focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                        placeholder="请输入工号"
                                        required
                                    >
                                </div>
                                
                                <div>
                                    <label for="user-email" class="block text-sm font-medium text-title-gray mb-2">
                                        邮箱 <span class="text-error">*</span>
                                    </label>
                                    <input
                                        id="user-email"
                                        type="email"
                                        x-model="userForm.email"
                                        class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm placeholder-aux-gray focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                        placeholder="请输入邮箱地址"
                                        required
                                    >
                                </div>
                                
                                <div>
                                    <label for="user-phone" class="block text-sm font-medium text-title-gray mb-2">
                                        手机号
                                    </label>
                                    <input
                                        id="user-phone"
                                        type="tel"
                                        x-model="userForm.phone"
                                        class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm placeholder-aux-gray focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                        placeholder="请输入手机号"
                                    >
                                </div>
                            </div>
                        </div>

                        <!-- 组织信息 -->
                        <div class="border-b border-border-gray pb-6">
                            <h4 class="text-md font-medium text-title-gray mb-4">组织信息</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="user-department" class="block text-sm font-medium text-title-gray mb-2">
                                        部门 <span class="text-error">*</span>
                                    </label>
                                    <select
                                        id="user-department"
                                        x-model="userForm.department"
                                        class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                        required
                                    >
                                        <option value="">请选择部门</option>
                                        <option value="生产部">生产部</option>
                                        <option value="销售部">销售部</option>
                                        <option value="技术部">技术部</option>
                                        <option value="财务部">财务部</option>
                                        <option value="行政部">行政部</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label for="user-position" class="block text-sm font-medium text-title-gray mb-2">
                                        职位
                                    </label>
                                    <input
                                        id="user-position"
                                        type="text"
                                        x-model="userForm.position"
                                        class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm placeholder-aux-gray focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                        placeholder="请输入职位"
                                    >
                                </div>
                                
                                <div>
                                    <label for="user-manager" class="block text-sm font-medium text-title-gray mb-2">
                                        直属上级
                                    </label>
                                    <select
                                        id="user-manager"
                                        x-model="userForm.managerId"
                                        class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                    >
                                        <option value="">请选择直属上级</option>
                                        <template x-for="user in users.filter(u => u.id !== editingUser?.id)" :key="user.id">
                                            <option :value="user.id" x-text="user.name + ' - ' + user.department"></option>
                                        </template>
                                    </select>
                                </div>
                                
                                <div>
                                    <label for="user-entry-date" class="block text-sm font-medium text-title-gray mb-2">
                                        入职日期
                                    </label>
                                    <input
                                        id="user-entry-date"
                                        type="date"
                                        x-model="userForm.entryDate"
                                        class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                    >
                                </div>
                            </div>
                        </div>

                        <!-- 账号设置 -->
                        <div>
                            <h4 class="text-md font-medium text-title-gray mb-4">账号设置</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="user-status" class="block text-sm font-medium text-title-gray mb-2">
                                        账号状态 <span class="text-error">*</span>
                                    </label>
                                    <select
                                        id="user-status"
                                        x-model="userForm.status"
                                        class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                        required
                                    >
                                        <option value="active">启用</option>
                                        <option value="inactive">禁用</option>
                                        <option value="pending">待激活</option>
                                    </select>
                                </div>
                                
                                <div x-show="!editingUser">
                                    <label for="user-password" class="block text-sm font-medium text-title-gray mb-2">
                                        初始密码 <span class="text-error">*</span>
                                    </label>
                                    <input
                                        id="user-password"
                                        type="password"
                                        x-model="userForm.password"
                                        class="w-full px-3 py-2 border border-border-gray rounded-md shadow-sm placeholder-aux-gray focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                        placeholder="请输入初始密码"
                                        :required="!editingUser"
                                    >
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <div class="flex items-start">
                                    <input
                                        id="send-welcome-email"
                                        type="checkbox"
                                        x-model="userForm.sendWelcomeEmail"
                                        class="h-4 w-4 text-primary focus:ring-primary border-border-gray rounded mt-1"
                                    >
                                    <label for="send-welcome-email" class="ml-2 block text-sm text-body-gray">
                                        发送欢迎邮件（包含登录信息和系统使用指南）
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex justify-end space-x-3 pt-4 border-t border-border-gray">
                            <button
                                type="button"
                                @click="showUserModal = false"
                                class="px-4 py-2 text-sm font-medium text-body-gray bg-white border border-border-gray rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                            >
                                取消
                            </button>
                            <button
                                type="submit"
                                class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
                            >
                                <span x-text="editingUser ? '更新用户' : '创建用户'"></span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 用户详情模态框 -->
        <div x-show="showDetailModal" 
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
             @click="showDetailModal = false">
            
            <div class="relative top-10 mx-auto p-5 border w-[600px] shadow-lg rounded-md bg-white max-h-[90vh] overflow-y-auto"
                 @click.stop>
                <div class="mt-3" x-show="viewingUser">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-medium text-title-gray">用户详情</h3>
                        <button @click="showDetailModal = false" class="text-aux-gray hover:text-title-gray">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                    
                    <div class="space-y-6">
                        <!-- 用户头像和基本信息 -->
                        <div class="text-center">
                            <div class="mx-auto h-20 w-20 rounded-full avatar flex items-center justify-center mb-4">
                                <span class="text-white font-bold text-2xl" x-text="viewingUser?.name?.charAt(0)"></span>
                            </div>
                            <h4 class="text-xl font-medium text-title-gray" x-text="viewingUser?.name"></h4>
                            <p class="text-aux-gray" x-text="viewingUser?.position + ' · ' + viewingUser?.department"></p>
                        </div>

                        <!-- 详细信息 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-body-gray mb-1">工号</label>
                                <p class="text-title-gray" x-text="viewingUser?.employeeId"></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-body-gray mb-1">邮箱</label>
                                <p class="text-title-gray" x-text="viewingUser?.email"></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-body-gray mb-1">手机号</label>
                                <p class="text-title-gray" x-text="viewingUser?.phone || '未设置'"></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-body-gray mb-1">入职日期</label>
                                <p class="text-title-gray" x-text="viewingUser?.entryDate || '未设置'"></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-body-gray mb-1">账号状态</label>
                                <span 
                                    class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                    :class="viewingUser?.status === 'active' ? 'bg-green-100 text-green-800' : 
                                           viewingUser?.status === 'inactive' ? 'bg-red-100 text-red-800' : 
                                           'bg-yellow-100 text-yellow-800'"
                                    x-text="getStatusLabel(viewingUser?.status)"
                                ></span>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-body-gray mb-1">最后登录</label>
                                <p class="text-title-gray" x-text="viewingUser?.lastLoginAt || '从未登录'"></p>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="flex justify-end space-x-3 pt-4 border-t border-border-gray">
                            <button
                                @click="editUser(viewingUser); showDetailModal = false"
                                class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
                            >
                                编辑用户
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 批量导入模态框 -->
        <div x-show="showImportModal" 
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
             @click="showImportModal = false">
            
            <div class="relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white"
                 @click.stop>
                <div class="mt-3">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-medium text-title-gray">批量导入用户</h3>
                        <button @click="showImportModal = false" class="text-aux-gray hover:text-title-gray">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                    
                    <div class="space-y-4">
                        <div class="border-2 border-dashed border-border-gray rounded-lg p-6 text-center">
                            <svg class="mx-auto h-12 w-12 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"/>
                            </svg>
                            <p class="mt-2 text-sm text-aux-gray">点击上传或拖拽Excel文件到此处</p>
                            <input type="file" accept=".xlsx,.xls" class="hidden" id="import-file">
                            <button 
                                onclick="document.getElementById('import-file').click()"
                                class="mt-2 px-4 py-2 bg-primary text-white text-sm rounded-md hover:bg-blue-600 transition-colors"
                            >
                                选择文件
                            </button>
                        </div>
                        
                        <div class="text-sm text-aux-gray">
                            <p class="font-medium mb-2">导入说明：</p>
                            <ul class="list-disc list-inside space-y-1">
                                <li>支持Excel格式（.xlsx, .xls）</li>
                                <li>第一行必须为表头</li>
                                <li>必需字段：姓名、工号、邮箱、部门</li>
                                <li>可选字段：职位、手机号、入职日期</li>
                            </ul>
                        </div>
                        
                        <div class="flex justify-between items-center pt-4 border-t border-border-gray">
                            <button 
                                class="text-primary hover:text-blue-600 text-sm"
                                @click="downloadTemplate"
                            >
                                下载模板文件
                            </button>
                            <div class="space-x-3">
                                <button
                                    @click="showImportModal = false"
                                    class="px-4 py-2 text-sm font-medium text-body-gray bg-white border border-border-gray rounded-md hover:bg-gray-50"
                                >
                                    取消
                                </button>
                                <button
                                    @click="processImport"
                                    class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-blue-600 transition-colors"
                                >
                                    开始导入
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function userManagementApp() {
            return {
                // 用户数据
                users: [
                    {
                        id: 1,
                        name: '张三',
                        employeeId: 'EMP001',
                        email: '<EMAIL>',
                        phone: '13812345678',
                        department: '生产部',
                        position: '生产经理',
                        status: 'active',
                        entryDate: '2023-01-15',
                        lastLoginAt: '2024-07-30 09:30',
                        managerId: null
                    },
                    {
                        id: 2,
                        name: '李四',
                        employeeId: 'EMP002',
                        email: '<EMAIL>',
                        phone: '13812345679',
                        department: '销售部',
                        position: '销售专员',
                        status: 'active',
                        entryDate: '2023-02-01',
                        lastLoginAt: '2024-07-29 14:20',
                        managerId: null
                    },
                    {
                        id: 3,
                        name: '王五',
                        employeeId: 'EMP003',
                        email: '<EMAIL>',
                        phone: '13812345680',
                        department: '技术部',
                        position: '工艺工程师',
                        status: 'inactive',
                        entryDate: '2023-03-01',
                        lastLoginAt: '2024-07-25 16:45',
                        managerId: 1
                    },
                    {
                        id: 4,
                        name: '赵六',
                        employeeId: 'EMP004',
                        email: '<EMAIL>',
                        phone: '13812345681',
                        department: '财务部',
                        position: '会计',
                        status: 'pending',
                        entryDate: '2024-07-01',
                        lastLoginAt: null,
                        managerId: null
                    },
                    {
                        id: 5,
                        name: '孙七',
                        employeeId: 'EMP005',
                        email: '<EMAIL>',
                        phone: '13812345682',
                        department: '生产部',
                        position: '操作员',
                        status: 'active',
                        entryDate: '2023-06-15',
                        lastLoginAt: '2024-07-30 08:15',
                        managerId: 1
                    }
                ],

                // UI状态
                showUserModal: false,
                showDetailModal: false,
                showImportModal: false,
                showHelp: false,
                editingUser: null,
                viewingUser: null,
                selectedUsers: [],

                // 搜索和筛选
                searchQuery: '',
                filterDepartment: '',
                filterStatus: '',
                
                // 排序
                sortField: 'name',
                sortDirection: 'asc',

                // 分页
                currentPage: 1,
                pageSize: 10,

                // 表单数据
                userForm: {
                    name: '',
                    employeeId: '',
                    email: '',
                    phone: '',
                    department: '',
                    position: '',
                    status: 'active',
                    password: '',
                    entryDate: '',
                    managerId: null,
                    sendWelcomeEmail: true
                },

                // 计算属性
                get filteredUsers() {
                    let filtered = this.users;

                    // 搜索过滤
                    if (this.searchQuery) {
                        const query = this.searchQuery.toLowerCase();
                        filtered = filtered.filter(user => 
                            user.name.toLowerCase().includes(query) ||
                            user.employeeId.toLowerCase().includes(query) ||
                            user.email.toLowerCase().includes(query)
                        );
                    }

                    // 部门过滤
                    if (this.filterDepartment) {
                        filtered = filtered.filter(user => user.department === this.filterDepartment);
                    }

                    // 状态过滤
                    if (this.filterStatus) {
                        filtered = filtered.filter(user => user.status === this.filterStatus);
                    }

                    // 排序
                    filtered.sort((a, b) => {
                        let aVal = a[this.sortField];
                        let bVal = b[this.sortField];
                        
                        if (this.sortDirection === 'asc') {
                            return aVal < bVal ? -1 : aVal > bVal ? 1 : 0;
                        } else {
                            return aVal > bVal ? -1 : aVal < bVal ? 1 : 0;
                        }
                    });

                    return filtered;
                },

                get paginatedUsers() {
                    const start = (this.currentPage - 1) * this.pageSize;
                    const end = start + this.pageSize;
                    return this.filteredUsers.slice(start, end);
                },

                get totalPages() {
                    return Math.ceil(this.filteredUsers.length / this.pageSize);
                },

                get visiblePages() {
                    const pages = [];
                    const total = this.totalPages;
                    const current = this.currentPage;
                    
                    if (total <= 7) {
                        for (let i = 1; i <= total; i++) {
                            pages.push(i);
                        }
                    } else {
                        if (current <= 4) {
                            for (let i = 1; i <= 5; i++) {
                                pages.push(i);
                            }
                            pages.push('...');
                            pages.push(total);
                        } else if (current >= total - 3) {
                            pages.push(1);
                            pages.push('...');
                            for (let i = total - 4; i <= total; i++) {
                                pages.push(i);
                            }
                        } else {
                            pages.push(1);
                            pages.push('...');
                            for (let i = current - 1; i <= current + 1; i++) {
                                pages.push(i);
                            }
                            pages.push('...');
                            pages.push(total);
                        }
                    }
                    
                    return pages;
                },

                // 方法
                handleSearch() {
                    this.currentPage = 1;
                },

                applyFilters() {
                    this.currentPage = 1;
                },

                resetFilters() {
                    this.searchQuery = '';
                    this.filterDepartment = '';
                    this.filterStatus = '';
                    this.currentPage = 1;
                },

                sortBy(field) {
                    if (this.sortField === field) {
                        this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
                    } else {
                        this.sortField = field;
                        this.sortDirection = 'asc';
                    }
                },

                changePage(page) {
                    if (page >= 1 && page <= this.totalPages) {
                        this.currentPage = page;
                    }
                },

                toggleAllUsers() {
                    if (this.selectedUsers.length === this.filteredUsers.length) {
                        this.selectedUsers = [];
                    } else {
                        this.selectedUsers = this.filteredUsers.map(user => user.id);
                    }
                },

                getStatusLabel(status) {
                    const labels = {
                        'active': '启用',
                        'inactive': '禁用',
                        'pending': '待激活'
                    };
                    return labels[status] || status;
                },

                getRelativeTime(dateStr) {
                    const date = new Date(dateStr);
                    const now = new Date();
                    const diff = now - date;
                    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
                    
                    if (days === 0) return '今天';
                    if (days === 1) return '1天前';
                    if (days < 7) return `${days}天前`;
                    if (days < 30) return `${Math.floor(days / 7)}周前`;
                    return `${Math.floor(days / 30)}个月前`;
                },

                viewUser(user) {
                    this.viewingUser = user;
                    this.showDetailModal = true;
                },

                editUser(user) {
                    this.editingUser = user;
                    this.userForm = {
                        name: user.name,
                        employeeId: user.employeeId,
                        email: user.email,
                        phone: user.phone || '',
                        department: user.department,
                        position: user.position || '',
                        status: user.status,
                        password: '',
                        entryDate: user.entryDate || '',
                        managerId: user.managerId,
                        sendWelcomeEmail: false
                    };
                    this.showUserModal = true;
                },

                resetUserForm() {
                    this.userForm = {
                        name: '',
                        employeeId: '',
                        email: '',
                        phone: '',
                        department: '',
                        position: '',
                        status: 'active',
                        password: '',
                        entryDate: '',
                        managerId: null,
                        sendWelcomeEmail: true
                    };
                },

                async submitUserForm() {
                    try {
                        if (this.editingUser) {
                            // 更新用户
                            const index = this.users.findIndex(u => u.id === this.editingUser.id);
                            if (index !== -1) {
                                Object.assign(this.users[index], {
                                    name: this.userForm.name,
                                    employeeId: this.userForm.employeeId,
                                    email: this.userForm.email,
                                    phone: this.userForm.phone,
                                    department: this.userForm.department,
                                    position: this.userForm.position,
                                    status: this.userForm.status,
                                    entryDate: this.userForm.entryDate,
                                    managerId: this.userForm.managerId
                                });
                            }
                            alert('用户更新成功');
                        } else {
                            // 创建新用户
                            const newUser = {
                                id: Date.now(),
                                name: this.userForm.name,
                                employeeId: this.userForm.employeeId,
                                email: this.userForm.email,
                                phone: this.userForm.phone,
                                department: this.userForm.department,
                                position: this.userForm.position,
                                status: this.userForm.status,
                                entryDate: this.userForm.entryDate,
                                lastLoginAt: null,
                                managerId: this.userForm.managerId
                            };
                            
                            this.users.push(newUser);
                            
                            if (this.userForm.sendWelcomeEmail) {
                                alert('用户创建成功，欢迎邮件已发送');
                            } else {
                                alert('用户创建成功');
                            }
                        }
                        
                        this.showUserModal = false;
                        this.resetUserForm();
                        this.editingUser = null;
                    } catch (error) {
                        alert('操作失败：' + error.message);
                    }
                },

                toggleUserStatus(user) {
                    const newStatus = user.status === 'active' ? 'inactive' : 'active';
                    if (confirm(`确定要${newStatus === 'active' ? '启用' : '禁用'}用户"${user.name}"吗？`)) {
                        user.status = newStatus;
                        alert(`用户${newStatus === 'active' ? '启用' : '禁用'}成功`);
                    }
                },

                resetPassword(user) {
                    if (confirm(`确定要重置用户"${user.name}"的密码吗？新密码将发送到用户邮箱。`)) {
                        alert('密码重置成功，新密码已发送到用户邮箱');
                    }
                },

                batchOperation(operation) {
                    if (this.selectedUsers.length === 0) {
                        alert('请选择要操作的用户');
                        return;
                    }

                    const operationText = {
                        'enable': '启用',
                        'disable': '禁用',
                        'delete': '删除'
                    };

                    if (confirm(`确定要${operationText[operation]}选中的${this.selectedUsers.length}个用户吗？`)) {
                        if (operation === 'delete') {
                            this.users = this.users.filter(user => !this.selectedUsers.includes(user.id));
                        } else {
                            const status = operation === 'enable' ? 'active' : 'inactive';
                            this.users.forEach(user => {
                                if (this.selectedUsers.includes(user.id)) {
                                    user.status = status;
                                }
                            });
                        }
                        
                        this.selectedUsers = [];
                        alert(`批量${operationText[operation]}成功`);
                    }
                },

                exportUsers() {
                    alert('用户数据导出功能开发中...');
                },

                downloadTemplate() {
                    alert('模板文件下载功能开发中...');
                },

                processImport() {
                    alert('批量导入功能开发中...');
                },

                // 初始化
                init() {
                    // 组件初始化完成
                }
            }
        }
    </script>
</body>
</html>