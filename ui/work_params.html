<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工艺参数库管理 - PDM系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        [x-cloak] { display: none !important; }
        .tree-node {
            transition: all 0.2s ease;
        }
        .tree-node:hover {
            background-color: #f5f5f5;
        }
        .tree-node.selected {
            background-color: #E6F7FF;
            border-left: 3px solid #1890ff;
        }
        .parameter-card {
            transition: all 0.3s ease;
        }
        .parameter-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .formula-editor {
            font-family: 'Courier New', monospace;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 8px;
        }
        .quality-chart-container {
            position: relative;
            height: 250px;
        }
    </style>
</head>
<body class="bg-gray-50" x-data="parameterLibrary()">
    <!-- 顶部导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="ri-settings-3-line text-blue-600 text-2xl mr-2"></i>
                        <span class="text-xl font-semibold text-gray-800">工艺参数库管理</span>
                    </div>
                    <div class="hidden md:ml-6 md:flex md:space-x-8">
                        <a href="#" class="border-blue-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            参数库管理
                        </a>
                        <a href="#" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            参数模板
                        </a>
                        <a href="#" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            质量分析
                        </a>
                    </div>
                </div>
                <div class="flex items-center">
                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center" @click="showCreateTemplateModal = true">
                        <i class="ri-add-line mr-1"></i>
                        创建参数模板
                    </button>
                    <div class="ml-4 relative flex-shrink-0">
                        <div class="relative">
                            <input type="text" placeholder="搜索参数..." class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="ri-search-line text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主内容区 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex flex-col md:flex-row gap-6">
            <!-- 左侧区域：工序分类树和参数模板库 -->
            <div class="w-full md:w-1/4 bg-white rounded-lg shadow p-4">
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-3">工序分类</h3>
                    <div class="space-y-1">
                        <template x-for="category in processCategories" :key="category.id">
                            <div>
                                <div class="tree-node flex items-center justify-between p-2 rounded cursor-pointer" 
                                     :class="selectedCategory === category.id ? 'selected' : ''"
                                     @click="toggleCategory(category.id)">
                                    <div class="flex items-center">
                                        <i class="ri-arrow-right-s-line transition-transform" 
                                           :class="category.expanded ? 'rotate-90' : ''"></i>
                                        <i :class="category.icon" class="ml-1 mr-2"></i>
                                        <span x-text="category.name"></span>
                                    </div>
                                    <span class="text-xs bg-gray-200 text-gray-700 px-2 py-1 rounded-full" x-text="category.count"></span>
                                </div>
                                <div x-show="category.expanded" x-transition class="ml-6 mt-1 space-y-1">
                                    <template x-for="subcategory in category.subcategories" :key="subcategory.id">
                                        <div class="tree-node flex items-center justify-between p-2 rounded cursor-pointer"
                                             :class="selectedSubcategory === subcategory.id ? 'selected' : ''"
                                             @click="selectSubcategory(subcategory.id)">
                                            <div class="flex items-center">
                                                <i class="ri-subtract-line mr-2"></i>
                                                <span x-text="subcategory.name"></span>
                                            </div>
                                            <span class="text-xs bg-gray-200 text-gray-700 px-2 py-1 rounded-full" x-text="subcategory.count"></span>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>

                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-3">参数模板库</h3>
                    <div class="space-y-3">
                        <template x-for="template in parameterTemplates" :key="template.id">
                            <div class="parameter-card border border-gray-200 rounded-lg p-3 cursor-pointer"
                                 :class="selectedTemplate === template.id ? 'border-blue-500 bg-blue-50' : ''"
                                 @click="selectTemplate(template.id)">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h4 class="font-medium text-gray-900" x-text="template.name"></h4>
                                        <p class="text-sm text-gray-500 mt-1" x-text="template.description"></p>
                                    </div>
                                    <span class="text-xs px-2 py-1 rounded-full"
                                          :class="template.status === '生效' ? 'bg-green-100 text-green-800' : 
                                                  template.status === '草稿' ? 'bg-yellow-100 text-yellow-800' : 
                                                  'bg-gray-100 text-gray-800'"
                                          x-text="template.status"></span>
                                </div>
                                <div class="flex justify-between items-center mt-2">
                                    <div class="flex space-x-2">
                                        <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded" x-text="template.operation"></span>
                                        <span class="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded" x-text="template.parameterCount + '个参数'"></span>
                                    </div>
                                    <span class="text-xs text-gray-500" x-text="template.updatedAt"></span>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>

            <!-- 中间区域：参数列表和参数配置界面 -->
            <div class="w-full md:w-2/5 bg-white rounded-lg shadow p-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">参数配置</h3>
                    <button class="text-blue-600 hover:text-blue-800 text-sm flex items-center" @click="showParameterForm = true">
                        <i class="ri-add-line mr-1"></i>
                        添加参数
                    </button>
                </div>

                <!-- 参数列表 -->
                <div class="mb-6">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="text-md font-medium text-gray-700">参数列表</h4>
                        <div class="flex space-x-2">
                            <select class="text-sm border border-gray-300 rounded px-2 py-1">
                                <option>全部类型</option>
                                <option>数值型</option>
                                <option>文本型</option>
                                <option>枚举型</option>
                                <option>布尔型</option>
                            </select>
                        </div>
                    </div>
                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">参数名称</th>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单位</th>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <template x-for="param in parameters" :key="param.id">
                                    <tr class="hover:bg-gray-50" :class="selectedParameter === param.id ? 'bg-blue-50' : ''">
                                        <td class="px-4 py-3 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900" x-text="param.name"></div>
                                            <div class="text-sm text-gray-500" x-text="param.code"></div>
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                                                  :class="param.type === '数值' ? 'bg-blue-100 text-blue-800' : 
                                                          param.type === '文本' ? 'bg-green-100 text-green-800' : 
                                                          param.type === '枚举' ? 'bg-purple-100 text-purple-800' : 
                                                          'bg-yellow-100 text-yellow-800'"
                                                  x-text="param.type"></span>
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500" x-text="param.unit"></td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm font-medium">
                                            <button class="text-blue-600 hover:text-blue-900 mr-2" @click="editParameter(param.id)">
                                                <i class="ri-edit-line"></i>
                                            </button>
                                            <button class="text-red-600 hover:text-red-900" @click="deleteParameter(param.id)">
                                                <i class="ri-delete-bin-line"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 参数配置表单 -->
                <div x-show="showParameterForm" x-transition class="border border-gray-200 rounded-lg p-4 bg-gray-50">
                    <h4 class="text-md font-medium text-gray-700 mb-3">参数配置</h4>
                    <form @submit.prevent="saveParameter">
                        <div class="grid grid-cols-1 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">参数编码</label>
                                <input type="text" x-model="parameterForm.code" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">参数名称</label>
                                <input type="text" x-model="parameterForm.name" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">参数类型</label>
                                    <select x-model="parameterForm.type" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                        <option value="数值">数值</option>
                                        <option value="文本">文本</option>
                                        <option value="枚举">枚举</option>
                                        <option value="布尔">布尔</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">计量单位</label>
                                    <input type="text" x-model="parameterForm.unit" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>
                            <div x-show="parameterForm.type === '数值'" class="grid grid-cols-3 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">最小值</label>
                                    <input type="number" x-model="parameterForm.minValue" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">标准值</label>
                                    <input type="number" x-model="parameterForm.standardValue" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">最大值</label>
                                    <input type="number" x-model="parameterForm.maxValue" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>
                            <div x-show="parameterForm.type === '枚举'">
                                <label class="block text-sm font-medium text-gray-700 mb-1">枚举值</label>
                                <div class="space-y-2">
                                    <template x-for="(value, index) in parameterForm.enumValues" :key="index">
                                        <div class="flex items-center">
                                            <input type="text" x-model="parameterForm.enumValues[index]" class="flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                            <button type="button" class="ml-2 text-red-600 hover:text-red-800" @click="removeEnumValue(index)">
                                                <i class="ri-delete-bin-line"></i>
                                            </button>
                                        </div>
                                    </template>
                                    <button type="button" class="text-blue-600 hover:text-blue-800 text-sm flex items-center" @click="addEnumValue">
                                        <i class="ri-add-line mr-1"></i>
                                        添加枚举值
                                    </button>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">计算公式</label>
                                <div class="formula-editor" contenteditable="true" x-text="parameterForm.formula"></div>
                                <p class="text-xs text-gray-500 mt-1">支持变量: length(长度), width(宽度), thickness(厚度)</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">关联产品特征</label>
                                <div class="flex flex-wrap gap-2">
                                    <template x-for="feature in productFeatures" :key="feature.id">
                                        <label class="inline-flex items-center">
                                            <input type="checkbox" :value="feature.id" x-model="parameterForm.features" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                            <span class="ml-2 text-sm text-gray-700" x-text="feature.name"></span>
                                        </label>
                                    </template>
                                </div>
                            </div>
                            <div class="flex justify-end space-x-3">
                                <button type="button" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50" @click="showParameterForm = false">
                                    取消
                                </button>
                                <button type="submit" class="px-4 py-2 bg-blue-600 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    保存参数
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 右侧区域：参数详情、计算预览、历史版本、质量分析 -->
            <div class="w-full md:w-1/3 bg-white rounded-lg shadow p-4">
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-3">参数计算器</h3>
                    <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
                        <h4 class="text-md font-medium text-gray-700 mb-3">输入产品特征</h4>
                        <div class="space-y-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">长度 (mm)</label>
                                <input type="number" x-model="calculator.length" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">宽度 (mm)</label>
                                <input type="number" x-model="calculator.width" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">厚度 (mm)</label>
                                <input type="number" x-model="calculator.thickness" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">材质</label>
                                <select x-model="calculator.material" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                    <option value="普通玻璃">普通玻璃</option>
                                    <option value="钢化玻璃">钢化玻璃</option>
                                    <option value="夹层玻璃">夹层玻璃</option>
                                    <option value="中空玻璃">中空玻璃</option>
                                </select>
                            </div>
                            <button class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium" @click="calculateParameters">
                                计算工艺参数
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 计算结果 -->
                <div x-show="calculator.showResults" x-transition class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-3">计算结果</h3>
                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">参数名称</th>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">计算值</th>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单位</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <template x-for="result in calculator.results" :key="result.name">
                                    <tr>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900" x-text="result.name"></td>
                                        <td class="px-4 py-3 whitespace-nowrap">
                                            <input type="number" x-model="result.value" class="w-20 border border-gray-300 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500" x-text="result.unit"></td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>
                    <div class="mt-3 flex justify-end">
                        <button class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center">
                            <i class="ri-save-line mr-1"></i>
                            保存配置
                        </button>
                    </div>
                </div>

                <!-- 质量分析面板 -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-3">质量分析</h3>
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="mb-4">
                            <h4 class="text-md font-medium text-gray-700 mb-2">参数趋势分析</h4>
                            <div class="quality-chart-container">
                                <canvas id="parameterTrendChart"></canvas>
                            </div>
                        </div>
                        <div>
                            <h4 class="text-md font-medium text-gray-700 mb-2">质量关联分析</h4>
                            <div class="quality-chart-container">
                                <canvas id="qualityCorrelationChart"></canvas>
                            </div>
                        </div>
                        <div class="mt-4 p-3 bg-yellow-50 rounded-md">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="ri-alert-line text-yellow-400"></i>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-yellow-800">优化建议</h3>
                                    <div class="mt-2 text-sm text-yellow-700">
                                        <p>切割速度参数波动较大，建议调整范围为 1200-1400 mm/min 以提高产品质量稳定性。</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建参数模板模态框 -->
    <div x-show="showCreateTemplateModal" x-cloak class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50" x-transition>
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">创建参数模板</h3>
                    <button @click="showCreateTemplateModal = false" class="text-gray-400 hover:text-gray-500">
                        <i class="ri-close-line text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="px-6 py-4">
                <form @submit.prevent="createTemplate">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">模板名称</label>
                            <input type="text" x-model="templateForm.name" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">模板描述</label>
                            <textarea x-model="templateForm.description" rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"></textarea>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">适用工序</label>
                            <select x-model="templateForm.operation" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                <option value="切割">切割</option>
                                <option value="磨边">磨边</option>
                                <option value="钢化">钢化</option>
                                <option value="合片">合片</option>
                                <option value="打孔">打孔</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">适用产品类型</label>
                            <div class="flex flex-wrap gap-2">
                                <template x-for="type in productTypes" :key="type.id">
                                    <label class="inline-flex items-center">
                                        <input type="checkbox" :value="type.id" x-model="templateForm.productTypes" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        <span class="ml-2 text-sm text-gray-700" x-text="type.name"></span>
                                    </label>
                                </template>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">参数项</label>
                            <div class="border border-gray-200 rounded-md p-3 bg-gray-50">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm text-gray-700">已选择参数项</span>
                                    <button type="button" class="text-blue-600 hover:text-blue-800 text-sm flex items-center" @click="showParameterSelector = true">
                                        <i class="ri-add-line mr-1"></i>
                                        添加参数
                                    </button>
                                </div>
                                <div class="space-y-2">
                                    <template x-for="param in templateForm.parameters" :key="param.id">
                                        <div class="flex items-center justify-between bg-white p-2 rounded border border-gray-200">
                                            <div>
                                                <span class="text-sm font-medium" x-text="param.name"></span>
                                                <span class="text-xs text-gray-500 ml-2" x-text="param.code"></span>
                                            </div>
                                            <button type="button" class="text-red-600 hover:text-red-800" @click="removeTemplateParameter(param.id)">
                                                <i class="ri-delete-bin-line"></i>
                                            </button>
                                        </div>
                                    </template>
                                    <div x-show="templateForm.parameters.length === 0" class="text-center py-4 text-gray-500 text-sm">
                                        暂未选择参数项
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50" @click="showCreateTemplateModal = false">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            创建模板
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 参数选择器模态框 -->
    <div x-show="showParameterSelector" x-cloak class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50" x-transition>
        <div class="bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-screen overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">选择参数项</h3>
                    <button @click="showParameterSelector = false" class="text-gray-400 hover:text-gray-500">
                        <i class="ri-close-line text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="px-6 py-4">
                <div class="mb-4">
                    <input type="text" placeholder="搜索参数..." class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div class="border border-gray-200 rounded-lg overflow-hidden">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">选择</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">参数名称</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">参数编码</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单位</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <template x-for="param in allParameters" :key="param.id">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <input type="checkbox" :value="param.id" x-model="selectedParameters" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900" x-text="param.name"></td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500" x-text="param.code"></td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                                              :class="param.type === '数值' ? 'bg-blue-100 text-blue-800' : 
                                                      param.type === '文本' ? 'bg-green-100 text-green-800' : 
                                                      param.type === '枚举' ? 'bg-purple-100 text-purple-800' : 
                                                      'bg-yellow-100 text-yellow-800'"
                                              x-text="param.type"></span>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500" x-text="param.unit"></td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
                <div class="mt-6 flex justify-end space-x-3">
                    <button type="button" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50" @click="showParameterSelector = false">
                        取消
                    </button>
                    <button type="button" class="px-4 py-2 bg-blue-600 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" @click="addSelectedParameters">
                        确认选择
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function parameterLibrary() {
            return {
                // 工序分类数据
                processCategories: [
                    {
                        id: 1,
                        name: '切割工序',
                        icon: 'ri-scissors-cut-line',
                        count: 12,
                        expanded: false,
                        subcategories: [
                            { id: 11, name: '直线切割', count: 5 },
                            { id: 12, name: '异形切割', count: 4 },
                            { id: 13, name: '水刀切割', count: 3 }
                        ]
                    },
                    {
                        id: 2,
                        name: '磨边工序',
                        icon: 'ri-brush-line',
                        count: 8,
                        expanded: false,
                        subcategories: [
                            { id: 21, name: '粗磨', count: 3 },
                            { id: 22, name: '精磨', count: 3 },
                            { id: 23, name: '抛光', count: 2 }
                        ]
                    },
                    {
                        id: 3,
                        name: '钢化工序',
                        icon: 'ri-fire-line',
                        count: 6,
                        expanded: false,
                        subcategories: [
                            { id: 31, name: '物理钢化', count: 3 },
                            { id: 32, name: '化学钢化', count: 3 }
                        ]
                    },
                    {
                        id: 4,
                        name: '合片工序',
                        icon: 'ri-stack-line',
                        count: 10,
                        expanded: false,
                        subcategories: [
                            { id: 41, name: '夹层合片', count: 5 },
                            { id: 42, name: '中空合片', count: 5 }
                        ]
                    }
                ],
                
                // 参数模板数据
                parameterTemplates: [
                    {
                        id: 1,
                        name: '标准切割参数模板',
                        description: '适用于普通玻璃的标准切割参数',
                        operation: '切割',
                        parameterCount: 8,
                        status: '生效',
                        updatedAt: '2025-07-28'
                    },
                    {
                        id: 2,
                        name: '异形切割参数模板',
                        description: '适用于异形玻璃切割的参数配置',
                        operation: '切割',
                        parameterCount: 12,
                        status: '生效',
                        updatedAt: '2025-07-25'
                    },
                    {
                        id: 3,
                        name: '钢化工艺参数模板',
                        description: '玻璃钢化工艺的标准参数配置',
                        operation: '钢化',
                        parameterCount: 15,
                        status: '草稿',
                        updatedAt: '2025-07-30'
                    }
                ],
                
                // 参数列表数据
                parameters: [
                    { id: 1, code: 'CUT_SPEED', name: '切割速度', type: '数值', unit: 'mm/min' },
                    { id: 2, code: 'CUT_PRESSURE', name: '切割压力', type: '数值', unit: 'MPa' },
                    { id: 3, code: 'CUT_ANGLE', name: '切割角度', type: '数值', unit: '°' },
                    { id: 4, code: 'EDGE_TYPE', name: '磨边类型', type: '枚举', unit: '' },
                    { id: 5, code: 'TEMP_PREHEAT', name: '预热温度', type: '数值', unit: '℃' },
                    { id: 6, code: 'TEMP_QUENCH', name: '淬火温度', type: '数值', unit: '℃' }
                ],
                
                // 产品特征数据
                productFeatures: [
                    { id: 1, name: '长度' },
                    { id: 2, name: '宽度' },
                    { id: 3, name: '厚度' },
                    { id: 4, name: '材质' },
                    { id: 5, name: '形状' }
                ],
                
                // 产品类型数据
                productTypes: [
                    { id: 1, name: '普通玻璃' },
                    { id: 2, name: '钢化玻璃' },
                    { id: 3, name: '夹层玻璃' },
                    { id: 4, name: '中空玻璃' },
                    { id: 5, name: '防火玻璃' }
                ],
                
                // 所有参数数据（用于参数选择器）
                allParameters: [
                    { id: 1, code: 'CUT_SPEED', name: '切割速度', type: '数值', unit: 'mm/min' },
                    { id: 2, code: 'CUT_PRESSURE', name: '切割压力', type: '数值', unit: 'MPa' },
                    { id: 3, code: 'CUT_ANGLE', name: '切割角度', type: '数值', unit: '°' },
                    { id: 4, code: 'EDGE_TYPE', name: '磨边类型', type: '枚举', unit: '' },
                    { id: 5, code: 'TEMP_PREHEAT', name: '预热温度', type: '数值', unit: '℃' },
                    { id: 6, code: 'TEMP_QUENCH', name: '淬火温度', type: '数值', unit: '℃' },
                    { id: 7, code: 'COOLING_RATE', name: '冷却速率', type: '数值', unit: '℃/s' },
                    { id: 8, code: 'HOLDING_TIME', name: '保温时间', type: '数值', unit: 'min' },
                    { id: 9, code: 'GRIT_SIZE', name: '磨料粒度', type: '枚举', unit: '' },
                    { id: 10, code: 'FEED_RATE', name: '进给速度', type: '数值', unit: 'mm/min' }
                ],
                
                // 状态变量
                selectedCategory: null,
                selectedSubcategory: null,
                selectedTemplate: null,
                selectedParameter: null,
                showParameterForm: false,
                showCreateTemplateModal: false,
                showParameterSelector: false,
                
                // 参数表单数据
                parameterForm: {
                    id: null,
                    code: '',
                    name: '',
                    type: '数值',
                    unit: '',
                    minValue: '',
                    standardValue: '',
                    maxValue: '',
                    enumValues: [''],
                    formula: '',
                    features: []
                },
                
                // 模板表单数据
                templateForm: {
                    name: '',
                    description: '',
                    operation: '切割',
                    productTypes: [],
                    parameters: []
                },
                
                // 参数计算器数据
                calculator: {
                    length: '',
                    width: '',
                    thickness: '',
                    material: '普通玻璃',
                    showResults: false,
                    results: []
                },
                
                // 选中的参数（用于参数选择器）
                selectedParameters: [],
                
                // 初始化方法
                init() {
                    this.$nextTick(() => {
                        this.initCharts();
                    });
                },
                
                // 初始化图表
                initCharts() {
                    // 参数趋势图
                    const trendCtx = document.getElementById('parameterTrendChart').getContext('2d');
                    new Chart(trendCtx, {
                        type: 'line',
                        data: {
                            labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                            datasets: [{
                                label: '切割速度',
                                data: [1200, 1250, 1180, 1300, 1350, 1280],
                                borderColor: 'rgb(59, 130, 246)',
                                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                                tension: 0.3
                            }, {
                                label: '切割压力',
                                data: [0.8, 0.85, 0.82, 0.9, 0.88, 0.86],
                                borderColor: 'rgb(16, 185, 129)',
                                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                                tension: 0.3
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'top',
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: false
                                }
                            }
                        }
                    });
                    
                    // 质量关联图
                    const correlationCtx = document.getElementById('qualityCorrelationChart').getContext('2d');
                    new Chart(correlationCtx, {
                        type: 'bar',
                        data: {
                            labels: ['切割速度', '切割压力', '切割角度', '预热温度', '淬火温度'],
                            datasets: [{
                                label: '质量关联度',
                                data: [0.85, 0.72, 0.65, 0.78, 0.92],
                                backgroundColor: [
                                    'rgba(59, 130, 246, 0.7)',
                                    'rgba(16, 185, 129, 0.7)',
                                    'rgba(245, 158, 11, 0.7)',
                                    'rgba(139, 92, 246, 0.7)',
                                    'rgba(236, 72, 153, 0.7)'
                                ],
                                borderColor: [
                                    'rgb(59, 130, 246)',
                                    'rgb(16, 185, 129)',
                                    'rgb(245, 158, 11)',
                                    'rgb(139, 92, 246)',
                                    'rgb(236, 72, 153)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    display: false
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    max: 1,
                                    ticks: {
                                        callback: function(value) {
                                            return (value * 100) + '%';
                                        }
                                    }
                                }
                            }
                        }
                    });
                },
                
                // 切换分类展开状态
                toggleCategory(categoryId) {
                    const category = this.processCategories.find(c => c.id === categoryId);
                    if (category) {
                        category.expanded = !category.expanded;
                        this.selectedCategory = category.expanded ? categoryId : null;
                    }
                },
                
                // 选择子分类
                selectSubcategory(subcategoryId) {
                    this.selectedSubcategory = subcategoryId;
                },
                
                // 选择参数模板
                selectTemplate(templateId) {
                    this.selectedTemplate = templateId;
                },
                
                // 编辑参数
                editParameter(parameterId) {
                    const parameter = this.parameters.find(p => p.id === parameterId);
                    if (parameter) {
                        this.parameterForm = { ...parameter };
                        this.showParameterForm = true;
                    }
                },
                
                // 删除参数
                deleteParameter(parameterId) {
                    if (confirm('确定要删除此参数吗？')) {
                        this.parameters = this.parameters.filter(p => p.id !== parameterId);
                    }
                },
                
                // 保存参数
                saveParameter() {
                    if (this.parameterForm.id) {
                        // 更新现有参数
                        const index = this.parameters.findIndex(p => p.id === this.parameterForm.id);
                        if (index !== -1) {
                            this.parameters[index] = { ...this.parameterForm };
                        }
                    } else {
                        // 添加新参数
                        const newId = Math.max(...this.parameters.map(p => p.id), 0) + 1;
                        this.parameters.push({ ...this.parameterForm, id: newId });
                    }
                    this.showParameterForm = false;
                    this.resetParameterForm();
                },
                
                // 重置参数表单
                resetParameterForm() {
                    this.parameterForm = {
                        id: null,
                        code: '',
                        name: '',
                        type: '数值',
                        unit: '',
                        minValue: '',
                        standardValue: '',
                        maxValue: '',
                        enumValues: [''],
                        formula: '',
                        features: []
                    };
                },
                
                // 添加枚举值
                addEnumValue() {
                    this.parameterForm.enumValues.push('');
                },
                
                // 删除枚举值
                removeEnumValue(index) {
                    if (this.parameterForm.enumValues.length > 1) {
                        this.parameterForm.enumValues.splice(index, 1);
                    }
                },
                
                // 创建参数模板
                createTemplate() {
                    const newTemplate = {
                        id: Math.max(...this.parameterTemplates.map(t => t.id), 0) + 1,
                        name: this.templateForm.name,
                        description: this.templateForm.description,
                        operation: this.templateForm.operation,
                        parameterCount: this.templateForm.parameters.length,
                        status: '草稿',
                        updatedAt: new Date().toISOString().split('T')[0]
                    };
                    this.parameterTemplates.push(newTemplate);
                    this.showCreateTemplateModal = false;
                    this.resetTemplateForm();
                },
                
                // 重置模板表单
                resetTemplateForm() {
                    this.templateForm = {
                        name: '',
                        description: '',
                        operation: '切割',
                        productTypes: [],
                        parameters: []
                    };
                },
                
                // 添加选中的参数到模板
                addSelectedParameters() {
                    this.selectedParameters.forEach(paramId => {
                        const param = this.allParameters.find(p => p.id === paramId);
                        if (param && !this.templateForm.parameters.find(p => p.id === paramId)) {
                            this.templateForm.parameters.push(param);
                        }
                    });
                    this.selectedParameters = [];
                    this.showParameterSelector = false;
                },
                
                // 从模板中移除参数
                removeTemplateParameter(parameterId) {
                    this.templateForm.parameters = this.templateForm.parameters.filter(p => p.id !== parameterId);
                },
                
                // 计算工艺参数
                calculateParameters() {
                    // 模拟计算过程
                    this.calculator.results = [
                        { name: '切割速度', value: Math.round(1200 + (this.calculator.length / 10)), unit: 'mm/min' },
                        { name: '切割压力', value: (0.8 + (this.calculator.thickness / 100)).toFixed(2), unit: 'MPa' },
                        { name: '预热温度', value: Math.round(550 + (this.calculator.thickness * 5)), unit: '℃' },
                        { name: '淬火温度', value: Math.round(650 + (this.calculator.thickness * 3)), unit: '℃' }
                    ];
                    this.calculator.showResults = true;
                }
            }
        }
    </script>
</body>
</html>
